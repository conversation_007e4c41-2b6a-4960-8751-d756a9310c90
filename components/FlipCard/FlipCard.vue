<template>
  <view
    :class="containerClass"
    :style="containerStyle"
    @click="handleClick"
  >
    <view class="flip-card-inner" :style="innerStyle" @transitionend="handleTransitionEnd">
      <view class="flip-card-front">
        <slot name="front"></slot>
      </view>
      <view class="flip-card-back">
        <!-- 背面内容容器 -->
        <view class="flip-card-back-content" :class="{ 'content-visible': showBackContent, 'content-fading-in': isContentFadingIn }">
          <slot name="back"></slot>
        </view>
        <!-- 粒子消散遮罩层 - 优化显示逻辑 -->
        <view class="scratch-mask" v-if="enableScratchEffect && modelValue" :class="{ 'scratching': isScratchAnimating, 'hidden': showBackContent }">
          <!-- 使用真实DOM元素替代伪元素，确保在小程序中正常工作 -->
          <view class="particle particle-1"></view>
          <view class="particle particle-2"></view>
          <view class="particle particle-3"></view>
          <view class="particle particle-4"></view>
          <view class="particle particle-5"></view>
          <view class="particle particle-6"></view>
          <view class="particle particle-7"></view>
          <view class="particle particle-8"></view>
          <view class="particle particle-9"></view>
          <view class="particle particle-10"></view>
          <view class="particle particle-11"></view>
          <view class="particle particle-12"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
/**
 * FlipCard 组件
 *
 * 实现3D翻转卡片效果，可以传入自定义的前后视图
 *
 * @props {Boolean} modelValue - 控制卡片是否翻转，可通过v-model绑定
 * @props {String|Number} height - 组件的高度，默认为'200rpx'
 * @props {Number} duration - 动画持续时间，默认为500ms
 * @props {String} direction - 翻转方向，可选值为'horizontal'或'vertical'，默认为'horizontal'
 * @props {String} customClass - 自定义CSS类
 * @props {Object} customStyle - 自定义内联样式
 * @props {Boolean} clickable - 是否可以通过点击切换翻转状态，默认为true
 * @props {Boolean} disableFlipBack - 翻转到背面后是否禁止点击返回正面，默认为false
 * @props {Number} rotations - 旋转圈数，默认为1
 * @props {String} animationType - 动画类型，可选值为'simple'（简单翻转）或'spin'（多圈旋转），默认为'simple'
 * @props {String} easing - 动画缓动函数，默认为'ease'
 * @props {Boolean} enableScratchEffect - 是否启用刮刮乐效果，默认为false
 * @props {Number} scratchDuration - 刮刮乐动画持续时间，默认为1000ms
 *
 * @emits {update:modelValue} - 更新modelValue的值
 * @emits {flip} - 翻转状态改变时触发，参数为当前的翻转状态
 * @emits {scratchComplete} - 刮刮乐动画完成时触发
 *
 * @example
 * <FlipCard v-model="isFlipped" height="300rpx" direction="vertical" enableScratchEffect>
 *   <template #front>
 *     <view>正面内容</view>
 *   </template>
 *   <template #back>
 *     <view>背面内容</view>
 *   </template>
 * </FlipCard>
 */

import { flipCardProps, flipCardEmits, useFlipCard } from './useFlipCard';

const props = defineProps(flipCardProps);
const emit = defineEmits(flipCardEmits);

// 使用组合式函数
// 解构出需要的属性和方法
const {
  innerStyle,
  containerStyle,
  containerClass,
  handleClick,
  isScratchAnimating,
  showBackContent,
  isContentFadingIn, // 添加这个属性以修复警告
  handleTransitionEnd
} = useFlipCard(props, emit);
</script>

<style lang="scss" scoped>
@import './FlipCard.scss';
</style>
