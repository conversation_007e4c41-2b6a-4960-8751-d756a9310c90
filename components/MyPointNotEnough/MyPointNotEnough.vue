<script setup>

import MyInfoPopup2 from "@/components/MyInfoPopup2/MyInfoPopup2.vue";
import {computed, onMounted, onUnmounted, ref} from "vue";
import {usercenter} from "@/api";
import {adViewPost} from "@/api/ad";
import {adViewGet} from "@/api/ad";
import {useGlobalStore} from "@/store/global";
import {navTo, showToast} from "@/utils";
import {isProductionIOS} from "@/utils/device";
import {hmacSha256} from '@/utils/hmac.js'

const emit = defineEmits(['pointChange'])

// 从环境变量获取HMAC密码
const password = import.meta.env.VITE_HMAC_PASSWORD
if (!password) {
  console.error('VITE_HMAC_PASSWORD 环境变量未配置')
}
const globalStore = useGlobalStore()
const task = computed(() => globalStore.shareMiniTask)
const userInfo = ref({})
const points = computed(() => {
  return userInfo.value?.package_info?.amount ?? 0
});
const showBuy = ref(false)

// 防止onClose回调多次调用的状态管理
let isProcessingReward = false

const showInvite = computed(() => {
  return userInfo.can_bind_invite
})
const showShareApp = computed(() => task.value?.task_id)
const showAd = computed(() => globalStore.adConfig.integral_pop !== undefined)
let rewardedVideoAd = null
let key = ''

async function onOpenAd() {
  try {
    // 重置处理状态，准备新的广告观看
    isProcessingReward = false
    console.log('开始加载新广告，重置处理状态')

    // 接口调用成功，显示广告加载提示
    showToast("正在加载视频广告")
    const {data} = await adViewGet()
    key = data.key
    console.log('获取广告key成功:', key)

    if (rewardedVideoAd) {
      rewardedVideoAd.show().then(() => console.log("激励视频广告显示"))
    }
  } catch (error) {
    // 根据错误类型显示不同的提示信息
    const errorMsg = error.msg || '广告加载失败，请稍后重试'
    console.error('广告加载失败:', error)
    showToast(errorMsg)
  }
}

onMounted(() => {
  if (wx && wx.createRewardedVideoAd) {
    rewardedVideoAd = wx.createRewardedVideoAd({
      adUnitId: globalStore.adConfig.integral_pop.ad_id,
    })
    rewardedVideoAd.onLoad(() => {

    })
    rewardedVideoAd.onError((err) => {
      console.log('onError event emit', err)
    })
    rewardedVideoAd.onClose((res) => {
      if (res && res.isEnded) {
        // 防止重复处理：检查是否正在处理
        if (isProcessingReward) {
          console.log('广告奖励处理被跳过：正在处理中', {
            isProcessing: isProcessingReward,
            currentKey: key
          })
          return
        }

        // 设置处理状态，防止重复调用
        isProcessingReward = true

        const data = {
          data: key,
          sign: hmacSha256(password, key),
          pos: 'integral_pop'
        }

        // 用户完整观看广告，调用积分增加接口
        adViewPost(data).then(() => {
          showToast("恭喜，获得积分成功").then(() => emit('pointChange'))
        }).catch((error) => {
          console.error('广告奖励接口调用失败:', error)
          showToast("获取积分失败，请稍后重试")
        }).finally(() => {
          // 无论成功失败都要重置处理状态
          isProcessingReward = false
        })
      } else {
        showToast("未看完视频广告")
      }
    })
  }

  usercenter().then(({data}) => {
    Object.assign(userInfo.value, data)
  })
  isProductionIOS().then(e => showBuy.value = !e)
})

onUnmounted(() => {
  rewardedVideoAd = null
  isProcessingReward = false
})

</script>

<template>
  <MyInfoPopup2>
    <view class="not-enough-container">
      <view class="header">
        <view>
          <image mode="aspectFill" src="https://rp.yjsoft.com.cn/yiban/static/not-enough/point.png"/>
        </view>
        <text class="h">积分不足</text>
        <text>当前积分不足，无法完成行程规划</text>
        <text>积分余额：{{ points }}</text>
      </view>
      <view class="get-list">
        <view v-if="showShareApp" class="share-item" @tap="navTo('pages/index/index', {}, true)">
          <text class="left iconfont icon-fenxiang1"></text>
          <view class="center">
            <view class="c-content">
              <view class="c-title">
                <text>{{ task?.short_desc }}</text>
                <text class="free">免费</text>
              </view>
              <text>每次分享可获得{{ task.value }}积分</text>
            </view>
            <text class="iconfont icon-gengduo"/>
          </view>
        </view>
        <view v-if="showInvite" class="invite-item">
          <text class="left iconfont icon-yaoqingma"></text>
          <view class="center">
            <view class="c-content">
              <view class="c-title">
                <text>使用邀请码</text>
              </view>
              <view class="input" @tap="navTo('pages/mine/invitecode')">输入好友邀请码兑换{{ task.value }}积分</view>
            </view>
          </view>
        </view>
        <view v-if="showAd" class="ad-item" @tap="onOpenAd">
          <text class="left iconfont icon-ziyuan"></text>
          <view class="center">
            <view class="c-content">
              <view class="c-title">
                <text>观看视频广告</text>
              </view>
              <text>每次观看可获得{{ globalStore.adConfig.integral_pop.amount }}积分</text>
            </view>
            <text class="iconfont icon-gengduo"/>
          </view>
        </view>
        <view v-if="showBuy" class="buy-item" @tap="navTo('pages/points/points')">
          <text class="left iconfont icon-gouwuche"></text>
          <view class="center">
            <view class="c-content">
              <view class="c-title">
                <text>购买积分套餐分</text>
              </view>
              <text>解锁更多权益</text>
            </view>
            <text class="iconfont icon-gengduo"/>
          </view>
        </view>
      </view>
    </view>
  </MyInfoPopup2>
</template>

<style lang="scss" scoped>
@use "../../styles/mix";
@use "../../styles/define";

.not-enough-container {
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  background: url(#{define.$static_res_host}/not-enough/not-enough-bg.png) repeat-x;
  background-size: cover;
}

.header {
  @include mix.center(column);
  padding: 120rpx 0 80rpx;

  > view {
    image {
      width: 216rpx;
      height: 216rpx;
    }
  }

  .h {
    font-size: 48rpx;
    font-weight: bold;
    color: black;
    margin-bottom: 20rpx;
  }

  text {
    color: #999999;
  }
}

.get-list {
  background: white;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  > view {
    @include mix.center();
    justify-content: space-between;
    border-radius: 20rpx;
    gap: 16rpx;
    padding: 30rpx;
  }

  .left {
    width: 68rpx;
    height: 68rpx;
    @include mix.center();
    border-radius: 24rpx;
  }

  .center {
    gap: 16rpx;
    flex: 1;
    @include mix.center();
    justify-content: space-between;

    .c-content {
      flex: 1;

      > text {
        &:last-child {
          color: #999999;
          font-size: 20rpx;
        }
      }
    }

    .c-title {
      font-weight: bold;
      @include mix.center();
      justify-content: flex-start;
      gap: 16rpx;
    }

    .input {
      background: #F7F7F9;
      border-radius: 8rpx;
      color: #B7B9BD;
      font-size: 20rpx;
      padding: 16rpx 20rpx;
      width: 70%;
    }
  }

  .share-item {
    .left {
      color: #FACA14;
      background: #FFEDB8;
    }

    .free {
      padding: 4rpx 16rpx;
      font-size: 20rpx;
      color: white;
      border-radius: 16rpx;
      background: #FACA14;
    }
  }

  .invite-item {
    .left {
      color: #52C41A;
      background: #D6FFC3;
    }
  }

  .ad-item {
    .left {
      color: #FF5EAF;
      background: #FFD2E9;
    }
  }

  .buy-item {
    .left {
      color: #1890FF;
      background: #D7ECFF;
    }

  }
}
</style>
