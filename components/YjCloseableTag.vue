<script setup>

const emit = defineEmits(['close']);

function close() {
  emit('close');
}

</script>

<template>
  <view class="yj-closeable-tag">
    <slot></slot>
    <view @tap="close" class="close">
      <text class="iconfont icon-jia2"/>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.yj-closeable-tag {
  position: relative;
  display: block;
  height: 100%;
  width: 100%;

  .close {
    position: absolute;
    right: 0;
    top: 0;
    color: white;
    background: black;
    cursor: pointer;
    width: 28rpx;
    height: 28rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transform: rotate(45deg);

    text {
      font-size: 20rpx;
    }
  }
}
</style>
