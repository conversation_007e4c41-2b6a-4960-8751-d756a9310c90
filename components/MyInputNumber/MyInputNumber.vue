<script setup>
import {ref, watch, computed} from 'vue'

const val = defineModel({type: [String, Number], default: 0})

const emit = defineEmits(['change', 'update:val'])
const props = defineProps({
  step: {type: Number, default: 1},
  type: {type: String, default: 'number'},
  min: {type: Number, default: -Infinity},
  max: {type: Number, default: Infinity},
})

const myVal = ref(0)

// 计算是否可以减少数量（当值等于最小值时不能再减少）
const canDecrease = computed(() => {
  return Number(myVal.value) > props.min
})

// 计算是否可以增加数量
const canIncrease = computed(() => {
  return Number(myVal.value) < props.max
})

watch(val, (value) => {
  myVal.value = +value
}, {immediate: true})

const doStep = (type) => {
  let v = Number(myVal.value)
  if (isNaN(v)) {
    v = 0
  }

  if (type == 'plus') {
    v += props.step
    if (props.max < v) {
      v = props.max
    }
  }
  if (type == 'minus') {
    v -= props.step
    if (props.min > v) {
      v = props.min
    }
  }


  v = +v
  myVal.value = v
  val.value = v
  emit('change', v)
}

function onBlur({detail: {value}}) {
  if (isNaN(value)) {
    myVal.value = props.val
    return
  }
  if (value > props.max) {
    value = props.max
  }
  if (value < props.min) {
    value = props.min
  }
  value = +value
  myVal.value = value
  val.value = value
  emit('change', value)
}

</script>

<template>
  <view class="input-number">
    <text :class="['iconfont', 'icon-minus-circle', {'disabled': !canDecrease}]"
          @click="canDecrease && doStep('minus')"></text>
    <input v-model="myVal" :type="type" auto-blur class="input" @blur="onBlur"/>
    <text :class="['iconfont', 'icon-add-circle', {'disabled': !canIncrease}]"
          @click="canIncrease && doStep('plus')"></text>
  </view>
</template>

<style lang="scss" scoped>
@import '@/styles/_mix.scss';
@import '@/styles/_define.scss';

.input-number {
  @include center();
  flex: 0;
  background: #E7F4FF;
  border-radius: 26rpx;
  gap: 10rpx;

  .iconfont {
    color: #1890FF;
    font-size: 36rpx;

    &.disabled {
      color: #CCCCCC;
      opacity: 0.6;
    }
  }

  .input {
    min-width: 90rpx;
    text-align: center;
    font-size: 32rpx;
    color: $black-color;
  }
}
</style>
