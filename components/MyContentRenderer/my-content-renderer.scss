@import '../../styles/mix';
@import '../../styles/define';

.my-content-renderer {
  width: 100%;
  margin-top: 20rpx;

  // 空列表状态样式
  .empty-state {
    width: 100%;
    min-width: 694rpx;
    box-sizing: border-box;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .empty-icon {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: #F5F5F5;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 30rpx;

        .iconfont {
          font-size: 60rpx;
          color: rgb(16, 161, 218);
        }
      }

      .empty-title {
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 16rpx;
      }

      .empty-description {
        font-size: 24rpx;
        color: #999999;
        line-height: 1.5;
        max-width: 500rpx;
      }
    }
  }

  // 快速操作按钮样式
  .fast-tips {
    display: flex;
    margin-top: 20rpx;

    .force {
      color: white;
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      background: #1890FF;
    }
  }
} 