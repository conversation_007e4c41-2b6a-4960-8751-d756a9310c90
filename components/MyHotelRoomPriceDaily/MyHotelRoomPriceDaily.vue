<template>
	<MyInfoPopup @close="emit('close')" title="费用明细" type="bottom">
		<view class="daily-prices descriptions">
			<view class="description-item total-price">
				<view class="desribe-item-title">合计</view>
				<view>{{formatMoney(totalPrice)}}</view>
			</view>
			<view class="description-item sub-total">
				<view class="desribe-item-title">房费</view>
				<view>{{formatMoney(totalPrice)}}</view>
			</view>
			<view class="description-item price-item" v-for="(item,index) in policy.price_daily" :key="index">
				<view class="desribe-item-title">{{formatTime(item.date, 'MM.DD') + " " + policy.breakfast}}</view>
				<view>{{num}} x {{formatMoney(item.sale_price)}}</view>
			</view>
		</view>

	</MyInfoPopup>
</template>

<script>
	/**
	 * @property {Number} totalPrice
	 */
</script>

<script setup>
	const props = defineProps({
		totalPrice: Number,
		num: Number,
		title: '费用明细',
		policy: {
			type: Object,
			default: {
				price_daily: [],
				breakfast: '',
			}
		}
	})
	const emit = defineEmits(['close'])

	import {
		formatMoney,
		formatTime
	} from '../../utils/index.js'
</script>

<style lang="scss" scoped>
	@import '@/styles/_define.scss';
	@import '@/styles/_mix.scss';

	.daily-prices {
		.total-price {
			border-bottom: 1rpx solid $border-color;
		}

		.description-item {
			justify-content: space-between;
		}

		.total-price,
		.sub-total {
			font-size: $h3;
			font-weight: bold;


			.desribe-item-title {
				color: black;
			}
		}

		.price-item {
			>view {
				&:last-child {
					@include center();
				}
			}
		}
	}
</style>