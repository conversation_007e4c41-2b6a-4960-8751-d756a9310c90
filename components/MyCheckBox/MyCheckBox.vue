<script setup>
import {ref} from "vue";

const props = defineProps({
  checked: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const c = ref(props.checked)
const emit = defineEmits(['change'])

function toggle() {
  if (props.disabled) return
  c.value = !c.value
  emit('change', c.value)
}

</script>

<template>
  <view class="wrap" @tap="toggle">
    <view :class="{
    checked: c
  }" class="checkbox"></view>
    <view v-if="label.length">{{ label }}</view>
  </view>
</template>

<style lang="scss" scoped>
.wrap {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 10rpx;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid #1890FF;
  border-radius: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  position: relative;

  &.checked::after {
    content: '';
    position: absolute;
    top: 2rpx;
    left: 2rpx;
    right: 2rpx;
    bottom: 2rpx;
    background: #1890FF;
    border-radius: 2rpx;
  }
}
</style>
