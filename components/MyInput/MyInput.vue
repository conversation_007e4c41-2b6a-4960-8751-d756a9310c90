<script setup>
const value = defineModel()

const emits = defineEmits(['confirm'])
const props = defineProps({
  beforeIcon: {
    type: String,
    default: ''
  },
  afterIcon: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  wrapStyle: { default: () => ({}) },
  inputStyle: { default: () => ({}) },
})

function onInput(e) {
  value.value = e.detail.value
}

</script>

<template>
  <view class="my-input" :style="wrapStyle">
    <text v-if="beforeIcon" :class="{
      iconfont: true,
      [beforeIcon]: true,
    }" />
    <input :style="inputStyle" :placeholder="placeholder" :value="value" @input="onInput"
      @confirm="e => emits('confirm', e)" />
    <text v-if="afterIcon" :class="{
      iconfont: true,
      [afterIcon]: true,
    }" />
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

.my-input {
  border: 2rpx solid transparent;
  padding: 20rpx 26rpx;
  border-radius: 36rpx;
  @include center();
  justify-content: space-between;
  gap: 20rpx;
  background-color: #EDEEF0;

  .iconfont {
    color: $font-color-gray;
  }

  input {
    color: #999;
    flex: 1;
    height: 100%;
  }
}
</style>