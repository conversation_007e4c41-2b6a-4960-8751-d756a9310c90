<script setup>
import {computed, nextTick, onMounted, onUnmounted, ref} from 'vue';

const props = defineProps({
  fixed: {
    type: Boolean,
    default: true
  }
});

const position = ref({x: 0, y: 0});
const isDragging = ref(false);
const startPos = ref({x: 0, y: 0});
const startTouch = ref({x: 0, y: 0});
const windowSize = ref({width: 0, height: 0});
const isHalfHidden = ref(false);
const buttonSize = ref(90); // rpx
const halfButtonSizePx = ref(0);
const isNearEdge = ref(false);
const rotation = ref(0); // 旋转角度
const isLeftSide = ref(false); // 是否在左侧贴边
const canTriggerContact = ref(true); // 控制是否可以触发客服功能
const showClickArea = ref(true); // 控制扩展点击区域显示
const isInitialized = ref(false); // 标记是否已完成初始化
const autoHideTimer = ref(null); // 自动隐藏定时器

// 获取窗口尺寸
const updateWindowSize = () => {
  const {windowWidth, windowHeight} = uni.getSystemInfoSync();
  windowSize.value = {width: windowWidth, height: windowHeight};
};

// 初始化位置 - 修改为默认贴边
const initPosition = () => {
  updateWindowSize();
  halfButtonSizePx.value = uni.upx2px(buttonSize.value / 2);

  // 默认贴右边并半隐藏
  const finalY = windowSize.value.height - uni.upx2px(buttonSize.value) - uni.upx2px(380);
  position.value = {
    x: windowSize.value.width - halfButtonSizePx.value, // 右侧贴边时隐藏左半边
    y: finalY
  };
  isHalfHidden.value = true;
  isLeftSide.value = false;
  rotation.value = -45; // 右侧贴边时逆时针旋转45度

  // 使用 nextTick 确保位置设置完成后再启用动画
  nextTick(() => {
    isInitialized.value = true;
  });
};

// 清除自动隐藏定时器
const clearAutoHide = () => {
  if (autoHideTimer.value) {
    clearTimeout(autoHideTimer.value);
    autoHideTimer.value = null;
  }
};

// 启动自动隐藏定时器
const startAutoHide = () => {
  clearAutoHide(); // 先清除之前的定时器
  autoHideTimer.value = setTimeout(() => {
    // 3秒后自动贴边隐藏
    if (!isHalfHidden.value) {
      // 确定贴边方向（选择距离最近的边）
      const centerX = position.value.x + halfButtonSizePx.value;
      isLeftSide.value = centerX < windowSize.value.width / 2;

      let finalX;
      if (isLeftSide.value) {
        finalX = -halfButtonSizePx.value; // 向左贴边时隐藏右半边
        rotation.value = 45; // 左侧贴边时顺时针旋转45度
      } else {
        finalX = windowSize.value.width - halfButtonSizePx.value; // 向右贴边时隐藏左半边
        rotation.value = -45; // 右侧贴边时逆时针旋转45度
      }

      position.value = {x: finalX, y: position.value.y};
      isHalfHidden.value = true;
    }
    autoHideTimer.value = null;
  }, 3000);
};

// 触摸开始
const onTouchStart = (e) => {
  isDragging.value = true;
  const touch = e.touches[0] || e.changedTouches[0];
  startTouch.value = {x: touch.clientX, y: touch.clientY};

  // 如果当前是半隐藏状态，先完全显示
  if (isHalfHidden.value) {
    showFully();
    // 更新起始位置为完全显示后的位置
    startPos.value = {...position.value};
    // 阻止自动贴边
    isNearEdge.value = false;
    return;
  }

  // 正常记录起始位置
  startPos.value = {...position.value};

  e.stopPropagation();
};

// 检查是否靠近边缘
const checkNearEdge = (x) => {
  const threshold = 50; // 距离边缘的阈值（像素）
  return x < threshold || x > windowSize.value.width - threshold - halfButtonSizePx.value * 2;
};

// 完全显示按钮
const showFully = () => {
  if (!isHalfHidden.value) return;

  let newX = position.value.x;
  if (newX < 0) {
    newX = 0; // 从左侧贴边完全显示
  } else if (newX > windowSize.value.width - halfButtonSizePx.value * 2) {
    newX = windowSize.value.width - halfButtonSizePx.value * 2; // 从右侧贴边完全显示
  }

  position.value = {...position.value, x: newX};
  isHalfHidden.value = false;
  rotation.value = 0; // 重置旋转角度
  
  // 从半隐藏状态展开后，下一次点击才能触发客服功能
  canTriggerContact.value = false;
  
  // 使用setTimeout确保动画完成后再允许触发客服功能
  setTimeout(() => {
    canTriggerContact.value = true;
    // 展开完成后启动自动隐藏定时器
    startAutoHide();
  }, 300); // 与CSS过渡时间一致
};

// 触摸移动
const onTouchMove = (e) => {
  if (!isDragging.value) return;

  // 用户开始拖动时清除自动隐藏定时器
  clearAutoHide();

  const touch = e.touches[0] || e.changedTouches[0];
  const dx = touch.clientX - startTouch.value.x;
  const dy = touch.clientY - startTouch.value.y;

  let newX = startPos.value.x + dx;
  let newY = startPos.value.y + dy;

  // 限制在屏幕范围内
  newX = Math.max(-halfButtonSizePx.value, Math.min(windowSize.value.width - halfButtonSizePx.value, newX));
  newY = Math.max(0, Math.min(windowSize.value.height - halfButtonSizePx.value * 2, newY));

  position.value = {x: newX, y: newY};

  // 检查是否靠近边缘
  isNearEdge.value = checkNearEdge(newX);

  e.stopPropagation();
  e.preventDefault();
};

// 触摸结束
const onTouchEnd = () => {
  if (!isDragging.value) return;
  isDragging.value = false;

  // 如果是从半隐藏状态拖出来的，不进行贴边处理
  if (isHalfHidden.value) {
    // 重置状态，使下一次点击能触发客服功能
    canTriggerContact.value = false;
    return;
  }

  const finalY = Math.max(0, Math.min(windowSize.value.height - halfButtonSizePx.value * 2, position.value.y));

  // 如果靠近边缘，则贴边并半隐藏
  if (isNearEdge.value) {
    isLeftSide.value = position.value.x < windowSize.value.width / 2;
    let finalX;

    if (isLeftSide.value) {
      finalX = -halfButtonSizePx.value; // 向左贴边时隐藏右半边
      rotation.value = 45; // 左侧贴边时顺时针旋转45度
    } else {
      finalX = windowSize.value.width - halfButtonSizePx.value; // 向右贴边时隐藏左半边
      rotation.value = -45; // 右侧贴边时逆时针旋转45度
    }

    position.value = {x: finalX, y: finalY};
    isHalfHidden.value = true;
  } else {
    // 不靠近边缘，保持当前位置
    position.value = {...position.value, y: finalY};
    isHalfHidden.value = false;
    rotation.value = 0; // 重置旋转角度
  }
};

// 响应式调整位置
const updatePositionOnResize = () => {
  const oldWidth = windowSize.value.width;
  updateWindowSize();

  if (isHalfHidden.value) {
    // 如果是半隐藏状态，保持贴边状态
    const isNearLeft = position.value.x < 0;
    if (isNearLeft) {
      position.value = {x: -halfButtonSizePx.value, y: position.value.y};
    } else {
      position.value = {x: windowSize.value.width - halfButtonSizePx.value, y: position.value.y};
    }
  } else {
    // 如果不是半隐藏状态，保持相对位置
    const ratio = windowSize.value.width / oldWidth;
    position.value = {
      x: Math.min(position.value.x * ratio, windowSize.value.width - halfButtonSizePx.value * 2),
      y: Math.min(position.value.y, windowSize.value.height - halfButtonSizePx.value * 2)
    };
  }
};

onMounted(() => {
  if (props.fixed) {
    initPosition();
    uni.onWindowResize(updatePositionOnResize);
  }

});

onUnmounted(() => {
  if (props.fixed) {
    uni.offWindowResize(updatePositionOnResize);
  }
  // 清除定时器
  clearAutoHide();
});

const btnStyle = computed(() => {
  if (props.fixed) {
    return {
      position: 'fixed',
      left: `${position.value.x}px`,
      top: `${position.value.y}px`,
      transform: `rotate(${rotation.value}deg)`,
      transformOrigin: 'center',
      transition: (isDragging.value || !isInitialized.value) ? 'none' : 'all 0.3s ease',
      zIndex: 9999,
      touchAction: 'none',
      background: 'transparent',
      border: 'none',
      padding: 0,
      margin: 0
    }
  } else {
    return {
      width: '100%',
      background: 'transparent',
      border: 'none',
      padding: 0,
      margin: 0
    }
  }
});

// 扩展点击区域样式
const clickAreaStyle = computed(() => {
  if (!isHalfHidden.value || !showClickArea.value) return { display: 'none' };

  const areaWidth = 60; // 扩展点击区域宽度（像素）
  const areaHeight = uni.upx2px(buttonSize.value) + 20; // 高度比按钮稍大
  const buttonCenterY = position.value.y + halfButtonSizePx.value;

  let left, top;

  if (isLeftSide.value) {
    // 左侧贴边时，点击区域在按钮右侧（屏幕内侧）
    left = position.value.x + halfButtonSizePx.value;
    top = buttonCenterY - areaHeight / 2;
  } else {
    // 右侧贴边时，点击区域在按钮左侧（屏幕内侧）
    left = position.value.x - areaWidth;
    top = buttonCenterY - areaHeight / 2;
  }

  return {
    position: 'fixed',
    left: `${left}px`,
    top: `${top}px`,
    width: `${areaWidth}px`,
    height: `${areaHeight}px`,
    zIndex: 10000,
    display: 'block'
  };
});

</script>
<template>
  <view class="customer-service-wrapper">
    <button
        :class="{ 'dragging': isDragging }"
        :style="btnStyle"
        :open-type="canTriggerContact && !isHalfHidden ? 'contact' : ''"
        @click="isHalfHidden ? showFully() : null"
        @touchcancel="onTouchEnd"
        @touchend="onTouchEnd"
        @touchmove="onTouchMove"
        @touchstart="onTouchStart"
    >
      <slot>
        <view class="gradient-bg">
          <view class="white-bg">
            <image mode="scaleToFill" src="https://rp.yjsoft.com.cn/yiban/static/customer-service.png"></image>
          </view>
        </view>
      </slot>
    </button>

    <!-- 扩展点击区域 - 仅在贴边状态下显示 -->
    <view
        v-if="isHalfHidden && showClickArea"
        :style="clickAreaStyle"
        class="click-area"
        @click="showFully"
        @touchstart.stop="showFully"
    ></view>
  </view>
</template>

<style lang="scss" scoped>
.customer-service-wrapper {
  width: 100%;
  display: block;
}

button {
  border: 0;
  margin: 0;
  padding: 0;
  line-height: 100%;
  white-space: nowrap;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  will-change: transform; /* 优化动画性能 */

  // 支持 slot 内容的布局
  display: block;

  &.dragging {
    opacity: 0.9;
  }

  // 新增样式，用于去除更多默认样式
  outline: none; // 去除点击时的外边框
  -webkit-appearance: none; // 去除 iOS 下的默认样式
  -moz-appearance: none; // 去除 Firefox 下的默认样式
  appearance: none; // 去除默认样式
  text-align: inherit; // 重置文本对齐方式
  text-transform: none; // 重置文本转换
  font-size: inherit; // 重置字体大小
  font-family: inherit; // 重置字体家族
  font-weight: inherit; // 重置字体粗细
  color: inherit; // 重置文字颜色

  &::after {
    border: 0;
  }

  &:before {
    background-color: transparent;
  }

  .gradient-bg {
    width: 90rpx;
    height: 90rpx;
    border-radius: 50%;
    background: linear-gradient(180deg, #7EECFF 0%, #C8E010 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
  }

  .white-bg {
    width: 82rpx;
    height: 82rpx;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 60rpx;
      height: 60rpx;
    }
  }

  // 支持 slot 中的 accordion-item 布局
  :deep(.accordion-item) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    gap: 16rpx;

    .item-right {
      margin-left: auto;
    }
  }
}

/* 扩展点击区域样式 */
.click-area {
  background: transparent;
  cursor: pointer;

  /* 调试时可以临时启用以下样式查看点击区域 */
  /* background: rgba(255, 0, 0, 0.2); */
  /* border: 1px solid red; */
}

</style>
