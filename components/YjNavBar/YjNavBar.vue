<script setup>

import {computed, onMounted, ref} from "vue";

/**
 * load 加载完成，返回{height,paddingBottom}
 * notFound 没有找到上一页
 */
const emit = defineEmits(['load', 'notFound'])

const props = defineProps({
  title: { // 标题
    type: String,
    default: '',
  },
  customClass: { // 自定义类
    type: String,
    default: '',
  },
  customStyle: { // 自定义样式
    type: Object,
    default: () => ({
      backgroundColor: '#fff',
    }),
  },
  paddingBottom: { // 底部间距
    type: Number,
    default: () => uni.rpx2px(12)
  },
  arrowColor: { // 箭头颜色
    type: String,
    default: () => '#000'
  }
})

const box = ref({
  height: 50,
  titleHeight: 32,
  padding: 10,
  maxWidth: 200,
})

const style = computed(() => {
  const ret = {
    height: `${box.value.height + props.paddingBottom}px`,
    paddingBottom: `${props.paddingBottom}px`,
  }

  return Object.assign(ret, props.customStyle)
})
const contentStyle = computed(() => {
  return {
    paddingLeft: `${box.value.padding}px`,
    maxHeight: `${box.value.titleHeight + 10}px`,
    height: `${box.value.titleHeight}px`,
    maxWidth: `${box.value.maxWidth}px`,
    paddingRight: `${box.value.padding}px`,
  }
})
const hasBack = computed(() => {
  return getCurrentPages().length > 1
})

function goBack() {
  if (hasBack.value) {
    uni.navigateBack()
  } else {
    emit('notFound')
  }
}

onMounted(() => {
  uni.getSystemInfo({
    success(res) {
      const {screenWidth} = res
      const ret = {
        maxWidth: screenWidth,
      }
      // #ifdef MP
      const menuButton = uni.getMenuButtonBoundingClientRect();
      if (menuButton) {
        Object.assign(ret, {
          height: menuButton.top + menuButton.height,
          titleHeight: menuButton.height,
          padding: (screenWidth - menuButton.right) * 2,
        })
        ret.maxWidth = screenWidth - menuButton.width - (ret.padding / 2)
      }
      // #endif

      Object.assign(box.value, ret)

      emit('load', {
        height: box.value.height,
        paddingBottom: props.paddingBottom,
      })
    }
  })

});
</script>

<template>
  <view :class="customClass" :style="style" class="yj-nav-bar">
    <view :style="contentStyle" class="content">
      <view class="back">
        <slot name="left">
          <image class="arrow" src="./back.svg" @tap="goBack"/>
        </slot>
      </view>
      <slot name="title">
        <view v-if="title" class="title">{{ title }}</view>
      </slot>

      <!-- 中间插槽 -->
      <!--      <view class="center-slot">-->
      <slot name="center"></slot>
      <!--      </view>-->
    </view>

  </view>
</template>

<style lang="scss" scoped>
.yj-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  //width: 1000%;
  display: flex;
  height: 50px; // 根据需要调整高度
  align-items: flex-end;
  //padding-bottom: 6rpx;
  box-sizing: border-box;
  z-index: 999;

  .content {
    box-sizing: border-box;
    //padding: 0 20rpx;
    display: flex;
    gap: 5rpx;
    align-items: center;
    flex: 1;

    //.title {
    //  color: white;
    //}

    .back {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .arrow {
        width: 44rpx;
        height: 44rpx;
      }
    }
  }
}
</style>
