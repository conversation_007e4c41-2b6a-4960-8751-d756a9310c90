<template>
  <uni-popup ref="popup" :class="type" :mask-click="maskClick" :type="type" border-radius="10rpx" class="my-info-popup"
             @change="handleChange">
    <view class="main">
      <view v-if="title.length > 0" class="popup-title">
        <text class="iconfont icon-guanbi" @tap="onClose"></text>
        <view>
          {{ title }}
        </view>
        <view class="action" @tap="onAction">{{ actionText }}</view>
      </view>
      <view class="popup-content">
        <slot>
          {{ content }}
        </slot>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import {computed, onBeforeUnmount, onMounted, ref} from 'vue';

const popup = ref(null)
const emit = defineEmits(['close', 'action'])
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: ''
  },
  close: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: "bottom"
  },
  actionText: {
    type: String,
    default: ''
  },
  maskClick: {
    type: Boolean,
    default: true
  }
})
const content = computed(() => props.text)
const show = ref(false)

function handleChange(e) {
  if (!e.show) {
    emit('close')
  }
}

function onClose() {
  popup.value.close()
}

function onAction() {
  emit('action')
}

onMounted(() => {
  popup.value.open()
  show.value = true
})
onBeforeUnmount(() => {
  show.value = false
})

</script>

<style lang="scss" scoped>
@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.center {
  .popup-content {
    margin: $padding;
    border-radius: $border-radius;
    @include center();
  }
}

.bottom {
  .main {
    background: #f8f8f8;
    border-top-left-radius: $border-radius-max;
    border-top-right-radius: $border-radius-max;

    .popup-title,
    .popup-content {
      padding: $padding-v2;
    }

    .popup-title {
      @include center();
      justify-content: space-between;

      .action {
        color: $plan-color-1;
      }

      // >view {
      // flex: 1;
      // text-align: center;
      // }

      // font-size: $h3-v2;
      // font-weight: bold;
    }

    .popup-content {
    }
  }
}
</style>