<template>
  <view
    :class="['event-blocker', customClass, `direction-${blockDirection}`]"
    :style="[{ height: height }, customStyle]"
    @touchstart="blockEvents.includes('touchmove') ? handleTouchStart : null"
    @touchmove="blockEvents.includes('touchmove') ? handleTouchMove : null"
    @wheel="blockEvents.includes('wheel') ? preventEvent : null"
    @click="blockEvents.includes('click') ? preventEvent : null"
    @tap="blockEvents.includes('tap') ? preventEvent : null"
  >
    <view class="event-blocker-content">
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue';

/**
 * EventBlocker 组件
 *
 * 用于阻止各种事件（点击、滑动等）向底层传递的通用布局组件
 *
 * @props {String|Number} height - 组件的高度，默认为 'auto'
 * @props {Array} blockEvents - 要阻止的事件类型数组，默认为 ['touchmove', 'wheel']
 * @props {String} customClass - 自定义 CSS 类
 * @props {Object} customStyle - 自定义内联样式
 * @props {String} blockDirection - 要阻止的滑动方向，可选值为 'vertical'、'horizontal' 或 'both'，默认为 'both'
 *
 * @example
 * <EventBlocker height="500rpx" blockDirection="horizontal">
 *   <view>这里的内容滚动不会影响底层视图，但只阻止横向滑动</view>
 * </EventBlocker>
 */

const props = defineProps({
  // 组件高度
  height: {
    type: [String, Number],
    default: 'auto'
  },
  // 要阻止的事件类型
  blockEvents: {
    type: Array,
    default: () => ['touchmove', 'wheel']
  },
  // 自定义类名
  customClass: {
    type: String,
    default: ''
  },
  // 自定义样式
  customStyle: {
    type: Object,
    default: () => ({})
  },
  // 要阻止的滑动方向
  blockDirection: {
    type: String,
    default: 'both',
    validator: (value) => ['vertical', 'horizontal', 'both'].includes(value)
  }
});

// 存储触摸开始位置
const touchStartX = ref(0);
const touchStartY = ref(0);

/**
 * 处理触摸开始事件
 *
 * @param {TouchEvent} e - 触摸事件对象
 */
function handleTouchStart(e) {
  // 记录触摸开始位置
  touchStartX.value = e.touches[0].clientX;
  touchStartY.value = e.touches[0].clientY;
}

/**
 * 处理触摸移动事件
 *
 * @param {TouchEvent} e - 触摸事件对象
 */
function handleTouchMove(e) {
  // 如果没有触摸点，直接返回
  if (!e.touches.length) return;

  // 计算触摸移动距离
  const deltaX = e.touches[0].clientX - touchStartX.value;
  const deltaY = e.touches[0].clientY - touchStartY.value;

  // 判断移动方向（水平或垂直）
  const isHorizontalMove = Math.abs(deltaX) > Math.abs(deltaY);

  // 根据 blockDirection 属性决定是否阻止事件
  if (
    (props.blockDirection === 'both') ||
    (props.blockDirection === 'horizontal' && isHorizontalMove) ||
    (props.blockDirection === 'vertical' && !isHorizontalMove)
  ) {
    // 阻止事件冒泡和默认行为
    preventEvent(e);
  }
}

/**
 * 阻止事件传播
 *
 * @param {Event} e - 事件对象
 */
function preventEvent(e) {
  // 阻止事件冒泡
  e.stopPropagation();

  // 阻止默认行为（如滚动）
  if (e.cancelable) {
    e.preventDefault();
  }
}
</script>

<style lang="scss" scoped>
@import './EventBlocker.scss';
</style>
