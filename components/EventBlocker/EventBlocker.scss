@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.event-blocker {
  // 基本容器样式
  position: relative;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;

  // 滚动行为控制
  overscroll-behavior: contain; // 防止滚动链接（现代浏览器）
  -webkit-overflow-scrolling: touch; // 平滑滚动（iOS）

  // 隐藏滚动条
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  // Firefox 隐藏滚动条
  scrollbar-width: none;

  // IE 和 Edge 隐藏滚动条
  -ms-overflow-style: none;

  // 根据方向设置触摸行为
  &.direction-both {
    touch-action: none; // 禁止所有触摸行为
  }

  &.direction-vertical {
    touch-action: pan-x; // 只允许水平滑动
  }

  &.direction-horizontal {
    touch-action: pan-y; // 只允许垂直滑动
  }

  // 内容容器
  .event-blocker-content {
    width: 100%;
    height: 100%;
    position: relative;
  }
}
