<template>
  <view v-if="visible" class="ios-alert-overlay" @tap="handleOverlayClick">
    <view class="ios-payment-alert" @tap.stop>
      <!-- 内容区域 -->
      <view class="content">
        <text class="title">iOS端暂不支持购买</text>
        <text class="subtitle">由于相关规范限制，iOS端小程序无法购买积分</text>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <view class="btn-confirm" @tap="handleClose">
          我知道了
        </view>
      </view>
    </view>
  </view>
</template>

<!--
  iOS支付提醒弹窗组件

  功能说明：
  - 专门针对iOS端小程序支付限制的提醒弹窗
  - 符合微信开发者中心规范，不引导其他支付方式
  - 简洁的提醒设计

  使用方法：
  <MyIOSPaymentAlert
    :visible="showAlert"
    @close="handleClose"
  />

  Props:
  - visible: Boolean - 控制弹窗显示/隐藏

  Events:
  - close: 关闭弹窗
-->

<script setup>
import { defineProps, defineEmits } from 'vue'

// Props定义
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits(['close'])

// 处理关闭弹窗
const handleClose = () => {
  emit('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  handleClose()
}
</script>

<style lang="scss" scoped>
@import './MyIOSPaymentAlert.scss';
</style>
