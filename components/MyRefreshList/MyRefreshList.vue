<template>
  <view class="my-refresh-list-wrapper" :style="{ height: height }">
    <!-- 自定义刷新区域 -->
    <view
      v-if="customRefresh"
      class="custom-refresh-area"
      :style="{
        height: pullDistance + 'px',
        transform: `translateY(${pullDistance > 0 ? 0 : -pullDistance}px)`
      }"
    >
      <slot name="refresh" :status="customRefreshStatus" :distance="pullDistance">
        <view class="default-refresh-content">
          <view v-if="customRefreshStatus === 'pulling'" class="refresh-text">下拉刷新</view>
          <view v-else-if="customRefreshStatus === 'refreshing'" class="refresh-text">正在刷新...</view>
          <view v-else class="refresh-text">释放刷新</view>
        </view>
      </slot>
    </view>

    <scroll-view
      class="my-refresh-list"
      :style="{ height: '100%' }"
      scroll-y
      :refresher-enabled="enableRefresh && !customRefresh"
      :refresher-triggered="refreshing"
      @refresherrefresh="handleRefresh"
      @scrolltolower="handleLoadMore"
      :lower-threshold="lowerThreshold"
      enhanced
      :show-scrollbar="false"
      :scroll-with-animation="true"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
    <!-- 列表内容 -->
    <view class="list-content">
      <!-- 列表项 -->
      <view v-if="data.length > 0" class="list-items">
        <view v-for="(item, index) in data" :key="getItemKey(item, index)" class="list-item">
          <slot :item="item" :index="index"></slot>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading && !refreshing" class="empty-state">
        <slot name="empty">
          <view class="empty-content">
            <text class="empty-text">{{ emptyText }}</text>
          </view>
        </slot>
      </view>

      <!-- 加载更多状态 -->
      <view v-if="enableLoadMore && data.length > 0" class="load-more-container">
        <uni-load-more
          :status="loadMoreStatus"
          :content-text="loadMoreText"
          @clickLoadMore="handleLoadMoreClick"
        />
      </view>
    </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'

/**
 * MyRefreshList - 支持上拉加载和下拉刷新的通用列表组件
 * 
 * @description 封装了完整的列表滚动加载逻辑，支持下拉刷新和上拉加载更多
 * 
 * Props:
 * @param {Array} data - 列表数据
 * @param {Boolean} loading - 是否正在加载
 * @param {Boolean} hasMore - 是否还有更多数据
 * @param {Boolean} refreshing - 是否正在刷新
 * @param {String} emptyText - 空状态文案
 * @param {Object} loadMoreText - 加载更多状态文案配置
 * @param {String} height - 列表高度
 * @param {Boolean} enableRefresh - 是否启用下拉刷新
 * @param {Boolean} enableLoadMore - 是否启用上拉加载
 * @param {Number} lowerThreshold - 距底部多远时触发加载更多
 * @param {String} itemKey - 列表项的唯一标识字段名
 * @param {Number} refreshTimeout - 刷新超时时间（毫秒）
 * @param {Number} loadMoreTimeout - 加载更多超时时间（毫秒）
 * @param {Boolean} customRefresh - 是否使用自定义刷新样式
 * @param {Number} refreshThreshold - 自定义刷新触发距离
 *
 * Events:
 * @event refresh - 下拉刷新事件
 * @event load-more - 上拉加载更多事件
 * @event refresh-timeout - 刷新超时事件（父组件需要在此事件中将refreshing设为false）
 * @event load-more-timeout - 加载更多超时事件（组件内部已自动重置loading状态）
 *
 * Slots:
 * @slot default - 列表项内容，提供item和index参数
 * @slot empty - 自定义空状态
 * @slot refresh - 自定义刷新样式，提供status和distance参数
 */

const props = defineProps({
  // 列表数据
  data: {
    type: Array,
    default: () => []
  },
  // 是否正在加载
  loading: {
    type: Boolean,
    default: false
  },
  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: true
  },
  // 是否正在刷新
  refreshing: {
    type: Boolean,
    default: false
  },
  // 空状态文案
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  // 加载更多状态文案配置
  loadMoreText: {
    type: Object,
    default: () => ({
      contentdown: '上拉显示更多',
      contentrefresh: '正在加载...',
      contentnomore: '没有更多数据了'
    })
  },
  // 列表高度
  height: {
    type: String,
    default: '100%'
  },
  // 是否启用下拉刷新
  enableRefresh: {
    type: Boolean,
    default: true
  },
  // 是否启用上拉加载
  enableLoadMore: {
    type: Boolean,
    default: true
  },
  // 距底部多远时触发加载更多
  lowerThreshold: {
    type: Number,
    default: 50
  },
  // 列表项的唯一标识字段名
  itemKey: {
    type: String,
    default: 'id'
  },
  // 刷新超时时间（毫秒）
  refreshTimeout: {
    type: Number,
    default: 10000
  },
  // 加载更多超时时间（毫秒）
  loadMoreTimeout: {
    type: Number,
    default: 10000
  },
  // 是否使用自定义刷新样式
  customRefresh: {
    type: Boolean,
    default: false
  },
  // 自定义刷新触发距离
  refreshThreshold: {
    type: Number,
    default: 80
  }
})

const emit = defineEmits(['refresh', 'load-more', 'refresh-timeout', 'load-more-timeout'])

// 防抖控制
const isLoadingMore = ref(false)
const refreshDebounceTimer = ref(null)
const loadMoreDebounceTimer = ref(null)

// 超时控制
const refreshTimeoutTimer = ref(null)
const loadMoreTimeoutTimer = ref(null)

// 自定义刷新相关状态
const customRefreshStatus = ref('normal') // normal, pulling, refreshing
const pullDistance = ref(0)
const startY = ref(0)
const isPulling = ref(false)

// 计算加载更多状态
const loadMoreStatus = computed(() => {
  if (props.loading || isLoadingMore.value) {
    return 'loading'
  }
  if (!props.hasMore) {
    return 'noMore'
  }
  return 'more'
})

// 获取列表项的key
function getItemKey(item, index) {
  if (item && typeof item === 'object' && item[props.itemKey]) {
    return item[props.itemKey]
  }
  return `item_${index}`
}

// 处理下拉刷新
function handleRefresh() {
  // 防抖处理
  if (refreshDebounceTimer.value) {
    clearTimeout(refreshDebounceTimer.value)
  }

  refreshDebounceTimer.value = setTimeout(() => {
    if (!props.refreshing && !props.loading) {
      emit('refresh')

      // 设置刷新超时
      if (refreshTimeoutTimer.value) {
        clearTimeout(refreshTimeoutTimer.value)
      }
      refreshTimeoutTimer.value = setTimeout(() => {
        if (props.refreshing) {
          console.warn('MyRefreshList: 刷新超时，自动结束刷新状态')
          // 发射超时事件，让父组件处理状态重置
          emit('refresh-timeout')
        }
      }, props.refreshTimeout)
    }
  }, 100)
}

// 处理上拉加载更多
function handleLoadMore() {
  // 防抖处理
  if (loadMoreDebounceTimer.value) {
    clearTimeout(loadMoreDebounceTimer.value)
  }

  loadMoreDebounceTimer.value = setTimeout(() => {
    if (props.enableLoadMore && props.hasMore && !props.loading && !isLoadingMore.value && !props.refreshing) {
      isLoadingMore.value = true
      emit('load-more')

      // 设置加载更多超时
      if (loadMoreTimeoutTimer.value) {
        clearTimeout(loadMoreTimeoutTimer.value)
      }
      loadMoreTimeoutTimer.value = setTimeout(() => {
        if (isLoadingMore.value) {
          console.warn('MyRefreshList: 加载更多超时，自动结束加载状态')
          isLoadingMore.value = false
          // 发射超时事件，让父组件处理
          emit('load-more-timeout')
        }
      }, props.loadMoreTimeout)
    }
  }, 100)
}

// 处理加载更多点击
function handleLoadMoreClick() {
  if (loadMoreStatus.value === 'more') {
    handleLoadMore()
  }
}

// 监听loading状态变化，重置加载更多状态和清理超时
watch(() => props.loading, (newLoading) => {
  if (!newLoading) {
    isLoadingMore.value = false
    // 清理加载更多超时定时器
    if (loadMoreTimeoutTimer.value) {
      clearTimeout(loadMoreTimeoutTimer.value)
      loadMoreTimeoutTimer.value = null
    }
  }
})

// 原有的refreshing监听已合并到上面的自定义刷新监听中

// 清理定时器
function cleanup() {
  if (refreshDebounceTimer.value) {
    clearTimeout(refreshDebounceTimer.value)
    refreshDebounceTimer.value = null
  }
  if (loadMoreDebounceTimer.value) {
    clearTimeout(loadMoreDebounceTimer.value)
    loadMoreDebounceTimer.value = null
  }
  if (refreshTimeoutTimer.value) {
    clearTimeout(refreshTimeoutTimer.value)
    refreshTimeoutTimer.value = null
  }
  if (loadMoreTimeoutTimer.value) {
    clearTimeout(loadMoreTimeoutTimer.value)
    loadMoreTimeoutTimer.value = null
  }
}

// 自定义刷新手势处理
function handleTouchStart(e) {
  if (!props.customRefresh) return

  startY.value = e.touches[0].clientY
  isPulling.value = false
}

function handleTouchMove(e) {
  if (!props.customRefresh || props.refreshing) return

  const currentY = e.touches[0].clientY
  const deltaY = currentY - startY.value

  // 只有向下拉且在顶部时才处理
  if (deltaY > 0) {
    isPulling.value = true
    pullDistance.value = Math.min(deltaY * 0.5, props.refreshThreshold * 1.5) // 阻尼效果

    if (pullDistance.value >= props.refreshThreshold) {
      customRefreshStatus.value = 'ready'
    } else {
      customRefreshStatus.value = 'pulling'
    }
  }
}

function handleTouchEnd() {
  if (!props.customRefresh || !isPulling.value) return

  if (pullDistance.value >= props.refreshThreshold && !props.refreshing) {
    // 触发刷新
    customRefreshStatus.value = 'refreshing'
    handleRefresh()
  } else {
    // 重置状态
    resetCustomRefresh()
  }
}

// 重置自定义刷新状态
function resetCustomRefresh() {
  customRefreshStatus.value = 'normal'
  pullDistance.value = 0
  isPulling.value = false
}

// 监听refreshing状态变化，处理自定义刷新
watch(() => props.refreshing, (newRefreshing) => {
  if (!newRefreshing && props.customRefresh) {
    // 刷新完成，重置自定义刷新状态
    setTimeout(() => {
      resetCustomRefresh()
    }, 300) // 延迟重置，让动画更自然
  }

  // 原有的超时清理逻辑
  if (!newRefreshing) {
    if (refreshTimeoutTimer.value) {
      clearTimeout(refreshTimeoutTimer.value)
      refreshTimeoutTimer.value = null
    }
  }
})

// 组件卸载时清理
onUnmounted(() => {
  cleanup()
})
</script>

<style lang="scss" scoped>
@import './MyRefreshList.scss';
</style>
