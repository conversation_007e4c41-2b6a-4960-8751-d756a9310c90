<script setup>
import { throttle } from '@/utils';
import { computed, getCurrentInstance, ref, watch } from 'vue';

const props = defineProps({
  first: { default: '#' },
  indexes: Array,
  list: Array,
  height: {
    type: [String, Number],
    default: '100%',
  }
})
const active = ref(-1)
const scrollId = ref('index-1')
const instance = getCurrentInstance()
const query = uni.createSelectorQuery().in(instance.proxy)
const touching = ref(false)
const tooltip = ref({
  content: '',
  styles: {},
})
const ch = ref(0)

const doScroll = (idx) => {
  active.value = idx
  scrollId.value = 'index' + idx;
  (async (idx) => {
    const [eidx, etooltip] = await Promise.all([
      new Promise(r => query.select(`.indexes .idx${idx}`).boundingClientRect(r).exec()),
      new Promise(r => query.select('.tooltip').boundingClientRect(r).exec()),
    ])
    Object.assign(tooltip.value, {
      content: idx >= 0 ? props.indexes[idx] : props.first,
      styles: {
        visibility: 'visible',
        top: (eidx.top + (eidx.height - etooltip.height) / 2) + 'px',
      },
    })
    if (ch.value) {
      clearTimeout(ch.value)
    }
    ch.value = setTimeout(() => {
      Object.assign(tooltip.value, {
        styles: {
          visibility: 'hidden',
        },
      })
    }, 300)
  })(idx);
}

const doScrolled = async ({ detail: { scrollTop } }) => {
  if (touching.value) {
    return
  }
  let dats = await new Promise(r => query.selectAll('.index-list-chunk').boundingClientRect(r).exec())
  let heights = 0
  dats.find((v, i) => {
    if (heights <= scrollTop && (heights + v.height) > scrollTop || i == (dats.length - 1)) {
      active.value = i - 1
      return true
    }
    heights += v.height
    return false
  })
}

const doTouchStart = () => {
  touching.value = true
}
const doTouchMove = throttle(async (e) => {
  if (!e.touches.length || !touching.value) {
    return
  }
  let offsetTop = e.touches[e.touches.length - 1].clientY
  const [wrap, dats, tp] = await Promise.all([
    new Promise(r => query.select('.indexes').boundingClientRect(r).exec()),
    new Promise(r => query.selectAll('.indexes .idx').boundingClientRect(r).exec()),
    new Promise(r => query.select('.tooltip').boundingClientRect(r).exec()),
  ])
  let top = dats[0].top
  dats.find((v, i) => {
    if (top < offsetTop && v.top + v.height >= offsetTop || i == dats.length - 1) {
      doScroll(i - 1)
      Object.assign(tooltip.value, {
        content: i > 0 ? props.indexes[i - 1] : props.first,
        styles: {
          visibility: 'visible',
          top: (v.top + v.height / 2 - tp.height / 2) + 'px',
        },
      })
      return true
    }
    top = v.top + v.height
  })

}, 50)

const doTouchEnd = () => {
  touching.value = false
  Object.assign(tooltip.value.styles, {
    visibility: 'hidden',
  })
}

</script>

<template>
  <view class="wrap" @touchend="doTouchEnd">
    <scroll-view :style="{ height }" :scroll-into-view="scrollId" enhanced :show-scrollbar="false" scroll-y
      class="container" @scroll="doScrolled">
      <view id="index-1" class="index-list-chunk">
        <slot name="header"></slot>
      </view>
      <view v-for="(chunk, i) in list" :key="i" :id="'index' + i" class="index-list-chunk">
        <view class="index-list-line">{{ indexes[i] }}</view>
        <view v-for="(item, j) in chunk" :key="j" class="index-list-line data">
          <slot :row="item"></slot>
        </view>
      </view>
      <slot name="footer"></slot>
    </scroll-view>
    <view class="indexes" @touchstart="doTouchStart">
      <view class="idx idx-1" :class="{ active: active == -1 }" @tap="doScroll(-1)">{{ first }}</view>
      <view class="idx" v-for="(item, i) in indexes" :key="i" :class="[active == i ? 'active' : '', ['idx' + i]]"
        @tap="doScroll(i)">
        {{ item }}
      </view>
    </view>
    <view class="tooltip" :style="tooltip.styles">
      <view class="cnt">{{ tooltip.content }}</view>
      <!-- <view class="arrow"></view> -->
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '@/styles/_mix.scss';

.wrap {
  overflow: hidden;
}

.wrap,
.container {
  // height: 100%;
  // position: relative;
}

.indexes {
  position: fixed;
  right: 20rpx;
  top: 50%;
  z-index: 999;
  translate: 0 -50%;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .idx {
    margin: 5rpx 0;
    padding: 0 6rpx;
    height: 30rpx;
    border-radius: 10rpx;
    line-height: 30rpx;
    font-size: 24rpx;
    text-align: center;
    color: #1890ff;

    &.active {
      background-color: #1890ff;
      color: white;
    }
  }
}

.index-list-line {
  padding: 20rpx 0 28rpx 0;
  border-bottom: 2rpx solid rgba(237, 237, 237, 0.9);

  &:active {
    background-color: #EDEEF0;
  }
}

.tooltip {
  visibility: hidden;
  position: absolute;
  right: 65rpx;
  top: 0;
  z-index: 999;
  color: white;
  font-size: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.7;

  .cnt {
    height: 56rpx;
    width: 56rpx;
    @include center();
    background-color: black;
    border-radius: 50%;
  }

  // .arrow {
  //   border-left: 14rpx solid black;
  //   border-top: 14rpx solid transparent;
  //   border-bottom: 14rpx solid transparent;
  //   border-radius: 18rpx;
  //   width: 0;
  //   height: 0;
  //   margin-left: -2rpx;
  // }
}
</style>
