<template>
  <view :class="classes" class="myinput2">
    <input :placeholder="placeholder" :value="value" @confirm="onConfirm" @input="onInput"/>
  </view>
</template>
<script setup>

import {computed} from "vue";

const value = defineModel()
const emit = defineEmits(['change', 'confirm'])
const props = defineProps({
  placeholder: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'normal' // small, normal, large
  },
  border: {
    type: Boolean,
    default: true
  }
})

const classes = computed(() => {
  const list = [`size-${props.size}`]
  if (props.border) {
    list.push('border')
  }

  return list
})

function onInput({detail}) {
  value.value = detail.value
  emit('change', value.value)
}

function onConfirm(e) {
  emit('confirm', e.detail.value)
}

</script>
<style lang="scss" scoped>
.myinput2 {
  input {
    padding-left: 20rpx;
    line-height: 76rpx;
    height: 76rpx;
    font-size: 28rpx;
  }

  &.border {
    input {
      border: 2rpx solid #EDEEF0;
      border-radius: 16rpx;
    }
  }

  &.size-small {
    input {
      height: 56rpx;
      line-height: 56rpx;
      font-size: 20rpx;
    }
  }
}
</style>
