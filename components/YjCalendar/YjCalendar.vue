<template>
  <view>
    <!-- 弹出模式 -->
    <uni-popup v-if="popup" ref="popupRef" type="bottom" @change="onPopupChange">
      <view class="yj-calendar">
        <view class="calendar-header">
          <text class="header-title">{{ calendarTitle }}</text>
        </view>
        <view class="calendar-body">
          <view class="month-header">
            <view class="direction-left" @click="prevMonth"></view>
            <text>{{ headerTitle }}</text>
            <view class="direction-right" @click="nextMonth"></view>
          </view>
          <template v-if="mode === 'month'">
            <view class="year-months">
              <view v-for="(day, index) in days" :key="index" :class="{
                'day-selected': isSelected(day),
              }" @tap="handleDayClick(day)">{{ `${day.date}月` }}
              </view>
            </view>
          </template>
          <template v-else>
            <view class="weekday-header">
              <text v-for="day in weekDays" :key="day" class="week-day">{{ day }}</text>
            </view>
            <view class="month-days-container">
              <view class="month-footer">
                <text class="current-month">{{ currentMonth }}</text>
              </view>
              <view class="month-days">
                <view v-for="(day, index) in days" :key="index" class="day-container">
                  <text :class="{
                'day-selected': isSelected(day),
                'day-disabled': isDisabled(day),
                'day-in-range': isInRange(day)
              }" class="day" @click="handleDayClick(day)">
                    {{ day.date }}
                  </text>
                  <text v-if="day.holidayName" class="holiday-name">{{ day.holidayName }}</text>
                </view>
              </view>
            </view>
          </template>

        </view>
        <view class="calendar-footer">
          <view class="confirm-btn" @click="confirmSelection">确认</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue';
import dayjs from "dayjs";

const props = defineProps({
  // 模式：single（单日期）或 range（日期范围）
  mode: {
    type: String,
    default: 'single',
    validator: (value) => ['single', 'range', 'month'].includes(value),
  },
  // 默认值（单日期模式为字符串，范围模式为数组）
  defaultValue: {
    type: [String, Array],
    default: '',
  },
  // 标题
  title: {
    type: String,
    default: '',
  },
  // 是否以弹出形式显示
  popup: {
    type: Boolean,
    default: false,
  },
  // 最大可选日期范围（单元素数组限制开始时间，双元素数组限制结束日期）
  maxRange: {
    type: Array,
    default: () => [],
  },
  // 自定义周几的显示文案
  weekDays: {
    type: Array,
    default: () => ['日', '一', '二', '三', '四', '五', '六'],
  },
  // 节假日回调函数
  holidayCallback: {
    type: Function,
    default: null,
  },
});

const emit = defineEmits(['confirm', 'close', 'monthChange']);

const selectedDate = ref(props.mode === 'single' ? props.defaultValue : '');
const selectedRange = ref(props.mode === 'range' ? (Array.isArray(props.defaultValue) ? [...props.defaultValue] : []) : []);
const selectMonth = ref(props.mode === 'month' ? props.defaultValue : '')
const popupRef = ref(null);

// 当前显示的年份和月份
const currentYear = ref(new Date().getFullYear());
const currentMonth = ref(new Date().getMonth() + 1);

const calendarTitle = computed(() => {
  if (props.title) {
    return props.title;
  }
  switch (props.mode) {
    case 'range':
      return '选择日期范围'
    case 'month':
      return '选择月份'
    default:
      return '选择日期'
  }
})
const headerTitle = computed(() => {
  return props.mode === 'month' ? `${currentYear.value}年` : `${currentYear.value}年${currentMonth.value}月`
})

const holidays = ref({})

// 生成当前月份的日期数据
const days = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month - 1, 1);
  const lastDay = new Date(year, month, 0);
  const daysInMonth = lastDay.getDate();
  const firstDayOfWeek = firstDay.getDay();

  const days = [];
  const today = new Date();
  const todayStr = dayjs(today).format('YYYY-MM-DD');

  if (props.mode === 'month') {
    for (let i = 1; i <= 12; i++) {
      days.push({
        date: i,
        dateStr: `${currentYear.value}-${String(i).padStart(2, '0')}`,
        disabled: false,
        holidayName: '',
      });
    }
    return days;
  }

  // 填充上个月的日期
  for (let i = 0; i < firstDayOfWeek; i++) {
    days.push({date: '', disabled: true});
  }
  // 填充当前月的日期
  for (let i = 1; i <= daysInMonth; i++) {
    const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
    const isToday = dateStr === todayStr;
    days.push({
      date: i,
      dateStr,
      disabled: isDateDisabled(dateStr),
      holidayName: isToday ? '今天' : holidays.value[dateStr] || '',
    });
  }
  return days;
});
// 判断日期是否禁用
const isDateDisabled = (dateStr) => {
  const date = new Date(dateStr);
  const today = new Date();
  const currentMonthDate = new Date(today.getFullYear(), today.getMonth(), 1);

  // 如果是过去的时间且不在maxRange范围内，则禁用
  if (date < currentMonthDate && (props.maxRange.length === 0 || date < new Date(props.maxRange[0]))) {
    return true;
  }

  // 根据 maxRange 限制可选时间范围
  if (props.maxRange.length === 1) {
    return date < new Date(props.maxRange[0]);
  } else if (props.maxRange.length === 2) {
    return date < new Date(props.maxRange[0]) || date > new Date(props.maxRange[1]);
  }
  return false;
};

// 上一月
const prevMonth = () => {
  if (props.mode === 'month') {
    currentYear.value--
  } else {
    if (currentMonth.value === 1) {
      currentMonth.value = 12;
      currentYear.value--;
    } else {
      currentMonth.value--;
    }

    monthChange()
  }

};

// 下一月
const nextMonth = () => {
  if (props.mode === 'month') {
    currentYear.value++
  } else {
    if (currentMonth.value === 12) {
      currentMonth.value = 1;
      currentYear.value++;
    } else {
      currentMonth.value++;
    }

    monthChange()
  }

};

function monthChange() {
  if (props.mode === 'month') {
    return
  }

  loadHolidays()
  emit('monthChange', {year: currentYear.value, month: currentMonth.value})
}

// 判断日期是否选中（单日期模式）
const isSelected = (day) => {
  if (props.mode === 'single' && day.dateStr === selectedDate.value) {
    return true
  } else if (props.mode === 'month') {
    return day.dateStr === selectMonth.value
  }

  return false
};

// 判断日期是否在选中范围内（范围模式）
const isInRange = (day) => {
  if (props.mode !== 'range' || selectedRange.value.length === 0) return false;
  if (selectedRange.value.length === 1) {
    return day.dateStr === selectedRange.value[0];
  }
  const [start, end] = selectedRange.value;
  const date = new Date(day.dateStr);
  return date >= new Date(start) && date <= new Date(end);
};

// 判断日期是否禁用
const isDisabled = (day) => {
  return day.disabled;
};

function onPopupChange(e) {
  if (!e.show) {
    emit('close');
  }
}

// 处理日期点击事件
const handleDayClick = (day) => {
  if (day.disabled) return;
  if (props.mode === 'month') {
    selectMonth.value = day.dateStr
  } else if (props.mode === 'single') {
    selectedDate.value = day.dateStr;
  } else {
    if (selectedRange.value.length === 0 || selectedRange.value.length === 2) {
      selectedRange.value = [day.dateStr];
    } else {
      const [start] = selectedRange.value;
      if (new Date(day.dateStr) < new Date(start)) {
        selectedRange.value = [day.dateStr, start];
      } else {
        selectedRange.value = [start, day.dateStr];
      }
    }
  }
};

// 关闭弹窗
const closePopup = () => {
  if (popupRef.value) popupRef.value.close();
  emit('close');
};

// 确认选择
const confirmSelection = () => {
  if (props.mode === 'single') {
    emit('confirm', selectedDate.value);
  } else if (props.mode === 'month') {
    emit('confirm', selectMonth.value);
  } else {
    emit('confirm', selectedRange.value.length === 2 ? selectedRange.value : []);
  }

  closePopup();
};

async function loadHolidays() {
  if (props.holidayCallback) {
    const holidaysData = await props.holidayCallback(`${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}`);
    holidaysData.forEach(item => {
      holidays.value[dayjs.unix(item.date).format('YYYY-MM-DD')] = item.festival
    })
  }
}

onMounted(() => {
  loadHolidays()
  if (props.maxRange.length > 0) {
    const minDate = new Date(props.maxRange[0]);
    const maxDate = props.maxRange.length > 1 ? new Date(props.maxRange[1]) : minDate;

    // 选择范围中间的月份作为初始月份
    const midDate = new Date((minDate.getTime() + maxDate.getTime()) / 2);

    currentYear.value = midDate.getFullYear();
    currentMonth.value = midDate.getMonth() + 1;
  }

  if (props.popup && popupRef.value) {
    popupRef.value.open();
  }
});
</script>

<style lang="scss" scoped>
$active-color: #FF4D4F;

.yj-calendar {
  width: 100%;
  padding: 20px;
  background-color: #fff;

  .calendar-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;

    .header-title {
      font-size: 18px;
      font-weight: bold;
    }

    .header-subtitle {
      font-size: 14px;
      color: #666;
    }
  }

  .year-months {
    display: grid;
    grid-template-columns: repeat(4, 1fr);

    > view {
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;

      &.day-selected {
        background-color: $active-color;
        color: #fff;
      }
    }
  }

  .weekday-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    justify-items: center;
    margin-bottom: 10px;

    .week-day {
      font-size: 14px;
      color: #666;
    }
  }

  .month-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .direction-left, .direction-right {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }

    .direction-left {
      background: url("data:image/svg+xml;charset=UTF-8,%3Csvg fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 18L9 12L15 6' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E") no-repeat center;
    }

    .direction-right {
      background: url("data:image/svg+xml;charset=UTF-8,%3Csvg fill='none' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 6L15 12L9 18' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'/%3E%3C/svg%3E") no-repeat center;
    }
  }

  .month-days-container {
    position: relative;

    .month-days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      //grid-gap: 5px;
      position: relative;
      z-index: 1;

      .day {
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;

        &.day-selected {
          background-color: $active-color;
          color: #fff;
          border-radius: 50%;
        }

        &.day-disabled {
          color: #ccc;
        }

        &.day-in-range {
          background-color: $active-color;
          color: white;
        }
      }

      .holiday-name {
        font-size: 10px;
        color: $active-color;
        display: block;
        text-align: center;
      }
    }
  }

  .month-footer {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .current-month {
      font-size: 300rpx;
      font-weight: bold;
      color: #ccc;
      opacity: 0.6;
    }
  }
}

.calendar-footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;

  .confirm-btn {
    width: 80%;
    padding: 22rpx 0;
    text-align: center;
    background: #1890FF;
    color: #fff;
    border-radius: 42rpx;
  }
}
</style>
