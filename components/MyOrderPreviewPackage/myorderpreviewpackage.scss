@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.container {
  height: 100vh;

  // #ifdef H5
  height: calc(100vh - 44px);
  //  #endif

  position: relative;
  display: flex;
  flex-direction: column;
}

.days {
  background: white;
  @include center();
  justify-content: space-between;
  gap: 64rpx;
  padding: 12rpx 48rpx 20rpx;
  border-bottom: 2rpx solid #EDEEF0;

  scroll-view {
    white-space: nowrap;
    flex: 1;
    overflow: hidden;
  }

  .calendar {
    font-size: 40rpx;
    color: #1890FF;
  }

  .day {
    display: inline-block;

    > view {
      padding: 6rpx 10rpx;
      @include center(column);
      font-size: 20rpx;
      margin-right: 32rpx;

      > view {
        &:first-child {
          .value {
            font-size: 40rpx;
            font-weight: 600;
          }

          margin-bottom: 4rpx;
        }

        &:last-child {
          text-align: center;
        }
      }

      &.active {
        border-radius: 20rpx;
        background: #E7F4FF;

        > view {
          &:first-child {
            color: #1890FF;
          }

          &:last-child {
            background: #FACA14;
            border-radius: 14rpx;
          }
        }
      }

      &:last-child {
        margin-right: 0;
      }
    }


  }
}

.container {
  > scroll-view {
    flex: 1;
    overflow: hidden;
  }
}

.form-card {
  background: $page-bg-color;
  padding-bottom: 20rpx;
  border-radius: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &:first-child {
    margin-top: 20rpx;

  }

  .product {
    background: white;
    padding: 18rpx 18rpx 30rpx 18rpx;
    @include center();
    justify-content: space-between;
    align-items: stretch;
    position: relative;

    .left {
      position: relative;

      .form-card-index {
        position: absolute;
        z-index: 2;
        font-style: italic;
        width: 32rpx;
        height: 32rpx;
        @include center(column);
        font-weight: bold;
        background: #FACA14;
        border-radius: 50%;
      }

      .img {
        width: 170rpx;
        height: 170rpx;
        border-radius: 50% 8rpx 8rpx 8rpx;
      }
    }

    .item-icon {
      color: white;
      background: #1890FF;
      box-shadow: inset 2rpx 0rpx 6rpx 0rpx rgba(0, 0, 0, 0.12);
      border-radius: 0rpx 20rpx 0rpx 30rpx;
      width: 60rpx;
      height: 60rpx;
      @include center();
      position: absolute;
      z-index: 1;
      top: 0;
      right: 0;

      &.scene {
        background: #52C41A;
      }
    }

    .right {
      flex: 1;
      @include center(column);
      align-items: stretch;
      margin-left: 16rpx;
      justify-content: flex-start;

      .line {
        max-width: 460rpx;
        @include ellipse();
        margin-bottom: 10rpx;
        font-size: 24rpx;
        color: $black-color;

        &.title {
          font-size: 28rpx;
          font-weight: bold;
        }
      }

      .hotel-switch {
        display: flex;
        justify-content: flex-end;
        padding-right: 48rpx;

        > view {
          gap: 4rpx;
          @include center();
          padding: 6rpx 16rpx;
          border-radius: 24rpx;
          border: 2rpx solid #1890FF;
          color: #1890FF;
          font-size: 20rpx;

          .iconfont {
            background: #1890FF;
            color: white;
            border-radius: 50%;
            font-size: 18rpx;
            padding: 4rpx;
          }
        }

      }
    }
  }

  .icard {
    margin: 20rpx 28rpx;

    & > .title {
      color: $black-color;
      font-weight: bold;
      font-size: 28rpx;
    }

    .inner {
      padding: 20rpx 0;
    }

    .picker-date {
      display: inline-block;
      padding: 16rpx 40rpx;
      background: #EDEEF0;
      border-radius: 20rpx;
    }

    .input-number {
      width: 194rpx;
      margin-top: 6rpx;
      margin-left: 36rpx;
    }

    .policy {
      @include center(column);
      //width: 634rpx;
      margin-bottom: 20rpx;

      .room {
        width: 100%;
        //padding: 20rpx 0 0;
        background: white;
        border: 2rpx solid transparent;
        border-radius: 20rpx;
        overflow: hidden;

        &.active {
          border-color: #1890FF;

          .rinfo {
            .right {
              .iconfont {
                transform: rotate(180deg);
              }
            }
          }
        }

        .rinfo {
          box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
          padding: 20rpx 20rpx 20rpx;
          @include center();
          justify-content: space-between;

          .left {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            gap: 16rpx;

            image {
              width: 120rpx;
              height: 120rpx;
              border-radius: 8rpx;
            }

            > view {
              @include center(column);
              gap: 6rpx;
              align-items: flex-start;

              .name {
                font-size: 28rpx;
                color: $black-color;
              }

              .lines {
                margin-top: 6rpx;
                font-size: 20rpx;
                color: $black-color-v3;
              }
            }

          }

          .right {
            @include center(column);
            align-items: flex-end;
            font-size: 20rpx;

            .iconfont {
              @include center();
              width: 40rpx;
              height: 40rpx;
              font-weight: bold;

              &.up {
                transform: rotate(180deg);
              }
            }

            .price {
              font-size: $normal-size;
              font-weight: 600;
            }
          }
        }

        .rextra {
          &:last-child {
            border-bottom: none;
          }

          border-bottom: 2rpx solid #EDEEF0;
          box-shadow: none;
          padding: 26rpx 20rpx 20rpx;
          @include center();
          justify-content: space-between;
          border-left: 8rpx solid transparent;

          &.active {
            border-left-color: #1890FF;
          }

          .left {
            display: flex;
            flex-direction: column;

            .name {
              @include center();
              justify-content: flex-start;

              > view {
                @include ellipse();
                max-width: 204rpx;
              }

              .tip {
                font-size: 20rpx;
                background: #FACA14;
                border-radius: 8rpx;
                padding: 4rpx 8rpx;
                color: #694209;
              }
            }

            > view {
              &:last-child {
                font-size: 20rpx;
                color: #999999;
              }
            }
          }

          .right {
            flex: 1;
            @include center();
            justify-content: flex-end;

            .price {
              color: #1890FF;
              margin-top: 0;
              font-weight: bold;
            }

            .number {
              margin-left: 24rpx;
            }
          }
        }
      }
    }

    .dates {
      @include center();
      justify-content: flex-start;
      font-size: 28rpx;
      color: $black-color;

      .drange {
        padding: 16rpx 20rpx;
        @include center();
        background: #EDEEF0;
        border-radius: 20rpx;

        .iconfont {
          margin-left: 44rpx;
          font-size: 18rpx;
        }
      }

      .nights {
        margin-left: 20rpx;
      }
    }

    .peoples {
      @include center(column);
      gap: 20rpx;
      align-items: flex-start;

      .people-item {
        width: 100%;
        @include center();
        justify-content: space-between;
        background: #EDEEF0;
        border-radius: 20rpx;
        padding: 16rpx 20rpx;
        gap: 16rpx;

        .number {
          font-weight: bold;
        }

        .name {
          flex: 1;
        }

        &.empty {
          .name {
            color: #999;
          }
        }
      }
    }

    .tickets {
      @include center(column);
      align-items: stretch;

      .lines {
        background: white;
        border-radius: 20rpx;
        box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
        padding: 20rpx 0;

        > view {
          @include center();
          justify-content: flex-start;
          gap: 20rpx;
          border-left: 8rpx solid transparent;
          padding: 0 20rpx;
        }

        &.active {
          > view {
            border-color: #52C41A;
          }
        }

        &:not(:first-child) {
          margin-top: 20rpx;
        }

        .ticket {
          flex: 1;
          @include center();
          justify-content: space-between;
          gap: 20rpx;

          .left {
            @include center(column);
            align-items: flex-start;
            font-size: 28rpx;
            color: $black-color;
            //max-width: 200rpx;

            .tip {
              margin-top: 6rpx;
              font-size: 20rpx;
              color: $black-color-v3;
            }
          }

          .price {
            font-weight: 600;
            //font-size: 20rpx;
            color: #1890FF;
          }
        }
      }
    }

    .book-date {
      @include center(column);
      align-items: flex-start;
      gap: 20rpx;

      .date {
        padding: 16rpx 20rpx;
        @include center();
        justify-content: space-between;
        width: 292rpx;
        background: #EDEEF0;
        border-radius: 20rpx;
        font-size: 28rpx;
        color: $black-color;

        .iconfont {
          font-size: 18rpx;
        }
      }

      .times {
        @include center();
        justify-content: flex-start;
        gap: 20rpx;
        flex-wrap: wrap;

        .time {
          padding: 16rpx 20rpx;
          background: #EDEEF0;
          border-radius: 20rpx;
          border: 2rpx solid transparent;

          &.active {
            border-color: #1890FF;
          }
        }
      }
    }

    .empty-tickets {
      @include center();
      color: #999999;
      background: #F7F7F9;
      gap: 32rpx;

      image {
        width: 162rpx;
      }
    }
  }
}

.peoples {
  background: white;
  padding: 20rpx;
}

.contacts {
  background: white;
  padding: 30rpx 28rpx;

  .title {
    font-weight: bold;
  }

  .people-item {
    margin: 20rpx 0;
    background: #EDEEF0;
    border-radius: 20rpx;
    padding: 16rpx 20rpx;
    @include center();
    gap: 16rpx;

    &.empty {
      .name, .iconfont {
        color: #999;
      }
    }

    .name {
      flex: 1;
    }

    .number {
      font-weight: bold;
    }
  }
}

.next-btn {
  @include center();
  justify-content: space-between;
  background: white;
  padding: 36rpx 28rpx;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  border-top-right-radius: 36rpx;
  border-top-left-radius: 36rpx;

  .total-amount {
    color: #1890FF;
    margin-left: 20rpx;
  }


}
