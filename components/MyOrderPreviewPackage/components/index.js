import {hotelRoomPolicy} from "@/api/hotel"
import {sceneTickets} from "@/api/scene"
import dayjs from 'dayjs'

export class OPeople {
  constructor() {
    this.reset()
  }
  
  reset() {
    this.name = null
    this.id_type = 0
    this.id_no = ''
    this.phone = ''
  }
  
}

export class OScene {
  constructor(scene) {
    Object.assign(this, scene)
    /** @type {Array<OTicket>} */
    this.tickets = []
    this.date = null
  }
  
  isFree() {
    return this.scene.is_free
  }
  
  /**
   * 清除门票选中状态
   */
  clearChecked() {
    this.tickets.forEach(it => {
      it.quantity = 0
      it.peoples = []
      it.price = 0
    })
  }
  
  getPeoples() {
    /** @type {Array<OPeople>} */
    let p = []
    this.tickets.forEach(t => {
      p = p.concat(t.peoples)
    })
    return p
  }
  
  async loadTickets() {
    const {data: {list}} = await sceneTickets(this.id, false)
    list.forEach(v => this.tickets.push(new OTicket(this, v)))
  }
  
}

export class OTicket {
  /**
   *
   * @param {OScene} scene
   * @param {Object} ticket
   */
  constructor(scene, ticket) {
    Object.assign(this, ticket)
    this.scene = scene
    /** @type {Array<OPeople>} */
    this.peoples = []
    this.quantity = 0
    this.date = null
    this.price = 0
  }
}

export class OHotel {
  constructor(hotel) {
    Object.assign(this, hotel)
    /** @type {Array<ORoom>} */
    this.rooms = []
    this.date = []
  }
  
  getNightCount() {
    return dayjs(this.date[1]).diff(this.date[0], 'day')
  }
  
  getPeoples() {
    /** @type {Array<OPeople>} */
    let p = []
    this.loopPolicy(po => {
      p = p.concat(po.peoples)
    })
    return p
  }
  
  /**
   * @param {function(OPolicy):void} cb
   */
  loopPolicy(cb) {
    this.rooms.forEach(v => v.policies.forEach(cb))
  }
  
  async loadRooms() {
    let that = this
    let rooms = []
    const params = {
      hotel_id: this.id,
      start: dayjs(this.date[0]).unix(),
      end: dayjs(this.date[1]).unix(),
    }
    const {data: {otas}} = await hotelRoomPolicy(params, false)
    otas.forEach(ota => {
      ota.rooms.forEach(room => {
        const totalPrice = room.policy.reduce((ret, policy) => {
          return ret + policy.sales_price
        }, 0)
        room.sale_price = totalPrice / room.policy.length
        let oroom = new ORoom(that, room, {
          ota_id: ota.ota_id,
          ota_code: ota.ota_code,
          product_type: ota.product_type,
        })
        rooms.push(oroom)
      })
    })
    this.rooms = rooms
  }
}

export class ORoom {
  /**
   *
   * @param {OHotel} hotel
   * @param {Object} room
   * @param {{ota_id:any,ota_code:any,product_type:any}} ota
   */
  constructor(hotel, room, ota) {
    Object.assign(this, room)
    this.hotel = hotel
    this.open = false
    /** @type {Array<OPolicy>} */
    this.policies = room.policy.map(po => new OPolicy(hotel, this, po))
    this.quantity = 0
    this.ota = ota
  }
}

export class OPolicy {
  /**
   *
   * @param {OHotel} hotel
   * @param {ORoom} room
   * @param {Object} policy
   */
  constructor(hotel, room, policy) {
    Object.assign(this, policy)
    this.hotel = hotel
    this.room = room
    /** @type {Array<OPeople>} */
    this.peoples = []
    this.quantity = 0
  }
}