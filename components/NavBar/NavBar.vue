<template>

  <view :style="{
            height: `${navbar.height}px`
        }" class="navbar">
    <view></view>
    <view :style="{
                height:`${navbar.titleHeight}px`,
                marginLeft: `${navbar.marginRight}px`
            }" class="inner">
      <template v-if="showNav">
        <view class="nav" @tap="onPrev">
          <text v-if="hasPrevPage" class="iconfont icon-fanhui1"></text>
          <text v-else-if="showHome" class="iconfont icon-zhuye"></text>
        </view>
      </template>
      <slot>
        <view class="title">{{ title }}</view>
      </slot>

    </view>
  </view>

</template>

<script setup>
import {
  computed,
  onMounted
} from 'vue';
import {
  useGlobalStore
} from '../../store/global';

const props = defineProps({
  title: {
    type: String,
    default: () => '义伴AI'
  },
  showNav: {
    type: <PERSON>olean,
    default: true
  }
})

const globalStore = useGlobalStore()

const navbar = computed(() => globalStore.navbar)
const hasPrevPage = computed(() => {
  return getCurrentPages().length > 1
})
const showHome = computed(() => {
  if (hasPrevPage.value) {
    return false
  }

  const pages = getCurrentPages()
  const current = pages[pages.length - 1]

  return current.route != '/pages/index/index'
})

function onPrev() {
  if (getCurrentPages().length > 1) {
    uni.navigateBack()
  } else {
    uni.redirectTo({
      url: '/pages/index/index'
    })
  }
}

onMounted(() => {

})
</script>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  // background-image: url('https://rp.yjsoft.com.cn/nanmu/static/top-bg.jpg');
  background-position: top center;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: column-reverse;
  z-index: 100;

  .inner {
    display: flex;
    gap: 10rpx;
    align-items: center;

    .nav text,
    .title {
      font-size: 38rpx;
    }
  }
}
</style>
