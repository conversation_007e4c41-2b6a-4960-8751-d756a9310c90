<script setup>

import {onMounted, ref} from "vue";

const emit = defineEmits(['do', 'close'])
const props = defineProps({
  showDoBtn: {
    type: Boolean,
    default: true
  },
  maskClick: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: 'center'
  }
})

const popup = ref(null)

function onChange(e) {
  if (e.show === false) {
    emit('close')
  }
}

function onTapClose() {
  popup.value.close()
}

onMounted(() => {
  popup.value.open()
})

</script>

<template>
  <view class="my-activity-popup">
    <uni-popup ref="popup" :mask-click="maskClick" :type="type" @change="onChange">
      <view class="my-activity-popup-header">
        <text class="iconfont icon-guanbi" @tap="onTapClose"></text>
      </view>
      <view class="my-activity-popup-content">
        <slot></slot>
      </view>

      <slot name="footer">
        <view v-if="showDoBtn" class="my-activity-popup-footer">
          <view class="popup-ok" @tap="emit('do')">去看看</view>
        </view>
      </slot>

    </uni-popup>
  </view>

</template>

<style lang="scss" scoped>
@use "../../styles/mix";

.my-activity-popup-header {
  @include mix.center();
  margin-bottom: 30rpx;

  text {
    background-color: rgba(100, 100, 100, 0.3); // 更深的灰色
    @include mix.center();
    width: 50rpx;
    height: 50rpx;
    color: white;
    border-radius: 50%;
    font-size: 25rpx;

    &:active {
      background-color: rgba(100, 100, 100, 0.1); // 更深的灰色
    }
  }
}

.my-activity-popup-footer {
  margin-top: 36rpx;
  @include mix.center();

  .popup-ok {
    padding: 16rpx 108rpx;
    background: #FBD35A;
    border-radius: 48rpx;
  }
}

</style>