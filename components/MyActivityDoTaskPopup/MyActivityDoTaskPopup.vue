<script setup>

import {ref, watch} from "vue";
import {doActivityTask} from "@/api/activity";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  joinActReward: {
    type: Object,
    default: () => ({}),
  },
})

const reward = ref({
  name: '',
  reward_amount: 0,
})

const doShow = (rd) => {
  if (!rd.reward_amount) {
    return
  }
  reward.value = Object.assign({}, rd)
  setTimeout(() => {
    reward.value = {
      name: '',
      reward_amount: 0,
    }
  }, 1500)
}

watch(() => props.data, async (dt) => {
  const {data: {rewards}} = await doActivityTask(Object.assign({}, dt))
  if (rewards.length > 0) {
    doShow(rewards[0])
  }
}, {deep: true})

watch(() => props.joinActReward, (dt) => {
  doShow(Object.assign({}, dt))
}, {deep: true})

</script>

<template>
  <view class="dotask" :class="{show:reward.name}">
    <view class="dotask-box" v-if="reward.name">
      <image class="icon" mode="aspectFit" src="https://rp.yjsoft.com.cn/yiban/static/activity/doActTaskPopIcon.png"/>
      <text class="label">活动：</text>
      <text class="name">“{{ reward.name }}”</text>
      <text class="reward">积分+{{ reward.reward_amount }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";


.dotask {
  &.show {
    display: block;
  }

  display: none;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;

  .dotask-box {
    position: absolute;
    left: 50%;
    top: 60vh;
    translate: -50%;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 34rpx;
    @include center();
    padding: 12rpx 24rpx 12rpx 8rpx;
    justify-content: flex-start;
    color: white;

    .icon {
      width: 60rpx;
      height: 60rpx;
    }

    .label {
      font-size: 34rpx;
      font-weight: bold;
    }

    .name {
      font-size: 28rpx;
      padding-right: 24rpx;
    }

    .reward {
      margin-left: auto;
      color: #52C41A;
      font-size: 28rpx;
    }

    text {
      vertical-align: middle;
      display: inline-flex;
      word-break: keep-all;
      white-space: nowrap;
      overflow-wrap: break-word;
      line-height: 0;
    }
  }
}
</style>