<script setup>
import {planSubmit} from "@/api/plan";
import {formatMoney, navTo, showToast} from "@/utils";
import {ref, computed} from "vue";
import {planRemove} from "@/api/user";

const props = defineProps({
  plan: {
    type: Object,
    default: () => {
      return {
        ai_reqid: '',
        plan_id: 0,
        is_own: false,
      }
    }
  },
  itemIndex: {
    type: Number,
    default: 0
  }
})

const is_own = ref(props.plan.is_own)
const plan_id = ref(props.plan.plan_id)

const gradientColors = [
  {from: 'rgba(153, 199, 255, 0.5)', to: 'rgba(243, 247, 255, 0.5)'}, // 蓝色系
  {from: 'rgba(210, 190, 251, 0.5)', to: 'rgba(251, 241, 255, 0.5)'}, // 紫色系
  {from: 'rgba(153, 255, 232, 0.5)', to: 'rgba(229, 255, 245, 0.5)'}, // 绿色系
  {from: 'rgba(251, 213, 190, 0.5)', to: 'rgba(255, 248, 241, 0.5)'}, // 橙色系
  {from: 'rgba(255, 188, 230, 0.5)', to: 'rgba(255, 243, 244, 0.5)'}  // 粉色系
]

const gradientStyle = computed(() => {
  const color = gradientColors[props.itemIndex % gradientColors.length]
  return {
    background: `radial-gradient(circle at 100% 0%, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%),
                linear-gradient(180deg, ${color.from} 0%, ${color.to} 100%)`
  }
})

function onTapFav() {
  const data = {
    ai_reqid: props.plan.ai_reqid
  }
  if (is_own.value) {
    planRemove(plan_id.value).then(() => {
      is_own.value = false
      plan_id.value = 0
      showToast('已取消收藏')
    })
  } else {
    planSubmit(data).then((res) => {
      plan_id.value = res.data.id
      is_own.value = true
      showToast('收藏成功')
    })
  }
}

function onTapDetail() {
  navTo('pages/details/details', {ai_reqid: props.plan.ai_reqid})
}

</script>

<template>
  <view :style="gradientStyle" class="item">
    <view class="item-header">
      <view @tap="onTapDetail">{{ plan.subject }}</view>
      <text :class="{
        fav: is_own,
        'icon-a-shoucang-weishoucang': !is_own,
        'icon-shoucang3': is_own,
      }" class="iconfont" @tap="onTapFav"></text>
    </view>
    <view class="summary" @tap="onTapDetail">
      <view class="tags">
        <text v-for="tag in plan.tags" :key="tag">{{ tag }}</text>
      </view>
      <view>预计费用：{{ formatMoney(plan.cost_avg1) }}元/人</view>
    </view>
    <view class="d" @tap="onTapDetail">{{ plan.subtitle }}</view>
  </view>
</template>

<style lang="scss" scoped>
@use "../../styles/mix";

.item {

  border-radius: 16rpx;
  padding: 30rpx 20rpx;

  .item-header {
    @include mix.center();
    justify-content: space-between;
    padding-bottom: 10rpx;

    view {
      font-weight: bold;
    }

    .fav {
      color: red;
    }
  }

  .summary {
    @include mix.center();
    justify-content: space-between;
    margin-bottom: 10rpx;
    font-size: 20rpx;

    .tags {
      display: flex;
      gap: 12rpx;

      text {
        color: #1890FF;
        border-radius: 4rpx;
        padding: 4rpx 12rpx;

        background: #C0FAFF;

        &:last-child {
          background: #FFF2C1;
          color: #694209;
        }
      }
    }

    > view {
      &:last-child {
        color: #1890FF;
      }
    }
  }
}
</style>
