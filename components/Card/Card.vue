<template>
  <view class="card">
    <slot name="header">
      <view v-if="title" class="header">
        <text v-if="icon" class="iconfont" :class="icon" :style="{
          color: iconColor
        }" />
        <text class="title">{{ title }}</text>
      </view>
    </slot>

    <div class="content">
      <slot></slot>
    </div>
  </view>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss" scoped>
@import '../../styles/_define.scss';

.card {
  //  box-shadow: 0rpx 0rpx 30rpx 3rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid #edeef0;
  border-radius: 20rpx;
  background: white;
  padding: 30rpx;

  .header {
    font-size: 34rpx;
    font-weight: 500;
    margin-bottom: 10rpx;

    .iconfont {
      margin-right: 10rpx;
    }

    .title {
      font-weight: bold;
    }
  }

  .content {
    // padding: 16rpx $padding-page;
  }
}
</style>
