<script setup>

import {computed, inject, onMounted, ref, watch} from "vue";

const props = defineProps({
  value: {
    type: Object,
    default: () => '',
  }, //选中的值
  label: {
    type: String,
    default: '',
  }, //显示的值
  checked: {
    type: Boolean,
    default: false,
  }, //默认选中状态
  disable: {
    type: Boolean,
    default: false,
  },
})

const isDisable = computed(() => props.disable)
const isChecked = ref(props.checked)
const group = inject('yj-checkbox-group')

watch(() => props.checked, (value) => {
  isChecked.value = value
})

function setChecked(checked) {
  if (!group) {
    return
  }

  if (checked) {
    group.addValue(props.value)
  } else {
    group.removeValue(props.value)
  }
}

onMounted(() => {
  if (isChecked.value) {
    setChecked(isChecked.value)
  }
})

function onTap() {
  if (isDisable.value) {
    return
  }
  isChecked.value = !isChecked.value
  setChecked(isChecked.value)
}

</script>

<template>
  <view class="yj-checkbox" @tap="onTap">
    <view :class="{
      checked: isChecked,
      disable: isDisable,
    }" class="yj-checkbox-icon">
    </view>
    <view v-if="label.length" class="label">{{ label }}</view>
  </view>
</template>

<style lang="scss" scoped>
@mixin center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

.yj-checkbox {
  @include center();
  justify-content: flex-start;
  gap: 20rpx;

  .yj-checkbox-icon {
    width: 30rpx;
    height: 30rpx;
    border: 2rpx solid #1890FF;
    border-radius: 50%;
    position: relative;

    &:after {
      position: absolute;
      top: 50%;
      left: 50%;
      content: '';
      width: 16rpx;
      height: 8rpx;
      border-left: 2rpx solid #fff;
      border-bottom: 2rpx solid #fff;
      transform: translate(-50%, -80%) rotate(-45deg);
      display: none;
    }

    &.checked {
      border-color: transparent;
      background: #1890FF;

      &:after {
        display: block;
      }
    }

    &.disable {
      background: #979797;
      border: 2rpx solid #979797;
    }
  }
}
</style>