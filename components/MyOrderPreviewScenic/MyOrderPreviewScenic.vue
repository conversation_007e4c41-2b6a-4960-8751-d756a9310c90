<template>
  <NavBar title=""></NavBar>
  <view class="container">
    <!-- 轮播图 -->
    <view class="banners">
      <swiper autoplay="true">
        <swiper-item v-for="(item, index) in [scenicData.pic]" :key="index">
          <image :src="item"></image>
        </swiper-item>
      </swiper>
    </view>
    <view class="scenic-cont">
      <view class="title">
        {{ detail.title }}
      </view>
      <view class="price">{{ formatMoney(totalPrice) }}</view>
    </view>

    <view class="card days">
      <view class="title">使用日期</view>
      <view class="content">
        <view class="left">
          <view v-for="(item, index) in quickStocks" :key="index" :class="{
            active: isSelectDate(item)
          }" class="item" @tap="onSelectDate(item)">
            <view>{{ weekday(item.date) }}</view>
            <view>{{ item.date.format('MM.DD') }}</view>
            <view class="price">{{ formatMoney(item.sale_price) }}</view>
          </view>
        </view>

        <view class="more" @tap="onOpenDateDialog">
          <view>更多日期</view>
          <text class="iconfont icon-gengduo"></text>
        </view>
      </view>
    </view>
    <view class="card peoples">
      <view class="title">
        使用旅客
        <view>
          请填写
          {{ needAdultNum > 0 ? `${needAdultNum}成人` : '' }}
          {{ needChildNum > 0 ? `${needChildNum}儿童` : '' }}
          {{ needOldNum > 0 ? `${needOldNum}老人` : '' }}
        </view>
      </view>
      <view class="content">
        <view class="tips">请务必填写与出行人证件一致的信息</view>
        <view class="shorts">
          <scroll-view scroll-x="true" enhanced :show-scrollbar="false">
            <view class="people-items">
              <view v-for="(item, index) in peoples" :key="index" :class="{
                active: selectPeoples.find((s) => s.id == item.id)
              }" @tap="onSelectPeople(index)">
                {{ item.name }}
              </view>
            </view>
          </scroll-view>

          <view class="plus" @tap="onShowAddPeople">
            新增
            <text class="iconfont icon-gengduo"></text>
          </view>
        </view>

        <view class="list">
          <view v-for="(item, index) in selectPeoples" :key="index" class="item">
            <view class="left">
              <text class="delete iconfont icon-iconzhenghe0729fuzhi95" @tap="onRemoveSelect(item.id)"></text>
              <view>
                <view>{{ item.name }}</view>
                <view>证件号：{{ item.id_no }}</view>
                <view>手机号：{{ item.phone }}</view>
              </view>
            </view>
            <text class="right iconfont icon-bianji" @tap="onEditPeople(index)" />
          </view>
        </view>
      </view>
    </view>

    <view class="footer">
      <view class="price">{{ formatMoney(totalPrice) }}</view>
      <view class="btn danger apply" @tap="onSubmit">去支付</view>
    </view>

    <MyInfoPopup v-if="showDateDialog" title="选择日期" @close="showDateDialog = false">
      <MyCalendar :month="month" :value="date" type="date" @change="onDialogDateSelect" @month-change="onMonthSwitch">
        <template #label="item">
          {{ stockLabel(item) }}
        </template>
      </MyCalendar>
    </MyInfoPopup>

    <MyInfoPopup v-if="showAddPeoples" action-text="保存" title="旅客信息" type="bottom" @action="onAddPeople"
      @close="showAddPeoples = false">
      <view class="popup">
        <uni-forms ref="peopleFormRef" :model-value="peopleForm" :rules="peopleRules">
          <uni-forms-item label="证件类型" name="id_type">
            <uni-data-select v-model="peopleForm.id_type" :localdata="detail.id_cards"></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="证件号码" name="id_no">
            <uni-easyinput v-model="peopleForm.id_no" placeholder="请输入证件号码"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="姓名" name="name">
            <uni-easyinput v-model="peopleForm.name" placeholder="请输入姓名"></uni-easyinput>
          </uni-forms-item>
          <uni-forms-item label="手机号码" name="phone">
            <uni-easyinput v-model="peopleForm.phone" placeholder="选填"></uni-easyinput>
          </uni-forms-item>
        </uni-forms>
        <view class="people-tips">你已知晓你填写的上述信息将被用于预定实名</view>
      </view>
    </MyInfoPopup>
  </view>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import { formatMoney, showToast, weekday } from '@/utils/index.js'
import dayjs from 'dayjs'
import { orderSubmit } from '@/api/order.js'
import { productCalendar, productDetail } from '../../api/product'
import { scenicDetail } from '@/api/scene'
import { peopleList, peopleSave } from '@/api/user'
import MyCalendar from "@/components/MyCalendar/MyCalendar.vue";
import MyInfoPopup from "@/components/MyInfoPopup/MyInfoPopup.vue";

const id = ref(0)
const peoples = ref([])
const type = ref('ticket')
const ticketId = ref('')
const ota = ref('')
const productType = ref('')
const totalPrice = computed(() => detail.value.sale_price * quantity.value)
const props = defineProps({
  query: {
    type: Object,
    default: () => {
      return {
        ticket_id: '',
        ota: '',
        scenic_id: '',
        product_type: ''
      }
    }
  }
})
const selectPeoples = computed(() => peoples.value.filter((item) => item.is_selected))
const scenicData = ref({})

const detail = ref({
  id_cards: [],
  buy_max_num: 99,
  buy_min_num: 1,
  buy_start_day: 0,
  buy_start_date: '',
  peoples: {
    adult_num: 0,
    child_num: 0,
    old_num: 0
  }
})
const start = computed(() => {
  const t1 = dayjs().add(detail.value.buy_start_day, 'day')
  let t2 = dayjs()
  if (detail.value.buy_start_date.length > 0) {
    t2 = dayjs(detail.value.buy_start_date, 'YYYY-MM-DD')
  }

  return (t2.unix() > t1.unix() ? t2 : t1).format('YYYY-MM-DD')
})
const date = ref('')
const quantity = ref(1)
const stocks = ref([])
const needAdultNum = computed(() => quantity.value * detail.value.peoples.adult_num)
const needChildNum = computed(() => quantity.value * detail.value.peoples.child_num)
const needOldNum = computed(() => quantity.value * detail.value.peoples.old_num)

const peopleFormRef = ref(null)
const peopleForm = ref({
  name: '',
  phone_type: '',
  id_type: '',
  id_no: '',
  id: 0
})
const peopleRules = {
  name: {
    rules: [
      {
        required: true,
        errorMessage: '请填写旅客姓名'
      }
    ]
  },
  id_type: {
    rules: [
      {
        required: true,
        errorMessage: '请选择证件类型'
      }
    ]
  },
  id_no: {
    rules: [
      {
        required: true,
        errorMessage: '请输入证件号码'
      }
    ]
  }
}
const activePeopleIndex = ref(-1)
const month = computed(() => {
  if (!date.value) {
    return dayjs().format('YYYY-MM')
  }

  return dayjs(date.value).format('YYYY-MM')
})
const quickStocks = computed(() => {
  if (!date.value) {
    return stocks.value.slice(0, 4)
  }
  let index = stocks.value.findIndex((s) => dayjs(s.date).isSame(date.value))

  if (index == -1) {
    return []
  }

  const result = []
  for (let i = 0; i < 4; i++) {
    if (index + i < stocks.value.length) {
      result.push(stocks.value[index + i])
    }
  }
  while (result.length < 4 && index > 0) {
    index--
    const s = stocks.value[index]
    if (!s) {
      continue
    }

    result.unshift(s)
  }

  return result
})

const rules = {
  name: {
    rules: [
      {
        required: true,
        errorMessage: '请填写联系人姓名'
      }
    ]
  },
  tel: {
    rules: [
      {
        required: true,
        errorMessage: '请填写电话是必填'
      }
    ]
  }
}
const showAddPeoples = ref(false)
const showDateDialog = ref(false)

function isSelectDate(item) {
  if (!date.value) {
    return false
  }

  return dayjs(date.value).isSame(dayjs(item.date))
}

function onSelectPeople(index) {
  const item = peoples.value[index]
  if (!item) {
    return
  }

  item.is_selected = !item.is_selected
}

function onOpenDateDialog() {
  showDateDialog.value = true
}

function onSelectDate(d) {
  date.value = d.date.toDate()
}

function onDialogDateSelect(d) {
  const s = stocks.value.find((s) => s.date.isSame(d))
  if (!s) {
    return
  }
  date.value = d
  showDateDialog.value = false
}

function stockLabel(item) {
  const s = stocks.value.find((s) => s.date.isSame(item.date))
  if (!s) {
    return ''
  }

  return formatMoney(s.sale_price)
}

function onShowAddPeople() {
  Object.keys(peopleForm.value).forEach((key) => {
    peopleForm.value[key] = ''
  })

  showAddPeoples.value = true
}

function onSubmit() {
  if (!date.value) {
    showToast('请选择日期')
    return
  }
  const needTotalPeople = needAdultNum.value + needOldNum.value + needChildNum.value

  if (needTotalPeople > selectPeoples.value.length) {
    showToast('请完善旅客信息！')
    return
  }

  const contacts = selectPeoples.value.find((item) => item.phone)
  if (!contacts) {
    showToast('至少要填写一个联系人的手机号码！')
    return
  }
  const { name, phone } = contacts

  const data = {
    product_id: ticketId.value,
    order_type: type.value,
    order_sub_type: productType.value,
    book_num: quantity.value,
    date: dayjs(date.value).format('YYYY-MM-DD'),
    name: name,
    tel: phone,
    peoples: JSON.stringify(selectPeoples.value),
    scenic_id: id.value
  }

  orderSubmit(data).then((res) => {
    const { data } = res
    if (!data.need_pay) {
      uni.showModal({
        title: '创建成功',
        content: '订单创建成功',
        success() {
          uni.redirectTo({
            url: '/pages/orders/orders'
          })
        }
      })

      return
    }

    uni.navigateTo({
      url: '/pages/pay/pay?order_id=' + data.order_id
    })
  })
}

function onMonthSwitch(m) {
  const d = dayjs(m, 'YYYY-MM-01')
  getMonth(d.toDate())
}

function onRemoveSelect(id) {
  const people = selectPeoples.value.find((item) => item.id === id)
  if (people) {
    people.is_selected = false
  }
}

function onEditPeople(index) {
  const people = selectPeoples.value[index]
  Object.assign(peopleForm.value, {
    ...people
  })
  showAddPeoples.value = true
  activePeopleIndex.value = index
}

function onAddPeople() {
  peopleFormRef.value
    .validate()
    .then(async () => {
      const postData = { ...peopleForm.value }
      const { data } = await peopleSave(postData)
      postData.id = data.id
      postData.is_selected = true

      const old = peoples.value.find((item) => item.id == postData.id)
      if (!old) {
        peoples.value.push(postData)
      } else {
        Object.assign(old, postData)
      }

      showAddPeoples.value = false
    })
    .catch((res) => {
      const err = res[0]
      showToast(err.errorMessage, 'error')
    })
}

function getMonth(month = new Date()) {
  const start = dayjs(month)
  const params = {
    product_type: productType.value,
    product_id: ticketId.value,
    start: start.format('YYYY-MM-DD')
    // end: start.add(1, 'month').format('YYYY-MM-DD')
  }
  productCalendar(params).then((res) => {
    const { data } = res

    stocks.value = data.list.map((item) => {
      item.date = dayjs(item.date, 'YYYY-MM-DD')
      return item
    })
  })
}

function initTicket(query) {
  ticketId.value = query.ticket_id
  ota.value = query.ota
  id.value = query.scenic_id
  productType.value = query.product_type

  const params = {
    product_type: productType.value,
    product_id: ticketId.value
  }
  productDetail(params).then((res) => {
    const { data } = res

    data.id_cards = data.id_cards.map((item) => {
      item.text = item.name
      return item
    })

    Object.assign(detail.value, data)
    getMonth()
  })
}

function getScenic() {
  scenicDetail(id.value).then(({ data }) => {
    Object.assign(scenicData.value, data)
  })
}

onMounted(() => {
  initTicket(props.query)
  getScenic()

  peopleList().then(({ data }) => {
    peoples.value = data.list
  })
})
</script>

<style lang="scss" scoped>
@import 'MyOrderPiewviewScenic.scss';
</style>
