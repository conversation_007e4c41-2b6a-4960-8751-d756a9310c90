<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>义伴AI软件服务须知</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            max-width: 900px;
            margin: 0 auto;
            color: #333;
        }
        h1 {
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
            font-size: 24px;
        }
        h2 {
            padding: 15px 0 10px;
            margin-top: 25px;
            font-size: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        h3 {
            margin: 15px 0 10px;
            font-size: 18px;
        }
        p {
            line-height: 1.6;
            margin: 10px 0;
        }
        .important {
            background-color: #f5f5f5;
            border-left: 4px solid #666;
            padding: 15px;
            margin: 20px 0;
            border-radius: 3px;
        }
        .package {
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .package h4 {
            margin-top: 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 17px;
        }
        .step {
            margin-left: 20px;
            list-style-type: decimal;
        }
        .risk, .notice, .support {
            background-color: #f5f5f5;
            border-left: 4px solid #666;
            padding: 15px;
            margin: 20px 0;
            border-radius: 3px;
        }
        ul, ol {
            margin: 10px 0 10px 20px;
        }
        li {
            margin-bottom: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>义伴AI软件服务须知</h1>
    
    <div class="important">
        <h2>重要提示</h2>
        <p>感谢您使用义伴AI软件！购买套餐前请仔细阅读本须知，您的购买行为即视为同意全部条款。套餐购买后积分一次性发放，未使用积分到期清零，且不支持退款。请根据需求谨慎选择套餐类型。</p>
    </div>
    
    <h2>一、套餐类型与积分规则</h2>
    <h3>1. 套餐分类及权益</h3>
    
    <div class="package">
        <h4>体验版（有效期1个月）</h4>
        <p><strong>价格：</strong>39元，含1080积分</p>
        <p><strong>适用场景：</strong>短期需求体验、基础功能试用</p>
    </div>
    
    <div class="package">
        <h4>基础版（有效期3个月）</h4>
        <p><strong>价格：</strong>98元，含3300积分</p>
        <p><strong>适用场景：</strong>中小项目规划、常规方案设计</p>
    </div>
    
    <div class="package">
        <h4>专家版（有效期12个月）</h4>
        <p><strong>价格：</strong>198元，含13000积分</p>
        <p><strong>适用场景：</strong>长期项目支持、复杂方案生成</p>
    </div>
    
    <h3>2. 积分发放与有效期</h3>
    <p><strong>一次性到账：</strong>支付成功后即时发放全周期积分。例如购买专家版套餐，立即获得13000积分。</p>
    <p><strong>到期清零：</strong>积分需在套餐有效期内使用（1个月/3个月/12个月），逾期未使用部分自动失效，不跨周期累计。</p>
    
    <h2>二、积分消耗与查询</h2>
    <h3>1. 消耗场景说明</h3>
    <p>- 生成功能：100积分/次（如生成行程规划、方案设计）</p>
    <p>- 修改功能：20积分/次（如调整方案细节、参数优化）</p>
    <p>- 导出功能：100积分/次（如导出PDF文档、代码片段）</p>
    <p><em>具体消耗值以操作时页面提示为准。</em></p>
    
    <h3>2. 查询路径</h3>
    <ol class="step">
        <li>登录义伴AI → 点击左上角【个人中心】</li>
        <li>进入【积分明细】页面，可查看：</li>
    </ol>
    <ul>
        <li>一次性发放的积分总额</li>
        <li>按时间排序的消耗记录</li>
        <li>实时剩余积分（系统自动更新）</li>
    </ul>
    
    <h2>三、套餐管理政策</h2>
    <h3>1. 升级规则</h3>
    <p>套餐有效期内可申请升级，需补足差价并发放新套餐剩余周期积分。例：剩余2个月的体验版升级为专家版，需补（298-39×2）元，并立即到账11000-1000×2=9000积分。</p>
    
    <h3>2. 降级规则</h3>
    <p>仅支持套餐到期后申请降级，新套餐生效时重新计算积分。</p>
    
    <h3>3. 退订说明</h3>
    <p><strong>不支持退款：</strong>套餐购买后无论何种原因（含操作失误、需求变更）均不退还费用。请确认需求后再购买。</p>
    <p><strong>特殊情形：</strong>因平台技术故障导致服务无法使用，可申请延长套餐有效期，不支持退款。</p>
    
    <h2>四、服务声明与责任</h2>
    <div class="risk">
        <h3>1. 风险提示</h3>
        <p>- 因技术限制、关键词识别误差等导致生成内容不符，不支持积分返还或退款。</p>
        <p>- 不同设备切换可能导致积分记录延迟，以网页端【积分明细】数据为准。</p>
    </div>
    
    <div class="notice">
        <h3>2. 规则变更</h3>
        <p>平台有权调整套餐价格或积分规则，将提前7天通过软件弹窗、站内信通知，用户继续使用即视为同意变更。</p>
    </div>
    
    <h2>五、购买建议</h2>
    <ol>
        <li>先试用免费功能评估实际需求</li>
        <li>核对套餐有效期与使用频率是否匹配</li>
        <li>确认无退款需求后再完成支付</li>
    </ol>
    
    <div class="support">
        <h2>六、客服支持</h2>
        <p>- 在线客服：首页→客服按钮小图标</p>
        <p>- 工作日内2小时响应积分异常查询</p>
    </div>
    
    <div class="footer">
        <p>本须知自2025年6月5日起生效，购买行为即视为完全理解并接受全部条款。</p>
    </div>
</body>
</html>
