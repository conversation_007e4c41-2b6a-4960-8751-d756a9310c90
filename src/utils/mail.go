package utils

import (
	"fmt"
	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
	"mime"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
)

func MailSubjectEnv(subject string) string {
	return fmt.Sprintf("[%s]%s", config.Config.App.Env, subject)
}

// Prod与Test(Dev)环境做了下帐号隔离，防止大量测试邮件把生产的邮箱弄死
func Mail(cnf *config.MailConf, subject, body string, to []string, attFiles map[string]string) error {
	if cnf == nil {
		cnf = config.Config.Mail.Conf
	}
	if cnf == nil {
		return fmt.Errorf("mail config not found")
	}
	if len(to) == 0 {
		to = config.Config.Mail.To
	}
	if len(to) == 0 {
		return fmt.Errorf("receiver emails empty")
	}

	d := gomail.NewDialer(cnf.Host, cnf.Port, cnf.User, cnf.Password)
	m := gomail.NewMessage(gomail.SetEncoding(gomail.Base64))
	m.SetHeader("From", m.FormatAddress(cnf.User, cnf.Name))
	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	if nil != attFiles {
		for name, filepath := range attFiles {
			attCtxDisposition := []string{fmt.Sprintf(`attachment; filename="%s"`, mime.QEncoding.Encode("UTF-8", name))}
			attHeader := map[string][]string{"Content-Disposition": attCtxDisposition}
			m.Attach(filepath, gomail.Rename(name), gomail.SetHeader(attHeader))
		}
	}

	err := d.DialAndSend(m)
	my_logger.Infof("mailto", zap.Strings("to", to), zap.String("subject", subject), zap.Error(err))
	return err
}
