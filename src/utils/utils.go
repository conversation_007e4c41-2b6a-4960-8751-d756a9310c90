package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"math"
	"math/rand"
	"net/http"
	"net/url"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"github.com/duke-git/lancet/v2/convertor"

	"github.com/duke-git/lancet/v2/condition"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
)

const (

	// 地球平均半径，单位为公里
	earthRadiusKm = 6371
	// 弧度转度
	degToRad = math.Pi / 180
	// 度转弧度
	radToDeg = 180 / math.Pi
)

// col 从0开始
func formatCol(col int) string {
	s := int8('A')
	b := new(bytes.Buffer)
	for col >= 0 {
		b.WriteByte(byte(s + int8(col%26) - If(b.Len() > 0, int8(1), 0)))
		col /= 26
		if col == 0 {
			break
		}
	}
	bs := b.Bytes()
	slice.Reverse(bs)
	return string(bs)
}

func SetCellValue[T any](file *excelize.File, sheet string, row int, column int, data []T) {
	column = column - 1

	for _, datum := range data {
		cell := fmt.Sprintf("%s%d", formatCol(column), row)
		_ = file.SetCellValue(sheet, cell, datum)
		column += 1
	}
}

func Ip(ctx *gin.Context) string {
	return ctx.ClientIP()
}

func DistanceStr(kilos float64) string {
	if kilos < 1.0 {
		return fmt.Sprintf("%d米", int(kilos*1000))
	} else {
		return fmt.Sprintf("%.1f千米", kilos)
	}
}

// 返回距离，单位km
func CalculateDistance(frLng, frLat, toLng, toLat float64) float64 {
	var lat1 = frLat
	var lon1 = frLng
	var lat2 = toLat
	var lon2 = toLng

	// 将经纬度从度转换为弧度
	lat1Rad := lat1 * degToRad
	lon1Rad := lon1 * degToRad
	lat2Rad := lat2 * degToRad
	lon2Rad := lon2 * degToRad

	// 计算经纬度之差
	dLat := lat2Rad - lat1Rad
	dLon := lon2Rad - lon1Rad

	// Haversine公式中的a部分
	a := math.Sin(dLat/2)*math.Sin(dLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(dLon/2)*math.Sin(dLon/2)

	//计算两点间的中心角c（以弧度为单位）
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
	// 计算并返回两点间的距离
	distance := earthRadiusKm * c
	return distance
}

func FormatGeo(lng, lat float64) string {
	if lat <= 0 || lng <= 0 {
		return ""
	}
	return fmt.Sprintf("%f,%f", lng, lat)
}
func SplitGeoLocation(location string) (lng float64, lat float64) {
	tmp := strings.Split(location, ",")
	if len(tmp) != 2 {
		return
	}
	lng, _ = strconv.ParseFloat(tmp[0], 64)
	lat, _ = strconv.ParseFloat(tmp[1], 64)

	return
}

func FormatMoney[T int | int64 | float64](value T) string {
	amount := float64(value) / 100
	formatted := fmt.Sprintf("%.2f", amount)
	if strings.HasSuffix(formatted, ".00") {
		return formatted[:len(formatted)-3]
	}
	return "￥" + formatted
}

func If[T any](isOk bool, a, b T) T {
	return condition.TernaryOperator(isOk, a, b)
}

func IfBy[T any](isOk bool, a, b func() T) T {
	if isOk {
		return a()
	}
	return b()
}

func EnableBool(state int) bool {
	return state == constmap.Enable
}

func GetPage(ctx *gin.Context) (page, pageSize int) {
	p, ok := ctx.GetQuery("page")
	size, sok := ctx.GetQuery("page_size")

	if ok {
		if p1, err := strconv.ParseInt(p, 10, 64); err == nil {
			page = int(p1)
		}
	}
	if sok {
		if s1, err := strconv.ParseInt(size, 10, 64); err == nil {
			pageSize = int(s1)
		}
	}

	if page < 1 {
		page = 1
	}

	if pageSize > 100 || pageSize < 1 {
		pageSize = constmap.DefaultPageSize
	}

	return
}

func UnWrapStaticUrl(urlStr string) string {
	u, err := url.Parse(urlStr)
	if err != nil || strings.Index(urlStr, config.Config.App.StaticHost) == -1 {
		return urlStr
	} else if strings.Index(u.Path, "/") == 0 {
		return u.Path[1:]
	}

	return u.Path
}

func AvatarUrl(avatar string) string {
	u := config.Config.App.StaticHost

	if strutil.IsBlank(avatar) {
		return u + "/resources/default-avatar.png"
	}

	return StaticUrl(avatar)
}

func StaticUrl(path string) string {
	u := config.Config.App.StaticHost

	if strutil.HasPrefixAny(path, []string{"http", "https"}) {
		return path
	} else if path != "" && strings.Index(path, "/") != 0 {
		path = "/" + path
	}

	if path != "" {
		path = u + path
	}

	return path
}

func GetDB(ctx *gin.Context) *gorm.DB {
	d, _ := ctx.Get("db")

	return d.(*gorm.DB)
}

func NewErrorStr(code int, msg string) constmap.AppError {
	return constmap.AppError{Code: code, Msg: msg}
}

func NewError(err error) constmap.AppError {
	return constmap.AppError{Code: constmap.ErrorParam, Msg: err.Error()}
}

func CurrencyFloat2Int(price float64) int64 {
	return int64(price * 100)
}

func CurrencyInt2Float(price int64) float64 {
	return float64(price) / 100
}

// 按ota计算溢价
func GetOtaPrice(priceRate, price int64, isPlus bool) int64 {
	oPrice := price * priceRate / 100
	if isPlus {
		return oPrice + price
	}
	return If(oPrice > price, 0, price-oPrice)
}

func GetOtaPriceRate(ota string) int64 {
	switch ota {
	default:
		return 0
	case constmap.OtaCodeCeekee:
		return config.Config.Ota.Ceekee.PriceRate
	}
}

// 随机抽取固定长度
func SliceSampled[T any](sl []T, length int) []T {
	if len(sl) <= length {
		return sl
	}
	l := float64(len(sl))
	end := l - 1
	ret := make([]T, 0, length)
	step := l / float64(length)
	for start := float64(0); start < end; start += step {
		pos := start + step
		if step >= 2 {
			pos = start + (step-1)*rand.Float64() //总是不要包含最后一个元素,因为在下个循环里可能随机到那个元素导致重复
		}
		if pos > end {
			ret = append(ret, sl[int(l)-1])
			break
		} else {
			ret = append(ret, sl[int(pos)])
		}
	}
	return ret
}

func SliceSampleByStep[T any](sl []T, step int) []T {
	if len(sl) <= step {
		return sl
	}
	ret := make([]T, 0, 1+len(sl)/step)
	for i := 0; i < len(sl); i += step {
		ret = append(ret, sl[i])
	}
	return ret
}

func Copy(src, dst any) error {
	b, _ := json.Marshal(src)
	return json.Unmarshal(b, dst)
}

func IncrVersion(version uint64) uint64 {
	return If(version >= (math.MaxUint64-1), 1, version+1)
}

func Unshift[T any](arr []T, item T) []T {
	return append([]T{item}, arr...)
}

// 将别名类型转换为原始类型，当转换切片类型的别名时为避免使用循环，使用了unsafe包
func UnsafeConvertType[S any, D any](s []S) []D {
	if len(s) == 0 {
		return nil
	}
	result := make([]D, len(s))
	for i, v := range s {
		result[i] = *(*D)(unsafe.Pointer(&v))
	}
	return result
}

// ToAnySlice 将固定类型的切片安全转换为 []any
func ToAnySlice[T any](slice []T) []any {
	if slice == nil {
		return nil
	}
	result := make([]any, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}

// FromAnySlice 将 []any 安全转换为固定类型的切片
func FromAnySlice[T any](anySlice []any) []T {
	if anySlice == nil {
		return nil
	}
	result := make([]T, 0, len(anySlice))
	for _, v := range anySlice {
		if val, ok := v.(T); ok {
			result = append(result, val)
		}
	}
	return result
}

func WxMiniPageUrl(page string, query *url.Values) string {
	if strings.Index(page, "/") != 0 {
		page = "/" + page
	}
	var u strings.Builder
	u.WriteString(fmt.Sprintf("http://h5.funfuntrip.cn/#%s?", page))
	if query != nil {
		u.WriteString(query.Encode())
	}
	return u.String()
}

// 计算图片指定区域的主色调
// 通过颜色量化和统计来找出出现频率最高的颜色
func GetPicMainColor(img image.Image, rect *image.Rectangle) string {
	if img == nil {
		return "#FFFFFF"
	}

	bounds := img.Bounds()

	// 如果没有指定区域，使用整张图片
	if rect == nil {
		rect = &bounds
	} else {
		// 确保区域在图片范围内，防止越界访问
		if rect.Min.X < bounds.Min.X {
			rect.Min.X = bounds.Min.X
		}
		if rect.Min.Y < bounds.Min.Y {
			rect.Min.Y = bounds.Min.Y
		}
		if rect.Max.X > bounds.Max.X {
			rect.Max.X = bounds.Max.X
		}
		if rect.Max.Y > bounds.Max.Y {
			rect.Max.Y = bounds.Max.Y
		}
	}

	// 颜色统计 - 使用量化减少颜色数量，避免相似颜色被分别统计
	// key: 量化后的颜色值(uint32), value: 出现次数
	colorCount := make(map[uint32]int)
	totalPixels := 0

	// 遍历指定区域的每个像素
	for y := rect.Min.Y; y < rect.Max.Y; y++ {
		for x := rect.Min.X; x < rect.Max.X; x++ {
			// 获取像素的RGBA值，注意：返回的是uint32类型，范围0-65535(16位)，不是常见的0-255(8位)
			r, g, b, a := img.At(x, y).RGBA()

			// 忽略透明像素，0x8000 = 32768，相当于50%透明度
			if a < 0x8000 {
				continue
			}

			// 颜色量化第一步：将16位RGBA值转换为8位RGB值
			// r >> 8 将16位值右移8位，相当于除以256，得到0-255的范围
			r8 := uint8(r >> 8)
			b8 := uint8(b >> 8)
			g8 := uint8(g >> 8)

			// 颜色量化第二步：将256级颜色减少到32级，减少颜色种类
			// (r8 / 8) * 8 的效果：0-7->0, 8-15->8, 16-23->16...
			// 这样可以将相似的颜色归为同一类，例如RGB(100,150,200)和RGB(102,151,203)会被量化为同一种颜色
			quantR := (r8 / 8) * 8
			quantG := (g8 / 8) * 8
			quantB := (b8 / 8) * 8

			// 使用位操作将RGB三个分量打包成一个uint32用于统计
			// quantR<<16: 将红色值左移16位，放到高16位 (位31-16)
			// quantG<<8:  将绿色值左移8位，放到中间8位 (位15-8)
			// quantB:     蓝色值不移位，放到低8位 (位7-0)
			// | 是按位或操作，将三个部分组合成一个32位整数
			// 例如：红色128,绿色64,蓝色32 -> 128<<16|64<<8|32 = 8405024
			quantColor := uint32(quantR)<<16 | uint32(quantG)<<8 | uint32(quantB)
			colorCount[quantColor]++
			totalPixels++
		}
	}

	if totalPixels == 0 {
		return "#FFFFFF"
	}

	// 找出最频繁的颜色
	var maxCount int
	var mainColor uint32

	for color, count := range colorCount {
		if count > maxCount {
			maxCount = count
			mainColor = color
		}
	}

	// 使用位操作从打包的32位整数中提取RGB分量
	// & 0xFF 的作用：0xFF=255=二进制11111111，按位与操作保留低8位，清零高位
	red := (mainColor >> 16) & 0xFF  // 右移16位后与0xFF按位与，提取红色分量
	green := (mainColor >> 8) & 0xFF // 右移8位后与0xFF按位与，提取绿色分量
	blue := mainColor & 0xFF         // 直接与0xFF按位与，提取蓝色分量

	// 转换为十六进制颜色码，%02X表示2位大写十六进制，不足2位前面补0
	return fmt.Sprintf("#%02X%02X%02X", red, green, blue)
}

// 从URL获取图片对象
func GetPicFromURL(src string) (image.Image, error) {
	if src == "" {
		return nil, fmt.Errorf("URL不能为空")
	}

	// 下载图片
	resp, err := http.Get(src)
	if err != nil {
		return nil, fmt.Errorf("下载图片失败: %w", err)
	}
	defer resp.Body.Close()

	// 解码图片
	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("解码图片失败: %w", err)
	}

	return img, nil
}

// 获取图片下半部分的主色调
func GetPicBottomHalfMainColor(img image.Image) string {
	if img == nil {
		return "#FFFFFF"
	}

	bounds := img.Bounds()

	// 定义下半部分区域
	bottomHalf := image.Rectangle{
		Min: image.Point{X: bounds.Min.X, Y: bounds.Min.Y + bounds.Dy()/2},
		Max: bounds.Max,
	}

	// 调用GetPicMainColor分析下半部分
	return GetPicMainColor(img, &bottomHalf)
}

// 比较两个颜色
func ColorGt(ca, cb string) bool {
	// 解析颜色A的亮度
	brightnessA := getColorBrightness(ca)
	// 解析颜色B的亮度
	brightnessB := getColorBrightness(cb)

	// 比较亮度值
	return brightnessA > brightnessB
}

// 计算颜色的感知亮度
func getColorBrightness(color string) float64 {
	// 移除#前缀
	if strings.HasPrefix(color, "#") {
		color = color[1:]
	}

	// 如果不是6位十六进制，返回0
	if len(color) != 6 {
		return 0
	}

	// 解析RGB值
	r, err1 := strconv.ParseInt(color[0:2], 16, 64)
	g, err2 := strconv.ParseInt(color[2:4], 16, 64)
	b, err3 := strconv.ParseInt(color[4:6], 16, 64)

	// 如果解析失败，返回0
	if err1 != nil || err2 != nil || err3 != nil {
		return 0
	}

	// 使用感知亮度公式 (ITU-R BT.709)
	// 人眼对绿色最敏感，红色次之，蓝色最不敏感
	return 0.299*float64(r) + 0.587*float64(g) + 0.114*float64(b)
}

func Base64Image(b []byte) string {
	return fmt.Sprintf("data:image/png;base64,%s", base64.StdEncoding.EncodeToString(b))
}

func FormatNumber(num float64, precesion int) string {
	s := strings.TrimRight(fmt.Sprintf("%."+convertor.ToString(precesion)+"f", num), ".0")
	return If(strutil.IsBlank(s), "0", s)
}

// StripHTML 去除字符串中的HTML标签，只保留文本内容
func StripHTML(html string) string {
	var result strings.Builder
	var inTag, inQuote bool
	var quoteChar byte

	for i := 0; i < len(html); i++ {
		c := html[i]

		// 处理HTML标签
		if c == '<' && !inQuote {
			inTag = true
			continue
		}

		if inTag {
			// 处理引号内的内容
			if (c == '"' || c == '\'') && (i == 0 || html[i-1] != '\\') {
				if !inQuote {
					inQuote = true
					quoteChar = c
				} else if c == quoteChar {
					inQuote = false
				}
			}

			// 标签结束
			if c == '>' && !inQuote {
				inTag = false
			}
			continue
		}

		// 处理HTML实体
		if c == '&' {
			entityEnd := strings.IndexByte(html[i:], ';')
			if entityEnd > 0 && entityEnd < 10 { // 限制实体长度，避免误判
				entity := html[i : i+entityEnd+1]
				switch entity {
				case "&nbsp;":
					result.WriteByte(' ')
				case "&lt;":
					result.WriteByte('<')
				case "&gt;":
					result.WriteByte('>')
				case "&amp;":
					result.WriteByte('&')
				case "&quot;":
					result.WriteByte('"')
				case "&apos;":
					result.WriteByte('\'')
				default:
					// 其他实体保持原样
					result.WriteString(entity)
				}
				i += entityEnd
				continue
			}
		}

		// 正常文本内容
		if !inTag {
			result.WriteByte(c)
		}
	}

	return result.String()
}

// 检测远程文件大小
func RemoteFileSize(u string) (int64, error) {
	return RemoteFileSizeWithTimeout(u, 5*time.Second)
}

// 检测远程文件大小，支持自定义超时时间
func RemoteFileSizeWithTimeout(u string, timeout time.Duration) (int64, error) {
	if u == "" {
		return 0, fmt.Errorf("URL不能为空")
	}

	if timeout <= 0 {
		timeout = 30 * time.Second // 默认30秒超时
	}

	// 创建HTTP客户端，设置超时时间
	client := &http.Client{
		Timeout: timeout,
	}

	// 方法1：尝试使用HEAD请求获取文件大小（最快方式）
	if size, err := getFileSizeByHead(client, u); err == nil {
		return size, nil
	}

	// 方法2：如果HEAD请求失败，使用GET请求获取Content-Length
	if size, err := getFileSizeByGet(client, u); err == nil {
		return size, nil
	}

	return 0, fmt.Errorf("无法获取文件大小")
}

// 使用HEAD请求获取文件大小
func getFileSizeByHead(client *http.Client, url string) (int64, error) {
	req, err := http.NewRequest("HEAD", url, nil)
	if err != nil {
		return 0, err
	}

	// 设置User-Agent，避免某些服务器拒绝请求
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; FileSize-Detector/1.0)")

	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("HEAD请求失败，状态码: %d", resp.StatusCode)
	}

	// 获取Content-Length头
	contentLength := resp.Header.Get("Content-Length")
	if contentLength == "" {
		return 0, fmt.Errorf("服务器未返回Content-Length")
	}

	// 解析文件大小
	size, err := strconv.ParseInt(contentLength, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("解析Content-Length失败: %w", err)
	}

	if size < 0 {
		return 0, fmt.Errorf("无效的文件大小: %d", size)
	}

	return size, nil
}

// 使用GET请求获取文件大小
func getFileSizeByGet(client *http.Client, url string) (int64, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return 0, err
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (compatible; FileSize-Detector/1.0)")

	// 尝试使用Range请求只获取第一个字节，这样可以获取Content-Range信息
	req.Header.Set("Range", "bytes=0-0")

	resp, err := client.Do(req)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	// 如果支持Range请求，状态码应该是206
	if resp.StatusCode == http.StatusPartialContent {
		// 从Content-Range头获取文件总大小
		contentRange := resp.Header.Get("Content-Range")
		if contentRange != "" {
			// Content-Range格式: "bytes 0-0/1234" 或 "bytes 0-0/*"
			parts := strings.Split(contentRange, "/")
			if len(parts) == 2 && parts[1] != "*" {
				size, err := strconv.ParseInt(parts[1], 10, 64)
				if err == nil && size >= 0 {
					return size, nil
				}
			}
		}
	}

	// 如果Range请求不支持或失败，检查普通的Content-Length
	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusPartialContent {
		contentLength := resp.Header.Get("Content-Length")
		if contentLength != "" {
			size, err := strconv.ParseInt(contentLength, 10, 64)
			if err == nil && size >= 0 {
				return size, nil
			}
		}
	}

	return 0, fmt.Errorf("GET请求无法获取文件大小，状态码: %d", resp.StatusCode)
}

// 格式化文件大小为人类可读的格式
func FormatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}

	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB", "PB"}
	if exp >= len(units) {
		exp = len(units) - 1
		div = int64(math.Pow(unit, float64(exp+1)))
	}

	return fmt.Sprintf("%.1f %s", float64(bytes)/float64(div), units[exp])
}

// 文件大小单位映射表（二进制，1024进制）
var fileSizeUnits = map[string]int64{
	"":   1,
	"b":  1,
	"k":  1024,
	"kb": 1024,
	"m":  1024 * 1024,
	"mb": 1024 * 1024,
	"g":  1024 * 1024 * 1024,
	"gb": 1024 * 1024 * 1024,
	"t":  1024 * 1024 * 1024 * 1024,
	"tb": 1024 * 1024 * 1024 * 1024,
	"p":  1024 * 1024 * 1024 * 1024 * 1024,
	"pb": 1024 * 1024 * 1024 * 1024 * 1024,
}

func ParseFileSizeStr(s string) (int64, error) {
	if s = strings.TrimSpace(s); s == "" {
		return 0, fmt.Errorf("文件大小字符串不能为空")
	}

	// 找数字结束位置
	i := 0
	for i < len(s) && (s[i] >= '0' && s[i] <= '9' || s[i] == '.') {
		i++
	}

	// 解析数字和单位
	num, err := strconv.ParseFloat(s[:i], 64)
	if err != nil {
		return 0, fmt.Errorf("无效的数字")
	}

	unit := strings.ToLower(strings.TrimSpace(s[i:]))
	multiplier, exists := fileSizeUnits[unit]
	if !exists {
		return 0, fmt.Errorf("不支持的单位: %s", unit)
	}

	if result := num * float64(multiplier); result <= float64(math.MaxInt64) {
		return int64(result), nil
	}
	return 0, fmt.Errorf("文件大小超出范围")
}

// 将字节数转换为最合适的单位字符串（简洁格式）
func FormatFileSizeShort(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%dB", bytes)
	}

	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"K", "M", "G", "T", "P"}
	if exp >= len(units) {
		exp = len(units) - 1
		div = int64(math.Pow(unit, float64(exp+1)))
	}

	result := float64(bytes) / float64(div)
	// 如果是整数，不显示小数点
	if result == float64(int64(result)) {
		return fmt.Sprintf("%.0f%s", result, units[exp])
	}
	return fmt.Sprintf("%.1f%s", result, units[exp])
}
