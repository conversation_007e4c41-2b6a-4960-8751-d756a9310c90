package utils

import (
	"bufio"
	"bytes"
	"github.com/duke-git/lancet/v2/strutil"
	"os"
)

type TempFile struct {
	*os.File
}

func (tmp *TempFile) Close() error {
	if err := tmp.File.Close(); err != nil {
		return err
	}
	return os.Remove(tmp.Name())
}

func CreateTemp(dir, pattern string) (*TempFile, error) {
	if f, err := os.CreateTemp(dir, pattern); err != nil {
		return nil, err
	} else {
		return &TempFile{File: f}, nil
	}
}

type BytesBuffer struct {
	*bytes.Buffer
}

func (o *BytesBuffer) Close() error {
	o.Reset()
	return nil
}
func NewBytesBuffer(buf *bytes.Buffer) *BytesBuffer {
	return &BytesBuffer{buf}
}

// 读取文本文件，一行一个
func ReadLines(path string) ([]string, error) {
	f, err := os.Open(path)
	if err != nil {
		return []string{}, err
	}
	defer f.Close()
	sc := bufio.NewScanner(f)
	out := make([]string, 0, 10)
	for sc.Scan() {
		if !strutil.IsBlank(sc.Text()) {
			out = append(out, sc.Text())
		}
	}
	return out, nil
}
