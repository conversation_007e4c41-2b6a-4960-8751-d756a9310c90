package utils

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"roadtrip-api/src/constmap"
)

func IndexOf[T any](arr []T, condition func(index int, item T) bool) int {
	for i, i2 := range arr {
		if condition(i, i2) {
			return i
		}
	}

	return -1
}

func SliceColumn[T any, K comparable](arr []T, column func(val T) K) map[K][]int {
	ret := make(map[K][]int)

	for i, t := range arr {
		key := column(t)

		if values, ok := ret[key]; ok {
			ret[key] = append(values, i)
		} else {
			ret[key] = []int{i}
		}
	}

	return ret
}

func NewGenericList[T any](t []T) *constmap.GenericList[T] {
	var p constmap.GenericList[T]
	if t != nil {
		p = t
	} else {
		p = make(constmap.GenericList[T], 0)
	}
	return &p
}

type Marshaller[T any] struct {
	Data *T
}

func (z *Marshaller[T]) UnmarshalParam(param string) error {
	if z.Data == nil {
		z.Data = new(T)
	}
	if param == "" {
		return nil
	}
	return json.Unmarshal([]byte(param), z.Data)
}

func (z *Marshaller[T]) UnmarshalJSON(bytes []byte) error {
	if z.Data == nil {
		z.Data = new(T)
	}
	if len(bytes) == 0 || len(bytes) == 2 && bytes[0] == '"' && bytes[1] == '"' {
		return nil
	}
	return json.Unmarshal(bytes, z.Data)
}

func (z *Marshaller[T]) MarshalJSON() ([]byte, error) {
	if z == nil || z.Data == nil {
		return []byte(`""`), nil
	}
	return json.Marshal(z.Data)
}

func (z Marshaller[T]) MarshalBinary() (data []byte, err error) {
	return json.Marshal(z.Data)
}

func (z *Marshaller[T]) UnmarshalBinary(bytes []byte) error {
	if z.Data == nil {
		z.Data = new(T)
	}
	if len(bytes) == 0 || len(bytes) == 2 && bytes[0] == '"' && bytes[1] == '"' {
		return nil
	}
	return json.Unmarshal(bytes, z.Data)
}

// Value insert value
func (z Marshaller[T]) Value() (driver.Value, error) {
	if z.Data == nil {
		return "", nil
	}
	v, e := json.Marshal(z.Data)
	return string(v), e
}

// Scan value
func (z *Marshaller[T]) Scan(v interface{}) error {
	value, ok := v.([]byte)
	if ok {
		vv := Marshaller[T]{new(T)}
		*z = vv
		var err error
		if len(value) > 0 {
			err = json.Unmarshal(value, z.Data)
		}
		return err
	}
	return fmt.Errorf("Marshaller.Scan error:%v", v)
}
