export const ZoneLevelProvince = 2; //地区类型（省份）
export const ZoneLevelCity = 3; //地区类型（城市）

export const Disable = 2;
export const Enable = 1;

export const FlowCardTypeCommon = 1; //流量卡类型（通用码）
export const FlowCardTypeUnique = 2; //流量卡类型（唯一码）

export const ZoneLevels = [
  {
    value: ZoneLevelProvince,
    label: "省份",
  },
  {
    value: ZoneLevelCity,
    label: "城市",
  },
];
export const OnlineStates = [
  {
    value: Enable,
    label: "在线",
  },
  {
    value: Disable,
    label: "离线",
  },
];
export const SceneLevels = new Array(5).fill(0).map((item, index) => {
  return {
    value: index + 1,
    label: "A".repeat(index + 1),
  };
});
export const FlowCardTypes = [
  {
    value: FlowCardTypeCommon,
    label: "通用码",
  },
  {
    value: FlowCardTypeUnique,
    label: "唯一码",
  },
];
export const PayMethods = [
  {
    label: "对公转账",
    value: 2,
  },
];
export const Airlines = [
  {
    label: "吉祥航空",
    value: "ho",
  },
  {
    label: "东方航空",
    value: "mu",
  },
];
export const SettlementStates = [
  {
    value: 1,
    label: "待结算",
  },
  {
    value: 2,
    label: "结算中",
  },
  {
    value: 3,
    label: "已结算",
  },
  {
    value: 4,
    label: "已取消",
  },
];

export const SettlementStateMaps = Object.fromEntries(
  SettlementStates.map((item) => [item.value, item.label])
);

export const ZoneAreas = [
  {
    label: "全国",
    value: "all",
  },
  {
    label: "江浙沪皖",
    value: "yangtze",
  },
  {
    label: "偏远地区",
    value: "remote",
  },
];

export const ZoneAreaMaps = Object.fromEntries(
  ZoneAreas.map((item) => [item.value, item.label])
);
export const OrderStateNotPay = 1; //订单状态（未支付）
export const OrderStatePayEarnest = 2; //订单状态（已付预付款）
export const OrderStatePayed = 3; //订单状态（已支付）
export const OrderStateProcessing = 4; //订单状态（处理中）【非团游订单才用】
export const OrderStateSuccess = 5; //订单状态（处理成功）【非团游订单才用】
export const OrderStateCancel = 11; //订单状态（已取消）
export const OrderStateRefund = 12; //订单状态（已退款，全部子订单已退款）
export const OrderStateComplete = 13; //订单状态（已完成）
export const OrderStates = [
  {
    value: OrderStateNotPay,
    label: "未支付",
  },
  {
    value: OrderStatePayEarnest,
    label: "已付预付款",
  },
  {
    value: OrderStatePayed,
    label: "已支付",
  },
  {
    value: OrderStateProcessing,
    label: "处理中",
  },
  {
    value: OrderStateSuccess,
    label: "处理成功",
  },
  {
    value: OrderStateCancel,
    label: "已取消",
  },
  {
    value: OrderStateRefund,
    label: "已退款",
  },
  {
    value: OrderStateComplete,
    label: "已完成",
  },
];

export const UpgradeMethods = [
  {
    value: 1,
    label: "消费金额",
  },
  {
    value: 2,
    label: "累计业绩",
  },
];

export const UpgradeMethodMaps = Object.fromEntries(
  UpgradeMethods.map((item) => [item.value, item.label])
);

export const Deliveries = [
  {
    value: "jd",
    label: "京东",
  },
  {
    value: "youzhengguonei",
    label: "邮政",
  },
  {
    value: "custom",
    label: "自配送",
  },
];

export const OrderTypeHotel = "hotel"; //订单类型（酒店）
export const OrderTypeTicket = "ticket"; //订单类型（门票）
export const OrderTypeTuan = "tuan"; //订单类型（团游）
export const OrderTypeTravel = "travel"; //订单类型（套餐）
export const OrderTypes = [
  {
    value: OrderTypeHotel,
    label: "酒店",
  },
  {
    value: OrderTypeTicket,
    label: "门票",
  },
  {
    value: OrderTypeTuan,
    label: "团游",
  },
  {
    value: OrderTypeTravel,
    label: "套餐",
  },
];

export const DeliveryMaps = Object.fromEntries(
  Deliveries.map((item) => [item.value, item.label])
);

export const ProductStates = [
  {
    value: 1,
    label: "在线",
  },
  {
    value: 2,
    label: "下线",
  },
];
export const ProductStateMaps = Object.fromEntries(
  ProductStates.map((item) => [item.value, item.label])
);

export const MaxImageSize = 1024 * 1024;

export const BannerPositions = [
  {
    label: "首页",
    value: 1,
  },
  {
    label: "发现",
    value: 2,
  },
  {
    label: "心愿广场",
    value: 3,
  },
];

export const DurationUnitWeek = 1;
export const DurationUnitMonth = 2;
export const DurationUnitQuarter = 3;
export const DurationUnitYear = 4;
export const DurationUnits = [
  {value: DurationUnitWeek, label: "周"},
  {value: DurationUnitMonth, label: "月"},
  {value: DurationUnitQuarter, label: "季度"},
  {value: DurationUnitYear, label: "年"},
];

export const TaskIntervalOnce = 1;
export const TaskIntervalDay = 2;
export const TaskIntervalWeek = 3;
export const TaskIntervals = [
  {value: TaskIntervalOnce, label: "单次任务"},
  {value: TaskIntervalDay, label: "日任务"},
  {value: TaskIntervalWeek, label: "周任务"},
];

export const TaskCondTaskAccIntegral = 2;
export const TaskCondSharePlan = 3;
export const TaskCondSavePlan = 4;
export const TaskCondMakePlan = 5;
export const TaskCondJoinAct = 6;
export const TaskCondShareActLogin = 7;
export const TaskCondCheckInScenic = 8; //签到（景点）
export const TaskCondSceneSignAll = 9; //打卡（完成所有景点打卡）
export const TaskCondShareMini = 10; //分享小程序
export const TaskCondRankSettle = 11 //排行榜结算入榜
export const TaskCondParent = 12 //完成父级任务
export const TaskConds = [
  {value: TaskCondTaskAccIntegral, label: "累计积分"},
  {value: TaskCondSharePlan, label: "分享行程"},
  {value: TaskCondSavePlan, label: "收藏行程"},
  {value: TaskCondMakePlan, label: "行程规划"},
  {value: TaskCondJoinAct, label: "参于活动"},
  {value: TaskCondShareActLogin, label: "分享活动"},
  {value: TaskCondShareMini, label: "分享小程序"},
  {value: TaskCondCheckInScenic, label: "打卡（景点）"},
  {value: TaskCondSceneSignAll, label: "打卡（完成所有景点打卡）"},
  {value: TaskCondRankSettle, label: "排行榜结算入榜"},
  {value: TaskCondParent, label: "完成父级任务"},
];

export const TaskRewardActIntegral = 1; //任务奖励（活动内积分）
export const TaskRewardAirWifi = 2;
export const TaskRewardEntity = 3; //任务奖励（实物）
export const TaskRewardAccountIntegral = 4; //任务奖励（账户积分）
export const TaskRewards = [
  {value: TaskRewardActIntegral, label: "活动积分"},
  {value: TaskRewardAccountIntegral, label: "账户积分"},
  {value: TaskRewardAirWifi, label: "机上WIFI"},
  {value: TaskRewardEntity, label: "实物奖励"},
];

export const DateFmtLong = "YYYY-MM-DD";
export const DateFmtLongZh = "YYYY年MM月DD日";
export const DateFmtLongFull = "YYYY-MM-DD HH:mm:ss";
export const DateFmtLongMinute = "YYYY-MM-DD HH:mm";

export const RefundOrderWaitReview = 1; //退款订单默认值（待审核）
export const RefundOrderProcessing = 2; //退款订单默认值（未完成）
export const RefundOrderComplete = 3; //退款订单默认值（已完成）

export const RefundSubOrderWaitReview = 1; //退款子订单默认值（待审核）
export const RefundSubOrderReject = 2; //退款子订单（已拒绝）
export const RefundSubOrderAbnormal = 6; //退款子订单（异常）

export const refundOrderStates = [
  {
    value: RefundOrderWaitReview,
    label: "待处理",
  },
  {
    value: RefundOrderProcessing,
    label: "未完成",
  },
  {
    value: RefundOrderComplete,
    label: "已完成",
  },
];
export const refundOrderStateMaps = Object.fromEntries(
  refundOrderStates.map((item) => [item.value, item.label])
);

export const OrderSubTypeTuan = 1; //子订单类型（团购）

export const UserTaskRewardWait = 1; //用户任务奖励状态（待发货）
export const UserTaskRewardSend = 3; //用户任务奖励状态（已发货）

// 心愿状态常量
export const WishStateWaitReview = 1; //待审核
export const WishStateRejected = 2; //已驳回
export const WishStateProcessing = 3; //进行中
export const WishStateSuccess = 4; //已达成
export const WishStateFinished = 5; //已去过
export const WishStateClosed = 6; //关闭

export const WishStates = [
  {value: WishStateWaitReview, label: "待审核"},
  {value: WishStateRejected, label: "已驳回"},
  {value: WishStateProcessing, label: "进行中"},
  {value: WishStateSuccess, label: "已达成"},
  {value: WishStateFinished, label: "已去过"},
  {value: WishStateClosed, label: "关闭"},
];

export const WishStateMaps = Object.fromEntries(
  WishStates.map((item) => [item.value, item.label])
);

// 跟进状态常量
export const WishFollowStateDefault = 1; //默认
export const WishFollowStateWait = 2; //待跟进
export const WishFollowStateProcessing = 3; //跟进中
export const WishFollowStateTransFinished = 4; //达成交易
export const WishFollowStateTransCancelled = 5; //交易取消

export const WishFollowStates = [
  {value: WishFollowStateDefault, label: "默认"},
  {value: WishFollowStateWait, label: "待跟进"},
  {value: WishFollowStateProcessing, label: "跟进中"},
  {value: WishFollowStateTransFinished, label: "达成交易"},
  {value: WishFollowStateTransCancelled, label: "交易取消"},
];

export const WishFollowStateMaps = Object.fromEntries(
  WishFollowStates.map((item) => [item.value, item.label])
);

// 同行人状态常量
export const WishMemberStateWaitReview = 1; //待审核
export const WishMemberStateApproved = 2; //已通过
export const WishMemberStateRejected = 3; //已拒绝

export const WishMemberStates = [
  {value: WishMemberStateWaitReview, label: "待审核"},
  {value: WishMemberStateApproved, label: "已通过"},
  {value: WishMemberStateRejected, label: "已拒绝"},
];

export const WishMemberStateMaps = Object.fromEntries(
  WishMemberStates.map((item) => [item.value, item.label])
);

// 预算类型常量
export const WishBudgetSingle = 1; //单人预算
export const WishBudgetTeam = 2; //团队预算

// 媒体类型常量
export const WishMediaPicture = 1; //图片
export const WishMediaVideo = 2; //视频
