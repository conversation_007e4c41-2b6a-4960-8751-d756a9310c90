package lru

import (
	"roadtrip-api/src/utils/lru/simplelru"
	"sync"
)

//LRU 内存缓存

// Cache is a thread-safe fixed size LRU cache.
type Cache[K comparable, V any] struct {
	lru  simplelru.LRUCache[K, V]
	lock sync.RWMutex
}

// NewLRU constructs a fixed size cache with the given eviction
// callback.
func NewLRU[K comparable, V any](size int, onEvicted func(key K, value V)) (*Cache[K, V], error) {
	lru, err := simplelru.NewLRU(size, onEvicted)
	if err != nil {
		return nil, err
	}
	c := &Cache[K, V]{
		lru: lru,
	}
	return c, nil
}

// Purge is used to completely clear the cache.
func (c *Cache[K, V]) Purge() {
	c.lock.Lock()
	c.lru.Purge()
	c.lock.Unlock()
}

// get stats
func (c *Cache[K, V]) Stats() *simplelru.LRUStat {
	return c.lru.Stats()
}

// Add adds a value to the cache.  Returns true if an eviction occurred.
func (c *Cache[K, V]) Add(key K, value V, expiresIn int64) (evicted bool) {
	c.lock.Lock()
	evicted = c.lru.Add(key, value, expiresIn)
	c.lock.Unlock()
	return evicted
}

// Get looks up a key's value from the cache.
func (c *Cache[K, V]) Get(key K) (value V, ok bool) {
	c.lock.Lock()
	value, ok = c.lru.Get(key)
	c.lock.Unlock()
	return value, ok
}

// Contains checks if a key is in the cache, without updating the
// recent-ness or deleting it for being stale.
func (c *Cache[K, V]) Contains(key K) bool {
	c.lock.RLock()
	containKey := c.lru.Contains(key)
	c.lock.RUnlock()
	return containKey
}

// Peek returns the key value (or undefined if not found) without updating
// the "recently used"-ness of the key.
func (c *Cache[K, V]) Peek(key K) (value V, ok bool) {
	c.lock.RLock()
	value, ok = c.lru.Peek(key)
	c.lock.RUnlock()
	return value, ok
}

// Cas checks if a key is in the cache  without updating the
// recent-ness or deleting it for being stale,  and if not, adds the value.
// Returns whether found and whether an eviction occurred.
func (c *Cache[K, V]) Cas(key K, value V, expiresIn int64) (ok, evicted bool) {
	c.lock.Lock()
	defer c.lock.Unlock()

	if c.lru.Contains(key) {
		return true, false
	}
	evicted = c.lru.Add(key, value, expiresIn)
	return false, evicted
}

// Remove removes the provided key from the cache.
func (c *Cache[K, V]) Remove(key K) (present bool) {
	c.lock.Lock()
	present = c.lru.Remove(key)
	c.lock.Unlock()
	return
}

// Resize changes the cache size.
func (c *Cache[K, V]) Resize(size int) (evicted int) {
	c.lock.Lock()
	evicted = c.lru.Resize(size)
	c.lock.Unlock()
	return evicted
}

// RemoveOldest removes the oldest item from the cache.
func (c *Cache[K, V]) RemoveOldest() (key K, value V, ok bool) {
	c.lock.Lock()
	key, value, ok = c.lru.RemoveOldest()
	c.lock.Unlock()
	return
}

// GetOldest returns the oldest entry
func (c *Cache[K, V]) GetOldest() (key K, value V, ok bool) {
	c.lock.Lock()
	key, value, ok = c.lru.GetOldest()
	c.lock.Unlock()
	return
}

// Keys returns a slice of the keys in the cache, from oldest to newest.
func (c *Cache[K, V]) Keys() []K {
	c.lock.RLock()
	keys := c.lru.Keys()
	c.lock.RUnlock()
	return keys
}

// Len returns the number of items in the cache.
func (c *Cache[K, V]) Len() int {
	c.lock.RLock()
	length := c.lru.Len()
	c.lock.RUnlock()
	return length
}
