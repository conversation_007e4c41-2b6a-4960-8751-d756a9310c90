package utils

import (
	"errors"
	"github.com/xiaoqidun/qqwry"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"sync"
)

var ipOnceDo sync.Once

var ipLoadErr error

type ipCli struct{}

var IpLoc = &ipCli{}

// 返回 国、省、市
func (o *ipCli) QueryIP(ip string) (string, string, string, error) {
	ipOnceDo.Do(func() {
		if err := qqwry.LoadFile(config.Config.Ip.Dat); err != nil {
			ipLoadErr = err
			my_logger.Errorf("qqwry load file error", zap.Error(err))
			return
		}
	})
	if ipLoadErr != nil {
		return "", "", "", ipLoadErr
	}
	if ip == "" {
		return "", "", "", errors.New("ip is empty")
	}
	location, err := qqwry.QueryIP(ip)
	if err != nil {
		return "", "", "", err
	}
	return location.Country, location.Province, location.City, err
}
