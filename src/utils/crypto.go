package utils

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
)

// 生成 HMAC-SHA256 签名（与 JS 对应）
func GenerateHMAC(key, message string) string {
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(message))
	my_logger.Infof("GenerateHMAC", zap.String("key", key), zap.String("message", message))
	return hex.EncodeToString(mac.Sum(nil))
}

// 校验签名
func VerifyHMAC(password, message, sign string) bool {
	expected := GenerateHMAC(password, message)
	return hmac.Equal([]byte(expected), []byte(sign))
}
