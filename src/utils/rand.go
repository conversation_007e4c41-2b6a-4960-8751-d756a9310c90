package utils

import (
	"github.com/bwmarrin/snowflake"
	"github.com/duke-git/lancet/v2/random"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

var node *snowflake.Node

func init() {
	st, _ := time.Parse(time.DateOnly, "2023-02-28")
	snowflake.Epoch = st.UnixNano() / 1e6

	var err error
	if node, err = snowflake.NewNode(1); err != nil {
		panic(err)
	}
}

// GenOrderNo 雪花算法生成唯一订单号
func GenOrderNo() string {
	return strconv.FormatInt(node.Generate().Int64(), 10)
}

func GenInviteCode(length int) string {
	return strings.Join(
		random.RandSliceFromGivenSlice(
			strings.Split(random.UpperLetters+random.Numeral, ""), length, true),
		"")
}

// 生成包含标点的随机串
func RandomComplexStr(size int) string {
	return strings.Join(
		random.RandStringSlice(random.AllChars, size, 1),
		"")
}

func RandomStr(size int) string {
	return random.RandString(size)
}

func RandNumeral(siz int) string {
	return random.RandNumeral(siz)
}

func RandomFloat64(min, max float64) float64 {
	rand.Seed(time.Now().UnixNano())
	r := min + rand.Float64()*(max-min)

	return r
}

func RandomInt(min, max int) int {
	return random.RandInt(min, max)
}

func RandTime(max time.Duration) time.Duration {
	return time.Duration(rand.Int63n(100) * int64(max) / 100)
}
