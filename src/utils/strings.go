package utils

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/duke-git/lancet/v2/random"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"math"
	"math/rand"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils/typeset"
	"strconv"
	"strings"
	"time"
)

func HideMobile(mobile string) string {
	return strutil.HideString(mobile, 3, 7, "*")
}

func UUID() string {
	s, _ := random.UUIdV4()
	return s
}

func Md5(string2 string) string {
	return cryptor.Md5String(string2)
}

func RemoveJsonTail(s string) string {
	if strings.Index(s, "```json") == 0 {
		s = s[7:]
		s = s[:len(s)-3]
	}
	return s
}

// 将字符串按指定分隔符分割成数组
func ToArray[T ~int | ~uint | ~uint64 | ~int64 | ~float32 | ~float64](s string, sep string) ([]T, error) {
	var ret []T
	ss := strings.Split(strutil.Trim(s), sep)
	switch interface{}(ret).(type) {
	case []uint, []uint64:
		for _, v := range ss {
			if v == "" {
				continue
			}
			vv, err := strconv.ParseUint(v, 10, 64)
			if err != nil {
				return nil, err
			}
			ret = append(ret, T(vv))
		}
	case []int, []int64:
		for _, v := range ss {
			if v == "" {
				continue
			}
			vv, err := strconv.ParseInt(v, 10, 64)
			if err != nil {
				return nil, err
			}
			ret = append(ret, T(vv))
		}
	case []float32, []float64:
		for _, v := range ss {
			if v == "" {
				continue
			}
			vv, err := strconv.ParseFloat(v, 64)
			if err != nil {
				return nil, err
			}
			ret = append(ret, T(vv))
		}
	default:
		return nil, fmt.Errorf("not support type")
	}
	return ret, nil
}

func Join[T ~uint | ~uint64 | ~int | ~int64 | ~string](slice []T, sep string) string {
	strValues := make([]string, len(slice))
	if s, ok := interface{}(slice).([]string); ok {
		strValues = s
	} else {
		for i, value := range slice {
			strValues[i] = fmt.Sprintf("%v", value)
		}
	}
	return strings.Join(strValues, sep)
}

// 随机指定长度的字符串
func RandStr(ascLen, numLen int) string {
	var (
		asc = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		num = "0123456789"
		rsp = make([]byte, ascLen+numLen)
	)
	for i := 0; i < ascLen; i++ {
		rsp[i] = asc[rand.Intn(len(asc))]
	}
	for i := 0; i < numLen; i++ {
		rsp[ascLen+i] = num[rand.Intn(len(num))]
	}
	return string(rsp)
}

// CosineSimilarity 计算两个字符串的余弦相似度,返回0-1的浮点数
func CosineSimilarity(s1, s2 string) float64 {
	vector1 := make(map[rune]int)
	vector2 := make(map[rune]int)

	for _, c := range s1 {
		vector1[c]++
	}
	for _, c := range s2 {
		vector2[c]++
	}

	dotProduct := 0
	magnitude1 := 0.0
	magnitude2 := 0.0

	for k, v := range vector1 {
		if val, ok := vector2[k]; ok {
			dotProduct += v * val
		}
		magnitude1 += float64(v * v)
	}
	for _, v := range vector2 {
		magnitude2 += float64(v * v)
	}

	if magnitude1 == 0 || magnitude2 == 0 {
		return 0.0
	}
	ret := float64(dotProduct) / (math.Sqrt(magnitude1) * math.Sqrt(magnitude2))
	return ret
}

func SplitBySymbol(text string, limit int) []string {
	return constmap.RegexSymbolExp.Split(text, limit)
}

func TrimSymbol(text string) string {
	return constmap.RegexSymbolExp.ReplaceAllString(text, "")
}

func TrimCitySuffix(name string) string {
	return constmap.RegexCitySuffix.ReplaceAllString(name, "")
}
func TrimCitySuffixAll(text string) string {
	return constmap.RegexCitySuffixAll.ReplaceAllString(text, "")
}

func SplitByRunes(text []rune, sep []rune) []string {
	sepSet := typeset.NewTypeSet(false, sep...)
	ret := make([]string, 0)
	var i, j = 0, 0
	var l = len(text)
	for i < l {
		if sepSet.Has(text[i]) {
			ret = append(ret, string(text[j:i]))
			j = i + 1
		}
		i++
	}
	ret = append(ret, string(text[j:]))
	return slice.Filter(ret, func(index int, item string) bool {
		return item != ""
	})
}

func JoinPoi(lng, lat float64) string {
	return fmt.Sprintf("%s,%s", convertor.ToString(lng), convertor.ToString(lat))
}

// 由后到前分割字符串，以分隔符第一个字符位置作为切割点切割并保留分隔符
func SplitBefore(s, sep string) []string {
	ret := make([]string, 0)
	for len(s) > 0 {
		if idx := strings.LastIndex(s, sep); idx > -1 {
			ret = append(ret, s[idx:])
			s = s[0:idx]
		} else {
			ret = append(ret, s)
			s = ""
		}
	}
	slice.Reverse(ret)
	return ret
}

func Season(month time.Month) string {
	if month >= 3 && month <= 5 {
		return "春季"
	} else if month >= 6 && month <= 8 {
		return "夏季"
	} else if month >= 9 && month <= 11 {
		return "秋季"
	} else {
		return "冬季"
	}
}
