package parallel_task

import (
	ants "github.com/panjf2000/ants"
	"sync"
	"sync/atomic"
)

type ParallelTask struct {
	wg        sync.WaitGroup
	pool      *ants.Pool
	Errors    []error      `json:"errors"`
	LastError atomic.Value `json:"last_error"`
}

// NewPool 创建一个并行任务池
// ```
// pool := parallel_task.NewPool(5)
// defer pool.Release()
// pool.AddTask(func()error{return nil})
//
//	if err := pool.Wait(); err != nil {
//	  // catch error
//	}
//
// ```
func NewPool(size int) *ParallelTask {
	if size < 1 {
		size = 1
	}
	pool, _ := ants.NewPool(size)
	return &ParallelTask{
		pool: pool,
	}
}

func (o *ParallelTask) AddTask(task func() error) {
	o.wg.Add(1)
	_ = o.pool.Submit(func() {
		defer o.wg.Done()
		err := task()
		if err != nil {
			o.Errors = append(o.Errors, err)
			if v := o.LastError.Load(); v == nil {
				o.LastError.Store(err)
			}
		}
	})
}

func (o *ParallelTask) Wait() error {
	o.wg.Wait()
	err := o.LastError.Load()
	if err != nil {
		return err.(error)
	}
	return nil
}

func (o *ParallelTask) Release() {
	o.pool.Release()
}
