package utils

import (
	"image"
	"image/color"
	"image/draw"
	"math"
	"os"
	"path"
	"roadtrip-api/src/config"
	"strconv"

	draw2 "golang.org/x/image/draw"
	"golang.org/x/image/font"
	"golang.org/x/image/font/opentype"
	"golang.org/x/image/math/fixed"
)

func ColorHex(hex string) (*color.RGBA, error) {
	if hex[0] == '#' {
		hex = hex[1:]
	}
	values, err := strconv.ParseUint(hex, 16, 32)
	if err != nil {
		return nil, err
	}
	return &color.RGBA{
		R: uint8(values >> 16),
		G: uint8((values >> 8) & 0xFF),
		B: uint8(values & 0xFF),
		A: 255,
	}, nil
}

type FontLoader struct {
	fonts map[string]*opentype.Font
	faces map[string][]font.Face
}

func (f *FontLoader) Close() error {
	return nil
}

func (f *FontLoader) GetFace(faceKey string, r rune) (font.Face, bool) {
	faces, ok := f.faces[faceKey]
	if !ok {
		return nil, false
	}
	for _, v := range faces {
		if _, _, ok := v.GlyphBounds(r); ok {
			return v, true
		}
	}
	return nil, false
}

func (fl *FontLoader) LoadFace(key string, opt *opentype.FaceOptions) error {
	for _, t := range fl.fonts {
		if opt != nil {
			opt.DPI = 72
			opt.Hinting = font.HintingNone
		}
		face, err := opentype.NewFace(t, opt)
		if err != nil {
			return err
		}
		fl.faces[key] = append(fl.faces[key], face)
	}

	return nil
}

func loadFont(fl *FontLoader, file string) error {
	if _, ok := fl.fonts[file]; ok {
		return nil
	}
	cnt, err := os.ReadFile(file)
	if err != nil {
		return err
	}
	t, err := opentype.Parse(cnt)
	if err != nil {
		return err
	}
	fl.fonts[file] = t
	return nil
}
func NewFontLoader() (*FontLoader, error) {
	v := &FontLoader{
		fonts: make(map[string]*opentype.Font),
		faces: make(map[string][]font.Face),
	}

	if err := loadFont(v, path.Join(config.Config.App.FontDir, config.Config.App.DefaultFont)); err != nil {
		return v, err
	}

	if err := loadFont(v, path.Join(config.Config.App.FontDir, config.Config.App.EmojiFont)); err != nil {
		return v, err
	}

	return v, nil
}

func DrawText(dst draw.Image, x, y int, text string, fd *FontLoader, faceKey string, color color.Color) {
	d := &font.Drawer{
		Dst: dst,
		Src: image.NewUniform(color),
	}

	// 预先计算所有字符中最大的Ascent值，确保统一基线
	var maxAscent fixed.Int26_6
	runes := []rune(text)
	for _, r := range runes {
		if f, ok := fd.GetFace(faceKey, r); ok {
			if f.Metrics().Ascent > maxAscent {
				maxAscent = f.Metrics().Ascent
			}
		}
	}

	// 使用统一的基线位置渲染所有字符
	baselineY := y + maxAscent.Ceil()
	for _, r := range runes {
		if f, ok := fd.GetFace(faceKey, r); ok {
			d.Face = f
			d.Dot = fixed.P(x, baselineY)
			d.DrawString(string(r))
			x += font.MeasureString(f, string(r)).Ceil()
		}
	}
}

// 文字宽高
func MeasureString(text string, fd *FontLoader, faceKey string) (fixed.Int26_6, fixed.Int26_6) {
	var w, h fixed.Int26_6
	for _, r := range []rune(text) {
		if f, ok := fd.GetFace(faceKey, r); ok {
			x1 := font.MeasureString(f, string(r))
			y1 := f.Metrics().Height
			w += x1
			if y1 > h {
				h = y1
			}
		}
	}
	return w, h
}

func DrawRect(dst draw.Image, rect image.Rectangle, c color.Color) {
	src := image.NewUniform(c)
	draw.Draw(dst, rect, src, image.Point{}, draw.Src)
}

// 创建圆角矩形蒙版 并平移到 sp 位置
func CreateRoundedRectMask(width, height, radius float64) *image.Alpha {
	mask := image.NewAlpha(image.Rect(0, 0, int(width), int(height)))

	x1, y1 := radius, radius              //左上角
	x2, y2 := width-radius, radius        //右上角
	x3, y3 := radius, height-radius       //左下角
	x4, y4 := width-radius, height-radius //右下角
	c2 := radius * radius
	var inside bool

	for x := float64(0); x < width; x++ {
		for y := float64(0); y < height; y++ {
			if x >= x1 && x <= x2 ||
				y >= y1 && y <= y3 ||
				x < x1 && y < y1 && (math.Pow(x1-x, 2)+math.Pow(y1-y, 2) <= c2) ||
				x > x2 && y < y2 && (math.Pow(x-x2, 2)+math.Pow(y-y2, 2) <= c2) ||
				x < x3 && y > y3 && (math.Pow(x3-x, 2)+math.Pow(y-y3, 2) <= c2) ||
				x > x4 && y > y4 && (math.Pow(x-x4, 2)+math.Pow(y-y4, 2) <= c2) {
				inside = true
			} else {
				inside = false
			}
			if inside {
				mask.SetAlpha(int(x), int(y), color.Alpha{A: 255}) // 不透明
			} else {
				mask.SetAlpha(int(x), int(y), color.Alpha{A: 0}) // 透明
			}
		}
	}
	return mask
}

func ImageResize(src image.Image, width, height int) image.Image {
	if width == 0 {
		scaleRate := float64(src.Bounds().Dy()-height) / float64(src.Bounds().Dy())
		width = int(float64(src.Bounds().Dx()) * (1 - scaleRate))
	}
	if height == 0 {
		scaleRate := float64(src.Bounds().Dx()-width) / float64(src.Bounds().Dx())
		height = int(float64(src.Bounds().Dy()) * (1 - scaleRate))
	}
	avatarDst := image.NewRGBA(image.Rect(0, 0, width, height))
	draw2.ApproxBiLinear.Scale(avatarDst, avatarDst.Bounds(), src, src.Bounds(), draw.Over, nil)
	return avatarDst
}

// 返回图片左上角偏移点
func ImageCenterInRect(src image.Image, rectWidth, rectHeight int) image.Point {
	sb := src.Bounds()
	sc := image.Point{ //中心点
		X: sb.Dx() / 2,
		Y: sb.Dy() / 2,
	}
	return sc.Sub(image.Point{
		X: rectWidth / 2,
		Y: rectHeight / 2,
	})
}
