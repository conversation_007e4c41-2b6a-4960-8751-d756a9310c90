package typeset

import (
	"sync"
)

type TypeSet[T comparable] struct {
	v     map[T]struct{}
	block bool
	l     sync.Mutex
}

// block:在多个线程中使用同一个对象请传入true
func NewTypeSet[T comparable](block bool, values ...T) *TypeSet[T] {
	// 注意要返回指针,否则会拷贝出多个sync.Mutex对象导致并发异常
	ts := &TypeSet[T]{
		v:     make(map[T]struct{}),
		block: block,
	}
	if ts.block {
		ts.l.Lock()
		defer ts.l.Unlock()
	}
	for _, val := range values {
		ts.v[val] = struct{}{}
	}
	return ts
}

func (t *TypeSet[T]) Has(val T) bool {
	if t.block {
		t.l.Lock()
		defer t.l.Unlock()
	}
	_, has := t.v[val]
	return has
}

func (t *TypeSet[T]) Add(values ...T) {
	if t.block {
		t.l.Lock()
		defer t.l.<PERSON>lock()
	}
	for _, val := range values {
		t.v[val] = struct{}{}
	}
}

func (t *TypeSet[T]) Del(values ...T) {
	if t.block {
		t.l.Lock()
		defer t.l.Unlock()
	}
	for _, val := range values {
		delete(t.v, val)
	}
}

func (t *TypeSet[T]) Len() int {
	if t.block {
		t.l.Lock()
		defer t.l.Unlock()
	}
	return len(t.v)
}

func (t *TypeSet[T]) Values() []T {
	if t.block {
		t.l.Lock()
		defer t.l.Unlock()
	}
	values := make([]T, len(t.v))
	i := 0
	for val := range t.v {
		values[i] = val
		i++
	}
	return values
}
