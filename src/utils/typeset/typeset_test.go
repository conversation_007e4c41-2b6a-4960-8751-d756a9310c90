package typeset

import (
	"testing"
	"time"
)

var initStrings = []string{"a", "b", "c", "d"}
var initInts = []int{2, 3, 4, 5}

func newTypeSet(block bool) (*TypeSet[string], *TypeSet[int]) {
	stringSet := NewTypeSet(block, initStrings...)
	intSet := NewTypeSet(block, initInts...)
	return stringSet, intSet
}

func TestSpecifyType(t *testing.T) {
	nowWeek := time.Now().Weekday()
	set := NewTypeSet[time.Weekday](false)
	set.Add(time.Monday, time.Wednesday)
	if set.Has(nowWeek) {
		t.Logf("today[%v] is in test weeks:%v", nowWeek, set.Values())
	} else {
		t.Logf("today[%v] is not in test weeks:%v", nowWeek, set.Values())
	}
}

func TestNewTypeSet(t *testing.T) {
	stringSet, intSet := newTypeSet(false)
	if stringSet.Has("e") {
		t.<PERSON>rrorf("should not has 'e'!!!")
	}
	for _, s := range initStrings {
		if !stringSet.Has(s) {
			t.<PERSON>("should has '%s' but not", s)
		}
	}

	if intSet.Has(1) {
		t.Errorf("should not has 1!!!")
	}
	for _, i := range initInts {
		if !intSet.Has(i) {
			t.Errorf("should has %d but not", i)
		}
	}
}

func BenchmarkTypeSet_Add(b *testing.B) {
	stringSet, intSet := newTypeSet(true)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			stringSet.Add("e", "f")
			if !stringSet.Has("e") {
				b.Errorf("should has 'e'!!!")
			}
			if !stringSet.Has("f") {
				b.Errorf("should has 'f'!!!")
			}
		}
	})
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			intSet.Add(5, 6, 7)
			if !intSet.Has(6) {
				b.Errorf("should has 6!!!")
			}
			if !intSet.Has(7) {
				b.Errorf("should has 6!!!")
			}
		}
	})
}

func BenchmarkTypeSet_Del(b *testing.B) {
	stringSet, intSet := newTypeSet(true)
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			stringSet.Del("a", "b")
			if stringSet.Has("a") {
				b.Errorf("should not has 'a'!!!")
			}
			if stringSet.Has("b") {
				b.Errorf("should not has 'b'!!!")
			}
		}
	})
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			intSet.Del(2, 3)
			if intSet.Has(2) {
				b.Errorf("should not has 2!!!")
			}
			if intSet.Has(3) {
				b.Errorf("should not has 3!!!")
			}
		}
	})
}

func TestTypeSet_Values(t *testing.T) {
	stringSet, intSet := newTypeSet(false)
	if len(stringSet.Values()) != 4 || len(intSet.Values()) != 4 {
		t.Errorf("values length should be 4")
	}
	t.Logf("string set values:%v\nint set values:%v", stringSet.Values(), intSet.Values())
}
