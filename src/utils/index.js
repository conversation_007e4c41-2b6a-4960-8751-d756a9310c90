import dayjs from "dayjs";
import {ElMessage} from "element-plus";
import {isProxy, isReactive, isRef, toRaw, unref} from "vue";

// 格式化表单验证错误信息
/**
 * 格式化表单验证错误信息
 * @param error 错误信息
 * @returns {string} 格式化后的错误信息
 */
export function formatElErrorMsg(error) {
  if (error == null) {
    return ''
  }
  
  for (const [key, value] of Object.entries(error)) {
    return value[0].message
  }
  return ''
}

/**
 * 递归地将响应式数据（包括 reactive 和 ref）转换为原始数据
 * @param {Object|Array|Ref} obj - 要转换的对象或 ref
 * @param {WeakMap} [seen] - 用于处理循环引用的WeakMap
 * @returns {Object|Array|any} 原始数据
 */
export function deepToRaw(obj, seen = new WeakMap()) {
  // 基本类型直接返回
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  // 检查循环引用
  if (seen.has(obj)) {
    return seen.get(obj);
  }
  
  // 处理Ref
  if (isRef(obj)) {
    obj = unref(obj);
  }
  
  // 处理Reactive
  if (isProxy(obj) && isReactive(obj)) {
    obj = toRaw(obj);
  }
  
  // 处理Date
  if (obj instanceof Date) {
    const copy = new Date(obj.getTime());
    seen.set(obj, copy);
    return copy;
  }
  
  // 处理Map
  if (obj instanceof Map) {
    const copy = new Map();
    seen.set(obj, copy);
    for (const [key, val] of obj.entries()) {
      copy.set(key, deepToRaw(val, seen));
    }
    return copy;
  }
  
  // 处理Set
  if (obj instanceof Set) {
    const copy = new Set();
    seen.set(obj, copy);
    for (const item of obj) {
      copy.add(deepToRaw(item, seen));
    }
    return copy;
  }
  
  // 处理数组
  if (Array.isArray(obj)) {
    const copy = [];
    seen.set(obj, copy);
    for (let i = 0; i < obj.length; i++) {
      copy[i] = deepToRaw(obj[i], seen);
    }
    return copy;
  }
  
  // 处理普通对象
  const copy = {};
  seen.set(obj, copy);
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      copy[key] = deepToRaw(obj[key], seen);
    }
  }
  
  // 处理Symbol属性
  const symbolKeys = Object.getOwnPropertySymbols(obj);
  for (const symKey of symbolKeys) {
    copy[symKey] = deepToRaw(obj[symKey], seen);
  }
  
  return copy;
}

export function genRandomColor() {
  return '#' + ((Math.random() * 0x1000000) | 0).toString(16).padStart(6, '0');
}

export function tableFormatTime(row, column, cellValue, index) {
  return formatTime(cellValue)
}

export function tableFormatMoney(row, column, cellValue, index) {
  return formatMoney(cellValue)
}

export function tableFormatSize(row, column, cellValue, index) {
  return formatSize(cellValue)
}

export function formatMoney(val) {
  if (null == val || val === '' || val === 0) {
    return '-'
  }
  return '¥ ' + (val / 100).toFixed(2)
}

export function formatSize(value) {
  if (null == value || value === '') {
    return '0 Bytes'
  }
  const unitArr = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  let index = 0
  const srcsize = parseFloat(value)
  index = Math.floor(Math.log(srcsize) / Math.log(1024))
  let size = srcsize / Math.pow(1024, index)
  size = size.toFixed(2) //保留的小数位数
  return size + unitArr[index]
}

export function formatTime(time, format = 'YYYY-MM-DD HH:mm') {
  if (!time) {
    return '-'
  }
  
  return dayjs.unix(time).format(format)
}

export function deepClone(source) {
  const data = JSON.stringify(source)
  return JSON.parse(data)
}

export async function copyText(content, showToast = true) {
  try {
    await navigator.clipboard.writeText(content)
    if (showToast) {
      ElMessage.success('内容已复制')
    }
  } catch (e) {
    if (showToast) {
      ElMessage.error('复制失败')
    }
    throw e
  }
}
