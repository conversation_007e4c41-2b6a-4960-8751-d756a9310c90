package utils

import (
	"github.com/duke-git/lancet/v2/datetime"
	"time"
)

// RangeOfQuarter 获取当前季度的开始和结束时间
func RangeOfQuarter(t time.Time) (startOfQuarter, endOfQuarter time.Time) {
	// 计算当前季度的开始和结束时间
	year, month, _ := t.Date()

	quarter := (month - 1) / 3 // 计算当前月份属于哪个季度（季度从1开始计数）

	startMonth := quarter*3 + 1 // 季度开始月份

	endMonth := (quarter + 1) * 3 // 季度结束月份，若为第四季度则自然会超过12，下一行代码会处理这种情况

	// 创建季度开始和结束时间

	startOfQuarter = time.Date(year, startMonth, 1, 0, 0, 0, 0, t.Location())

	if endMonth > 12 {
		endMonth = 12 // 若为第四季度，将结束月份调整为12月
	}

	endOfQuarter = time.Date(year, endMonth, 1, 23, 59, 59, 999999999, t.Location())
	endOfQuarter = endOfQuarter.AddDate(0, 1, 0).Add(-time.Nanosecond)

	return
}

func GetQuarter(time2 time.Time) int {
	month := int(time2.Month())
	switch {
	case month >= 1 && month <= 3:
		return 1
	case month >= 4 && month <= 6:
		return 2
	case month >= 7 && month <= 9:
		return 3
	default:
		return 4
	}
}

func BeginOfWeek(tm time.Time) time.Time {
	return datetime.BeginOfWeek(tm, time.Monday)
}
