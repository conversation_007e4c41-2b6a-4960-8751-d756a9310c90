import {createApp} from 'vue'
import router from './router/index'
import './styles/common.scss'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'element-plus/dist/index.css'
import App from './App.vue'
import * as ELIcons from '@element-plus/icons-vue'
// import './assets/fonts/font.scss'
import 'nprogress/nprogress.css'

import {createPinia} from 'pinia'

const app = createApp(App)

// 小图标引用
for (const iconName in ELIcons) {
    // 注册组件
    app.component(iconName, ELIcons[iconName])
}

app.use(router).use(createPinia()).use(ElementPlus, {
    locale: zhCn
})

app.mount('#app')
