package beans_asm

import (
	"roadtrip-api/src/models"
)

//========= 放置需要引入models包等可能导致包循环引用的对象 ==================

type GetCard struct {
	IsReward bool
	GotState int
	GetByMy  bool
	RelId    uint
	Reward   models.FlowCardRewardCode
	Card     models.FlowCard
}

// 支付单扩展
type PaymentExtra struct {
	// 开通会员
	VipConfId uint
	VipId     uint //vip记录id
	RealName  string
	FirstName string
	LastName  string
	IdCard    string
	// 充值套餐
	PackageId    uint
	PackageIsNew int //是否使用首充
	Package      *models.Package
}

type TravelOrderSubmitDto struct {
	OrderDetail    *models.OrderDetail
	OrderDetailExt *models.OrderDetailExt
	ContactsName   string
	ContactsTel    string
}

type TravelOrderSubmitResp struct {
	ThirdOrderId string
	ThirdOrder   models.OrderThirdOrder
}

type TravelOrderPayDto struct {
	ThirdOrder *models.OrderThirdOrder
}

type UserPackageExtra struct {
	IsNew   int            //是否首充
	Package models.Package //套餐信息
}
