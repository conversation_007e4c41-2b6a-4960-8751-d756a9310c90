package beans_asm

import (
	"encoding/json"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/models"

	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

func OrderDetailLoadExt(db *gorm.DB, orderId uint, orderDetails []models.OrderDetail) []models.OrderDetail {
	var exts []models.OrderDetailExt
	db.Model(&exts).Where(models.OrderDetailExt{OrderId: orderId}).Find(&exts)
	extMap := slice.KeyBy(exts, func(item models.OrderDetailExt) uint {
		return item.OrderDetailId
	})

	return slice.Map(orderDetails, func(_ int, item models.OrderDetail) models.OrderDetail {
		if ext, ok := extMap[item.ID]; ok {
			item.Ext = &ext
		}
		return item
	})
}

func UnmarshalPeoplesFrmDetailExt(ext *models.OrderDetailExt) ([]beans.TravelPeople, error) {
	var peoples = make([]beans.TravelPeople, 0)
	err := json.Unmarshal([]byte(ext.Peoples), &peoples)
	return peoples, err
}
