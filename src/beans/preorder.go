package beans

import "roadtrip-api/src/constmap"

type IFaceHotelOrderPreloadRes interface {
	GetType() constmap.OrderSubType
}

type HotelOrderPreloadDto struct {
	HotelId         uint
	Num             int
	OtaId           string //Ota产品id
	RoomId          string //Ota房间id
	PolicyId        string //Ota房型政策id
	Start           int64
	End             int64
	LoadPolicyPrice bool
	PriceRate       int64
}

type SubOrderFeeItem struct {
	TotalPrice int64  `json:"total_price"`
	SalePrice  int64  `json:"sale_price"`
	Name       string `json:"name"`
	Num        int    `json:"num"`
}

type SubOrderFee struct {
	constmap.OrderSubType `json:"order_sub_type"`
	FeeTotalPrice         int64             `json:"fee_total_price"`
	FeeName               string            `json:"fee_name"`
	FeeItems              []SubOrderFeeItem `json:"fee_items"`
}

type HotelOrderPreloadRes struct {
	*SubOrderFee
	HotelRoom *HotelRoom `json:"hotel_room"`
}

func (p *HotelOrderPreloadRes) GetType() constmap.OrderSubType {
	return p.OrderSubType
}
