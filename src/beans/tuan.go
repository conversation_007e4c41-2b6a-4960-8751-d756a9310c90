package beans

type Sku struct {
	Id              uint   `json:"id"`
	Name            string `json:"name"`
	Price           int    `json:"price"`
	SettlementPrice int    `json:"settlement_price"`
	AdultNum        int    `json:"adult_num"`
	ChildrenNum     int    `json:"children_num"`
	IsRequire       int    `json:"is_require"`
}

// 行程安排
type TuanSchedule struct {
	Name      string `json:"name"`
	ShortDesc string `json:"short_desc"`
	Content   string `json:"content"`
}
