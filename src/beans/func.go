package beans

import (
	"encoding/json"
	"roadtrip-api/src/constmap"

	"github.com/duke-git/lancet/v2/slice"
)

func UnmarshalTuanRefundRule(rules string) ([]TuanRefundRule, error) {
	var rr []TuanRefundRule
	err := json.Unmarshal([]byte(rules), &rr)
	slice.SortBy(rr, func(a, b TuanRefundRule) bool { return a.Day > b.Day })
	return rr, err
}

func GetProductExtraTicket(product TravelProduct) (*TravelProductExtraTicket, error) {
	var err error
	var extra = new(TravelProductExtraTicket)
	if err = json.Unmarshal(product.Extra, &extra); err != nil {
		return nil, err
	}
	if err = extra.Check(); err != nil {
		return nil, err
	}

	return extra, nil
}
func GetProductExtraHotel(product TravelProduct) (*TravelProductExtraHotel, error) {
	var err error
	var extra = new(TravelProductExtraHotel)
	if err = json.Unmarshal(product.Extra, &extra); err != nil {
		return nil, err
	}
	if err = extra.Check(); err != nil {
		return nil, err
	}

	return extra, nil
}
func ProductType2OrderSubType(productType constmap.ProductType) constmap.OrderSubType {
	switch productType {
	case constmap.ProductTypeZwyTicket:
		return constmap.OrderSubTypeTicket
	case constmap.ProductTypeCkHotel:
		return constmap.OrderSubTypeHotel
	case constmap.ProductTypeTuan:
		return constmap.OrderSubTypeTuan
	}
	return -1
}
func ProductType2OtaCode(productType constmap.ProductType) string {
	switch productType {
	case constmap.ProductTypeZwyTicket:
		return constmap.OtaCodeZwy
	case constmap.ProductTypeCkHotel:
		return constmap.OtaCodeCeekee
	case constmap.ProductTypeTuan:
		return constmap.OtaCodeYb
	}
	return ""
}
