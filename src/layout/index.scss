.el-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .el-aside {
    width: auto;
    overflow: visible;
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .el-header,
  .el-footer {
    height: auto;
    padding: 0;
  }

  .main-tabs {
    width: 100%;
    margin-bottom: 10px;
    background-color: #fff;
    border: 0;
    box-shadow: none;
    overflow: hidden;
    padding: 5px 0 5px 5px;
  }

  .el-main {
    background: #f0f2f5;
    padding: 10px 13px;
    box-sizing: border-box;
    // min-width: 1020px;
    // 防止切换出现横向滚动条
    overflow-x: hidden;

    .main-box {
      width: 100%;
      height: 100%;
      // background-color: #f5f5f5;
      // box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
      // border-radius: 4px;
      // padding: 20px;
      box-sizing: border-box;
      //overflow: auto;
      //overflow-x: hidden !important;

      &::-webkit-scrollbar {
        background-color: white;
      }
    }
  }
}

.shake {
  animation: shake 0.5s linear;
}

@keyframes shake {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

// 小屏幕适配
@media (max-width: 768px) {
  .el-container {
    .el-aside {
      width: auto !important;
      min-width: 200px;
    }

    .el-main {
      padding: 8px 10px;

      .main-tabs {
        padding: 3px 0 3px 3px;
        margin-bottom: 8px;
      }
    }
  }
}

// 极小屏幕适配
@media (max-width: 480px) {
  .el-container {
    .el-aside {
      min-width: 180px;
    }

    .el-main {
      padding: 5px 8px;

      .main-tabs {
        padding: 2px 0 2px 2px;
        margin-bottom: 5px;
      }
    }
  }
}
