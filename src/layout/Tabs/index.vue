<template>
  <div class="tags">
    <el-tag
        v-for="tag in tags"
        :key="tag.title"
        ref="tagRef"
        :closable="tag.close"
        :effect="tag.checked ? 'dark' : 'plain'"
        class="mR10 tabs"
        @click="tagChange(tag)"
        @close="deleteTag(tag)"
    >
      <el-icon class="tabs-icon">
        <component :is="tag.icon"></component>
      </el-icon>
      {{ tag.title }}
    </el-tag>
  </div>
</template>

<script setup>
import {useRoute, useRouter} from 'vue-router'
import {ref, watch} from 'vue'
import {useTabStore} from '../../store/modules/tabs'

const tabStore = useTabStore()
const tagRef = ref()
const route = useRoute()
const router = useRouter()
// 监听路由的变化（防止浏览器后退/前进不变化 tabsMenuValue）
watch(
    () => route.path,
    (r) => {
      const params = {
        title: route.meta.title,
        url: route.path,
        query: route.query,
        icon: 'Menu',
        close: true,
        checked: true
      }

      if (route?.meta?.fixed) {
        params.close = false
      }

      tabStore.addTabs(params)

      const main = document.querySelector('main')
      if (main) {
        main.scrollTo(0, 0)
      }
    },
    {
      immediate: true
    }
)
const tags = tabStore.tabsMenuList

const tagChange = (item) => {
  item.checked = true
  router.push({path: item.url, query: item.query})
}
const deleteTag = (item) => {
  tabStore.deleteTag(item)
}
</script>

<style lang="scss" scoped>
.tags {
  width: 95%;
  height: 100%;
  display: flex;
  flex-flow: row;
  overflow-y: auto;
  white-space: nowrap;

  &::-webkit-scrollbar {
    display: none;
  }

  .tabs {
    height: 30px;
    cursor: pointer;
  }
}
</style>
