<template>
  <div id="guide" :style="{
    width: userStore.isCollapse === true ? '' : '200px',
    height: '100%',
    color: themeConfig.textColor,
  }" class="menu">
    <div v-if="!userStore.isCollapse" class="logo">
      <img alt="" src="../../assets/logo.jpg" style="margin-right: 5px" />
      义伴运营平台
    </div>
    <div v-else class="logo">
      <img alt="" src="../../assets/logo.jpg" />
    </div>
    <el-scrollbar class="menu-scrollbar">
      <el-menu :active-text-color="themeConfig.primary" :background-color="themeConfig.backgroundColor"
        :collapse="userStore.isCollapse" :collapse-transition="false" :default-active="activeMenu" :router="true"
        :text-color="themeConfig.textColor" :unique-opened="true">
        <menuItems :menuList="all"></menuItems>
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'
import menuItems from './components/menuItems.vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/store/modules/users'
import routes from "../../router/routes";

const userStore = useUserStore()
const route = useRoute()
const activeMenu = computed(() => {
  return route.path
})
const themeConfig = userStore.themeConfig
const all = ref([])

onMounted(() => {
  let list = JSON.parse(JSON.stringify(routes))
  list = list.filter(item => {
    if (item?.meta?.constant) {
      return true
    }
    if (item.children) {
      item.children = item.children.filter(child => {
        return userStore.checkRoutePermission(child)
      })
      return item.children.length > 0
    }

    return false
  })

  all.value = list
})

</script>

<style lang="scss" scoped>
.menu {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // width: 200px;
  .logo {
    height: 48px;
    line-height: 48px;
    padding: 0 20px;
    list-style: none;
    cursor: pointer;
    position: relative;
    flex-shrink: 0;

    img {
      width: 25px;
      vertical-align: middle;
    }
  }

  .menu-scrollbar {
    flex: 1;
    height: calc(100vh - 48px);
    overflow: hidden;
  }
}
</style>
