<template>
  <template v-for="(subItem, key) in menuList" :key="key">
    <template v-if="!subItem.hidden">
      <el-menu-item v-if="!subItem.children" :index="resolvePath(subItem)">
        <el-icon>
          <component :is="subItem.meta.icon ?? 'Menu'"/>
        </el-icon>
        <template #title>
          <span>{{ subItem.meta.title }}</span>
        </template>
      </el-menu-item>
      <template v-else-if="subItem.children.length === 1">
        <template v-for="item in subItem.children">
          <el-menu-item v-if="!item.hidden" :index="resolvePath(item, subItem.path)">
            <el-icon>
              <component :is="item.meta.icon ?? 'Menu'"/>
            </el-icon>
            <template #title>
              <span>{{ item.meta.title }}</span>
            </template>
          </el-menu-item>
        </template>

      </template>
      <el-sub-menu v-else :index="resolvePath(subItem)">
        <template #title>
          <el-icon>
            <component :is="subItem.meta.icon ?? 'Menu'"/>
          </el-icon>
          <span>{{ subItem.meta.title }}</span>
        </template>
        <menu-items :menu-list="subItem.children" :parent="resolvePath(subItem)"/>
      </el-sub-menu>
    </template>
  </template>
</template>

<script setup>

import qs from "qs";

const props = defineProps({
  menuList: [Array, Object],
  parent: {
    type: String,
    default: () => ''
  }
})

function resolvePath(item, parent = '') {
  parent = parent.length > 0 ? parent : props.parent

  const p = parent.split('/').filter(item => item.length > 0)
  p.push(item.path)

  let r = p.join('/')
  if (r.indexOf('/') !== 0) {
    r = '/' + r
  }

  if (item.query) {
    r += '?' + qs.stringify(item.query)
  }

  return r
}

</script>

<style scoped>
.icons {
  width: 24px;
  height: 18px;
}
</style>
