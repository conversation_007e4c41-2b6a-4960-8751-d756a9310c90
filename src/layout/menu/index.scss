.menu {
  transition: all 0.3s ease;
  background: #20222a;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .logo {
    transition: width 0.3s ease;
    height: 55px;
    box-sizing: border-box;
    border-bottom: 2px solid #1d1e26;
    box-shadow: 2px 0 6px rgb(0 21 41 / 35%);

    span {
      font-size: 22px;
      font-weight: bold;
      color: #dadada;
      white-space: nowrap;
    }

    img {
      width: 30px;
      object-fit: contain;
      margin-right: 8px;
    }
  }

  .el-scrollbar {
    height: calc(100vh - 55px);
    flex: 1;
    overflow: hidden;

    .el-menu {
      // flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      border: none !important;
      width: 100% !important;
      height: auto;
      min-height: max(calc(100vh - 55px), 2000px);

      &::-webkit-scrollbar {
        background-color: #20222a;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #41444b;
      }

      .el-menu-item {
        width: 200px;

        &.is-active {
          background-color: #060708 !important;
        }

        &.is-active::before {
          content: "";
          top: 0;
          left: 0;
          bottom: 0;
          width: 4px;
          background: $bg-color;
          position: absolute;
        }
      }

      .href {
        display: inline-block;
        text-decoration: none;
        color: #bdbdc0;
        width: 100%;
        height: 100%;
      }
    }
  }
}

// 小屏幕适配
@media (max-width: 768px) {
  .menu {
    .el-scrollbar {
      height: calc(100vh - 55px);

      .el-menu {
        min-height: max(calc(100vh - 55px), 2000px);

        .el-menu-item {
          width: 100%;
          min-height: 48px;
          padding: 0 15px;

          .el-icon {
            margin-right: 8px;
          }
        }

        .el-sub-menu {
          .el-sub-menu__title {
            min-height: 48px;
            padding: 0 15px;
          }
        }
      }
    }
  }
}

// 极小屏幕适配
@media (max-width: 480px) {
  .menu {
    .logo {
      height: 48px;
      padding: 0 15px;

      img {
        width: 24px;
      }
    }

    .el-scrollbar {
      height: calc(100vh - 48px);

      .el-menu {
        min-height: max(calc(100vh - 48px), 2000px);

        .el-menu-item {
          min-height: 44px;
          padding: 0 12px;
          font-size: 14px;
        }

        .el-sub-menu {
          .el-sub-menu__title {
            min-height: 44px;
            padding: 0 12px;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// .el-menu--popup {
//   .el-menu-item {
//     background-color: #20222a;
//     width: 200px;

//     i {
//       margin-right: 5px;
//     }

//     i,
//     span {
//       color: hsla(0, 0%, 100%, 0.7);
//     }
// // 
//     &:hover {

//       i,
//       span {
//         color: #fff !important;
//       }
//     }

//     &.is-active {
//       background-color: #060708 !important;

//       &:before {
//         content: "";
//         top: 0;
//         left: 0;
//         bottom: 0;
//         width: 4px;
//         background: $bg-color;
//         position: absolute;
//       }

//       i,
//       span {
//         color: #fff !important;
//       }
//     }
//   }
// }