<script setup>

import {ref, watch} from "vue";
import {resetpwd} from "../../../api/modules/index";
import {ElNotification} from "element-plus";

const show = ref(true)
const emit = defineEmits(['close'])

const form = ref({
  old_password: '',
  new_password: '',
  confirm_password: ''
})
const formRef = ref(null)

const rules = {
  old_password: [
    {required: true, message: '请输入原密码', trigger: 'blur'},
    {min: 6, message: '密码长度不能小于6位', trigger: 'blur'}
  ],
  new_password: [
    {required: true, message: '请输入新密码', trigger: 'blur'},
    {min: 6, message: '密码长度不能小于6位', trigger: 'blur'}
  ],
  confirm_password: [
    {required: true, message: '请输入确认密码'},
    {
      validator: (rule, value, callback) => {
        if (value !== form.value.new_password) {
          callback(new Error('两次密码不一致'))
        } else {
          callback()
        }
      }
    }
  ]
}

function onSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    resetpwd({...form.value}).then(() => {
      ElNotification.success('密码修改成功')
      show.value = false
    })

  })
}

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" title="修改密码">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
      <el-form-item label="原密码" prop="old_password">
        <el-input v-model="form.old_password" type="password"/>
      </el-form-item>
      <el-form-item label="新密码" prop="new_password">
        <el-input v-model="form.new_password" type="password"/>
      </el-form-item>
      <el-form-item label="旧密码" prop="confirm_password">
        <el-input v-model="form.confirm_password" type="password"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>