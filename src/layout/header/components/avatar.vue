<template>
  <el-dropdown placement="bottom" @command="handleCommand">
    <div class="flx-center">
      <div class="avatar">
        <img alt="avatar" src="../../../assets/images/avart.jpg"/>
      </div>
      <span :style="{ color: themeConfig.footColor }" class="username"
      >{{ userStore.userInfo.name }}</span
      >
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="password">修改密码</el-dropdown-item>
        <el-dropdown-item command="logout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>

  <ChangePassword v-if="showPassword" @close="showPassword = false"/>

</template>
<script setup>

import {useRouter} from 'vue-router'
import {useUserStore} from '../../../store/modules/users'
import ChangePassword from "./ChangePassword.vue";
import {ref} from "vue";

const router = useRouter()
const userStore = useUserStore()
const themeConfig = userStore.themeConfig
const showPassword = ref(false)

function handleCommand(cmd) {
  switch (cmd) {
    case 'password':
      showPassword.value = true
      break
    case 'logout':
      userStore.logout()
      break
  }
}
</script>
<style lang="scss" scoped>
.el-dropdown {
  cursor: pointer;

  .el-tooltip__trigger {
    display: flex;
    flex-flow: row;
    align-items: center;
  }
}

.username {
  font-size: 15px;

  margin: 0 0 0 10px;
}

.avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  margin-left: 20px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
