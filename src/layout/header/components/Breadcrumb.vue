<template>
  <el-icon id="collapseIcon" class="icon-style" @click="changeShrinkage">
    <component :is="!isCollapse ? 'Fold' : 'Expand'"></component>
  </el-icon>
  <el-breadcrumb id="breadcrumb" separator="/">
    <el-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index">
      <span v-if="index === breadcrumbList.length - 1" :style="{ color: themeConfig.footColor }" class="no-redirect">{{ item.meta.title }}</span>
      <span v-else :style="{ color: themeConfig.footColor }" class="redirect" @click="handleRedirect(item.path)">{{ item.meta.title }}</span>
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { watch, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../../../store/modules/users'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const breadcrumbList = ref([])
const isCollapse = ref(false)
const themeConfig = userStore.themeConfig
const initBreadcrumbList = () => {
  breadcrumbList.value = route.matched
}
const handleRedirect = (path) => {
  router.push(path)
}

const changeShrinkage = () => {
  isCollapse.value = !userStore.isCollapse
  userStore.changeIsCollapse(isCollapse.value)
}

watch(
    () => route.path,
  () => {
    initBreadcrumbList()
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.no-redirect {
  // color: #97a8be;
  cursor: text;
}
.redirect {
  font-weight: 600;
  cursor: pointer;
  &:hover {
    color: #97a8be;
  }
}
.icon-style {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
}
</style>
