<template>
  <el-container class="container">
    <el-aside :style="{ backgroundColor: themeConfig.backgroundColor }"
    >
      <Menu></Menu
      >
    </el-aside>
    <el-container>
      <el-header>
        <Header></Header>
      </el-header>
      <el-main>
        <div v-if="themeConfig.istags" class="main-tabs flx-row">
          <tabs></tabs>
        </div>

        <div class="main-box">
          <router-view v-slot="{Component}" :class="{ shake: disabled }">
            <keep-alive :exclude="excludes" :max="15">
              <component :is="Component"/>
            </keep-alive>

          </router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script name="layout" setup>
import {useRoute} from 'vue-router'
import {ref, watch} from 'vue'
import Header from './header/index.vue'
import Menu from './menu/index.vue'
import tabs from './Tabs/index.vue'
import {useUserStore} from '../store/modules/users'

const userStore = useUserStore()

const themeConfig = userStore.themeConfig

const route = useRoute()
const disabled = ref(false)
const excludes = ref([])

watch(() => route.path, () => {
  if (route.meta.noCache && !excludes.value.includes(route.name)) {
    excludes.value.push(route.name)
  }

  disabled.value = true
  setTimeout(() => {
    disabled.value = false
  }, 1000)
}, {immediate: true})

</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
