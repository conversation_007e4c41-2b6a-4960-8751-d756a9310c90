package validators

import (
	"regexp"
	"strconv"
	"time"
)

func IsMobile(string2 string) bool {
	re, _ := regexp.Compile("^1\\d{10}$")
	return re.MatchString(string2)
}

// 是否有效身份证号
func IsIdCardNo(s string) bool {
	return validateIdCardNo18(s) || validateIdCardNo15(s)
}

func validateIdCardNo18(idCard string) bool {
	// 1. 检查长度
	if len(idCard) != 18 {
		return false
	}

	// 2. 正则表达式匹配格式
	re := regexp.MustCompile(`^\d{17}[\dXx]$`)
	if !re.MatchString(idCard) {
		return false
	}

	// 3. 计算校验码
	var sum int
	coefficients := []int{7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2}
	checkCodeMap := map[int]string{
		0: "1", 1: "0", 2: "X", 3: "9", 4: "8", 5: "7", 6: "6", 7: "5", 8: "4", 9: "3", 10: "2",
	}

	for i := 0; i < 17; i++ {
		digit, _ := strconv.Atoi(string(idCard[i]))
		sum += digit * coefficients[i]
	}

	calculatedCheckCode := checkCodeMap[sum%11]

	// 4. 比较计算出的校验码和实际的校验码
	actualCheckCode := idCard[17:]
	if actualCheckCode != calculatedCheckCode {
		return false
	}

	return true
}

func validateIdCardNo15(idCard string) bool {
	// 1. 检查长度
	if len(idCard) != 15 {
		return false
	}

	// 2. 正则表达式匹配格式
	re := regexp.MustCompile(`^\d{15}$`)
	if !re.MatchString(idCard) {
		return false
	}

	// 3. 提取出生日期
	birthYear, _ := strconv.Atoi(idCard[6:8])
	birthMonth, _ := strconv.Atoi(idCard[8:10])
	birthDay, _ := strconv.Atoi(idCard[10:12])

	// 4. 计算完整的出生年份
	if birthYear >= 0 && birthYear <= 37 { // 1937年及以后
		birthYear += 1900
	} else if birthYear >= 38 && birthYear <= 99 { // 1900年至1936年
		birthYear += 1800
	} else {
		return false
	}

	// 5. 检查日期是否有效
	birthDate := time.Date(birthYear, time.Month(birthMonth), birthDay, 0, 0, 0, 0, time.UTC)
	if birthDate.IsZero() || birthDate.After(time.Now()) {
		return false
	}

	// 6. 检查性别
	genderDigit, _ := strconv.Atoi(string(idCard[14]))
	if genderDigit%2 != 0 && genderDigit%2 != 1 {
		return false
	}

	return true
}
