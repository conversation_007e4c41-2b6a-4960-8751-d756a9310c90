<template>
  <div class="login-container flx-center">
    <div class="login-box">
      <div class="login-left">
        <div class="title"></div>
        <div class="sub"></div>
        <img alt="" src="../../assets/login.png"/>
      </div>

      <div class="login-form">
        <div class="login-logo">
          <img alt="" class="login-icon" src="../../assets/logo.jpg"/>
          <p class="logo-text">义伴欢迎登录</p>
        </div>
        <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" size="large">
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" :prefix-icon="User" placeholder="用户名">
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="loginForm.password" :prefix-icon="Lock" placeholder="密码" show-password type="password">
            </el-input>
          </el-form-item>
        </el-form>

        <div class="login-btn">
          <el-button :icon="CircleClose" round size="large" @click="resetForm(loginFormRef)">重置
          </el-button>
          <el-button :icon="UserFilled" :loading="loading" round size="large" type="primary"
                     @click="login(loginFormRef)">
            登录
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script name="login" setup>
import {CircleClose, Lock, User, UserFilled,} from '@element-plus/icons-vue'
import {reactive, ref, onMounted, onUnmounted, computed} from 'vue'
import {useRouter} from 'vue-router'
import {useUserStore} from '../../store/modules/users'

const loginFormRef = ref()
const loading = ref(false)
const router = useRouter()
const loginForm = reactive({
  username: '',
  password: '',
  checked: true
})
const loginRules = reactive({
  username: [{required: true, message: '请输入用户名', trigger: 'blur'}],
  password: [{required: true, message: '请输入密码', trigger: 'blur'}]
})

const userStore = useUserStore()

const login = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      loading.value = true

      userStore.login(loginForm).catch(() => {
        loading.value = false
      })
    } else {
      return false
    }
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
