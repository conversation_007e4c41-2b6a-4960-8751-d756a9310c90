<template>
  <el-card>
    <el-form inline>
      <el-form-item label="套餐名称">
        <el-input v-model="search.keyword" clearable/>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="search.state" clearable>
          <el-option v-for="(item, index) in PackageStateMaps" :key="index" :label="item" :value="index"/>
        </el-select>
      </el-form-item>
      <el-form-item label="新用户专享">
        <el-select v-model="search.new_usable" clearable>
          <el-option :value="1" label="是"/>
          <el-option :value="0" label="否"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="() => {
          page = 1
          getList()
        }">搜索
        </el-button>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="() => {
      id = 0;
      showDetail = true
    }">新增套餐
    </el-button>

    <el-table v-loading="loading" :data="list" align="center">
      <el-table-column align="center" label="套餐ID" prop="id" width="80"/>
      <el-table-column align="center" label="套餐名称" prop="name"/>
      <el-table-column align="center" label="套餐描述" prop="desc" show-overflow-tooltip/>
      <el-table-column align="center" label="套餐价格">
        <template #default="{ row }">
          <div>
            <div>原价: {{ formatMoney(row.price) }}</div>
            <div v-if="row.real_price !== row.price" class="real-price">实际: {{ formatMoney(row.real_price) }}</div>
            <div v-if="row.new_usable === Enable" class="real-price">
              <div>新用户专享价格: {{ formatMoney(row.new_real_price) }}</div>
            </div>
            <div>积分数: {{ row.amount }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="套餐有效期" width="100">
        <template #default="{ row }">
          {{ row.exp_num }} {{ row.exp_unit_text }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="排序值" prop="sort" width="80"/>
      <el-table-column align="center" label="标签" width="150">
        <template #default="{ row }">
          <el-tag v-for="tag in (row.tags || [])" :key="tag" class="mx-1" size="small"
                  style="margin-right: 4px; margin-bottom: 4px;">
            {{ tag }}
          </el-tag>
          <span v-if="!row.tags || row.tags.length === 0">-</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.state === 1 ? 'success' : 'danger'">
            {{ PackageStateMaps[row.state] }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :formatter="tableFormatTime" align="center" label="创建时间" prop="created_at" width="150"/>
      <el-table-column fixed="right" label="操作" width="200">
        <template #default="{ row }">
          <el-space wrap>
            <el-button size="small" type="primary" @click="() => {
              id = row.id;
              showDetail = true
            }">编辑
            </el-button>

            <!-- 根据不同状态显示启用/禁用按钮 -->
            <template v-if="row.state === 2">
              <el-button size="small" type="success" @click="handleEnable(row.id, 1)">启用</el-button>
            </template>
            <template v-else>
              <el-popconfirm title="您确定要禁用此套餐吗？" @confirm="handleEnable(row.id, 2)">
                <template #reference>
                  <el-button size="small" type="danger">禁用</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-space>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="handlePage"/>
    <PackageDetail v-if="showDetail" :id="id" @close="handleDetailClose"/>
  </el-card>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import MyPagination from '../../components/MyPagination.vue'
import {packages, packageEnable} from '../../api/modules/package'
import {tableFormatTime, formatMoney} from '../../utils'
import {ElNotification} from 'element-plus'
import PackageDetail from './PackageDetail.vue'
import {Enable} from "../../utils/constmap";


// 套餐状态映射
const PackageStateMaps = {
  1: '可使用',
  2: '不可用'
}

// 数据和状态
const list = ref([])
const total = ref(0)
const loading = ref(false)
const showDetail = ref(false)
const id = ref(0)
let page = 1
const search = ref({
  keyword: '',
  state: '',
  new_usable: ''
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 使用实际API调用
    const res = await packages({
      page,
      ...search.value
    })

    // 处理返回的数据，将tags字符串转换为数组
    if (res.data && res.data.list) {
      res.data.list.forEach(item => {
        if (item.tags && typeof item.tags === 'string') {
          item.tags = item.tags.split(',').filter(tag => tag.trim() !== '')
        } else {
          item.tags = []
        }
      })
    }

    list.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handlePage = (val) => {
  page = val
  getList()
}

// 处理套餐状态变更
const handleEnable = async (id, state) => {
  try {
    // 调用API更新套餐状态
    await packageEnable(id, state)

    // 模拟成功响应
    // 更新本地数据状态
    const item = list.value.find(item => item.id === id)
    if (item) {
      item.state = state
    }
    // 根据不同状态显示不同消息
    let message = '操作成功'
    if (state === 1) message = '套餐已设为可使用'
    else if (state === 2) message = '套餐已设为不可用'
    ElNotification({
      title: '成功',
      message,
      type: 'success'
    })
  } catch (error) {
    console.error(error)
    ElNotification({
      title: '错误',
      message: '操作失败',
      type: 'error'
    })
  }
}

// 处理详情对话框关闭
const handleDetailClose = () => {
  showDetail.value = false
  getList()
}

// 初始化加载数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.el-image {
  width: 100px;
  height: 60px;
}

.real-price {
  color: #F56C6C;
  font-weight: bold;
}
</style>
