<template>
  <el-dialog v-model="dialogVisible" :title="id ? '编辑套餐' : '新增套餐'" width="500px" @close="handleClose">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" status-icon>
      <el-form-item label="套餐名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入套餐名称"/>
      </el-form-item>
      <el-form-item label="套餐描述" prop="desc">
        <el-input v-model="form.desc" :rows="3" placeholder="请输入套餐描述" type="textarea"/>
      </el-form-item>
      <el-form-item label="排序值" prop="sort">
        <el-input-number v-model="form.sort" :min="0" :step="1" placeholder="请输入排序值（数字越大越靠前）"/>
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <div class="tag-input">
          <el-tag v-for="tag in form.tags" :key="tag" class="mx-1" closable @close="handleRemoveTag(tag)">
            {{ tag }}
          </el-tag>
          <el-input v-if="inputVisible" ref="InputRef" v-model="inputValue" class="ml-1 w-20" size="small"
                    @blur="handleInputConfirm" @keyup.enter="handleInputConfirm"/>
          <el-button v-else class="button-new-tag ml-1" size="small" @click="showInput">
            + 添加标签
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="原价(元)" prop="price">
        <el-input-number v-model="form.price" :min="0" :precision="2" :step="1" placeholder="请输入套餐原价(元)"/>
      </el-form-item>
      <el-form-item label="实际价(元)" prop="real_price">
        <el-input-number v-model="form.real_price" :min="0" :precision="2" :step="1"
                         placeholder="请输入套餐实际价(元)"/>
      </el-form-item>
      <el-form-item label="积分数" prop="amount">
        <el-input-number v-model="form.amount" :min="0" :step="10" placeholder="请输入积分数"/>
      </el-form-item>
      <el-form-item label="有效期" prop="exp_num">
        <el-input-number v-model="form.exp_num" :min="1" :step="1" placeholder="请输入有效期数量"/>
        <el-select v-model="form.exp_unit" class="ml-2" style="width: 100px">
          <el-option v-for="item in DurationUnits" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="新用户专享价" prop="new_usable">
        <el-checkbox v-model="form.new_usable" :false-value="Disable" :true-value="Enable"/>
      </el-form-item>

      <el-form-item v-if="form.new_usable === Enable" label="新用户价格" prop="new_real_price">
        <el-input-number v-model="form.new_real_price" :min="0" :precision="2" :step="1"
                         placeholder="请输入新用户价格(元)"/>
      </el-form-item>

      <!-- 移除套餐状态编辑功能 -->
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import {ref, reactive, defineProps, defineEmits, onMounted, nextTick} from 'vue'
import {ElMessage} from 'element-plus'
import {packageDetail, packageSave} from '../../api/modules/package'
import {Disable, DurationUnits, Enable} from '../../utils/constmap'
import {deepToRaw} from "../../utils";

const props = defineProps({
  id: {
    type: [Number, String],
    default: 0
  }
})

const emit = defineEmits(['close'])

const dialogVisible = ref(true)
const loading = ref(false)
const formRef = ref(null)

// 标签输入相关
const inputValue = ref('')
const inputVisible = ref(false)
const InputRef = ref(null)

// 表单数据
const form = reactive({
  id: 0,
  name: '',
  desc: '',
  price: 0,
  real_price: 0,
  amount: 0,
  exp_num: 1,
  exp_unit: 1,
  exp_unit_text: '周',
  new_usable: 2,
  new_real_price: 0,
  state: 1,
  sort: 0,
  tags: []
})

// 表单验证规则
const rules = {
  name: [
    {required: true, message: '请输入套餐名称', trigger: 'blur'},
    {min: 2, max: 30, message: '长度在 2 到 30 个字符', trigger: 'blur'}
  ],
  desc: [
    {required: true, message: '请输入套餐描述', trigger: 'blur'}
  ],
  price: [
    {required: true, message: '请输入套餐原价', trigger: 'blur'}
  ],
  real_price: [
    {required: true, message: '请输入套餐实际价格', trigger: 'blur'}
  ],
  amount: [
    {required: true, message: '请输入积分数', trigger: 'blur'}
  ],
  exp_num: [
    {required: true, message: '请输入有效期数量', trigger: 'blur'}
  ],
  exp_unit: [
    {required: true, message: '请选择有效期单位', trigger: 'change'}
  ],
  state: [
    {required: true, message: '请选择套餐状态', trigger: 'change'}
  ],
  new_real_price: [
    {required: true, message: '请输入新用户价格', trigger: 'blur'}
  ]
}

// 获取套餐详情
const getDetail = async () => {
  // 如果是新增套餐，使用默认值初始化表单
  if (!props.id) {
    // 使用默认值
    return
  }

  try {
    // 调用API获取详情
    const res = await packageDetail(props.id)

    // 更新表单数据
    if (res.data) {
      // 将分转换为元
      const data = {...res.data}
      data.price = data.price / 100
      data.real_price = data.real_price / 100

      if (data.new_usable) {
        data.new_real_price = data.new_real_price / 100
      }

      // 将标签字符串转换为数组
      if (data.tags && typeof data.tags === 'string') {
        data.tags = data.tags.split(',').filter(tag => tag.trim() !== '')
      } else {
        data.tags = []
      }

      Object.assign(form, data)
    }
  } catch (error) {
    console.error('获取套餐详情失败', error)
    ElMessage.error('获取套餐详情失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 处理exp_unit_text
    const unitMapping = {1: '周', 2: '月', 3: '年'}
    form.exp_unit_text = unitMapping[form.exp_unit] || '周'

    // 将元转换为分
    const submitData = deepToRaw(form)
    submitData.price = Math.round(submitData.price * 100)
    submitData.real_price = Math.round(submitData.real_price * 100)

    if (submitData.new_usable) {
      submitData.new_real_price = Math.round(submitData.new_real_price * 100)
    } else {
      submitData.new_real_price = 0
    }

    // 将标签数组转换为英文逗号拼接的字符串
    if (Array.isArray(submitData.tags)) {
      submitData.tags = submitData.tags.join(',')
    } else {
      submitData.tags = ''
    }

    loading.value = true
    // 调用API保存数据
    await packageSave(submitData)

    ElMessage.success('保存成功')
    handleClose()
  } catch (error) {
    console.error('保存失败', error)
    ElMessage.error(error.msg || '保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 处理标签输入显示
const showInput = () => {
  inputVisible.value = true
  // 自动聚焦输入框
  nextTick(() => {
    InputRef.value.focus()
  })
}

// 处理标签输入确认
const handleInputConfirm = () => {
  if (inputValue.value) {
    // 避免重复添加相同标签
    if (!form.tags.includes(inputValue.value)) {
      form.tags.push(inputValue.value)
    }
  }
  inputVisible.value = false
  inputValue.value = ''
}

// 移除标签
const handleRemoveTag = (tag) => {
  form.tags.splice(form.tags.indexOf(tag), 1)
}

// 初始化
onMounted(() => {
  if (props.id) {
    getDetail()
  }
})
</script>

<style scoped>
.ml-2 {
  margin-left: 8px;
}

.ml-1 {
  margin-left: 4px;
}

.w-20 {
  width: 100px;
}

.tag-input {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
</style>
