<script setup>

import {useRouter} from "vue-router";
import {computed, onActivated, ref} from "vue";
import {checkMobile} from "../../utils/validator";

const router = useRouter()
const form = ref({
  name: '',
  mobile: '',
  username: '',
  password_old: '',
  password_new: '',
  password_new_2: '',
})
const formRef = ref(null)

const rules = computed(() => {
  const r = {
    name: [{required: true, message: '姓名是必填的'}],
    mobile: [
      {required: true, message: '手机号码是必填的'},
      {validator: checkMobile}
    ],
  }
  if (form.value.password_old || form.value.password_new) {
    r["password_old"] = [{required: true, message: '原密码是必填的'}]
    r["password_new"] = [{required: true, message: '新密码是必填的'}]
    r["password_new_2"] = [{required: true, message: '确认密码是必填的'}]
  }

  return r
})

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {...form.value}
  })
}

onActivated(() => {
  getMy()
})

</script>

<template>
  <el-card>
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="登录名">
        {{ form.username }}
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="form.mobile"/>
      </el-form-item>
      <el-form-item label="原密码" prop="password_old">
        <el-input v-model="form.password_old" placeholder="如需重置密码，请输入原密码和新密码" type="password"/>
      </el-form-item>
      <el-form-item label="新密码" prop="password_new">
        <el-input v-model="form.password_new" placeholder="如需重置密码，请输入原密码和新密码" type="password"/>
      </el-form-item>
      <el-form-item label="确认密码" prop="password_new_2">
        <el-input v-model="form.password_new_2" placeholder="如需重置密码，请输入原密码和新密码" type="password"/>
      </el-form-item>

      <el-form-item>
        <el-button @click="() => {
          router.back()
        }">取消
        </el-button>
        <el-button type="primary" @click="handleSubmit">修改</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<style lang="scss" scoped>
.el-input {
  width: 300px;
}
</style>
