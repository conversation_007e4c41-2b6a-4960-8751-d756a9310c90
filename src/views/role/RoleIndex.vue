<script setup>

import {onActivated, ref} from "vue";
import {roles} from "../../api/modules/staff";
import MyPagination from "../../components/MyPagination.vue";
import {tableFormatTime} from "../../utils";
import RoleDetail from "./components/RoleDetail.vue";

const list = ref([])
const total = ref(0)
let page = 1
const showDetail = ref(false)
const id = ref(0)

function getList() {
  const params = {
    page,
  }

  roles(params).then(res => {
    const {data} = res

    list.value = data.list
    total.value = data.total
  })
}

function handlePage(p) {
  page = p.page
  getList()
}

onActivated(() => {
  getList()
})

</script>
<template>
  <el-card>
    <el-button type="primary" @click="() => {
      id = 0
      showDetail = true
    }">新增角色
    </el-button>

    <el-table :data="list">
      <el-table-column label="名称" prop="name">
        <template #default="{row}">
          {{ row.name }}
          <el-tag v-if="row.is_admin" type="danger">超级管理员</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="角色代码" prop="code"/>
      <el-table-column label="初始客户归属">
        <template #default="{row}">
          <el-tag v-if="row.customer_own">是</el-tag>
          <el-tag v-else type="warning">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column :formatter="tableFormatTime" label="创建时间" prop="created_at"></el-table-column>
      <el-table-column label="状态">
        <template #default="{row}">
          <el-tag v-if="row.state === 1">启用</el-tag>
          <el-tag v-else type="danger">禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{row}">
          <el-button size="small" type="primary" @click="() => {
            id = row.id
            showDetail = true
          }">详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <MyPagination :total="total" @current-change="handlePage"/>

    <RoleDetail v-if="showDetail" :id="id" @close="() => {
      getList()
      showDetail = false
    }"/>

  </el-card>
</template>
