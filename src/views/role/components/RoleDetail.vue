<script setup>

import {computed, onMounted, ref, toRaw, watch} from "vue";
import MyPermissionSelector from "../../../components/MyPermissionSelector.vue";
import {roleDetail, roleSave} from "../../../api/modules/staff";
import {ElNotification} from "element-plus";

const props = defineProps({
  id: Number,
})
const emit = defineEmits(['close'])
const isEdit = computed(() => props.id > 0)
const title = computed(() => isEdit.value ? '编辑角色' : '新增角色')
const show = ref(true)
const form = ref({
  name: '',
  code: '',
  state: 2,
  id: '',
  customer_own: 2,
})
const permissions = ref([])
const formRef = ref(null)
const rules = {
  name: [{required: true, message: '角色名称是必填的！'}],
  code: [{required: true, message: '角色代码是必填的！'}],
  // permissions: [{required: true, message: '权限集是必填的！'}],
}

function handleOk() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = toRaw(form.value)
    data.permissions = toRaw(permissions.value).map(item => {
      return (item instanceof Array) ? item[1] : item
    })
    data.permissions = JSON.stringify(data.permissions)

    roleSave(data).then(() => {
      ElNotification.success('操作成功')
      show.value = false
    })
  })
}

onMounted(() => {
  if (isEdit.value) {
    roleDetail(props.id).then(res => {
      const {data} = res
      Object.assign(form.value, data)
      permissions.value = data.permissions
    })
  }
})

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" :title="title">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="角色名称" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      <el-form-item label="角色代码" prop="code">
        <el-input v-model="form.code" placeholder="英文或数字"/>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-radio-group v-model="form.state">
          <el-radio-button :label="1">启用</el-radio-button>
          <el-radio-button :label="2">禁用</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="初始客户归属" prop="state">
        <el-radio-group v-model="form.customer_own">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="2">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="权限集">
        <MyPermissionSelector v-model="permissions"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>

  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
