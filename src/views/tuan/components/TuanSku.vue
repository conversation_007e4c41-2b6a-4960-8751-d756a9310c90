<script setup>

import { computed, onMounted, ref, watch } from "vue";
import { tuanSkuDetail } from "../../../api/modules/product";

const emit = defineEmits(['close'])
const show = ref(true)
const props = defineProps({
  id: {
    type: Number,
  }
})

const form = ref({
  id: 0,
  name: '',
  adult_num: 1,
  children_num: 1,
  is_require: 2,
  price: '',
  settlement_price: '',
})
const formRef = ref(null)
const rules = computed(() => {
  return {
    name: [{ required: true, message: '规格名称是必填的！' }],
    // price: [{required: true, message: '价格是必填的！'}, {
    //   validator(rule, value, callback) {
    //     if (value <= 0) {
    //       callback(new Error('价格必须大于0！'))
    //       return
    //     }
    //     callback()
    //   }
    // }],
    // settlement_price: [
    //   {required: true, message: '结算价是必填的！'},
    //   {
    //     validator(rule, value, callback) {
    //       if (value <= 0) {
    //         callback(new Error('结算价必须大于0！'))
    //         return
    //       }
    //       callback()
    //     }
    //   }],
    adult_num: [{
      validator(rule, value, callback) {
        if (form.value.adult_num === 0 && form.value.children_num === 0) {
          callback(new Error('成人和儿童数量至少选一个！'))
          return
        }
        callback()
      }
    }]
  }
})

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    emit('close', { ...form.value })
  })
}

onMounted(() => {
  if (!props.id) {
    return
  }
  tuanSkuDetail(props.id).then(res => {
    const { data } = res

    data.price /= 100
    data.settlement_price /= 100

    Object.assign(form.value, data)
  })
})

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" title="商品规格" append-to-body>
    <el-form label-width="80px" :model="form" ref="formRef" :rules="rules">
      <el-form-item label="规格名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <!-- <el-form-item label="价格" prop="price">
        <el-input v-model="form.price">
        </el-input>
      </el-form-item>
      <el-form-item prop="settlement_price" label="结算价">
        <el-input v-model="form.settlement_price"/>
      </el-form-item> -->
      <el-form-item label="是否必选" prop="is_require">
        <el-radio-group v-model="form.is_require">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="2">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="成人数量" prop="adult_num">
        <el-input-number :max="20" v-model="form.adult_num" :min="0" />
      </el-form-item>
      <el-form-item label="儿童数量" prop="children_num">
        <el-input-number :min="0" v-model="form.children_num" :max="20" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button @click="handleSubmit" type="primary">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.el-input {
  width: 200px;
}
</style>