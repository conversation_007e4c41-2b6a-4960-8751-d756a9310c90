<script setup>
import { computed, onBeforeMount, ref, watch } from 'vue';
import { productDetail, productSaveSkuDate } from '@/api/modules/product';
import dayjs from 'dayjs'
import MyDateRangePicker from '@/components/MyDateRangePicker.vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  id: Number,
})
const emits = defineEmits(['close'])
const show = ref(true)
const skus = ref([])
const list = ref([])
const formRef = ref(null)
const weeks = ['一', '二', '三', '四', '五', '六', '日']
const numberValidator = (valGetter, min, message) => {
  return {
    validator(_, __, callback) {
      if (valGetter() < min) {
        return new Error(message)
      }
      callback()
    },
  }
}
const rules = computed(() => {
  let rules = {}
  list.value.forEach((d, di) => {
    rules[`item[${di}].date`] = [{
      validator(rule, value, callback, source, options) {
        if (d.weeks.length == 0) {
          return new Error('请致少选择一项')
        }
        if (d.date.length != 2) {
          return new Error('请选择开始结束日期')
        }
        if (d.date[0].getTime() > d.date[1].getTime()) {
          return new Error('开始日期不得大于结束日期')
        }
        callback()
      },
    }]
    rules[`item[${di}].stock`] = [numberValidator(() => d.stock, 1, '请填写库存')]
    d.skus.forEach((s, si) => {
      if (!s.enable) {
        return
      }
      rules[`item[${di}].skus[${si}].price`] = [numberValidator(() => s.price, 0.01, '请填写价格')]
      rules[`item[${di}].skus[${si}].settlement_price`] = [numberValidator(() => s.settlement_price, 0.01, '请填写结算价格')]
    })
  })
  return rules
})
const tableData = computed(() => {
  let dataList = []
  list.value.forEach((d, i) => {
    d.skus.forEach((s, si) => {
      dataList.push({
        data: d,
        sku: s,
        rowspan: si == 0 ? d.skus.length : 0,
        rowIdx: i,
        skuIdx: si,
      })
    })
  })
  return dataList
})

const doLoad = async () => {
  const { data } = await productDetail(props.id)
  skus.value = data.skus
  list.value = data.date_skus.map(d => {
    let litem = {
      date: [dayjs.unix(d.start).toDate(), dayjs.unix(d.end).toDate()],
      weeks: d.weeks.split(',').map(v => Number(v)),
      stock: d.stock,
    }
    litem.skus = skus.value.map(sku => {
      let dsku = d.skus.find(s => s.sku_id == sku.id)
      return {
        enable: !!dsku,
        sku_id: sku.id,
        name: sku.name,
        price: dsku ? dsku.price / 100 : 0,
        settlement_price: dsku ? dsku.settlement_price / 100 : 0,
      }
    })
    return litem
  })
  if (list.value.length == 0) {
    doAddDates()
  }
}

const allWeeks = () => (Array(7).fill(0).map((_, i) => (i + 1)))
const doAddDates = () => {
  list.value.push({
    date: [],
    weeks: allWeeks(),
    stock: 0,
    skus: skus.value.map(s => ({
      enable: false,
      sku_id: s.id,
      name: s.name,
      price: 0,
      settlement_price: 0,
    }))
  })
}

const doRowspan = ({
  row,
  column,
  rowIndex,
  columnIndex,
}) => {
  if (columnIndex <= 2) {
    return {
      colspan: 1,
      rowspan: row.rowspan,
    }
  }
  return {
    rowspan: 1,
    colspan: 1,
  }
}

const doCheckAll = (row) => {
  if (row.data.weeks.length > 0) {
    row.data.weeks = []
  } else {
    row.data.weeks = allWeeks()
  }
}

const doSave = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) {
      return
    }
    let params = {
      tuan_id: props.id,
      dates: list.value.map(d => {
        return {
          ...d,
          date: undefined,
          weeks: d.weeks.join(','),
          start: dayjs(d.date[0]).unix(),
          end: dayjs(d.date[1]).unix(),
          skus: d.skus.filter(v => v.enable).map(s => ({
            ...s,
            price: parseInt(s.price * 100),
            settlement_price: parseInt(s.settlement_price * 100),
          }))
        }
      }),
    }

    params.dates = JSON.stringify(params.dates)

    await productSaveSkuDate(params)

    ElMessage.success({
      message: '已保存',
    })
    emits('close')
  })
}

const doDel = rowIdx => {
  list.value.splice(rowIdx, 1)
}

onBeforeMount(() => {
  doLoad()
})
watch(show, (v) => {
  if (!v) {
    emits('close')
  }
})
</script>

<template>
  <el-drawer v-model="show" title="设置团期" size="80%">
    <el-form ref="formRef" :model="list" :rules="rules">
      <el-table :data="tableData" :span-method="doRowspan" border>

        <el-table-column label="操作" :width="60">
          <template #default="{ row }">
            <el-popconfirm , title="删除此行？" @confirm="doDel(row.rowIdx)">
              <template #reference>
                <el-button link size="small" type="danger">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>

        <el-table-column label="时间段/星期几" :width="320">
          <template #default="{ row }">
            <el-form-item :prop="`item[${row.rowIdx}].date`">
              <MyDateRangePicker v-model="row.data.date" is-next></MyDateRangePicker>
              <el-checkbox-group v-model="row.data.weeks">
                <el-checkbox v-for="(txt, i) in weeks" :key="i" :label="`周${txt}`" :value="i + 1" />
              </el-checkbox-group>
              <el-button link @click="doCheckAll(row)">全选/反选</el-button>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column label="每日库存" :width="90">
          <template #default="{ row }">
            <el-form-item :prop="`item[${row.rowIdx}].stock`">
              <el-input-number v-model="row.data.stock" :controls="false" />
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column label="规格" :width="200">
          <template #default="{ row }">
            <el-checkbox v-model="row.sku.enable" :label="row.sku.name" :title="row.sku.name" />
          </template>
        </el-table-column>

        <el-table-column label="单价">
          <template #default="{ row }">
            <el-form-item :prop="`item[${row.rowIdx}].skus[${row.skuIdx}].price`">
              <el-input-number v-model="row.sku.price" :precision="2" :controls="false" />
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column label="结算价">
          <template #default="{ row }">
            <el-form-item :prop="`item[${row.rowIdx}].skus[${row.skuIdx}].settlement_price`">
              <el-input-number v-model="row.sku.settlement_price" :precision="2" :controls="false" />
            </el-form-item>
          </template>
        </el-table-column>

      </el-table>
    </el-form>
    <el-divider border-style="none" />
    <el-button @click="doAddDates">添加一行</el-button>
    <el-button type="primary" @click="doSave">保存</el-button>
    <el-button @click="emits('close')">取消</el-button>
  </el-drawer>
</template>