<script setup>
import { computed, onBeforeMount, ref, watch } from 'vue';
import { productDetail, productSaveTripNode } from '@/api/modules/product';
import MyEditor from '@/components/MyEditor.vue';
import { ElMessage, ElNotification } from 'element-plus';

const props = defineProps({
  id: Number,
})
const emits = defineEmits(['close'])
const show = ref(true)
const list = ref([])
const formRef = ref(null)
const rules = computed(() => {
  let rules = []
  list.value.forEach((v, i) => {
    rules[`item[${i}].name`] = [{ required: true, message: '请输入标题' }]
    rules[`item[${i}].short_desc`] = [{ required: true, message: '请输入简介' }]
    // rules[`item[${i}].content`] = [{ required: true, message: '内容不能为空' }]
  })
  return rules
})

const doLoad = async () => {
  const { data } = await productDetail(props.id)
  list.value = data.schedules
  if (list.value.length == 0) {
    doAdd()
  }
}

const doAdd = () => {
  list.value.push({
    name: '',
    short_desc: '',
    content: '',
  })
}

const doDel = i => list.value.splice(i)

const doSave = async () => {
  if (list.value == 0) {
    return ElNotification.error('请至少填写一项')
  }
  let params = {
    tuan_id: props.id,
    schedules: list.value.map(v => ({
      name: v.name,
      short_desc: v.short_desc,
      content: v.content,
    }))
  }
  params.schedules = JSON.stringify(params.schedules)
  await productSaveTripNode(params)
  ElMessage.success({
    message: '行程內容已保存',
  })
  emits('close')
}

const swap = (list, a, b) => {
  let tmp = list[a]
  list[a] = list[b]
  list[b] = tmp
}

const doUp = i => swap(list.value, i, i - 1)
const doDown = i => swap(list.value, i, i + 1)

watch(show, v => {
  if (!v) {
    emits('close')
  }
})
onBeforeMount(() => {
  doLoad()
})
</script>

<template>
  <el-drawer v-model="show" title="设置行程" size="80%">
    <el-form ref="formRef" :model="{ item: list }" :rules="rules" label-position="top">
      <el-collapse accordion>

        <template v-for="(node, i) in list" :key="i">
          <el-collapse-item :title="node.name ? node.name : '请输入标题'">
            <el-form-item label="标题" :prop="`item[${i}].name`">
              <el-input placeholder="请输入标题" v-model="node.name" />
            </el-form-item>

            <el-form-item label="简介" :prop="`item[${i}].short_desc`">
              <el-input type="textarea" v-model="node.short_desc" :rows="2" />
            </el-form-item>

            <el-form-item label="內容" :prop="`item[${i}].content`">
              <MyEditor class="editor" v-model="node.content" height="300px" />
            </el-form-item>
            <el-button link v-if="i > 0" @click="doUp(i)">上移</el-button>
            <el-button link v-if="i < list.length - 1" @click="doDown(i)">下移</el-button>
            <el-popconfirm , title="删除此行？" @confirm="doDel(i)">
              <template #reference>
                <el-button link size="small" type="danger">删除行程</el-button>
              </template>
            </el-popconfirm>
          </el-collapse-item>
        </template>

      </el-collapse>
    </el-form>
    <el-divider border-style="none" />
    <el-button @click="doAdd">添加一行</el-button>
    <el-button type="primary" @click="doSave">保存</el-button>
    <el-button @click="emits('close')">取消</el-button>
  </el-drawer>
</template>

<style lang="scss" scoped>
.editor {
  border: 1px solid #999999;
}
</style>