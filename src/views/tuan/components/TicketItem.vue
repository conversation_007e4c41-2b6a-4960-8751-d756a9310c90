<script setup>

import {computed, onMounted, ref, watch} from "vue";
import {ticketCrud, ticketDetail} from "../../../api/modules/product";

const emit = defineEmits(['close'])
const props = defineProps({
  ticketId: {
    type: Number,
    default: 0
  }
})

const show = ref(true)
const rules = {
  title: [
    {required: true, message: '门票名称是必填的！'}
  ],
  max_day: [
    {required: true, message: '最长预订天数是必填的！'}
  ],
  features: {
    validator: (rule, value, callback) => {
      let err = null
      const codes = []

      for (const item of value) {
        if (codes.includes(item.code)) {
          err = new Error('权益不能重复')
          break
        } else if (item.code === '') {
          err = new Error('权益不能为空')
          break
        }
        codes.push(item.code)
      }

      if (err != null) {
        callback(err)
      } else {
        callback()
      }
    }
  }
}
const form = ref({
  id: '',
  state: 2,
  sales_weekday: [],
  title: '',
  times: {},
  max_day: 10,
  start_at: '',
  end_at: '',
  features: []
})
const pickerOptions = ref({
  selectableRange: '09:00:00 - 21:00:00'
})
const canEdit = ref(true)
const weekdays = [{
  value: 1,
  label: '周一'
},
  {
    value: 2,
    label: '周二'
  },
  {
    value: 3,
    label: '周三'
  },
  {
    value: 4,
    label: '周四'
  },
  {
    value: 5,
    label: '周五'
  },
  {
    value: 6,
    label: '周六'
  },
  {
    value: 0,
    label: '周日'
  }]

const isNew = computed(() => props.ticketId === 0)
const title = computed(() => isNew.value ? '新增门票' : '编辑门票')
const flowcard = ref(null)

watch(show, () => emit('close'))
watch(() => form.value.sales_weekday, (newValue) => {
  for (const weekday of newValue) {
    if (!this.form.times[weekday]) {
      this.$set(this.form.times, weekday, [])
    }
  }
})

function onAddFeature() {
  form.value.features.push({code: ''})
}

function onDeleteTime(weekday, index) {
  form.value.times[weekday].splice(index, 1)
}

function onAddTime(weekday) {
  form.value.times[weekday].push({
    time: ['2023-05-10 09:00:00', '2023-05-10 20:00:00'],
    price: '',
    stock: '',
    film: '',
    weekday
  })
}

function selectWeekday(i) {
  const v = parseInt(i)

  for (const item of this.weekdays) {
    if (item.value === v) {
      return item
    }
  }

  return null
}

function getDetail() {
  ticketDetail({id: props.ticketId}).then(res => {
    const {data} = res

    this.form.title = data.title
    this.form.state = data.state
    this.form.id = data.id
    this.form.max_day = data.max_day
    this.form.features = data.features.map((code) => {
      return {code}
    })

    if (data.start_at > 0) {
      this.form.start_at = new Date(data.start_at * 1000)
    }
    if (data.end_at > 0) {
      this.form.end_at = new Date(data.end_at * 1000)
    }

    if (data.state === 1) {
      this.$notify.warning('在线状态的门票，只能查看，不能编辑')
      this.canEdit = false
    }

    const times = {}
    for (const item of data.times) {
      if (!times[item.weekday]) {
        times[item.weekday] = []
      }

      times[item.weekday].push({
        time: ['2023-05-10 ' + item.start, '2023-05-10 ' + item.end],
        price: item.price / 100,
        stock: item.stock,
        weekday: item.weekday,
        film: item.film
      })
    }

    this.$set(this.form, 'times', times)

    this.form.sales_weekday = data.sales_weekday.split(',').map((item) => {
      return parseInt(item)
    })
  })
}

function onSave() {
  flowcard.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {...form.value}

    if (form.value.start_at) {
      data.start_at = Math.ceil(this.form.start_at.getTime() / 1000)
    }
    if (this.form.end_at) {
      data.end_at = Math.ceil(this.form.end_at.getTime() / 1000)
    }
    data.features = this.form.features.map((item) => {
      return item.code
    })
    data.features = JSON.stringify(data.features)

    const times = []

    for (const weekday in data.times) {
      const more = this.form.times[weekday].map(function (time) {
        const item = {
          start: new Date(time.time[0]),
          end: new Date(time.time[1]),
          price: parseFloat(time.price) * 100,
          stock: parseInt(time.stock),
          weekday: parseInt(weekday),
          film: time.film
        }

        item.start = ('' + item.start.getHours()).padStart(2, '0') +
            ':' + ('' + item.start.getMinutes()).padStart(2, '0') +
            ':' + ('' + item.start.getSeconds()).padStart(2, '0')

        item.end = ('' + item.end.getHours()).padStart(2, '0') +
            ':' + ('' + item.end.getMinutes()).padStart(2, '0') +
            ':' + ('' + item.end.getSeconds()).padStart(2, '0')

        return item
      })

      times.push(...more)
    }

    data.times = JSON.stringify(times)
    data.sales_weekday = this.form.sales_weekday.join(',')

    ticketCrud(data).then(res => {
      this.$notify.success('操作成功')

      this.$emit('close')
    })
  })
}

onMounted(() => {
  if (!isNew.value) {
    getDetail()
  }
})

</script>

<template>
  <el-drawer v-model="show" size="90%" :title="title">
    <div class="app-container">
      <el-form ref="flowcard" :model="form" :rules="rules" label-position="top">
        <el-input v-model="form.id" type="hidden"/>
        <el-form-item label="门票名称" prop="title">
          <el-input v-model="form.title"/>
        </el-form-item>
        <el-form-item label="最长预订天数" prop="max_day">
          <el-input v-model="form.max_day" type="number"/>
        </el-form-item>
        <el-form-item label="起售日期" prop="start_at">
          <el-date-picker v-model="form.start_at"/>
        </el-form-item>
        <el-form-item label="截止日期" prop="end_at">
          <el-date-picker v-model="form.end_at"/>
        </el-form-item>
        <el-form-item label="可用权益" prop="features">
          <el-button type="primary" size="mini" @click="onAddFeature">添加权益</el-button>
          <div class="features">
            <div v-for="(item,index) in form.features" :key="index">
              <MyFeatureSelector v-model="item.code"/>
              <el-button icon="el-icon-delete" circle @click="form.features.splice(index, 1)"/>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="可售卖日期">
          <el-checkbox-group v-model="form.sales_weekday">
            <el-checkbox v-for="item in weekdays" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="时间段价格库存">
          <div v-for="weekday in form.sales_weekday" :key="weekday" class="times">
            <div class="weekday-title" style="flex: 1;">
              {{ selectWeekday(weekday).label }}
            </div>
            <div style="flex: 9;">
              <el-button size="mini" type="primary" style="margin-bottom: 20px;" @click="onAddTime(weekday)">添加时间段
              </el-button>
              <div
                  v-for="(time, key) in form.times[weekday]"
                  :key="key"
                  style="display: flex;gap: 20px;margin-bottom: 10px;"
              >
                <el-time-picker
                    v-model="time.time"
                    :clearable="false"
                    :picker-options="pickerOptions"
                    is-range
                    style="flex: 2;"
                />
                <MyFilmSelector v-model="time.film" style="flex:1"/>
                <el-input v-model="time.price" placeholder="价格" style="flex: 1;"/>
                <el-input v-model="time.stock" placeholder="库存" style="flex: 1;"/>
                <el-button icon="el-icon-delete" circle @click="onDeleteTime(weekday, key)"/>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button :disabled="!canEdit" type="primary" @click="onSave">确定</el-button>
          <el-button @click="show = false">取消</el-button>
        </el-form-item>
      </el-form>
    </div>

  </el-drawer>
</template>

<style scoped lang="scss">

</style>