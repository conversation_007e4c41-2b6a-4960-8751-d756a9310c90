<script setup>

import {ref, watch} from "vue";
import {ZoneAreas} from "../../../utils/constmap";

const emit = defineEmits(['close'])
const show = ref(true)

const form = ref({
  zone: '',
  amount: '',
})

function handleOk() {
  if (form.value.zone === '') {
    show.value = false
    return
  }
  const zone = ZoneAreas.find((value) => value.value === form.value.zone)
  if (!zone) {
    show.value = false
    return;
  }
  const data = {...zone}
  data.amount = Number.parseFloat(form.value.amount) * 100
  if (Number.isNaN(data)) {
    data.amount = 0
  }

  emit('close', data)
}

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" append-to-body title="添加运费">
    <el-form :model="form">
      <el-form-item label="地区" prop="zone">
        <el-select v-model="form.zone">
          <el-option v-for="item in ZoneAreas" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="金额">
        <el-input v-model="form.amount"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>

  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
