<template>
  <el-dialog v-model="show" append-to-body title="积分设置" width="80%">
    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="等级" prop="vip_id">
      </el-form-item>
      <el-form-item label="直推积分">
        <el-space>
          <el-input v-model="form.direct_commission" placeholder="0-100">
            <template #prepend>当月</template>
            <template #suffix>%</template>
          </el-input>
          <el-input v-model="form.direct_commission_1to3" placeholder="0-100">
            <template #prepend>1到3个月</template>
            <template #suffix>%</template>
          </el-input>
          <el-input v-model="form.direct_commission_next" placeholder="0-100">
            <template #prepend>3个月以上</template>
            <template #suffix>%</template>
          </el-input>
        </el-space>
      </el-form-item>
      <el-form-item label="级差积分">
        <el-space>
          <el-input v-model="form.indirect_commission" placeholder="0-100">
            <template #prepend>当月</template>
            <template #suffix>%</template>
          </el-input>
          <el-input v-model="form.indirect_commission_1to3" placeholder="0-100">
            <template #prepend>1到3个月</template>
            <template #suffix>%</template>
          </el-input>
          <el-input v-model="form.indirect_commission_next" placeholder="0-100">
            <template #prepend>3个月以上</template>
            <template #suffix>%</template>
          </el-input>
        </el-space>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>

  </el-dialog>
</template>

<script setup>
import {ref, watch} from "vue";

const emit = defineEmits(['close'])
const show = ref(true)
watch(show, () => emit('close'))

function handleOk() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

  })
}

const formRef = ref(null)
const form = ref({
  vip_id: '',
  direct_commission: '',
  direct_commission_1to3: '',
  direct_commission_next: '',
  indirect_commission: '',
  indirect_commission_1to3: '',
  indirect_commission_next: '',
})
const rules = ref({
  vip_id: [
    {required: true, message: '请选择关联的等级！'}
  ]
})

</script>
