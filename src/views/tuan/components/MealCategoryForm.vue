<script setup>

import {onMounted, ref, watch} from "vue";
import MyUpload from "../../../components/MyUpload.vue";
import {categoryDetail, mealSaveCategory} from "../../../api/modules/meal";
import MyProductPics from "../../../components/MyProductPics.vue";
import {ElNotification} from "element-plus";

const emit = defineEmits(['close'])
const show = ref(true)

const props = defineProps({
  id: {
    type: [String, Number],
    default: ''
  }
})

const formRef = ref(null)
const form = ref({
  name: '',
  pic: '',
  pic_res_id: '',
  id: '',
})
const formRules = {
  name: [{required: true, message: '请输入分组名称'}]
}

function onUpload(f) {
  console.log(f.main)
  if (f.main.length > 0) {
    form.value.pic_res_id = f.main[0]
  }
}

function onSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {...form.value}
    mealSaveCategory(data).then(() => {
      ElNotification.success('保存成功')
      show.value = false
    })
  })
}

onMounted(() => {
  categoryDetail(props.id).then(res => {
    const {data} = res

    Object.assign(form.value, data)
  })
})

watch(show, () => emit('close'))

</script>

<template>
<el-dialog append-to-body v-model="show" title="编辑套餐分组">
  <el-form ref="formRef" :rules="formRules" label-position="top" :model="form">
    <el-form-item label="名称" prop="name">
      <el-input v-model="form.name"/>
    </el-form-item>
    <el-form-item label="图片">
      <MyProductPics @change="onUpload" :has-other-pic="false" :main-pic="form.pic" has-main/>
    </el-form-item>
  </el-form>
  <template #footer>
    <el-button @click="show = false">取消</el-button>
    <el-button @click="onSubmit" type="primary">确定</el-button>
  </template>
</el-dialog>
</template>

<style scoped lang="scss">

</style>