<template>
  <el-dialog v-model="show" title="设置团期">
    <el-alert title="注意：数据将实时保存！" type="warning"/>
    <my-calendar :value="value" v-model="list" @month-change="onMonthChange"/>
    <template #footer>
      <el-button @click="show = false">关闭</el-button>
    </template>

  </el-dialog>
</template>

<script setup>

import {onMounted, ref, watch} from "vue";
import dayjs from "dayjs";
import {productPrices, productSaveDate} from "../../api/modules/product";
import MyCalendar from "../../components/MyCalendar/MyCalendar.vue";

const props = defineProps(['id'])
const emit = defineEmits(['close'])

const show = ref(true)
const list = ref([])
const value = new Date()
let month = ''

function onMonthChange(date) {
  save(month)

  getMonthPrice(date)
}

function save(month) {
  const data = {
    month,
    id: props.id,
    list: JSON.stringify(list.value.map(item => dayjs(item).unix()))
  }
  return productSaveDate(data)
}

function getMonthPrice(d) {
  month = dayjs(d).format('YYYY-MM')

  productPrices(props.id, month).then(res => {
    const {data} = res

    list.value = data.list.map(item => {
      return dayjs.unix(item).toDate()
    })
  })
}

onMounted(() => {
  getMonthPrice(value)
})

watch(show, () => {
  save(month).then(() => {
    emit('close')
  })
})

</script>

<style lang="scss" scoped>
.shorts {
  display: flex;
  gap: 10px;

  .el-form-item {
    flex: 1;
  }
}
</style>