<template>
  <el-drawer v-model="show" :close-on-click-modal="false" :title="title" append-to-body size="80%">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      <el-form-item label="简短描述" prop="short_desc">
        <el-input v-model="form.short_desc" type="textarea"/>
      </el-form-item>
      <el-space>
        <el-form-item label="地区" prop="zone_id">
          <MyZoneSelector v-model="form.zone_id"/>
        </el-form-item>
        <el-form-item label="类型" prop="cate">
          <div class="node-area">
            <div class="node">
              <MyCategorySelector v-model="form.cate" multiple/>
            </div>
          </div>
        </el-form-item>
      </el-space>
      <div>
        <el-space>
          <el-form-item label="行程天数" prop="days">
            <el-input v-model="form.days" style="width: 100px;"/>
          </el-form-item>
          <el-form-item label="关联任务ID">
            <el-input v-model="form.task_id"/>
          </el-form-item>
        </el-space>
      </div>

      <el-space>
        <el-form-item label="报名年龄" prop="age_limit">
          <el-input v-model="form.age_limit"/>
        </el-form-item>
        <el-form-item label="是否需要出行人信息">
          <el-radio-group v-model="form.need_people">
            <el-radio-button :value="Disable" label="否"/>
            <el-radio-button :value="Enable" label="是"/>
          </el-radio-group>
        </el-form-item>
      </el-space>

      <el-form-item label="团队人数" prop="member_limit">
        <el-input v-model="form.member_limit"/>
      </el-form-item>
      <el-form-item label="集散/解散" prop="base">
        <el-input v-model="form.base"/>
      </el-form-item>
      <el-form-item label="微定制" prop="customization">
        <el-input v-model="form.customization"/>
      </el-form-item>
      <el-form-item>
        <template #label>
          规格
          <el-button size="small" type="warning" @click="showSkuDetail = true">添加</el-button>
        </template>
        <el-space class="skus" wrap>
          <div v-for="(item, index) in skus" :key="index" class="sku">
            <div class="sku-name">
              {{ item.name }}
              <el-tag v-if="item.is_require === 1" type="danger">必选</el-tag>
            </div>
            <div>
              <div v-if="item.adult_num > 0">成人：{{ item.adult_num }}</div>
              <div v-if="item.children_num > 0">儿童：{{ item.children_num }}</div>
            </div>
            <div class="actions">
              <el-button size="small" type="danger" @click="() => {
                skus.splice(index, 1)
              }">删除
              </el-button>
              <el-button size="small" type="primary" @click="handleEditSku(index)">修改</el-button>
            </div>
          </div>
        </el-space>
      </el-form-item>
      <el-tabs v-model="curTab">
        <el-tab-pane label="图文信息" name="desc">
          <el-form-item label="商品图片" prop="main_pic_res_id">
            <MyProductPics :main-pic="form.main_pic" :pics="pics" @change="handlePicChange"/>
          </el-form-item>
          <el-form-item label="商品详情" prop="desc">
            <MyEditor v-model="form.desc" height="300px"/>
          </el-form-item>
        </el-tab-pane>
        <el-tab-pane label="退款政策" name="refund_rule">

          <el-form-item prop="can_refund">
            <el-space>
              <el-radio-group v-model.number="form.can_refund">
                <el-radio-button :value="Enable">支持退款</el-radio-button>
                <el-radio-button :value="Disable">不支持退款</el-radio-button>
              </el-radio-group>
              <el-button v-if="form.can_refund === Enable" :icon="Plus" circle type="warning"
                         @click="onAddRefundRule"/>
            </el-space>
          </el-form-item>
          <el-form-item v-if="form.can_refund === Enable" prop="refund_rules" style="margin-top: 10px">
            <el-space direction="vertical" size="large">
              <div v-for="(item, index) in form.refund_rules" :key="index" class="refund-rule-item">
                <el-select v-model="item.day" placeholder="选择出发前日期">
                  <el-option v-for="(day, i) in refundDays" :key="i" :label="refundRuleDayLabel(day)" :value="day"/>
                </el-select>
                <el-input v-model="item.percent" :disabled="item.can_refund !== Enable"
                          placeholder="请输入退款比例（0-1）"/>
                <el-checkbox v-model="item.can_refund" :checked="item.can_refund === Enable" :false-value="Disable"
                             :true-value="Enable"
                             label="支持退款"/>
              </div>
            </el-space>
          </el-form-item>
        </el-tab-pane>
      </el-tabs>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSave">确定
      </el-button>
    </template>
  </el-drawer>

  <TuanSku v-if="showSkuDetail" :id="skuId" @close="handleAddSku"/>

</template>

<script setup>

import {computed, onMounted, ref, watch} from "vue";
import MyEditor from "../../components/MyEditor.vue";
import MyProductPics from "../../components/MyProductPics.vue";
import MyCategorySelector from "../../components/MyCategorySelector.vue";
import {productDetail, productSave} from "../../api/modules/product";
import {ElNotification} from "element-plus";
import {useUserStore} from "../../store/modules/users";
import TuanSku from "./components/TuanSku.vue";
import {deepClone, deepToRaw, formatElErrorMsg} from "../../utils";
import MyZoneSelector from "../../components/MyZoneSelector.vue";
import {Disable, Enable} from "../../utils/constmap";
import {Plus} from "@element-plus/icons-vue";

const props = defineProps(['id'])
const emit = defineEmits(['close'])
const userStore = useUserStore()

const isEdit = computed(() => props.id > 0)
const title = computed(() => isEdit.value ? '编辑商品' : '新增商品')
const show = ref(true)
const form = ref({
  name: '',
  children_price: '',
  price: '',
  main_pic: '',
  main_pic_res_id: '',
  desc: '',
  id: '',
  short_desc: '',
  days: 5,
  zone_id: '',
  cate: [],
  member_limit: '',
  age_limit: '',
  base: '',
  customization: '',
  can_refund: 0,
  refund_rules: [],
  need_people: Enable,
  task_id: '',
})
const now = new Date()
const pics = ref([])
const pics_res_ids = ref([])
const delete_pics = ref([])
const formRef = ref(null)
const rules = computed(() => {
  const r = {
    name: [
      {required: true, message: '商品名称是必填的！'},
    ],
    desc: [{required: true, message: '商品详情是必填的！'}],
    days: [{required: true, message: '行程天数是必填的！'}],
    zone_id: [{required: true, message: '地区是必填的！'}],
    can_refund: [
      {min: 1, type: 'number', message: '退款政策是必选的！'}
    ],
  }
  if (!isEdit.value) {
    r.main_pic_res_id = [{required: true, message: '商品主图是必传的'}]
  }

  if (form.value.can_refund === Enable) {
    r.refund_rules = [
      {
        validator: (rule, value, callback) => {
          if (value.length === 0) {
            callback()
          } else {
            for (let i = 0; i < value.length; i++) {
              const percent = Number.parseFloat(value[i].percent)
              if (isNaN(percent) || percent <= 0 || percent > 1) {
                callback(new Error('请填写正确的退款比例！'))
                return
              } else {
                callback()
              }
            }
          }
        },
        message: '请填写正确的退款比例！'
      }
    ]
  }

  return r
})
const curTab = ref('desc')
const nodes = ref([])
const skus = ref([])
const skuId = ref(0)
const showSkuDetail = ref(false)
const refundDays = [5, 3, 2, 1, 0]

function handleAddSku(s) {
  if (s) {
    let isAdd = s.id === 0
    if (isAdd) {
      skus.value.push(s)
    } else {
      const index = skus.value.findIndex(item => item.id === s.id)
      if (index > -1) {
        skus.value[index] = s
      }
    }
  }

  skuId.value = 0
  showSkuDetail.value = false
}

function refundRuleDayLabel(day) {
  return day === 0 ? '当天' : `出发前${day}天`
}

function onAddRefundRule() {
  form.value.refund_rules.push({
    day: '',
    percent: '',
    can_refund: Enable,
  })
}

function handleEditSku(index) {
  const s = skus.value[index]
  if (!s) {
    return
  }
  skuId.value = s.id
  showSkuDetail.value = true
}

function handleSave() {
  formRef.value.validate((valid, errors) => {
    if (!valid) {
      ElNotification.error(formatElErrorMsg(errors))
      return
    } else if (skus.value.length === 0) {
      ElNotification.error('请添加规格！')
      return
    }

    const data = deepToRaw(form.value)
    // const data = {...form.value}

    data.cate = data.cate.join(',')
    data.pics = pics_res_ids.value.join(',')
    data.delete_pics = JSON.stringify(delete_pics.value)
    data.trip_node = JSON.stringify(nodes.value.map(item => item.value))
    data.skus = deepClone(skus.value).map(item => {
      item.price *= 100
      item.settlement_price *= 100
      return item
    })
    data.skus = JSON.stringify(data.skus)
    if (form.value.zone_id instanceof Array) {
      const zoneId = deepClone(form.value.zone_id)
      data.zone_id = zoneId[zoneId.length - 1]
    }
    data.refund_rules.forEach(item => {
      item.percent = Number.parseFloat(item.percent)
    })

    data.refund_rules = JSON.stringify(data.refund_rules)

    productSave(data).then(() => {
      ElNotification.success('操作成功')
      show.value = false
    })
  })
}

function handlePicChange(val) {
  form.value.main_pic_res_id = val.main.length > 0 ? val.main[0] : ''
  delete_pics.value = val.remove
  pics_res_ids.value = val.pics
}

onMounted(() => {
  if (isEdit.value) {
    productDetail(props.id).then(res => {
      const {data} = res
      pics.value = data.pics
      nodes.value = data.nodes.map(item => {
        return {value: item}
      })
      skus.value = data.skus.map(item => {
        item.price /= 100
        item.settlement_price /= 100
        return item
      })

      Object.assign(form.value, data)
    })
  }
})

watch(show, () => emit('close'))

</script>

<style lang="scss" scoped>
.node-area {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .node {
    display: flex;
    gap: 10px;
    width: 30%;

    .btns {
      display: flex;
    }
  }
}

.refund-rule-item {
  display: flex;
  gap: 10px;
}

.skus {
  .sku {
    border: 1px solid;
    padding: 10px;

    .actions {
      display: flex;
      justify-content: center;
    }
  }
}
</style>
