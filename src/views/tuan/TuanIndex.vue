<template>
  <el-card>
    <el-form inline>
      <el-form-item label="地区">
        <MyZoneSelector v-model="search.zone_id" clearable multiple/>
      </el-form-item>
      <el-form-item label="分类">
        <MyCategorySelector v-model="search.cate" multiple/>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="search.state" clearable>
          <el-option v-for="(item, index) in ProductStateMaps" :key="index" :label="item" :value="index"/>
        </el-select>
      </el-form-item>
      <el-form-item label="名称">
        <el-input v-model="search.keyword" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="() => {
          page = 1
          getList()
        }">搜索
        </el-button>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="() => {
      id = 0; showDetail = true
    }">新增团游
    </el-button>

    <el-table :data="list">
      <el-table-column fixed label="ID" prop="id"/>
      <el-table-column fixed label="商品名称" prop="name">
      </el-table-column>
      <el-table-column label="图片" width="130">
        <template #default="{ row }">
          <el-image :src="row.pic" fit="contain"/>
        </template>
      </el-table-column>
      <el-table-column label="地区" prop="zone_name"/>
      <el-table-column label="规格" width="300px">
        <template #default="{ row }">
          <el-space direction="vertical" wrap>
            <div v-for="(item, index) in row.skus" :key="index">
              {{ item.name }}
            </div>
          </el-space>
        </template>
      </el-table-column>
      <el-table-column label="分类">
        <template #default="{ row }">
          <el-space wrap>
            <el-tag v-for="(item, index) in row.cate" :key="index">{{ item }}</el-tag>
          </el-space>

        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag v-if="row.state === 1">{{ ProductStateMaps[row.state] }}</el-tag>
          <el-tag v-else type="danger">{{ ProductStateMaps[row.state] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :formatter="tableFormatTime" label="创建时间" prop="created_at"/>
      <el-table-column fixed="right" label="操作">
        <template #default="{ row }">
          <el-space wrap>
            <el-button size="small" type="primary" @click="() => {
              id = row.id; showDetail = true
            }">编辑
            </el-button>

            <el-button size="small" @click="() => {
              id = row.id
              showProductDate = true
            }">设置团期
            </el-button>

            <el-button size="small" type="primary" @click="() => {
              id = row.id
              showScheduling = true
            }">设置行程
            </el-button>
            <el-button v-if="row.has_scene_sign" size="small" type="danger" @click="() => onShowSignTasks(row)">
              查看打卡二维码
            </el-button>

            <el-button size="small" type="warning" @click="handleCopy(row)">复制链接</el-button>

            <el-button v-if="row.state === 2" size="small" type="success" @click="handleEnable(row.id, 1)">上线
            </el-button>
            <el-popconfirm v-else title="您确定要将商品下线掉吗？" @confirm="handleEnable(row.id, 2)">
              <template #reference>
                <el-button size="small" type="danger">下线</el-button>
              </template>
            </el-popconfirm>
          </el-space>

        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="handlePage"/>
    <TuanDetail v-if="showDetail" :id="id" @close="handleDetailClose"/>
    <TuanDateSku v-if="showProductDate" :id="id" @close="() => {
      showProductDate = false
    }"/>
    <TuanScheduling v-if="showScheduling" :id="id" @close="() => {
      showScheduling = false
    }"/>

    <el-dialog v-if="showSceneSignQrcode" v-model="showSceneSignQrcode" title="打卡二维码"
               @close="showSceneSignQrcode = false">
      <el-space wrap>
        <div v-for="(item, index) in qrcode" :key="index" class="qrcode-item">
          <QrcodeVue :value="item.text" :width="200"/>
          <div>{{ item.scene_name }}</div>
        </div>
      </el-space>

    </el-dialog>

  </el-card>
</template>

<script setup>
import {onActivated, ref} from "vue";
import MyPagination from "../../components/MyPagination.vue";
import TuanDetail from "./TuanDetail.vue";
import {productEnable, products} from "../../api/modules/product";
import {deepClone, tableFormatTime} from "../../utils";
import {ProductStateMaps} from "../../utils/constmap";
import {ElNotification} from "element-plus";
import {useUserStore} from "../../store/modules/users";
import {useRoute} from "vue-router";
import MyZoneSelector from "../../components/MyZoneSelector.vue";
import MyCategorySelector from "../../components/MyCategorySelector.vue";
import TuanDateSku from "./components/TuanDateSku.vue";
import TuanScheduling from "./components/TuanScheduling.vue";
import {signTasks} from "../../api/modules/task";
import QrcodeVue from 'vue-qrcode'

const list = ref([])
const total = ref(0)
const showDetail = ref(false)
const id = ref(0)
let page = 1
const showProductDate = ref(false)
const userStore = useUserStore()
const route = useRoute()
const showScheduling = ref(false)
const showSceneSignQrcode = ref(false)
const qrcode = ref([])
const search = ref({
  keyword: '',
  zone_id: '',
  state: '',
  cate: '',
})

function handlePage(p) {
  page = p
  getList()
}

function handleDetailClose() {
  showDetail.value = false
  getList()
}

function handleCopy(row) {
  let content = `/pages/tuan/detail?id=${row.id}`
  navigator.clipboard.writeText(content).then(() => {
    ElNotification.success('复制成功')
  })
}

function onShowSignTasks(item) {
  signTasks({parent_task_id: item.task_id}).then(({data: {tasks}}) => {
    qrcode.value = tasks.map(task => {
      task.text = `http://h5.funfuntrip.cn/#/shj?tuan_id=${item.id}&scene_id=${task.scene_id}`

      return task
    })
    showSceneSignQrcode.value = true
  })
}

function handleEnable(id, state) {
  productEnable(id, state).then(() => {
    ElNotification.success('状态修改成功')
    getList()
  })
}

function getList() {
  const params = {page}
  Object.assign(params, deepClone(search.value))
  if (params.zone_id instanceof Array) {
    params.zone_id = params.zone_id.map(item => item[1]).join(',')
  }
  if (params.cate instanceof Array) {
    params.cate = params.cate.join(',')
  }

  products(params).then(res => {
    const {data} = res
    list.value = data.list
    total.value = data.total
  })
}

onActivated(() => {
  if (route.query.keyword) {
    search.value.keyword = route.query.keyword
  }

  getList()
})

</script>

<style lang="scss" scoped>
.qrcode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
