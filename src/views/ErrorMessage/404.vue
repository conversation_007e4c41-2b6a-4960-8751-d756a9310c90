<template>
  <div class="col-center">
    <svg
        fill="none"
        height="300px"
        width="400px"
        xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="33.329" cy="107.131" fill="#4060c7" opacity=".3" r="5.356"/>
      <path
          d="M373.107 238.514h-13.863c-1.471-7.141-2.584-21.424 4.726-21.424 7.309 0 9.137 14.283 9.137 21.424Z"
          fill="#E2E5EC"
      />
      <path
          d="M364.151 221.816c.84 1.785 2.52 7.499 2.52 16.068"
          stroke="#FFFEFE"
          stroke-linecap="round"
          stroke-width="1.26"
      />
      <path
          d="M355.958 238.514H341.78c1.786-4.096 6.365-12.162 10.398-11.657 4.032.504 4.2 7.981 3.78 11.657Z"
          fill="#E2E5EC"
      />
      <path
          d="M349.657 232.843c-.525 1.576-1.575 5.041-1.575 6.302"
          stroke="#FFFEFE"
          stroke-linecap="round"
          stroke-width="1.26"
      />
      <path
          d="M34.84 201.221c-40.337-44.164 20.284-44.435 23.447-67.198 7.684-55.319 57.32-93.027 111.792-79.099 73.24 18.719 67.033 76.611 129.865 64.354 80.714-15.706 114.15 37.333 51.403 94.438-32.732 29.792 19.258 25.667 19.258 25.667l-294.482 1.022c-34.78-30.41 7.822-1.098-17.836-10.077-32.063-11.201 3.097-.046-23.447-29.107Z"
          fill="url(#a)"
          opacity=".6"
      />
      <path
          d="M22.301 239.144h350.356M376.932 239.144h5.986"
          stroke="#011650"
          stroke-linecap="round"
          stroke-width="1.26"
      />
      <path
          d="M50.177 238.514h-25.34C18.878 228.2 9.95 207.011 21.901 204.77c14.938-2.8 18.806 16.005 18.806 18.406 7.682-2.454 9.514 9.203 9.47 15.338Z"
          fill="#E2E5EC"
      />
      <path
          d="M25.966 212.639c1.823 2.934 6.037 11.17 10.198 24.615M43.572 227.177l-3.334 6.936"
          stroke="#fff"
          stroke-linecap="round"
          stroke-width="1.26"
      />
      <path
          d="M128.521 238.514h-15.157v-13.863h-45.49c-4.55 0-7.6-4.673-5.768-8.838l50.548-114.947a10.083 10.083 0 0 1 9.229-6.023h.336a6.301 6.301 0 0 1 6.302 6.301v109.3h25.425a7.103 7.103 0 0 1 7.103 7.103 7.103 7.103 0 0 1-7.103 7.104h-25.425v13.863Zm-15.157-109.101-34.98 81.031h34.98v-81.031ZM160.419 167.918c0-18.586 3.76-34.942 11.279-49.067 7.637-14.126 16.39-21.189 26.26-21.189 9.869 0 18.622 7.063 26.259 21.189 7.636 14.125 11.455 30.543 11.455 49.253s-3.819 35.128-11.455 49.254c-7.637 14.125-16.39 21.188-26.259 21.188-9.87 0-18.623-7.063-26.26-21.188-7.519-14.126-11.279-30.606-11.279-49.44Zm16.214-1.074c0 17.223 2.232 32.051 6.697 41.964 4.465 9.789 9.34 14.683 14.628 14.683 5.287 0 10.162-4.894 14.627-14.683 4.582-9.913 6.873-23.543 6.873-40.89 0-17.719-2.291-31.348-6.873-40.889-4.465-9.541-9.34-14.312-14.627-14.312-5.288 0-10.163 4.771-14.628 14.312s-6.697 21.972-6.697 39.815ZM308.986 238.514H293.83v-13.863h-45.9c-4.557 0-7.607-4.688-5.761-8.854L293.11 100.84a10.083 10.083 0 0 1 9.218-5.997h.357a6.301 6.301 0 0 1 6.301 6.301v109.3h22.856a7.103 7.103 0 0 1 7.103 7.103 7.103 7.103 0 0 1-7.103 7.104h-22.856v13.863ZM293.83 129.413l-35.227 81.031h35.227v-81.031Z"
          fill="#CDDAFE"
      />
      <mask
          id="b"
          height="145"
          maskUnits="userSpaceOnUse"
          style="mask-type: alpha"
          width="278"
          x="61"
          y="94"
      >
        <path
            d="M128.521 238.514h-15.156v-13.863h-45.49c-4.55 0-7.6-4.673-5.769-8.838l53.198-120.97h13.217v115.601h25.425a7.103 7.103 0 1 1 0 14.207h-25.425v13.863Zm-15.156-109.101-34.98 81.031h34.98v-81.031ZM160.42 167.918c0-18.586 3.76-34.942 11.279-49.067 7.637-14.126 16.39-21.189 26.259-21.189s18.622 7.063 26.259 21.189c7.637 14.125 11.455 30.543 11.455 49.253s-3.818 35.128-11.455 49.254c-7.637 14.125-16.39 21.188-26.259 21.188s-18.622-7.063-26.259-21.188c-7.519-14.126-11.279-30.606-11.279-49.44Zm16.214.186c0 17.223 2.232 30.791 6.696 40.704 4.465 9.789 9.341 14.683 14.628 14.683 5.287 0 10.163-4.894 14.628-14.683 4.582-9.913 6.873-23.543 6.873-40.89 0-17.719-2.291-31.348-6.873-40.889-4.465-9.541-9.341-14.312-14.628-14.312-5.287 0-10.163 4.771-14.628 14.312-4.464 9.541-6.696 23.232-6.696 41.075ZM308.986 238.514H293.83v-13.863h-45.9c-4.557 0-7.607-4.688-5.761-8.854l53.6-120.954h13.217v115.601h22.856a7.104 7.104 0 1 1 0 14.207h-22.856v13.863ZM293.83 129.413l-35.227 81.031h35.227v-81.031Z"
            fill="#CDDAFE"
        />
      </mask>
      <g mask="url(#b)">
        <ellipse
            cx="198.741"
            cy="177.076"
            fill="url(#c)"
            opacity=".8"
            rx="44.11"
            ry="63.329"
            transform="rotate(-180 198.741 177.076)"
        />
        <ellipse
            cx="197.48"
            cy="201.651"
            fill="url(#d)"
            rx="44.11"
            ry="57.027"
        />
        <ellipse
            cx="54.998"
            cy="195.995"
            fill="url(#e)"
            opacity=".6"
            rx="44.11"
            ry="58.603"
            transform="rotate(22.14 54.998 195.995)"
        />
        <ellipse
            cx="247.196"
            cy="226.466"
            fill="url(#f)"
            opacity=".6"
            rx="19.626"
            ry="58.603"
            transform="rotate(22.14 247.196 226.466)"
        />
        <path
            d="m71.138 224.966 7.561-14.808h34.658v14.808h-42.22Z"
            fill="#8BD9FC"
        />
        <mask
            id="g"
            height="15"
            maskUnits="userSpaceOnUse"
            style="mask-type: alpha"
            width="43"
            x="71"
            y="210"
        >
          <path
              d="m71.138 224.966 7.561-14.808h34.658v14.808h-42.22Z"
              fill="#8BD9FC"
          />
        </mask>
        <g mask="url(#g)">
          <ellipse
              fill="url(#h)"
              opacity=".6"
              rx="20.794"
              ry="31.822"
              style="mix-blend-mode: multiply"
              transform="matrix(-1 0 0 1 74.603 213.624)"
          />
        </g>
        <path
            d="m251.042 225.281 7.715-15.123h35.134v15.123h-42.849Z"
            fill="#8BD9FC"
        />
        <mask
            id="i"
            height="16"
            maskUnits="userSpaceOnUse"
            style="mask-type: alpha"
            width="43"
            x="251"
            y="210"
        >
          <path
              d="m251.042 225.281 7.715-15.123h35.134v15.123h-42.849Z"
              fill="#8BD9FC"
          />
        </mask>
        <g mask="url(#i)">
          <ellipse
              fill="url(#j)"
              opacity=".6"
              rx="20.794"
              ry="31.822"
              style="mix-blend-mode: multiply"
              transform="matrix(-1 0 0 1 255.453 213.624)"
          />
        </g>
        <path
            d="M128.48 210.158h25.521a7.248 7.248 0 0 1 0 14.493H128.48v-14.493ZM309.014 210.158h23a7.248 7.248 0 0 1 0 14.493h-23v-14.493Z"
            fill="#8BD9FC"
        />
        <path
            d="M128.48 224.651v-14.178l6.932 14.178h-6.932ZM309.014 224.651v-14.178l6.932 14.178h-6.932Z"
            fill="#6DBADD"
        />
        <path
            d="m113.042 129.815 15.438-29.931v29.931h-15.438Z"
            fill="url(#k)"
        />
        <path
            d="m293.891 129.815 15.123-29.616.315 29.616h-15.438Z"
            fill="url(#l)"
        />
        <path
            d="M114.016 94.493c.371 4.57-2.625 17.776-11.171 34.503-10.683 20.91-39.795 91.472-41.474 97.545-1.343 4.859 2.267 4.876 3.78 0 .997-3.211 25.43-63.343 35.139-77.657 12.137-17.893 13.825-42.923 19.528-54.095 5.703-11.172 7.848-4.138 9.239-2.677 1.266 1.33 1.996-21.022-1.682-9.29-4.958 8.871-18.931 70.821-28.511 81.651-7.247 8.191-23.031 41.427-19.85 45.685M295.753 91.063c.371 4.57-2.453 19.504-10.999 36.232-10.683 20.91-39.967 89.743-41.646 95.817-1.343 4.859 2.268 4.876 3.781 0 .996-3.211 25.429-63.343 35.138-77.657 12.137-17.893 13.825-42.923 19.528-54.095 5.703-11.172 7.848-4.138 9.239-2.677 1.266 1.33 1.997-21.022-1.682-9.29-4.958 8.871-19.819 71.415-29.399 82.244-7.247 8.192-31.223 60.647-28.041 64.905"
            stroke="#fff"
            stroke-width=".441"
        />
        <circle
            cx="208.192"
            cy="118.788"
            fill="#758CD6"
            opacity=".3"
            r="4.411"
        />
        <circle
            cx="214.179"
            cy="130.446"
            fill="#758CD6"
            opacity=".3"
            r=".945"
        />
        <circle cx="214.494" cy="109.966" fill="#758CD6" opacity=".3" r=".63"/>
        <circle
            cx="229.932"
            cy="132.336"
            fill="#758CD6"
            opacity=".3"
            r="1.575"
        />
        <circle
            cx="230.877"
            cy="147.144"
            fill="#758CD6"
            opacity=".3"
            r="1.26"
        />
        <circle
            cx="218.905"
            cy="138.952"
            fill="#758CD6"
            opacity=".3"
            r="5.041"
        />
        <circle
            cx="227.727"
            cy="117.528"
            fill="#758CD6"
            opacity=".3"
            r="6.301"
        />
      </g>
      <path
          d="M373.918 77.64c1.524.761 2.221 2.468-.367 2.835m-2.469 0c-6.617-.63-26.781-6.617-35.288-14.808"
          stroke="#F6BCD1"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width=".945"
      />
      <path
          d="M363.836 56.53c-12.099-4.538-18.484 3.15-20.165 7.56.42.841 2.836 4.412 10.082 7.563 7.031 3.056 13.863 3.36 16.699 3.15 1.575-4.2 2.458-13.737-6.616-18.274Z"
          fill="#F6BCD1"
      />
      <path
          d="M359.424 61.57c-1.105-.553-2.835-.82-4.096 2.206-.504 1.26.21 3.045.631 3.78-1.051 0-3.214.378-3.466 1.89 0 .316 0 .63 1.575 1.261 1.26.504 2.416 1.05 2.836 1.26.21.105.567.378.315.63-.209.21-.577.144-.816.056l-.129-.056c.035.018.079.038.129.056l1.698.728c.065.235.249.226.378.162l-.378-.162c-.041-.149-.034-.394.063-.784.315-1.26.945-3.465-.945-4.41.63 0 2.52-.316 3.151-2.521.63-2.206.315-3.466-.946-4.096Z"
          fill="#fff"
      />
      <path
          d="M341.466 61.255c-3.256-.525-9.263-.82-9.768 2.206-.504 3.024 7.247 9.767 20.795 14.808 15.159 5.64 25.881 5.986 26.781 2.835.9-3.15-1.891-5.04-4.726-6.616"
          stroke="#F6BCD1"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.26"
      />
      <path
          d="m374.516 60.215-1.764 1.323M372.642 57.963l-1.103 1.91M375.965 62.988h-2.205"
          stroke="#F6BCD1"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width=".945"
      />
      <path
          d="m344.616 81.61-6.301 4.726M356.235 85.748v9.095"
          stroke="#F6BCD1"
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="1.26"
      />
      <path
          d="M182.185 184.416c3.269 2.569 13.913 20.531 18.973 29.19l-14.887 7.882c-1.07-1.265-4.32-6.072-7.589-9.341 1.167 3.269 2.53 18.584 2.919 25.103a3067.97 3067.97 0 0 0-16.347 3.211c-1.634-2.101-2.335-22.476-9.049-29.774l-3.502-3.21 2.043-11.677 20.141-14.886c1.07.097 4.028.934 7.298 3.502Z"
          fill="#5783FC"
      />
      <path
          d="m192.438 232.212 23.315-11.599c-1.459-2.336-5.546-1.752-7.881-1.752-1.868 0-5.254-3.503-6.714-5.254l-15.179 7.589 6.459 11.016Z"
          fill="#020641"
      />
      <path
          d="m199.869 217.319 3.503-1.751M200.584 218.747l3.956-2.012"
          stroke="#fff"
          stroke-width=".584"
      />
      <path
          d="M164.688 250.454h27.129c-.247-2.743-4.157-4.067-6.241-5.122-1.667-.843-3.756-5.86-4.267-8.081l-16.054 3.211-.567 9.992Z"
          fill="#020641"
      />
      <path
          d="m179.258 241.452 3.884-.505M179.464 243.036l4.398-.603"
          stroke="#fff"
          stroke-width=".584"
      />
      <path
          d="M118.55 210.104c-6.538-.934-7.005-11.481-6.422-16.638l63.051-14.303v1.751l-9.341 5.254-1.751 4.087 3.211 1.459 1.167 4.087c-2.53-.195-8.29.175-11.092 3.211-2.802 3.036-1.946 9.049-1.168 11.676-9.827.194-31.116.35-37.655-.584Z"
          fill="#5783FC"
      />
      <path
          d="M178.098 211.272c-4.379-3.795-7.998-11.968-9.049-14.595-.584-1.46-1.557-3.99-1.751-4.963-.682-.194-2.161-.7-2.628-1.167"
          stroke="#000"
          stroke-linecap="round"
          stroke-width=".584"
      />
      <path
          d="m175.179 183.011-13.271 9.341h-14.173c-1.09 0-1.586-1.361-.751-2.062l4.843-4.068 11.649-2.627 1.648-9.2a2.916 2.916 0 0 1 1.799-2.199l10.73-4.249a.584.584 0 0 1 .784.672l-3.258 14.392Z"
          fill="#66DED2"
      />
      <path
          clip-rule="evenodd"
          d="M133.145 156.739a8.174 8.174 0 1 0-6.196-13.502 4.086 4.086 0 1 0-1.642 7.651 8.175 8.175 0 0 0 7.838 5.851Z"
          fill="#FC466F"
          fill-rule="evenodd"
      />
      <path
          d="M146.281 140.685c0-6.305-5.449-5.741-8.173-4.67-3.211-3.503-7.298 1.167-8.174 2.335-1.751 0-2.627.292-3.794 1.459-.934.934-1.168 3.114-1.168 4.087.876.194 2.627.759 2.627 1.459-.233 1.401.292 1.946.584 2.044.876 0 1.053-1.408 1.46-2.627.875-2.628 5.254-.973 7.005 0 3.211 1.264 9.633 2.218 9.633-4.087Z"
          fill="#202020"
      />
      <path
          d="M131.686 158.199c-12.61-1.635-18.682 6.519-20.141 10.8-1.168 3.016-3.62 10.45-4.087 16.055-.584 7.005 1.46 12.551 14.011 13.427 10.042.701 21.115-4.573 25.396-7.297l4.962-7.59c4.184-1.946 12.085-5.955 10.217-6.422-1.869-.467-8.757-.973-11.968-1.167-.487-.876-1.81-3.503-3.211-7.006-1.752-4.378-5.838-9.633-15.179-10.8Z"
          fill="#011650"
      />
      <path
          d="M146.865 191.184c-1.635-4.204-1.071-10.217-.584-12.552 8.465-.876 10.8 0 11.676.292.233-.701 1.459-1.46 3.211-1.752 1.751-.292 3.503.584 3.503.584l-1.46 7.59c-.194.097-.7.233-1.167 0 .467 2.802-.973 3.113-1.752 2.919-.584.291-2.043.875-3.211.875-2.919.876-4.378-.292-4.962-.875-.584-.584-1.46-.876-2.043 0-.584.875-2.044 2.043-3.211 2.919Z"
          fill="#FC466F"
      />
      <path
          d="m125.264 169 .292 11.092c4.379-.487 14.362-1.46 19.265-1.46M141.027 167.832c.876 1.751 2.627 5.429 2.627 6.13"
          stroke="#fff"
          stroke-linecap="round"
          stroke-width=".584"
      />
      <path
          d="M153.287 182.427c1.167.973 3.502 3.62 3.502 6.422M157.081 180.967c1.07.973 3.153 3.737 2.919 7.006M158.833 179.799c.973.973 2.977 3.211 3.21 4.379M162.627 179.507c.389.487 1.168 1.635 1.168 2.336"
          stroke="#000"
          stroke-linecap="round"
          stroke-width=".584"
      />
      <ellipse
          cx="170.801"
          cy="178.34"
          fill="#fff"
          rx=".876"
          ry="1.751"
          transform="rotate(7.122 170.801 178.34)"
      />
      <path
          d="m180.931 155.117 4.196-2.427c.538-.311 1.154.264.881.823l-1.492 3.053 2.348 2.433a.63.63 0 0 1-.187 1.009l-5.155 2.401c-.629.293-1.201-.497-.725-1.003l2.289-2.435-2.331-2.915a.63.63 0 0 1 .176-.939ZM174.853 143.68l2.382-2.976c.191-.239.575-.086.549.218l-.223 2.684 2.233 1.029a.31.31 0 0 1 .094.495l-3.207 3.349c-.236.246-.637-.013-.51-.329l1.078-2.672-2.31-1.338a.308.308 0 0 1-.086-.46Z"
          fill="#5783FC"
      />
      <defs>
        <linearGradient
            id="a"
            gradientUnits="userSpaceOnUse"
            x1="201.893"
            x2="201.893"
            y1="108.609"
            y2="240.405"
        >
          <stop stop-color="#F7F4FD"/>
          <stop offset="1" stop-color="#F7F4FD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="c"
            gradientUnits="userSpaceOnUse"
            x1="186.768"
            x2="182.306"
            y1="113.747"
            y2="188.099"
        >
          <stop stop-color="#ABE7FF"/>
          <stop offset="1" stop-color="#ADE4FF" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="d"
            gradientUnits="userSpaceOnUse"
            x1="194.645"
            x2="202.045"
            y1="251.117"
            y2="197.234"
        >
          <stop stop-color="#A4BCFE"/>
          <stop offset="1" stop-color="#B0C5FD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="e"
            gradientUnits="userSpaceOnUse"
            x1="47.309"
            x2="99.469"
            y1="221.217"
            y2="200.52"
        >
          <stop stop-color="#92AFFD"/>
          <stop offset="1" stop-color="#92AFFD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="f"
            gradientUnits="userSpaceOnUse"
            x1="247.196"
            x2="272.201"
            y1="240.392"
            y2="230.641"
        >
          <stop stop-color="#92AFFD"/>
          <stop offset="1" stop-color="#92AFFD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="h"
            gradientUnits="userSpaceOnUse"
            x1="28.671"
            x2="3.896"
            y1="18.904"
            y2="20.454"
        >
          <stop stop-color="#92AFFD"/>
          <stop offset="1" stop-color="#92AFFD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="j"
            gradientUnits="userSpaceOnUse"
            x1="28.671"
            x2="3.896"
            y1="18.904"
            y2="20.454"
        >
          <stop stop-color="#6DBADD"/>
          <stop offset="1" stop-color="#8BD9FC" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="k"
            gradientUnits="userSpaceOnUse"
            x1="120.761"
            x2="120.761"
            y1="94.843"
            y2="129.815"
        >
          <stop stop-color="#7793DE"/>
          <stop offset="1" stop-color="#92AFFD" stop-opacity="0"/>
        </linearGradient>
        <linearGradient
            id="l"
            gradientUnits="userSpaceOnUse"
            x1="301.61"
            x2="301.61"
            y1="94.843"
            y2="129.815"
        >
          <stop stop-color="#7793DE"/>
          <stop offset="1" stop-color="#92AFFD" stop-opacity="0"/>
        </linearGradient>
      </defs>
    </svg>
    <el-button type="primary" @click="backUrl()">返回首页</el-button>
  </div>
</template>

<script setup>
import {useRouter} from 'vue-router'

const router = useRouter()
const backUrl = () => {
  router.push({
    name: 'Home',
    query: {
      // ...route.query,
    },
  })
}
</script>
