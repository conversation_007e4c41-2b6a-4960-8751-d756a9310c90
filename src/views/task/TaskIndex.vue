<script setup>
import {taskEnable, taskList} from '@/api/modules/task'
import MyPagination from '@/components/MyPagination.vue'
import {Disable, Enable, OnlineStates} from '@/utils/constmap'
import {onActivated, ref} from 'vue'
import {useRouter} from 'vue-router'


const search = ref({
  name: '',
  state: '',
})
const list = ref([])
const total = ref(0)
let page = 1
const router = useRouter()

const doShowDetail = (task_id = 0) => {
  router.push({
    name: 'TaskDetail',
    query: {
      id: task_id,
    },
  })
}
const doLoad = async (reset = false) => {
  if (reset) {
    page = 1
  }
  let params = {
    ...search.value,
    page,
  }
  const {data} = await taskList(params)
  total.value = data.total
  list.value = data.list.map(v => v)
}
const doEnable = async (task_id, state) => {
  await taskEnable({
    task_id,
    state,
  })
  doLoad()
}

onActivated(() => {
  doLoad()
})
</script>

<template>
  <el-card>
    <el-space alignment="normal" direction="vertical">
      <div>
        <el-button type="primary" @click="doShowDetail()">添加任务</el-button>
      </div>

      <el-form inline>
        <el-form-item label="任务名称">
          <el-input v-model="search.name" clearable placeholder="请输入任务名称"/>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="search.state" clearable placeholder="请选择状态">
            <el-option v-for="item in OnlineStates" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="doLoad(true)">查询</el-button>
        </el-form-item>
      </el-form>
    </el-space>

    <el-table :data="list">
      <el-table-column fixed="left" label="ID" prop="id"/>
      <el-table-column fixed="left" label="任务名称" prop="name"/>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.state === 2 ? 'danger' : 'primary'">{{ row.state_text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务周期" prop="interval_type_text"></el-table-column>
      <el-table-column label="可达成次数" prop="max_times"></el-table-column>
      <el-table-column label="达成条件" prop="cond_type_text"></el-table-column>
      <el-table-column label="达成值" prop="cond_amount"></el-table-column>
      <el-table-column label="奖励内容" prop="reward_type_text"></el-table-column>
      <el-table-column label="奖励数量" prop="reward_amount"></el-table-column>
      <el-table-column label="父任务" prop="parent_task_name"></el-table-column>
      <el-table-column fixed="right" label="操作">
        <template #default="{ row }">
          <el-space wrap>
            <el-button size="small" type="primary" @click="doShowDetail(row.id)">编辑</el-button>

            <el-popconfirm v-if="row.state === Enable" title="您确定要下线吗？" @confirm="doEnable(row.id, Disable)">
              <template #reference>
                <el-button size="small" type="danger">下线</el-button>
              </template>
            </el-popconfirm>
            <el-button v-else size="small" type="primary" @click="doEnable(row.id, Enable)">上线</el-button>

          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <MyPagination :page="page" :total="total" @current-change="p => {
      page = p
      doLoad()
    }"/>
  </el-card>
</template>