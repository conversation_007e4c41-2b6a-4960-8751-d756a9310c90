<script setup>
import {taskDetail, taskSave} from '@/api/modules/task';
import MyEditor from '@/components/MyEditor.vue';
import MyTaskSelector from '@/components/MyTaskSelector.vue';
import router from '@/router';
import {deepClone} from '@/utils';
import {
  TaskCondCheckInScenic,
  TaskConds,
  TaskCondShareActLogin,
  TaskCondTaskAccIntegral,
  TaskIntervalOnce,
  TaskIntervals,
  TaskRewardActIntegral,
  TaskRewardAirWifi,
  TaskRewardEntity,
  TaskRewards,
  TaskCondRankSettle,
  TaskCondParent, Disable, Enable,
} from '@/utils/constmap';
import {ElMessage} from 'element-plus';
import {computed, onActivated, ref} from 'vue';
import {useRoute} from 'vue-router';
import MyImageUpload from "../../components/MyImageUpload.vue";
import {TaskRewardAccountIntegral} from "../../utils/constmap";


const route = useRoute()
const id = ref(-1)
const _init = {
  name: '',
  parent_task_id: 0,
  interval_type: TaskIntervalOnce,
  max_times: 1,
  cond_type: TaskCondShareActLogin,
  cond_amount: 1,
  reward_type: TaskRewardActIntegral,
  reward_amount: 1,
  reward_id: '',
  short_desc: '',
  scenic_id: '',
  tutorial: '',
  cover: '',
  cover_id: '',
  rank_settle_num: 0,
  reward_duplicate: Disable,
}
const detail = ref(deepClone(_init))
const formRef = ref(null)
const reward_duplicate = computed({
  get() {
    return detail.value.reward_duplicate === Enable
  },
  set(v) {
    detail.value.reward_duplicate = v ? Enable : Disable
  },
})
const rules = computed(() => {
  let rules = {
    name: [{required: true, message: '请输入活动名称'}],
    max_times: [{required: true, message: '最小为1'}],
    cond_amount: [{
      validator(rule, value, callback, source, options) {
        const {cond_type} = detail.value
        if (cond_type == TaskCondTaskAccIntegral && value < 1) {
          console.log('=====', cond_type, value)
          return new Error('最少为1')
        }
        callback()
      },
    }],
    reward_amount: [{
      validator(rule, value, callback, source, options) {
        const {reward_type} = detail.value
        if (reward_type == TaskRewardActIntegral && value < 1) {
          return new Error('最少为1')
        }
        callback()
      }
    }],
    reward_id: [{
      validator(_, value, callback) {
        const {reward_type} = detail.value
        if (reward_type == TaskRewardAirWifi && value == '') {
          return new Error('请输入奖励ID')
        }
        callback()
      },
    }],
    rank_settle_num: [{
      validator(_, value, callback) {
        const {cond_type} = detail.value
        if (cond_type === TaskCondRankSettle && value < 1) {
          return new Error('请输入排行结算数')
        }
        callback()
      },
    }],
  }
  return rules
})

const doSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (!valid) {
      return
    }
    const params = {
      ...detail.value,
      id: id.value,
      cover: '', //防止提交图片bin数据
    }
    const {data} = await taskSave(params)
    ElMessage.success('已保存')
    detail.value.cover_id = ''
    id.value = data.id
    router.replace({
      name: 'Tasks',
    })
  })
}

onActivated(() => {
  id.value = parseInt(route.query.id) || 0
  if (id.value > 0) {
    taskDetail(id.value).then(({data}) => {
      data.children = undefined
      Object.assign(detail.value, data)
      console.log(detail.value)
    })
  } else {
    Object.assign(detail.value, deepClone(_init))
  }
})

</script>

<template>
  <el-card :header="`#${id}`">
    <el-form ref="formRef" :model="detail" :rules="rules" label-position="top">
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="detail.name"/>
      </el-form-item>
      <el-form-item label="简短描述" prop="short_desc">
        <el-input v-model="detail.short_desc" :rows="2" type="textarea"/>
      </el-form-item>
      <el-form-item label="父任务" prop="parent_task_id">
        <MyTaskSelector v-model="detail.parent_task_id" :ignoreIds="[id]"></MyTaskSelector>
      </el-form-item>
      <el-form-item label="执行周期" prop="interval_type">
        <el-select v-model="detail.interval_type">
          <el-option v-for="(item, i) in TaskIntervals" :key="i" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="可完成次数" prop="max_times">
        <el-input-number v-model="detail.max_times" :min="1"/>
      </el-form-item>
      <div>
        <el-space>
          <el-form-item label="条件类型" prop="cond_type">
            <el-select v-model="detail.cond_type">
              <el-option v-for="(item, i) in TaskConds" :key="i" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="detail.cond_type == TaskCondTaskAccIntegral" label="积分数量" prop="cond_amount">
            <el-input-number v-model="detail.cond_amount"/>
          </el-form-item>
          <el-form-item v-else-if="detail.cond_type === TaskCondCheckInScenic" label="景点ID">
            <el-input v-model="detail.scenic_id"/>
          </el-form-item>
          <el-form-item v-else-if="detail.cond_type === TaskCondRankSettle" label="榜单入榜数" prop="rank_settle_num">
            <el-input-number v-model="detail.rank_settle_num"/>
          </el-form-item>
        </el-space>
      </div>

      <el-space v-if="detail.cond_type !== TaskCondCheckInScenic">
        <el-form-item label="奖励内容" prop="reward_type">
          <el-select v-model="detail.reward_type">
            <el-option v-for="(item, i) in TaskRewards" :key="i" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="可叠加未领取奖励" prop="reward_duplicate">
          <el-switch v-model="reward_duplicate"></el-switch>
        </el-form-item>
        <el-form-item v-if="detail.reward_type == TaskRewardAirWifi" label="奖励ID" prop="reward_id">
          <el-input v-model="detail.reward_id"/>
        </el-form-item>
        <el-form-item v-else-if="detail.reward_type === TaskRewardEntity" label="实物名称" prop="reward_amount">
          <el-input v-model="detail.reward_id"/>
        </el-form-item>
        <el-form-item v-else-if="[TaskRewardActIntegral, TaskRewardAccountIntegral].includes(detail.reward_type)"
                      label="奖励积分数量" prop="reward_amount">
          <el-input-number v-model="detail.reward_amount"/>
        </el-form-item>
      </el-space>

      <el-form-item label="封面">
        <MyImageUpload v-model="detail.cover_id" v-model:url="detail.cover"/>
      </el-form-item>

      <el-form-item label="任务攻略">
        <MyEditor v-model="detail.tutorial"></MyEditor>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="doSubmit">保存</el-button>
    </template>
  </el-card>
</template>
