<template>
  <div class="wish-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.user_id" clearable placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="心愿状态">
          <el-select v-model="searchForm.state" clearable placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in WishStates" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="跟进状态">
          <el-select v-model="searchForm.follow_state" clearable placeholder="全部">
            <el-option label="全部" value="" />
            <el-option v-for="item in WishFollowStates" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="公开状态">
          <el-select v-model="searchForm.open_scope" clearable placeholder="全部">
            <el-option label="全部" value="" />
            <el-option label="公开" :value="Enable" />
            <el-option label="隐藏" :value="Disable" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <MyDateRangePicker v-model="dateRange" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 心愿列表表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>旅行心愿管理</span>
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="wishList" style="width: 100%">
        <el-table-column label="ID" prop="id" width="80" align="center" />
        <el-table-column label="用户信息" width="120">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" :size="32" />
              <div class="user-details">
                <div class="nickname">{{ scope.row.nickname }}</div>
                <div class="user-id">ID: {{ scope.row.user_id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="心愿标题" prop="title" min-width="200" />
        <el-table-column label="预计出行时间" prop="date_str" width="120" />
        <el-table-column label="预算" width="120">
          <template #default="scope">
            <div class="budget-info">
              <span>{{ scope.row.budget }}</span>
              <el-tag v-if="scope.row.budget_type" :type="getBudgetTypeTagType(scope.row.budget_type)" size="small"
                style="margin-left: 4px">
                {{ getBudgetTypeText(scope.row.budget_type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="同行人数" prop="member_count" width="80" align="center" />
        <el-table-column label="心愿状态" width="100">
          <template #default="scope">
            <el-tag :type="getStateTagType(scope.row.state)">
              {{ scope.row.state_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="跟进状态" width="100">
          <template #default="scope">
            <el-tag :type="getFollowStateTagType(scope.row.follow_state)">
              {{ scope.row.follow_state_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="公开状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.open_scope === Enable ? 'success' : 'info'">
              {{ scope.row.open_scope_text }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :formatter="tableFormatTime" label="创建时间" prop="created_at" width="150" />
        <el-table-column label="关闭原因" width="200">
          <template #default="scope">
            <span v-if="scope.row.reject_reason" class="reject-reason">
              {{ scope.row.reject_reason }}
            </span>
            <span v-else class="no-reason">-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="viewWishDetail(scope.row)">
              查看详情
            </el-button>
            <el-button v-if="scope.row.can_close" link type="danger" @click="handleCloseWish(scope.row)">
              关闭心愿
            </el-button>
            <el-button v-if="scope.row.can_finish" link type="success" @click="handleFinishWish(scope.row)">
              标记已去过
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <MyPagination :total="total" @current-change="handleCurrentChange" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onActivated, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import MyPagination from "../../components/MyPagination.vue"
import MyDateRangePicker from "../../components/MyDateRangePicker.vue"
import { getWishList, closeWish, finishWish } from "../../api/modules/wish"
import { deepToRaw, tableFormatTime } from "../../utils"
import {
  Enable, Disable,
  WishStates, WishFollowStates,
  WishStateWaitReview, WishStateRejected, WishStateProcessing, WishStateSuccess, WishStateFinished, WishStateClosed,
  WishFollowStateDefault, WishFollowStateWait, WishFollowStateProcessing, WishFollowStateTransFinished, WishFollowStateTransCancelled,
  WishBudgetSingle, WishBudgetTeam
} from "../../utils/constmap"

const router = useRouter()

// 搜索表单数据
const searchForm = reactive({
  user_id: '',
  state: '',
  follow_state: '',
  open_scope: '',
  start_time: '',
  end_time: ''
})

// 时间范围
const dateRange = ref([])

// 表格数据
const wishList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取心愿列表数据
const loadData = async () => {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...deepToRaw(searchForm)
    }

    const res = await getWishList(params)
    wishList.value = res.data.list
    total.value = res.data.total

    // 调试：打印接收到的数据
    console.log('心愿列表数据:', wishList.value)
    if (wishList.value.length > 0) {
      console.log('第一条数据:', wishList.value[0])
      console.log('预算字段:', wishList.value[0].budget)
      console.log('预算类型字段:', wishList.value[0].budget_type)
    }
  } catch (error) {
    ElMessage.error('获取心愿列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 监听时间范围变化
watch(dateRange, (dates) => {
  if (dates && dates.length === 2) {
    searchForm.start_time = Math.floor(dates[0].getTime() / 1000)
    searchForm.end_time = Math.floor(dates[1].getTime() / 1000)
  } else {
    searchForm.start_time = ''
    searchForm.end_time = ''
  }
})

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.user_id = ''
  searchForm.state = ''
  searchForm.follow_state = ''
  searchForm.open_scope = ''
  searchForm.start_time = ''
  searchForm.end_time = ''
  dateRange.value = []
  currentPage.value = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 查看心愿详情
const viewWishDetail = (row) => {
  router.push({
    name: 'WishDetail',
    query: { wish_id: row.id }
  })
}

// 关闭心愿
const handleCloseWish = async (row) => {
  try {
    const { value: reason } = await ElMessageBox.prompt('请输入关闭原因', '关闭心愿', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入关闭原因',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '关闭原因不能为空'
        }
        return true
      }
    })

    await closeWish({
      wish_id: row.id,
      reason: reason.trim()
    })

    ElMessage.success('心愿已关闭')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('关闭心愿失败')
      console.error(error)
    }
  }
}

// 标记心愿已去过
const handleFinishWish = async (row) => {
  try {
    await ElMessageBox.confirm('确定要标记此心愿单为已去过吗？', '标记已去过', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await finishWish({
      wish_id: row.id
    })

    ElMessage.success('心愿单已标记为已去过')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标记失败')
      console.error(error)
    }
  }
}

// 获取心愿状态标签类型
const getStateTagType = (state) => {
  const stateMap = {
    [WishStateWaitReview]: 'warning',     // 待审核
    [WishStateRejected]: 'danger',        // 已驳回
    [WishStateProcessing]: 'primary',     // 进行中
    [WishStateSuccess]: 'success',        // 已达成
    [WishStateFinished]: 'success',       // 已去过
    [WishStateClosed]: 'info'             // 关闭
  }
  return stateMap[state] || 'info'
}

// 获取跟进状态标签类型
const getFollowStateTagType = (followState) => {
  const followStateMap = {
    [WishFollowStateDefault]: 'info',           // 默认
    [WishFollowStateWait]: 'danger',            // 待跟进
    [WishFollowStateProcessing]: 'warning',     // 跟进中
    [WishFollowStateTransFinished]: 'success', // 达成交易
    [WishFollowStateTransCancelled]: 'danger'  // 交易取消
  }
  return followStateMap[followState] || 'info'
}

// 获取预算类型标签类型
const getBudgetTypeTagType = (budgetType) => {
  return budgetType === WishBudgetSingle ? 'primary' : 'success'
}

// 获取预算类型文本
const getBudgetTypeText = (budgetType) => {
  return budgetType === WishBudgetSingle ? '单人' : '团队'
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 页面加载时获取数据
onActivated(() => {
  loadData()
})
</script>

<style scoped>
.wish-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info .el-avatar {
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.reject-reason {
  color: #f56c6c;
  font-size: 12px;
  max-width: 180px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-reason {
  color: #c0c4cc;
}

.budget-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}
</style>