<template>
  <div class="wish-detail-container">
    <!-- 基本信息 -->
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>心愿详情</span>
          <div class="header-actions">
            <el-button v-if="wishInfo.can_close" type="danger" @click="handleCloseWish">
              关闭心愿
            </el-button>
            <el-button v-if="wishInfo.can_finish" type="success" @click="handleFinishWish">
              标记已去过
            </el-button>
            <el-button v-if="wishInfo.can_approve" type="primary" @click="handleApproveWish">
              审核通过
            </el-button>
            <el-button @click="loadData(wishInfo.wish_id)">刷新</el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </template>

      <el-descriptions :column="3" border>
        <el-descriptions-item label="心愿ID">{{ wishInfo.wish_id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ wishInfo.user_id }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">
          <div class="user-info">
            <el-avatar :src="wishInfo.avatar" :size="24"/>
            <span>{{ wishInfo.nickname }}</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="心愿标题" :span="3">{{ wishInfo.title }}</el-descriptions-item>
        <el-descriptions-item label="出发地">{{ wishInfo.from }}</el-descriptions-item>
        <el-descriptions-item label="目的地">{{ wishInfo.to }}({{ wishInfo.to_zone_name }})</el-descriptions-item>
        <el-descriptions-item label="预算">
          {{ wishInfo.budget }}
          <el-tag v-if="wishInfo.budget_type" :type="getBudgetTypeTagType(wishInfo.budget_type)" size="small"
                  style="margin-left: 8px">
            {{ getBudgetTypeText(wishInfo.budget_type) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="总人数">{{ wishInfo.total_people }}人</el-descriptions-item>
        <el-descriptions-item label="出行时间">{{ wishInfo.date_str }}</el-descriptions-item>
        <el-descriptions-item label="截止时间">{{ formatTime(wishInfo.deadline) }}</el-descriptions-item>
        <el-descriptions-item label="心愿状态">
          <el-tag :type="getStateTagType(wishInfo.state)">
            {{ wishInfo.state_text }}
          </el-tag>
        </el-descriptions-item>

        <el-descriptions-item label="公开状态">
          <el-tag :type="wishInfo.open_scope === Enable ? 'success' : 'info'">
            {{ wishInfo.open_scope_text }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="点赞数">{{ wishInfo.likes }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatTime(wishInfo.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatTime(wishInfo.updated_at) }}</el-descriptions-item>
        <el-descriptions-item v-if="wishInfo.wish_desc" label="心愿描述">
          <div class="wish-desc-content">{{ wishInfo.wish_desc }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="wishInfo.member_desc" label="成员要求">
          <div class="wish-desc-content">{{ wishInfo.member_desc }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="wishInfo.reject_reason" label="关闭原因">
          <div class="reject-reason-detail">
            <el-text type="danger">{{ wishInfo.reject_reason }}</el-text>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 心愿事项 -->
    <el-card v-if="wishInfo.todos && wishInfo.todos.length > 0" class="detail-card">
      <template #header>
        <span>心愿事项</span>
      </template>
      <el-table :data="wishInfo.todos" style="width: 100%">
        <el-table-column label="序号" type="index" width="60"/>
        <el-table-column label="事项内容" prop="todo" width="300"/>
        <el-table-column label="是否必做">
          <template #default="scope">
            <el-tag :type="scope.row.is_must === Enable ? 'danger' : 'info'">
              {{ scope.row.is_must === Enable ? '必做' : '可选' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 同行人信息 -->
    <el-card v-if="wishInfo.members && wishInfo.members.length > 0" class="detail-card">
      <template #header>
        <span>同行人信息</span>
      </template>
      <el-table :data="wishInfo.members" style="width: 100%">
        <el-table-column label="用户信息" width="150">
          <template #default="scope">
            <div class="user-info">
              <el-avatar :src="scope.row.avatar" :size="32"/>
              <div class="user-details">
                <div class="nickname">{{ scope.row.nickname }}</div>
                <div class="user-id">ID: {{ scope.row.user_id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="身份" width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.is_owner === Enable" type="warning" size="small">
              发起人
            </el-tag>
            <el-tag v-else type="info" size="small">
              同行人
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="真实姓名" prop="real_name"/>
        <el-table-column label="联系电话" prop="phone"/>
        <el-table-column label="预算" prop="budget"/>
        <el-table-column label="备注" prop="remark"/>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getMemberStateTagType(scope.row.state)">
              {{ getMemberStateText(scope.row.state) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="跟进状态" width="150">
          <template #default="scope">
            <el-tag :type="getFollowStateTagType(scope.row.follow_state)">
              {{ getFollowStateText(scope.row.follow_state) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button v-if="scope.row.state === WishMemberStateWaitReview" link type="success"
                       @click="handleReviewMember(scope.row, Enable)">
              通过
            </el-button>
            <el-button v-if="scope.row.state === WishMemberStateWaitReview" link type="danger"
                       @click="handleReviewMember(scope.row, Disable)">
              拒绝
            </el-button>
            <el-button link type="primary" @click="showUpdateMemberDialog(scope.row)">
              更新
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 媒体文件 -->
    <el-card v-if="wishInfo.medias && wishInfo.medias.length > 0" class="detail-card">
      <template #header>
        <span>媒体文件</span>
      </template>
      <div class="media-grid">
        <div v-for="media in wishInfo.medias" :key="media.id" class="media-item">
          <el-image v-if="media.media_type === WishMediaPicture" :src="media.res_url" :preview-src-list="getImageList()"
                    fit="cover" class="media-image"/>
          <video v-else-if="media.media_type === WishMediaVideo" :src="media.res_url" controls class="media-video"/>
        </div>
      </div>
    </el-card>

    <!-- 行程信息 -->
    <el-card v-if="wishInfo.plan && wishInfo.plan.sections && wishInfo.plan.sections.length > 0" class="detail-card">
      <template #header>
        <span>行程内容</span>
      </template>

      <!-- 行程基本信息 -->
      <div class="plan-basic-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="行程主题">{{ wishInfo.plan.subject }}</el-descriptions-item>
          <el-descriptions-item label="行程副标题">{{ wishInfo.plan.subtitle }}</el-descriptions-item>
          <el-descriptions-item v-if="wishInfo.plan.fit_for" label="适合季节">{{
              wishInfo.plan.fit_for
            }}
          </el-descriptions-item>
          <el-descriptions-item v-if="wishInfo.plan.notice" label="注意事项" :span="2">{{
              wishInfo.plan.notice
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 每日行程 -->
      <div class="daily-itinerary">
        <div v-for="(section, index) in wishInfo.plan.sections" :key="index" class="day-section">
          <div class="day-header">
            <h3>{{ section.title }}</h3>
            <span v-if="section.subject" class="day-subject">{{ section.subject }}</span>
          </div>

          <!-- 每日行程内容：景点1 -> 景点2 -> ... -> 住宿:xxx大酒店 -->
          <div class="day-timeline">
            <span v-for="(item, itemIndex) in formatDayTimeline(section.content.timeline)" :key="itemIndex"
                  class="timeline-item">
              <span v-if="item.type === 'scene'" class="scene-item">{{ item.name }}</span>
              <span v-else-if="item.type === 'hotel'" class="hotel-item">住宿: {{ item.name }}</span>
              <span v-else class="other-item">{{ item.name }}</span>
              <span v-if="itemIndex < formatDayTimeline(section.content.timeline).length - 1" class="arrow"> → </span>
            </span>
          </div>

          <!-- 详细时间线（可折叠） -->
          <el-collapse class="timeline-collapse">
            <el-collapse-item :name="index" class="collapse-item">
              <template #title>
                <span class="collapse-title">查看第{{ index + 1 }}天详细安排</span>
              </template>
              <div v-for="(timeline, timeIndex) in section.content.timeline" :key="timeIndex" class="timeline-detail">
                <div class="timeline-time">{{ timeline.time || '全天' }}</div>
                <div class="timeline-content">
                  <div class="timeline-title">{{ timeline.title }}</div>
                  <div v-if="timeline.desc" class="timeline-desc">{{ timeline.desc }}</div>
                  <div v-if="timeline.poi && timeline.poi.price" class="timeline-price">参考价格: {{
                      timeline.poi.price
                    }}
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-card>

    <!-- 更新同行人信息对话框 -->
    <el-dialog v-model="showUpdateMemberDialogVisible" title="更新同行人信息" width="500px">
      <el-form :model="updateMemberForm" label-width="100px">
        <el-form-item label="用户信息">
          <div class="user-info">
            <el-avatar :src="currentMember.avatar" :size="32"/>
            <span>{{ currentMember.nickname }} (ID: {{ currentMember.user_id }})</span>
          </div>
        </el-form-item>
        <el-form-item label="跟进状态">
          <el-select v-model="updateMemberForm.follow_state" placeholder="请选择跟进状态">
            <el-option v-for="item in WishFollowStates" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="预算">
          <el-input v-model="updateMemberForm.budget" placeholder="请输入预算金额" clearable/>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="updateMemberForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息" clearable/>
        </el-form-item>
        <!--        <el-form-item>-->
        <!--          <el-text type="info" size="small">-->
        <!--            注：至少需要填写预算或备注中的一项-->
        <!--          </el-text>-->
        <!--        </el-form-item>-->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showUpdateMemberDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateMember">确认更新</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {ref, reactive, onActivated} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  getWishDetail,
  closeWish,
  reviewMember,
  updateMember,
  finishWish,
  approveWish
} from '../../api/modules/wish'
import {formatTime} from '../../utils'
import {
  Enable,
  Disable,
  WishFollowStates,
  WishStateWaitReview,
  WishStateRejected,
  WishStateProcessing,
  WishStateSuccess,
  WishStateFinished,
  WishStateClosed,
  WishFollowStateDefault,
  WishFollowStateWait,
  WishFollowStateProcessing,
  WishFollowStateTransFinished,
  WishFollowStateTransCancelled,
  WishMemberStateWaitReview,
  WishMemberStateApproved,
  WishMemberStateRejected,
  WishBudgetSingle,
  WishBudgetTeam,
  WishMediaPicture,
  WishMediaVideo
} from '../../utils/constmap'

const route = useRoute()
const router = useRouter()

// 心愿详情数据
const wishInfo = ref({
  wish_id: '',
  user_id: '',
  nickname: '',
  avatar: '',
  title: '',
  from: '',
  to: '',
  to_zone_name: '',
  budget: '',
  total_people: 0,
  date_str: '',
  deadline: 0,
  state: 0,
  state_text: '',
  open_scope: 0,
  open_scope_text: '',
  likes: 0,
  created_at: 0,
  updated_at: 0,
  wish_desc: '',
  member_desc: '',
  reject_reason: '',
  can_close: false,
  can_finish: false,
  can_approve: false,
  todos: [],
  members: [],
  medias: []
})


// 更新同行人信息表单
const updateMemberForm = reactive({
  budget: '',
  remark: ''
})

const showUpdateMemberDialogVisible = ref(false)
const currentMember = ref({})

// 获取心愿详情
const loadData = async (wishId) => {
  try {
    const res = await getWishDetail(wishId)
    wishInfo.value = res.data
  } catch (error) {
    ElMessage.error('获取心愿详情失败')
    console.error(error)
    router.push({name: 'WishIndex'})
  }
}

// 关闭心愿
const handleCloseWish = async () => {
  try {
    const {value: reason} = await ElMessageBox.prompt('请输入关闭原因', '关闭心愿', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入关闭原因',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '关闭原因不能为空'
        }
        return true
      }
    })

    await closeWish({
      wish_id: wishInfo.value.wish_id,
      reason: reason.trim()
    })

    ElMessage.success('心愿已关闭')
    await loadData(wishInfo.value.wish_id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('关闭心愿失败')
      console.error(error)
    }
  }
}

// 标记心愿已去过
const handleFinishWish = async () => {
  try {
    await ElMessageBox.confirm('确定要标记此心愿单为已去过吗？', '标记已去过', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await finishWish({
      wish_id: wishInfo.value.wish_id
    })

    ElMessage.success('心愿单已标记为已去过')
    await loadData(wishInfo.value.wish_id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标记失败')
      console.error(error)
    }
  }
}

// 审核通过心愿单
const handleApproveWish = async () => {
  try {
    await ElMessageBox.confirm('确定要审核通过此心愿单吗？', '审核通过', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await approveWish({
      wish_id: wishInfo.value.wish_id
    })

    ElMessage.success('心愿单审核通过成功')
    await loadData(wishInfo.value.wish_id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('审核通过失败')
      console.error(error)
    }
  }
}


// 获取跟进状态文本
const getFollowStateText = (followState) => {
  const stateMap = {
    [WishFollowStateDefault]: '默认',
    [WishFollowStateWait]: '待跟进',
    [WishFollowStateProcessing]: '跟进中',
    [WishFollowStateTransFinished]: '达成交易',
    [WishFollowStateTransCancelled]: '交易取消'
  }
  return stateMap[followState] || '未知'
}

// 显示更新同行人信息对话框
const showUpdateMemberDialog = (member) => {
  currentMember.value = member
  updateMemberForm.budget = member.budget || ''
  updateMemberForm.remark = member.remark || ''
  updateMemberForm.follow_state = member.follow_state
  showUpdateMemberDialogVisible.value = true
}

// 更新同行人信息
const handleUpdateMember = async () => {
  try {

    const requestData = {
      member_id: currentMember.value.id,
      follow_state: updateMemberForm.follow_state
    }

    // 只传递非空字段
    if (updateMemberForm.budget.trim()) {
      requestData.budget = updateMemberForm.budget.trim()
    }
    if (updateMemberForm.remark.trim()) {
      requestData.remark = updateMemberForm.remark.trim()
    }

    await updateMember(requestData)

    ElMessage.success('同行人信息已更新')
    showUpdateMemberDialogVisible.value = false
    await loadData(wishInfo.value.wish_id)
  } catch (error) {
    console.error(error)
  }
}

// 审核同行人
const handleReviewMember = async (member, state) => {
  const actionText = state === Enable ? '通过' : '拒绝'

  try {
    let rejectReason = ''

    // 如果是拒绝操作，需要输入拒绝原因
    if (state === Disable) {
      const {value: reason} = await ElMessageBox.prompt('请输入拒绝原因', '拒绝同行人申请', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入拒绝原因',
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '拒绝原因不能为空'
          }
          return true
        }
      })
      rejectReason = reason.trim()
    } else {
      // 通过操作需要确认
      await ElMessageBox.confirm(`确定要${actionText}该同行人申请吗？`, '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    }

    const requestData = {
      member_id: member.id,
      is_approve: state
    }

    // 拒绝时添加拒绝原因
    if (state === Disable && rejectReason) {
      requestData.reject_reason = rejectReason
    }

    await reviewMember(requestData)

    ElMessage.success(`已${actionText}该同行人申请`)
    await loadData(wishInfo.value.wish_id)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`${actionText}同行人申请失败`)
      console.error(error)
    }
  }
}

// 获取心愿状态标签类型
const getStateTagType = (state) => {
  const stateMap = {
    [WishStateWaitReview]: 'warning',     // 待审核
    [WishStateRejected]: 'danger',        // 已驳回
    [WishStateProcessing]: 'primary',     // 进行中
    [WishStateSuccess]: 'success',        // 已达成
    [WishStateFinished]: 'success',       // 已去过
    [WishStateClosed]: 'info'             // 关闭
  }
  return stateMap[state] || 'info'
}

// 获取跟进状态标签类型
const getFollowStateTagType = (followState) => {
  const followStateMap = {
    [WishFollowStateDefault]: 'info',           // 默认
    [WishFollowStateWait]: 'danger',            // 待跟进
    [WishFollowStateProcessing]: 'warning',     // 跟进中
    [WishFollowStateTransFinished]: 'success', // 达成交易
    [WishFollowStateTransCancelled]: 'danger'  // 交易取消
  }
  return followStateMap[followState] || 'info'
}

// 获取同行人状态标签类型
const getMemberStateTagType = (state) => {
  const stateMap = {
    [WishMemberStateWaitReview]: 'warning', // 待审核
    [WishMemberStateApproved]: 'success',   // 已通过
    [WishMemberStateRejected]: 'danger'     // 已拒绝
  }
  return stateMap[state] || 'info'
}

// 获取同行人状态文本
const getMemberStateText = (state) => {
  const stateMap = {
    [WishMemberStateWaitReview]: '待审核',
    [WishMemberStateApproved]: '已通过',
    [WishMemberStateRejected]: '已拒绝'
  }
  return stateMap[state] || '未知'
}

// 获取预算类型标签类型
const getBudgetTypeTagType = (budgetType) => {
  return budgetType === WishBudgetSingle ? 'primary' : 'success'
}

// 获取预算类型文本
const getBudgetTypeText = (budgetType) => {
  return budgetType === WishBudgetSingle ? '单人预算' : '团队预算'
}

// 获取图片列表用于预览
const getImageList = () => {
  return wishInfo.value.medias
      .filter(media => media.media_type === WishMediaPicture)
      .map(media => media.res_url)
}

// 格式化每日行程时间线，提取景点和住宿信息
const formatDayTimeline = (timeline) => {
  if (!timeline || !Array.isArray(timeline)) {
    return []
  }

  const items = []

  timeline.forEach(item => {
    if (item.poi && item.poi.name) {
      let type = 'other'

      // 根据时间线类型判断是景点还是住宿
      if (item.type === 1) { // JTimelineScene = 1
        type = 'scene'
      } else if (item.type === 2) { // JTimelineHotel = 2
        type = 'hotel'
      } else if (item.title && (item.title.includes('住宿') || item.title.includes('酒店') || item.title.includes('宾馆'))) {
        type = 'hotel'
      } else if (item.title && !item.title.includes('交通') && !item.title.includes('用餐')) {
        type = 'scene'
      }

      items.push({
        name: item.poi.name,
        type: type,
        title: item.title,
        time: item.time
      })
    } else if (item.title && !item.title.includes('交通')) {
      // 如果没有POI信息但有标题，也显示出来
      let type = 'other'
      if (item.title.includes('住宿') || item.title.includes('酒店') || item.title.includes('宾馆')) {
        type = 'hotel'
      } else if (!item.title.includes('用餐')) {
        type = 'scene'
      }

      items.push({
        name: item.title,
        type: type,
        title: item.title,
        time: item.time
      })
    }
  })

  return items
}

// 返回上一页
const goBack = () => {
  router.push({name: 'WishIndex'})
}

// 页面激活时获取数据
onActivated(() => {
  const wishId = route.query.wish_id
  if (wishId) {
    loadData(wishId)
  } else {
    ElMessage.error('心愿ID不能为空')
    router.push({name: 'WishIndex'})
  }
})
</script>

<style scoped>
.wish-detail-container {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-info .el-avatar {
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
}

.nickname {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.user-id {
  font-size: 12px;
  color: #909399;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.media-item {
  border-radius: 8px;
  overflow: hidden;
}

.media-image,
.media-video {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.reject-reason-detail {
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.5;
}

.wish-desc-content {
  max-width: 300px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.6;
  overflow-wrap: break-word;
}

/* 行程信息样式 */
.plan-basic-info {
  margin-bottom: 20px;
}

.daily-itinerary {
  margin-top: 16px;
}

.day-section {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.day-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #409eff;
}

.day-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
  background: linear-gradient(135deg, #409eff, #67c23a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.day-subject {
  font-size: 14px;
  color: #909399;
  background-color: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.day-timeline {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  line-height: 1.8;
}

.timeline-item {
  display: inline;
}

.scene-item {
  color: #409eff;
  font-weight: 500;
  padding: 2px 6px;
  background-color: #ecf5ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.hotel-item {
  color: #e6a23c;
  font-weight: 600;
  padding: 2px 8px;
  background: linear-gradient(135deg, #fdf6ec, #faecd8);
  border-radius: 4px;
  border: 1px solid #f5dab1;
  box-shadow: 0 1px 3px rgba(230, 162, 60, 0.2);
}

.other-item {
  color: #606266;
  font-weight: 500;
}

.arrow {
  color: #909399;
  font-weight: bold;
  margin: 0 8px;
}

.timeline-collapse {
  margin-top: 12px;
}

.collapse-item {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;
}

.collapse-title {
  margin-left: 20px;
  color: #409eff;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.collapse-title:hover {
  color: #66b1ff;
  text-decoration: underline;
}

.timeline-detail {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f5f7fa;
}

.timeline-detail:last-child {
  border-bottom: none;
}

.timeline-time {
  min-width: 60px;
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  text-align: center;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.timeline-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
  line-height: 1.5;
}

.timeline-price {
  font-size: 12px;
  color: #e6a23c;
  font-weight: 500;
}
</style>
