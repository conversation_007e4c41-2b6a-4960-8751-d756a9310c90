<script setup>

import {onMounted, ref, watch} from "vue";
import {flowcardGetRecords, flowcardRollback} from "../../../api/modules/wifi";
import MyPagination from "../../../components/MyPagination.vue";
import {tableFormatTime} from "../../../utils";
import {ElNotification} from "element-plus";

const props = defineProps(['flowcardId'])
const emit = defineEmits(['close'])
const show = ref(true)
const list = ref([])
const total = ref(0)
let page = 1
const pageSize = 12

watch(show, () => emit('close'))

function onRollback(row) {
  flowcardRollback({rel_id: row.id}).then(() => {
    ElNotification.success('操作成功')
    getList()
  })
}

function getList() {
  const params = {flowcard_id: props.flowcardId, page, page_size: pageSize}
  flowcardGetRecords(params).then(res => {
    const {data} = res

    total.value = data.total
    list.value = data.list
  })
}

onMounted(() => {
  getList()
})

</script>

<template>
  <el-dialog v-model="show" title="领取记录">

    <el-table :data="list">
      <el-table-column prop="user_id" label="用户ID"/>
      <el-table-column prop="name" label="领取用户"/>
      <el-table-column prop="created_at" :formatter="tableFormatTime" label="领取时间"/>
      <el-table-column prop="airline_name" label="航空公司"/>
      <el-table-column prop="ip" label="IP"/>
      <el-table-column label="操作">
        <template #default="{row}">
          <el-popconfirm @confirm="onRollback(row)" title="您确定要取消领取吗？">
            <template #reference>
              <el-button size="small" type="warning">取消领取</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="(p) => {
      page = p
      getList()
    }"/>

    <template #footer>
      <el-button @click="show = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>