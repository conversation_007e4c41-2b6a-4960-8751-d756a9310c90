<script setup>

import {computed, ref, watch} from "vue";
import MyDateRangePicker from "../../../components/MyDateRangePicker.vue";
import {Airlines, FlowCardTypes} from "../../../utils/constmap";
import MyAdvertiserSelector from "../../../components/MyAdvertiserSelector.vue";
import {ElNotification} from "element-plus";
import dayjs from "dayjs";
import {flowcardPurchase} from "../../../api/modules/wifi";

const emit = defineEmits(['close'])
const props = defineProps(['id'])
const show = ref(true)
const isEdit = computed(() => props.id)
const title = computed(() => isEdit.value ? '编辑流量卡采购' : '新增采购')

const detail = ref({
  title: '',
  type: '',
  advertiser_id: '',
  code: '',
})
const formRef = ref(null)
const date = ref('')
const rules = {
  title: [
    {required: true, message: '请输入标题', trigger: 'blur'},
  ],
  type: [
    {required: true, message: '请选择类型', trigger: 'blur'},
  ],
  advertiser_id: [
    {required: true, message: '请选择品牌方', trigger: 'blur'},
  ],
}
const airlines = ref([])

function handleAddAirline() {
  const a = Airlines[0]
  airlines.value.push({
    ...a,
    total_stock: ''
  })
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    if (!date.value) {
      ElNotification.error('请选择有效期！');
      return;
    } else if (airlines.value.length === 0) {
      ElNotification.error('请添加航空公司！');
      return;
    }

    for (const airline of airlines.value) {
      if (!airline.total_stock) {
        const a = Airlines.find(item => item.value === airline.value)

        ElNotification.error('请输入' + a.label + '的库存！');
        return;
      }
    }

    const data = {...detail.value}
    data.start_time = dayjs(date.value[0]).unix()
    data.end_time = dayjs(date.value[1]).unix()

    data.airlines = airlines.value.map(item => {
      return {
        code: item.value,
        stock: Number.parseInt(item.total_stock)
      }
    })
    data.airlines = JSON.stringify(data.airlines)

    flowcardPurchase(data).then(res => {
      ElNotification.success('操作成功！');
      show.value = false
    })
  })
}

watch(show, () => emit('close'))

</script>

<template>
  <el-drawer size="60%" v-model="show" :title="title">
    <el-form ref="formRef" :model="detail" :rules="rules" label-width="80px">
      <el-form-item label="品牌方" prop="advertiser_id">
        <MyAdvertiserSelector v-model="detail.advertiser_id"/>
      </el-form-item>
      <el-form-item label="标题" prop="title">
        <el-input v-model="detail.title"/>
      </el-form-item>
      <el-form-item label="有效期" prop="date">
        <MyDateRangePicker clearable is-next v-model="date"/>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="detail.type">
          <el-option v-for="item in FlowCardTypes" :key="item.value" :value="item.value"
                     :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="航空公司">
        <el-button type="warning" size="small" @click="handleAddAirline">添加</el-button>
        <el-table :data="airlines">
          <el-table-column label="航空公司名称">
            <template #default="{row}">
              <el-select v-model="row.value">
                <el-option v-for="item in Airlines" :key="item.value" :value="item.value"
                           :label="item.label"/>
              </el-select>
            </template>

          </el-table-column>
          <el-table-column label="数量">
            <template #default="{row}">
              <el-input v-model="row.total_stock"/>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{row, $index}">
              <el-button @click="airlines.splice($index, 1)" link size="small" type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>

  </el-drawer>
</template>

<style scoped lang="scss">
.el-select {
  width: 200px;
}
</style>