<template>
  <el-card>

    <el-button @click="() => {
      id = 0
      showDetail = true
    }" type="primary">新增采购
    </el-button>

    <el-table :data="list">
      <el-table-column fixed="left" label="标题" prop="title" />
      <el-table-column label="品牌方" prop="advertiser_name" />
      <el-table-column label="类型" prop="type_text" />
      <el-table-column label="状态">
        <template v-slot="{ row }">
          <el-tag v-if="row.state === 1">{{ row.state_text }}</el-tag>
          <el-tag v-else type="danger">{{ row.state_text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="有效期">
        <template v-slot="{ row }">
          <el-space direction="vertical">
            {{ formatTime(row.start_time) }}
            至
            {{ formatTime(row.end_time) }}
          </el-space>
        </template>
      </el-table-column>
      <el-table-column width="300px" label="库存">
        <template #default="{ row }">
          <div class="stock-item" v-for="item in row.airs" :key="item.code">
            <div>{{ item.name }}</div>
            <div>{{ `总数：${item.total_stock}` }}</div>
            <div>{{ `剩余：${item.left_stock}` }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="created_at" :formatter="tableFormatTime" />
      <el-table-column fixed="right" label="操作">
        <template v-slot="{ row }">
          <el-space wrap>
            <el-button link size="small" type="primary" @click="copyText(row.code)">复制Code</el-button>
            <el-button link size="small" type="primary" @click="handleGetRecord(row)">查看领取记录</el-button>
            <el-button @click="onExport(row)" size="small" type="info"
              v-if="row.type === FlowCardTypeUnique">导出</el-button>
            <el-button @click="handleQrcode(row)" link size="small" v-if="row.type === 1">查看卡片</el-button>

            <el-popconfirm @confirm="handleEnable(row)" title="您确定要进行该项操作吗?">
              <template #reference>
                <el-button size="small" type="primary" v-if="row.state === Disable">上线
                </el-button>
                <el-button size="small" type="danger" v-if="row.state === Enable">下线
                </el-button>
              </template>
            </el-popconfirm>

          </el-space>
        </template>
      </el-table-column>
    </el-table>

    <GetRecord :flowcard-id="id" v-if="showGetRecord" @close="showGetRecord = false" />
    <el-dialog v-model="showQrCard" append-to-body title="查看卡片">
      <el-image :src="cardImageUrl" />
    </el-dialog>
    <MyPagination :total="total" @current-change="(p) => {
      page = 1
      getList()
    }" />

    <FlowCardDetail v-if="showDetail" @close="() => {
      showDetail = false
      getList()
    }" />

  </el-card>
</template>

<script setup>

import { computed, onActivated, ref } from "vue";
import { flowcardEnable, flowcards } from "../../api/modules/wifi";
import { copyText, formatTime, tableFormatTime } from '../../utils/index';
import MyPagination from "../../components/MyPagination.vue";
import { useRouter } from "vue-router";
import GetRecord from "./components/GetRecord.vue";
import FlowCardDetail from "./components/FlowCardDetail.vue";
import { ElNotification } from "element-plus";
import { Disable, Enable, FlowCardTypeUnique } from "../../utils/constmap";

let page = 1
const list = ref([])
const total = ref(0)
const router = useRouter()
const id = ref(0)
const showGetRecord = ref(false)
const showQrCard = ref(false)
const showDetail = ref(false)
const cardImageUrl = computed(() => import.meta.env.VITE_BASE_API + "/api/v1/admin/wifi/cardimage?id=" + id.value)

function handleQrcode(row) {
  id.value = row.id
  showQrCard.value = true
}

function handleGetRecord(row) {
  id.value = row.id
  showGetRecord.value = true
}

function onExport(row) {
  const url = import.meta.env.VITE_BASE_API + '/api/v1/admin/wifi/export?id=' + row.id
  window.open(url)
}

function handleEnable(row) {
  const state = row.state === Disable ? Enable : Disable
  const data = {
    id: row.id,
    state
  }
  console.log(data)
  flowcardEnable(data).then(() => {
    ElNotification.success('操作成功')
    getList()
  })
}

function getList() {
  flowcards({ page }).then(res => {
    const { data } = res

    list.value = data.list
    total.value = data.total
  })
}

onActivated(() => {
  getList()
})

</script>

<style lang="scss" scoped>
.stock-item {
  display: flex;
  gap: 10px;
}
</style>
