<template>
  <el-card>
    <el-space direction="vertical" alignment="normal">
      <div>
        <el-button v-if="!showUpload" @click="showUpload = true" type="primary">导入上网码</el-button>
        <el-form :rules="uploadRules" ref="uploadFormRef" inline v-if="showUpload" :model="uploadForm">
          <el-form-item prop="airline" label="选择航空公司">
            <MyAirlineSelector v-model="uploadForm.airline"/>
          </el-form-item>
          <el-form-item prop="file">
            <input id="file" name="file" type="file">
          </el-form-item>
          <el-form-item>
            <el-button @click="onUpload" type="warning">上传</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-form inline>
        <el-form-item label="上网码">
          <el-input v-model="search.code" placeholder="请输入上网码"/>
        </el-form-item>
        <el-form-item label="航空公司">
          <MyAirlineSelector v-model="search.airline"/>
        </el-form-item>
        <el-form-item label="状态">
          <el-select clearable v-model="search.state">
            <el-option v-for="item in OnlineStates" :label="item.label" :value="item.value" :key="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="() => {
          page = 1
          getList()
        }">查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-space>

    <el-table :data="list">
      <el-table-column label="上网码" prop="code"/>
      <el-table-column label="状态">
        <template #default="{row}">
          <el-tag :type="row.state === Disable ? 'danger' : ''">{{ row.state_text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="领取状态" prop="get_state_text"/>
      <el-table-column label="领取时间" prop="get_at" :formatter="tableFormatTime"/>
      <el-table-column label="创建时间" prop="created_at" :formatter="tableFormatTime"/>
      <el-table-column label="到期时间" prop="end_at" :formatter="tableFormatTime"/>
      <el-table-column label="航空公司" prop="airline_name"/>
      <el-table-column label="操作">
        <template #default="{row}">
          <el-popconfirm @confirm="onRollback(row)" title="您确定要取消领取吗？">
            <template #reference>
              <el-button size="small" type="warning" v-if="row.get_state === 2">取消领取</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <MyPagination :total="total" @current-change="p => {
      page = p
      getList()
    }"/>
  </el-card>
</template>

<script setup>

import {onActivated, ref} from "vue";
import MyPagination from "../../components/MyPagination.vue";
import {Disable, OnlineStates} from "../../utils/constmap";
import {flowcardCodes, flowcardImport, flowcardRollback} from "../../api/modules/wifi";
import {tableFormatTime} from "../../utils";
import {ElNotification} from "element-plus";
import MyAirlineSelector from "../../components/MyAirlineSelector.vue";

const list = ref([])
const total = ref(0)
const showUpload = ref(false)
let page = 1
const search = ref({
  airline: '',
  state: '',
  code: '',
})
const uploadForm = ref({
  airline: '',
})
const uploadFormRef = ref(null)
const uploadRules = {
  airline: [
    {required: true, message: '请选择航空公司'}
  ]
}

function onRollback(row) {
  flowcardRollback({code: row.code}).then(() => {
    ElNotification.success('操作成功')
    getList()
  })
}

function onUpload() {
  uploadFormRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = new FormData()
    data.append('airline', uploadForm.value.airline)

    const file = document.getElementById('file')
    if (file.files.length === 0) {
      ElNotification.error('请选择文件')
      return;
    }

    data.append('file', file.files[0], file.files[0].name)

    flowcardImport(data).then(() => {
      ElNotification.success('操作成功')
      getList()
      showUpload.value = false
    })
  })
}

function getList() {
  const params = {
    page,
    ...search.value
  }
  flowcardCodes(params).then(res => {
    const {data} = res

    list.value = data.list
    total.value = data.total
  })
}

onActivated(() => {
  getList()
})

</script>

<style lang="scss" scoped>
//.upload-form {
//  display: flex;
//  align-items: center;
//}
</style>
