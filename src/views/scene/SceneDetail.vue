<template>
  <el-card>
    <el-form ref="formRef" v-loading="loading" :model="form" :rules="rules" label-width="100px"
             style="max-width: 800px">
      <el-form-item label="景点名称" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      <el-form-item label="所属城市" prop="zone_id">
        <MyZoneSelector v-model="zone_id" :level="3" :multiple="false"/>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="form.address"/>
      </el-form-item>
      <el-form-item label="经纬度" prop="location">
        <el-input v-model="form.location">
          <template #prepend>
            <el-button type="success" @click="getLocation">获取经纬度</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-space>
        <el-form-item label="等级" prop="level">
          <el-select v-model="form.level" placeholder="请选择等级">
            <el-option v-for="item in sceneLevels" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="是否参与模型" prop="llm">
          <el-radio-group v-model="form.llm" placeholder="请选择是否参与模型">
            <el-radio-button v-for="item in llmStates" :key="item.value" :label="item.label" :value="item.value"/>
          </el-radio-group>
        </el-form-item>
      </el-space>

      <el-form-item label="海拔" prop="altitude">
        <el-input v-model="form.altitude"/>
      </el-form-item>
      <el-form-item label="开放时间" prop="open_time">
        <el-input v-model="form.open_time"/>
      </el-form-item>
      <el-form-item label="建议游玩时间" prop="suggest_hour">
        <el-input v-model="form.suggest_hour" style="width: 120px">
          <template #append>小时</template>
        </el-input>
      </el-form-item>
      <el-form-item label="主图" prop="pic">
        <MyImageUpload v-model="form.pic_id" v-model:url="form.pic"/>
      </el-form-item>

      <el-form-item label="其他图片">
        <div>
          <MyUpload accept="image/*" simple @success="handleUploadSuccess"/>
          <div class="pics">
            <MyImageUpload v-for="(item, index) in form.pics"
                           :key="index"
                           v-model="item.res_id"
                           v-model:url="item.url"
                           :can-upload="false"
                           :file="item.file"
                           @remove="() => removePics(index)"/>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="详细描述" prop="desc">
        <MyEditor v-model="form.desc" height="300px"/>
      </el-form-item>
      <el-form-item label="景点类型" prop="cate">
        <MyTagSelector v-model="form.cate" :create="createTag" :fetch="fetchCate" clearable height="300px" multiple/>
      </el-form-item>
      <el-form-item label="景点标签" prop="tags">
        <MyTagSelector v-model="form.tags" :create="createTag" :fetch="fetchTag" clearable height="300px" multiple/>
      </el-form-item>
      <el-form-item label="门票" prop="cost_text">
        <el-input v-model="form.cost_text" style="width:120px"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script setup>
import {computed, onActivated, ref} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {sceneDetail, sceneSave, sceneTaglist} from "../../api/modules/scene"
import {categories} from "../../api/modules/index"
import MyZoneSelector from '../../components/MyZoneSelector.vue';
import {Disable, Enable, SceneLevels} from "../../utils/constmap";
import MyEditor from "../../components/MyEditor.vue"
import MyTagSelector from "../../components/MyTagSelector.vue"
import {ElNotification} from "element-plus"
import {geo} from "../../api/modules";
import MyImageUpload from "../../components/MyImageUpload.vue";
import MyUpload from "../../components/MyUpload.vue";
import {deepToRaw} from "../../utils";


const route = useRoute()
const router = useRouter()
const loading = ref(false)
const llmStates = [
  {label: '是', value: Enable},
  {label: '否', value: Disable},
]
const formDefault = {
  state: Disable,
  address: '',
  location: '',
  llm: Disable,
  pic: '',
  pic_id: 0,
  pics: [],
  remove_pics: [],
}
const formRef = ref(null)
const form = ref({})
const zone_id = computed({
  get: () => form.value.zone_id,
  set: val => {
    form.value = {
      ...form.value,
      zone_id: val[1],
    }
  }
})
const sceneLevels = [{label: '无', value: 0}, ...SceneLevels]

const onSubmit = async () => {
  await formRef.value.validate(valid => {
    if (!valid) {
      return
    }
    const data = deepToRaw(form.value)
    const location = data.location.split(',')
    data.lng = location[0]
    data.lat = location[1]
    data.tags = data.tags.join(',')
    data.cate = data.cate.join(',')
    data.pic = data.pic_id

    // 过滤出 data.pics 中 res_id 存在的图片，并用逗号连接
    data.pics = data.pics
        .filter(pic => pic.res_id)
        .map(pic => pic.res_id)
        .join(',');

    // 将 remove_pics 数组用逗号连接
    data.remove_pics = data.remove_pics.join(',');

    sceneSave(data).then(({data: {id}}) => {
      form.value = {
        ...form.value,
        id,
        pic_id: 0,
      }
      ElNotification.success('数据已保存')
      router.push({
        name: 'Scenes'
      })
    })
  })

}
const rules = computed(() => {
  return {
    name: [{required: true, message: '请输入景点名称'}],
    zone_id: [{required: true, message: '请选择所属城市'}],
    address: [{required: true, message: '请输入详细地址'}],
    location: [{required: true, pattern: /\d+,\d+/, message: '请填写经纬度'}]
  }
})

const fetchCate = (keyword) => {
  return categories({
    type: "scenic",
    keyword,
  }).then(({data: {list}}) => {
    return list.map(i => ({label: i.value, value: i.value}))
  })
}
const fetchTag = (keyword) => {
  return sceneTaglist({
    keyword,
  }).then(({data: {list}}) => {
    return list.map(i => ({label: i.value, value: i.value}))
  })
}
const createTag = (keyword) => {
  return {label: keyword, value: keyword}
}
const getLocation = () => {
  if (form.value.address === '') {
    ElNotification.error('请先输入详细地址')
    return
  }

  geo(form.value.address, form.value.zone_name).then(res => {
    const {data} = res
    form.value.address = data.address
    form.value.location = `${data.lng},${data.lat}`
  })
}

const handleUploadSuccess = (fileInfo) => {
  if (!form.value.pics) {
    form.value.pics = [];
  }

  // 将上传成功的文件信息添加到form.pics数组
  form.value.pics.push({
    res_id: fileInfo.res_id,
    file: fileInfo.file // 直接使用fileInfo.file作为url
  });
};

function removePics(index) {
  const pic = form.value.pics[index];
  if (pic && pic.id) {
    form.value.remove_pics.push(pic.id);
  }
  form.value.pics.splice(index, 1);
}

onActivated(() => {
  Object.assign(form.value, formDefault)

  if (route.query.id > 0) {
    loading.value = true
    sceneDetail({id: route.query.id}).then(({data}) => {
      data.location = data.lng + ',' + data.lat
      data.pics = data.pics.map(item => {
        item.res_id = 0
        return item
      })

      Object.assign(form.value, data)

    }).finally(() => {
      loading.value = false
    })

  }
})

</script>

<style lang="scss" scoped>
.pics {
  margin-top: 10px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
