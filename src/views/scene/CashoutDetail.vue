<script setup>

import {computed, onMounted, ref, watch} from "vue";
import {cashoutAudit, cashoutDetail} from "../../api/modules/cashout";
import {formatMoney, formatTime} from "../../utils";
import {ElMessageBox, ElNotification} from "element-plus";
import dayjs from "dayjs";
import {useUserStore} from "../../store/modules/users";

const emit = defineEmits(['close'])
const props = defineProps(['id'])
const show = ref(true)
const info = ref({})
const stateType = computed(() => {
  if (info.value.state === 3) {
    return 'danger'
  } else if (info.value.state === 2) {
    return 'warning'
  } else if (info.value.state === 4) {
    return 'success'
  }

  return ''
})
const payForm = ref({
  time: new Date(),
})
const payFormRules = {
  time: [{required: true, message: '打款时间是必填的！'}]
}
const payFormRef = ref(null)
const showPayDialog = ref(false)
const userStore = useUserStore()

function handleReject() {
  ElMessageBox.prompt('请输入驳回理由', '驳回', {
    inputPattern: /.+/,
    inputErrorMessage: '请输入驳回理由!'
  }).then(({value}) => {
    audit({state: 3, reason: value})
  })
}

function audit(data) {
  data.id = props.id

  cashoutAudit(data).then(() => {
    ElNotification.success('操作成功')
    show.value = false
  })
}

function handlePay() {
  payFormRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {
      state: 4,
      pay_time: dayjs(payForm.value.time).unix()
    }
    audit(data)
  })
}

watch(show, () => {
  emit('close')
})

onMounted(() => {
  cashoutDetail(props.id).then(res => {
    info.value = res.data
  })
})

</script>

<template>
  <el-drawer v-model="show" size="60%" title="提现详情">
    <el-descriptions title="用户信息">
      <el-descriptions-item label="用户名">{{ info.username }}</el-descriptions-item>
      <el-descriptions-item label="用户ID">{{ info.user_id }}</el-descriptions-item>
      <el-descriptions-item label="用户等级">{{ info.vip_name }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="提现信息">
      <el-descriptions-item label="提现金额">{{ formatMoney(info.amount) }}</el-descriptions-item>
      <el-descriptions-item label="应打款金额">{{ formatMoney(info.actual_amount) }}</el-descriptions-item>
      <el-descriptions-item label="手续费">{{ formatMoney(info.service_fee) }}</el-descriptions-item>
      <el-descriptions-item label="提现前总积分">{{ formatMoney(info.before_amount) }}</el-descriptions-item>
      <el-descriptions-item label="提现时间">{{ formatTime(info.created_at) }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="提现账号">
      <el-descriptions-item label="开户银行">{{ info.bank_name }}</el-descriptions-item>
      <el-descriptions-item label="所属支行">{{ info.bank_branch }}</el-descriptions-item>
      <el-descriptions-item label="银行卡号">{{ info.card_no }}</el-descriptions-item>
      <el-descriptions-item label="真实姓名">{{ info.name }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ info.bank_mobile }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="审核信息">
      <el-descriptions-item label="状态">
        <el-tag :type="stateType">{{ info.state_text }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item v-if="info.state === 3" label="驳回理由">
        {{ info.reason }}
      </el-descriptions-item>
      <el-descriptions-item v-if="info.audit_time" label="审核时间">
        {{ formatTime(info.audit_time) }}
      </el-descriptions-item>
      <el-descriptions-item v-if="info.pay_time" label="打款时间">
        {{ formatTime(info.pay_time) }}
      </el-descriptions-item>
    </el-descriptions>

    <template v-if="userStore.checkActionPermission('cashout_audit')">
      <template v-if="info.state === 1">
        <el-button type="danger" @click="handleReject">驳回</el-button>
        <el-popconfirm title="您确定要执行该项操作吗？" @confirm="() => {
        audit({id: props.id, state: 2})
      }">
          <template #reference>
            <el-button type="primary">通过</el-button>
          </template>
        </el-popconfirm>
        <el-button type="warning" @click="showPayDialog = true">通过并已打款</el-button>
      </template>
      <el-button v-if="info.state === 2" type="warning" @click="showPayDialog = true">已打款</el-button>
    </template>


    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary">确定</el-button>
    </template>

  </el-drawer>

  <el-dialog v-model="showPayDialog" title="确认打款时间" width="260px">
    <el-form ref="payFormRef" :model="payForm" :rules="payFormRules">
      <el-form-item prop="time">
        <el-date-picker v-model="payForm.time" type="datetime"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="showPayDialog = false">取消</el-button>
      <el-button type="primary" @click="handlePay">确定</el-button>
    </template>
  </el-dialog>

</template>

<style lang="scss" scoped>

</style>
