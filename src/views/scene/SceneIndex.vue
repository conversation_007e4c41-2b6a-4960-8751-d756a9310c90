<template>
  <el-card>
    <el-space alignment="normal" direction="vertical">
      <div>
        <el-button type="primary" @click="showCreateNew = true">添加景点</el-button>
      </div>

      <el-form inline>
        <el-form-item label="景点名称">
          <el-input v-model="search.name" clearable placeholder="请输入景点名称"/>
        </el-form-item>
        <el-form-item label="城市">
          <MyZoneSelector v-model="zone_ids" clearable multiple/>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="search.state" clearable placeholder="请选择状态">
            <el-option v-for="item in OnlineStates" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <MyTagSelector v-model="tags" :fetch="fetchTag" clearable multiple/>
        </el-form-item>
        <el-form-item label="景点类型">
          <MyTagSelector v-model="cate" :fetch="fetchCate" clearable multiple/>
        </el-form-item>
        <el-form-item label="等级">
          <el-select v-model="levels" clearable multiple placeholder="请选择等级">
            <el-option v-for="item in SceneLevels" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="ID">
          <el-input v-model="id" clearable placeholder="请输入ID"/>
        </el-form-item>
        <el-form-item label="OTA">
          <el-input v-model="otaId" clearable placeholder="OtaID">
            <template #prepend>
              <MyOtaSelector v-model="ota"/>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </el-space>

    <el-table :data="list">
      <el-table-column fixed="left" label="ID" prop="id"/>
      <el-table-column fixed="left" label="景点名称" prop="name"/>
      <el-table-column label="主图">
        <template #default="{ row }">
          <el-image :src="row.pic" style="width: 80px; height: 80px;"/>
        </template>

      </el-table-column>
      <el-table-column label="标签">
        <template #default="{ row }">
          <el-tag v-for="tag in row.tags" :key="tag">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="{ row }">
          <el-tag :type="row.state === 2 ? 'danger' : 'primary'">{{ row.state_text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="是否参与模型计算">
        <template #default="{ row }">
          <el-tag :type="row.llm === Enable ? 'primary' : 'warning'">
            {{ row.llm === Enable ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="关联OTA">
        <template #default="{ row }">
          <div class="ota">
            <div v-for="item in row.ota">
              <el-tag>{{ item.name }}</el-tag>
              <div v-for="id in item.ids">{{ id }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="所属城市" prop="zone_name"/>
      <el-table-column label="等级">
        <template #default="{ row }">
          <span>{{ 'A'.repeat(row.level) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="景点类型">
        <template #default="{ row }">
          <el-tag v-for="tag in row.cate" :key="tag">{{ tag }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template #default="{ row }">
          <el-space wrap>
            <el-button size="small" type="primary" @click="handleDetail(row.id)">编辑</el-button>

            <el-popconfirm v-if="row.state === Enable" title="您确定要下线吗？" @confirm="handleEnable(row.id, Disable)">
              <template #reference>
                <el-button size="small" type="danger">下线</el-button>
              </template>
            </el-popconfirm>
            <el-button v-else size="small" type="primary" @click="handleEnable(row.id, Enable)">上线</el-button>

            <el-button link size="small" type="primary" @click="handleOtaBind(row)">OTA绑定</el-button>

          </el-space>

        </template>
      </el-table-column>
    </el-table>
    <MyPagination :page="page" :total="total" @current-change="p => {
      page = p
      getList()
    }"/>
    <NewSceneDialog v-if="showCreateNew" @close="() => {
      showCreateNew = false
    }"/>
    <OtaBind :id="otaBindId" v-model="otaBindShow" @save="handleOtaBindSave"/>
  </el-card>

</template>

<script setup>
import {onActivated, ref} from "vue";
import {useRouter} from 'vue-router'
import MyZoneSelector from "../../components/MyZoneSelector.vue";
import {Disable, Enable, OnlineStates, SceneLevels} from "@/utils/constmap";
import MyPagination from "../../components/MyPagination.vue";
import {sceneEnable, scenes, sceneTaglist} from "@/api/modules/scene";
import {categories} from "../../api/modules/index"
import {ElNotification} from "element-plus";
import MyTagSelector from "../../components/MyTagSelector.vue";
import MyOtaSelector from "../../components/MyOtaSelector.vue";
import OtaBind from "./components/OtaBind.vue";
import NewSceneDialog from "@/views/scene/components/NewSceneDialog.vue";

const router = useRouter()
const list = ref([])
const total = ref(0)
const page = ref(1)
const otaBindShow = ref(false)
const otaBindId = ref(0)
const showCreateNew = ref(false)

const search = ref({
  name: '',
  state: ''
})
const zone_ids = ref([])
const levels = ref([])
const tags = ref([])
const cate = ref([])
const id = ref('')
const ota = ref('')
const otaId = ref('')

const handleOtaBindSave = () => {
  getList()
}
const handleOtaBind = (row) => {
  otaBindShow.value = true
  otaBindId.value = row.id
}

function getList() {
  const params = {
    ...search.value,
    page: page.value,
    level: levels.value.join(','),
    zone_id: zone_ids.value.map(v => v[1]).join(','),
    tags: tags.value.join(','),
    cate: cate.value.join(','),
    id: id.value,
    ota: ota.value,
    ota_id: otaId.value
  }

  scenes(params).then(res => {
    const {data} = res

    total.value = data.total
    list.value = data.list.map(item => {
      item.ota = []
      for (const [k, v] of Object.entries(item.ota_map)) {
        item.ota.push({
          name: k,
          ids: v
        })
      }
      return item
    })
  })
}

function handleEnable(id, state) {
  sceneEnable(id, state).then(() => {
    ElNotification.success('修改成功')
    getList()
  })
}

const fetchCate = (keyword) => {
  return categories({
    type: 'scenic',
    keyword
  }).then(({data: {list}}) => {
    return list.map(i => ({label: i.value, value: i.value}))
  })
}
const fetchTag = (keyword) => {
  return sceneTaglist({
    keyword
  }).then(({data: {list}}) => {
    return list.map(i => ({label: i.value, value: i.value}))
  })
}

function handleSearch() {
  page.value = 1
  getList()
}

function handleDetail(id) {
  router.push({
    name: 'ScenesDetail',
    query: {id: id || ''}
  })
}

onActivated(() => {
  getList()
})

</script>

<style lang="scss" scoped>
.el-select {
  width: 200px;
}

.ota {
  > div {
    border-bottom: 1px solid;

    &:last-child {
      border-bottom: none;
    }
  }
}

</style>
