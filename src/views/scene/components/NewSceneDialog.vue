<script setup>

import {ref, toRaw, watch} from "vue";
import MyZoneSelector from "@/components/MyZoneSelector.vue";
import {ElNotification} from "element-plus";
import {geo} from "@/api/modules";
import {zoneDetail} from "@/api/modules/setting";
import {sceneSave} from "@/api/modules/scene";
import {useRouter} from "vue-router";

const emit = defineEmits(['close'])

const router = useRouter()
const show = ref(true)
const formRef = ref(null)
const formData = ref({
  name: '',
  zone_id: '',
  address: '',
  location: '',
})

const rules = {
  name: [{required: true, message: '请输入景点名称'}],
  zone_id: [{required: true, message: '请选择所属城市'}],
  address: [{required: true, message: '请输入详细地址'}],
  location: [{required: true, pattern: /\d+,\d+/, message: '请填写经纬度'}]
}

const getLocation = async () => {
  if (formData.value.address === '' || formData.value.zone_id.length === 0) {
    ElNotification.error('请先输入详细地址，并且选择所属城市')
    return
  }

  const zone = await zoneDetail(formData.value.zone_id[formData.value.zone_id.length - 1])
  const cityName = zone.data.name

  geo(formData.value.address, cityName).then(res => {
    const {data} = res
    formData.value.location = `${data.lng},${data.lat}`
  })
}

function onSubmit() {
  formRef.value.validate(async (valid) => {
    if (!valid) {
      return
    }
    const data = toRaw(formData.value)
    data.zone_id = data.zone_id[data.zone_id.length - 1]

    const location = data.location.split(',')
    data.lng = location[0]
    data.lat = location[1]

    sceneSave(data).then(res => {
      const {data} = res

      ElNotification.success('数据已保存')
      router.push({
        name: 'ScenesDetail',
        query: {id: data.id}
      })
      show.value = false
    })

  })
}

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" title="新增景点">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="景点名称" prop="name">
        <el-input v-model="formData.name"/>
      </el-form-item>
      <el-form-item label="所属城市" prop="zone_id">
        <MyZoneSelector v-model="formData.zone_id" :level="3" :multiple="false"/>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="formData.address"/>
      </el-form-item>
      <el-form-item label="经纬度" prop="location">
        <el-input v-model="formData.location">
          <template #prepend>
            <el-button type="success" @click="getLocation">获取经纬度</el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">保存</el-button>
    </template>

  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
