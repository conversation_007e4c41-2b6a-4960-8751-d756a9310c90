<template>
    <el-dialog v-model="show" title="OTA绑定">
        <el-button type="primary" @click="handleNew">新增绑定关系</el-button>
        <el-form ref="formRef" :model="{ list: list }">
            <el-table v-loading="loading" :data="list">
                <el-table-column label="OTA">
                    <template #default="{ row, $index }">
                        <template v-if="!row.new">{{ row.ota_code_text }}</template>
                        <el-form-item v-else :prop="['list', $index, 'ota_code']"
                            :rules="[{ required: true, message: '请选择OTA' }]">
                            <MyOtaSelector v-model="row.ota_code" :clearable="false"></MyOtaSelector>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="OTA ID">
                    <template #default="{ row, $index }">
                        <template v-if="!row.new">{{ row.ota_id }}</template>
                        <el-form-item v-else :prop="['list', $index, 'ota_id']"
                            :rules="[{ required: true, message: '请填写OTA ID' }]">
                            <el-input v-model="row.ota_id"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default="{ $index }">
                        <el-button link type="danger" @click="handleDelete($index)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-form>
        <template #footer>
            <el-button @click="show = false">取消</el-button>
            <el-button @click="handleSubmit" type="primary">保存</el-button>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref, watch, defineEmits } from "vue"
import { otaList, otaBindSave } from "../../../api/modules/scene";
import MyOtaSelector from "../../../components/MyOtaSelector.vue";
import { ElNotification } from "element-plus";

const show = defineModel({ type: Boolean, default: false })
const loading = ref(false)
const list = ref([])
const formRef = ref(null)
const props = defineProps({
    id: {
        type: Number,
    }
})
const emit = defineEmits(['save'])
const handleNew = () => {
    list.value.push({
        ota_id: '',
        ota_code: '',
        new: true,
    })
}
const handleDelete = (idx) => {
    list.value.splice(idx, 1)
}
const handleSubmit = () => {
    if (loading.value) return

    formRef.value.validate(async (valid) => {
        if (!valid) return

        loading.value = true
        try {
            await otaBindSave({
                scenic_id: props.id,
                otas: JSON.stringify(list.value),
            })
            emit('save', props.id)
            show.value = false
        } catch (e) {
            ElNotification.error('保存失败:' + JSON.stringify(e))
        }
        loading.value = false
    })
}
const getList = async () => {
    if (loading.value) return
    loading.value = true
    try {
        const { data: { list: l } } = await otaList({ scenic_id: props.id })
        list.value = l
    } catch (e) {
        console.log(e)
    }
    loading.value = false
}
watch(() => [props.id, show.value], () => {
    if (show.value && props.id > 0) getList()
})
</script>