<script setup>

import {onMounted, ref, toRaw, watch} from "vue";
import {orderPickup, submitPickup} from "../../api/modules/order";
import {formatMoney, formatTime, tableFormatMoney} from "../../utils";
import {ElNotification} from "element-plus";
import MyAddProductDialog from "../../components/MyAddProductDialog.vue";

const props = defineProps({
  id: Number,
  go: Boolean,
})
const emit = defineEmits(['close'])
const show = ref(true)
const pickup = ref({
  created_at: '',
  state_text: '',
  state: '',
  freight: '',
  list: [],
  address: '',
  name: '',
  city: '',
  province: '',
  tel: '',
  remarks: '',
  delivery_code: '',
  delivery_no: '',
  is_self_pickup: false,
})
const formRef = ref(null)
const form = ref({
  remarks: '',
})
const rules = []
const showAddProductDialog = ref(false)

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = toRaw(form.value)
    data.id = props.id
    data.same_address = data.same_address ? 1 : 0
    if (data.zone_id instanceof Array) {
      data.zone_id = data.zone_id[data.zone_id.length - 1]
    }
    const products = pickup.value.list.map((item) => {
      item.quantity = Number.parseInt(item.quantity)
      return item
    })

    data.products = JSON.stringify(products)

    submitPickup(data).then(() => {
      ElNotification.success('操作成功')
      show.value = false
    })
  })
}

onMounted(() => {
  orderPickup(props.id).then(res => {
    const {data} = res
    Object.assign(pickup.value, data)
    Object.assign(form.value, {
      zone_id: data.zone_id,
      address: data.address,
      remarks: data.remarks
    })
  })
})

function handleAddProduct(product) {
  if (product) {
    const old = pickup.value.list.find((item) => item.product_id === product.product_id)
    if (old) {
      old.quantity = product.quantity
    } else {
      pickup.value.list.push({
        name: product.name,
        price: product.price,
        quantity: product.quantity,
        product_id: product.product_id,
      })
    }

  }

  showAddProductDialog.value = false
}

watch(show, () => {
  emit('close')
})

</script>

<template>
  <el-drawer v-model="show" size="80%" title="发货单">
    <el-descriptions title="申请信息">
      <el-descriptions-item label="申请时间">
        {{ formatTime(pickup.created_at) }}
        <el-tag v-if="pickup.is_self_pickup" type="danger">自提</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="pickup.state === 1 ? 'danger' : '' ">{{ pickup.state_text }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="运费">
        {{ formatMoney(pickup.freight) }}
      </el-descriptions-item>
      <template v-if="!pickup.is_self_pickup">
        <el-descriptions-item label="联系人">
          {{ pickup.name }}
        </el-descriptions-item>
        <el-descriptions-item label="电话">
          {{ pickup.tel }}
        </el-descriptions-item>
        <el-descriptions-item label="地址">
          {{ `${pickup.province}${pickup.city}${pickup.address}` }}
        </el-descriptions-item>
      </template>
    </el-descriptions>

    <el-descriptions>
      <el-descriptions-item>
        <el-button type="primary" @click="showAddProductDialog = true">添加商品</el-button>
        <el-table :data="pickup.list">
          <el-table-column label="商品名称">
            <template #default="{row}">
              <el-input v-model="row.name"/>
              <el-tag v-if="row.gift" style="margin-top: 12px;" type="danger">赠品</el-tag>
            </template>
          </el-table-column>
          <el-table-column :formatter="tableFormatMoney" label="单价" prop="price"></el-table-column>
          <el-table-column label="发货数量" prop="quantity">
            <template #default="{row}">
              <el-input v-model="row.quantity" size="small" style="width: 100px;"/>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{row, $index}">
              <el-popconfirm , title="您确定要删除吗？" @confirm="() => {
                pickup.list.splice($index, 1)
              }">
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions :column="1" title="配送信息">
      <el-descriptions-item v-if="props.go">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-form-item label="备注">
            <el-input v-model="form.remarks" type="textarea"/>
          </el-form-item>
        </el-form>
      </el-descriptions-item>
    </el-descriptions>

    <MyAddProductDialog v-if="showAddProductDialog" @close="handleAddProduct"/>

    <template v-if="props.go" #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>
