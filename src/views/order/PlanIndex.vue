<script setup>
import {onActivated, ref} from "vue";
import MyPagination from "../../components/MyPagination.vue";
import {tableFormatTime} from "../../utils";

import {useRouter} from "vue-router";
import {plans} from "../../api/modules/plan";

const props = defineProps({
  isUpgrade: <PERSON><PERSON><PERSON>,
  isMeal: {
    type: <PERSON>olean,
    default: true,
  },
  isNormal: <PERSON>olean,
})

const list = ref([])
const total = ref(0)
let page = 1
const router = useRouter()
const id = ref('')

function handlePage(p) {
  page = p
  getList()
}

function getList() {
  const params = {}

  plans(params).then(res => {
    const {data} = res
    list.value = data.list
    total.value = data.total
  })
}

function handleDetail(row) {
  router.push({
    name: 'OrderDetail',
    query: {id: row.order_id}
  })
}

onActivated(() => {
  getList()
})

</script>

<template>
  <el-card>
    <el-table :data="list" class="order-list">
      <el-table-column fixed label="联系人">
        <template #default="{row}">
          <div>{{ row.contacts_name }}</div>
          <div>{{ row.contacts_tel }}</div>
        </template>
      </el-table-column>
      <el-table-column label="出发地-目的地">
        <template #default="{row}">
          {{ `${row.from_zone_name}-${row.to_zone_name}` }}
        </template>
      </el-table-column>
      <el-table-column label="人数" prop="peoples"/>
      <el-table-column :formatter="tableFormatTime" label="咨询时间" prop="created_at"/>
    </el-table>

    <MyPagination :total="total" @current-change="handlePage"/>
  </el-card>
</template>

<style lang="scss" scoped>

</style>
