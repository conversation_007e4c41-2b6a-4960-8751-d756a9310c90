<script setup>
import {onActivated, ref} from "vue";
import {orders} from "../../api/modules/order"
import MyPagination from "../../components/MyPagination.vue";
import {deepToRaw, formatTime, tableFormatMoney, tableFormatTime} from "../../utils";

import {useRouter} from "vue-router";
import {OrderStates, OrderTypes} from "@/utils/constmap";
import MyDateRangePicker from "@/components/MyDateRangePicker.vue";
import dayjs from "dayjs";

const search = ref({
  order_id: '',
  phone: '',
  type: '',
  state: '',
  date: '',
})

const list = ref([])
const total = ref(0)
let page = 1
const router = useRouter()
const id = ref('')

function handlePage(p) {
  page = p
  getList()
}

function getList() {
  const params = {page, ...deepToRaw(search)}
  if (params.state instanceof Array) {
    params.state = params.state.join(',')
  }
  if (params.date instanceof Array) {
    params.start_time = dayjs(params.date[0]).unix()
    params.end_time = dayjs(params.date[1]).unix()
    delete params.date
  }

  orders(params).then(res => {
    const {data} = res
    list.value = data.list
    total.value = data.total
  })
}

function handleDetail(row) {
  router.push({
    name: 'OrderDetail',
    query: {id: row.order_id}
  })
}

onActivated(() => {
  getList()
})

</script>

<template>
  <el-card>
    <el-form inline>
      <el-form-item label="订单ID">
        <el-input v-model="search.order_id" clearable placeholder="请输入订单号"/>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="search.phone" clearable placeholder="请输入手机号"/>
      </el-form-item>
      <el-form-item label="订单类型">
        <el-select v-model="search.type" clearable>
          <el-option v-for="item in OrderTypes" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态">
        <el-select v-model="search.state" clearable multiple>
          <el-option v-for="item in OrderStates" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="下单时间">
        <MyDateRangePicker v-model="search.date" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList">查询</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list" class="order-list">
      <el-table-column fixed label="订单号" prop="order_id"/>
      <el-table-column fixed label="联系人">
        <template #default="{row}">
          <div>{{ row.contacts_name }}</div>
          <div>{{ row.contacts_tel }}</div>
        </template>
      </el-table-column>
      <el-table-column label="标题">
        <template #default="{row}">
          <el-tooltip :content="row.title" placement="top-start">
            <div class="title">{{ row.title }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="预约团期">
        <template #default="{row}">{{ formatTime(row.date, 'YYYY-MM-DD') }}</template>
      </el-table-column>
      <el-table-column label="详情">
        <template #default="{row}">
          <div v-for="item in row.details">
            <div>{{ `${item.title}：${item.quantity}` }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="state_text"/>
      <el-table-column :formatter="tableFormatTime" label="下单时间" prop="created_at"/>
      <el-table-column :formatter="tableFormatMoney" label="订单金额" prop="total_amount"/>
      <el-table-column fixed="right" label="操作">
        <template #default="{row}">
          <el-space wrap>
            <el-button link type="primary" @click="handleDetail(row)">详情</el-button>
          </el-space>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="handlePage"/>
  </el-card>
</template>

<style lang="scss" scoped>
.order-list {
  .title {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
