* {
  box-sizing: border-box;
}

.print-container {
  width: 98%;
  margin-left: 10px;

  h2 {
    font-size: 1.5em;
  }

  h2, .my-address {
    text-align: center;
  }

  .title {
    display: flex;
    margin-top: 10px;

    > div {
      flex: 1;
    }

    .right {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  .customer-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
  }

  @media print {
    table {
      border: 1px solid black;
      width: 100%;
      border-collapse: collapse;
      margin: 10px 0;
      table-layout: auto;

      th, td {
        border: 1px solid black;
        padding: 10px;
        text-align: left;
      }
    }
  }

  table {
    border: 1px solid black;
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
    table-layout: auto;

    th, td {
      border: 1px solid;
      padding: 10px;
      text-align: left;
    }
  }

  .footer {
    display: flex;
    justify-content: space-between;

    > div {
      flex: 1;
    }
  }
}
