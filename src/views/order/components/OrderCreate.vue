<script setup>

import {computed, ref, toRaw, watch} from "vue";
import MyMealSelector from "../../../components/MyMealSelector.vue";
import {mealDetail} from "../../../api/modules/meal";
import {ElNotification} from "element-plus";
import {orderCreate} from "../../../api/modules/order";
import {users, userSave} from "../../../api/modules/user";
import {checkMobile} from "../../../utils/validator";

const emit = defineEmits(['close'])

const form = ref({
  user_id: '',
  meal_id: '',
  settlement: 1,
  amount: '',
})
const orderInfo = ref({})
const rules = computed(() => {
  return {
    meal_id: [{required: true, message: '套餐是必选的！'}],
    amount: [{required: true, message: '订单金额是必填的！'}],
  }
})
const products = ref([])
const formRef = ref(null)
const show = ref(true)
const loading = ref(false)
const userOptions = ref([])
const showCreateUser = ref(false)
const formUser = ref({
  mobile: '',
  name: '',
  zone_id: '',
})
const formUserRef = ref(null)
const formUserRule = {
  mobile: [
    {required: true, message: '手机号码是必填的！'},
    {validator: checkMobile}
  ],
  name: [{required: true, message: '姓名是必填的！'}],
  zone_id: [{required: true, message: '地区是必选的！'}],
}

function handleQueryUser(queryString) {
  loading.value = true
  users({mobile: queryString}).then(res => {
    const {data} = res
    loading.value = false

    userOptions.value = data.list
  })
}

function handleCreateUser() {
  formUserRef.value.validate(valid => {
    if (!valid) {
      return
    }
    const data = {...formUser.value}
    data.zone_id = toRaw(data.zone_id[data.zone_id.length - 1])

    userSave(data).then(res => {
      const {data} = res

      userOptions.value = [data]
      form.value.user_id = data.id
      showCreateUser.value = false
    })

  })
}

function handleOk() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    } else if (products.value.length === 0) {
      ElNotification.error('请选择订单商品')
      return
    }

    const data = {...form.value}
    data.settlement = data.settlement ? 1 : 2
    data.products = JSON.stringify(products.value)
    data.amount *= 100

    orderCreate(data).then(res => {
      ElNotification.success('订单创建成功')

      orderInfo.value = res.data
      show.value = false
    })
  })
}

watch(() => form.value.meal_id, (mealId) => {
  if (!mealId) {
    products.value = []
    return
  }

  mealDetail(mealId).then(res => {
    const {data} = res
    products.value = data.products.map(item => {
      item.gift = false
      return item
    })
    const gifts = data.gifts.map(item => {
      item.gift = true
      return item
    })
    products.value.push(...gifts)

    form.value.amount = data.price / 100
  })
})

watch(show, () => emit('close', {...orderInfo.value}))

</script>

<template>
  <el-dialog v-model="show" append-to-body title="创建订单" width="80%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120">
      <el-form-item label="用户手机号码" prop="mobile">
        <el-space>
          <el-select v-model="form.user_id" :loading="loading" :remote-method="handleQueryUser" clearable filterable
                     placeholder="输入手机号码搜索"
                     remote
                     style="width: 200px;">
            <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id">
              {{ `${item.mobile}(${item.name})` }}
            </el-option>
            <template #footer>
              <el-button size="small" type="warning" @click="showCreateUser = true">用户不存在，创建用户</el-button>
            </template>
          </el-select>

        </el-space>
      </el-form-item>
      <el-form-item label="套餐" prop="meal_id">
        <MyMealSelector v-model="form.meal_id" state="1" style="width: 300px;"/>
      </el-form-item>
      <el-form-item label="选择商品">
        <el-table :data="products">
          <el-table-column label="商品名称" prop="name"/>
          <el-table-column label="是否赠品">
            <template #default="{row}">
              <el-tag v-if="row.gift" type="danger">是</el-tag>
              <el-tag v-else>否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="数量">
            <template #default="{row}">
              <el-input v-model="row.quantity"/>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{row, $index}">
              <el-button link type="danger" @click="() => {
                products.splice($index,1)
              }">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="订单金额" prop="amount">
        <el-input v-model="form.amount" style="width: 100px;"/>
      </el-form-item>
      <el-form-item label="是否结算">
        <el-radio-group v-model="form.settlement">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="2">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-popconfirm title="您确定要创建该订单吗？" @confirm="handleOk">
        <template #reference>
          <el-button type="primary">确定</el-button>
        </template>
      </el-popconfirm>
    </template>

  </el-dialog>

  <el-dialog v-model="showCreateUser" append-to-body title="创建用户">
    <el-form ref="formUserRef" :model="formUser" :rules="formUserRule" label-position="top">
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="formUser.mobile"/>
      </el-form-item>
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formUser.name"/>
      </el-form-item>
      <el-form-item label="所属城市" prop="zone_id">
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showCreateUser = false">取消</el-button>
      <el-button type="primary" @click="handleCreateUser">确定</el-button>
    </template>
  </el-dialog>

</template>

<style lang="scss" scoped>

</style>
