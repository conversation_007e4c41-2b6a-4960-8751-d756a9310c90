<script setup>
import {onMounted, ref, watch} from "vue";
import {applyRefund, payment} from "../../../api/modules/order";
import {ElNotification} from "element-plus";

const showRefund = ref(true)
const emit = defineEmits(['close'])
const props = defineProps(['payId'])
const form = ref({
  pay_id: '',
  refund_amount: '',
  refund_reason: '',
})
const rules = {
  refund_amount: [
    {required: true, message: '退款金额是必填的！'},
  ],
  refund_reason: [
    {required: true, message: '退款原因是必填的！'},
  ],
}
const formRef = ref(null)
const refundReasons = [
  '用户申请退款',
  '其他原因退款'
]

onMounted(() => {
  payment(props.payId).then(res => {
    const {data} = res

    form.value.refund_amount = data.pay_price / 100
    form.value.pay_id = data.pay_id
  })
})

function handleDo() {
  const data = {...form.value}
  data.refund_amount *= 100

  applyRefund(data).then(() => {
    ElNotification.success('申请成功')
    showRefund.value = false
  })
}

watch(showRefund, () => emit('close'))

</script>

<template>
  <el-dialog v-model="showRefund" title="申请退款">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="退款金额" prop="refund_amount">
        <el-input v-model="form.refund_amount"/>
      </el-form-item>
      <el-form-item label="退款原因">
        <el-select v-model="form.refund_reason" allow-create filterable>
          <el-option v-for="(item, index) in refundReasons" :key="index" :label="item" :value="item"/>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showRefund = false">取消</el-button>
      <el-popconfirm title="您确定要退款吗？" @confirm="handleDo">
        <template #reference>
          <el-button type="primary">确定</el-button>
        </template>
      </el-popconfirm>
    </template>
  </el-dialog>

</template>

<style lang="scss" scoped>

</style>
