<script setup>

import {computed, inject, onMounted} from "vue";
import {
  Disable,
  Enable,
  OrderSubTypeTuan,
  RefundOrderComplete,
  RefundOrderProcessing,
  RefundOrderWaitReview,
  RefundSubOrderAbnormal,
  RefundSubOrderReject,
  RefundSubOrderWaitReview
} from "@/utils/constmap";
import {formatMoney, formatTime} from "@/utils";
import {ElMessageBox, ElNotification} from "element-plus";
import {refundRetry, refundReview} from "@/api/modules/order";

const emit = defineEmits(['refresh'])

const orderInfo = inject('orderInfo')
const refundOrders = computed(() => orderInfo.value.refund_orders ?? [])

function stateTagType(state) {
  switch (state) {
    case RefundOrderWaitReview:
      return 'info'
    case RefundOrderComplete:
      return 'success'
    default:
      return 'primary'
  }
}

function onRejectItem(item) {
  ElMessageBox.prompt('请输入拒绝原因', '拒绝退款', {
    inputValidator: (value) => {
      // 验证逻辑：输入值不能为空
      if (!value) {
        return '拒绝原因不能为空'; // 返回错误提示信息
      }
      return true; // 验证通过
    }
  }).then(({value}) => {
    const data = {
      refund_order_id: item.refund_order_id,
      refund_suborder_id: item.id,
      is_agree: Disable,
      reject_reason: value
    }
    refundReview(data).then(res => {
      ElNotification.success('操作成功')
      emit('refresh')
    })
  })
}

function onRetryItem(item) {
  const data = {
    refund_order_id: item.refund_order_id,
    refund_suborder_id: item.id,
  }
  refundRetry(data).then(() => {
    ElNotification.success('操作成功')
    emit('refresh')
  })
}

function onApprovedItem(item) {
  if (item.type === OrderSubTypeTuan) {
    const opt = {
      inputValue: item.refund_amount / 100,
      inputValidator: (value) => {
        // 验证逻辑：输入值不能为空
        if (!value) {
          return '退款金额不能为空'; // 返回错误提示信息
        }
        return true; // 验证通过
      }
    }
    ElMessageBox.prompt('请输入退款金额', '同意退款', opt).then(({value}) => {
      const data = {
        refund_order_id: item.refund_order_id,
        refund_suborder_id: item.id,
        is_agree: Enable,
      }
      if (item.type === OrderSubTypeTuan) {
        data.refund_amount = Number.parseFloat(value) * 100
      }

      refundReview(data).then(res => {
        ElNotification.success('操作成功')
        emit('refresh')
      })
    })
  } else {
    ElMessageBox.confirm('您确定要同意退款？', '同意退款').then(() => {
      const data = {
        refund_order_id: item.refund_order_id,
        refund_suborder_id: item.id,
        is_agree: Enable,
      }
      refundReview(data).then(res => {
        ElNotification.success('操作成功')
        emit('refresh')
      })
    })
  }
}

function onBatchReview(refund_order_id, is_agree) {
  if (is_agree === Disable) {
    ElMessageBox.prompt('请输入拒绝原因', '批量拒绝退款', {
      inputValidator: (value) => {
        // 验证逻辑：输入值不能为空
        if (!value) {
          return '拒绝原因不能为空'; // 返回错误提示信息
        }
        return true; // 验证通过
      }
    }).then(({value}) => {
      const data = {
        refund_order_id,
        is_agree,
        reject_reason: value
      }
      refundReview(data).then(res => {
        ElNotification.success('操作成功')
        emit('refresh')
      })
    })
  } else {
    const data = {
      refund_order_id,
      is_agree,
    }
    refundReview(data).then(res => {
      ElNotification.success('操作成功')
      emit('refresh')
    })
  }
}

onMounted(() => {
  console.log(refundOrders.value)
})

</script>

<template>
  <el-descriptions title="退款信息">
    <el-descriptions-item>
      <div class="list">
        <div v-for="(refund, index) in refundOrders" :key="index" class="refund-item">
          <div class="status">
            <div>
              退款单号：{{ refund.id }}，申请时间：{{ formatTime(refund.created_at) }}
              申请退款金额：{{ formatMoney(refund.apply_refund_amount) }}
            </div>
            <div>状态：
              <el-tag :type="stateTagType(refund.state)">{{ refund.state_text }}</el-tag>
            </div>
          </div>
          <el-table :data="refund.subs" row-key="id">
            <el-table-column label="图片">
              <template #default="{row}">
                <el-image :src="row.pic" class="pic"/>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="type_text"/>
            <el-table-column label="商品名称" prop="title"/>
            <el-table-column label="规格" prop="sku_name"/>
            <el-table-column label="日期">
              <template #default="{row}">
                {{ formatTime(row.start_date, 'YYYY-MM-DD') }}
                <template v-if="row.end_date !== row.start_date ">
                  - {{ formatTime(row.end_date, 'YYYY-MM-DD') }}
                </template>
              </template>
            </el-table-column>
            <el-table-column label="状态">
              <template #default="{row}">
                <el-tag>{{ row.state_text }}</el-tag>
                <div v-if="row.state === RefundSubOrderReject || row.state === RefundSubOrderAbnormal">
                  {{ row.reject_reason }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="退款明细">
              <template #default="{row}">
                <div>退款金额：{{ formatMoney(row.refund_amount) }}</div>
                <div>OTA退款金额：{{ formatMoney(row.ota_refund_amount) }}</div>
                <div>OTA退款罚金：{{ formatMoney(row.ota_penalty_amount) }}</div>
              </template>
            </el-table-column>
            <el-table-column label="退款数量" prop="quantity"/>
            <el-table-column label="操作">
              <template #default="{row}">
                <el-space v-if="row.state === RefundSubOrderWaitReview" wrap>
                  <el-button size="small" type="primary" @click="onApprovedItem(row)">同意</el-button>
                  <el-button size="small" type="danger" @click="onRejectItem(row)">拒绝</el-button>
                </el-space>
                <el-popconfirm title="您确定要重新退款吗？" @confirm="() => onRetryItem(row)">
                  <template #reference>
                    <el-button v-if="row.state === RefundSubOrderAbnormal" size="small" type="warning">重新退款
                    </el-button>
                  </template>
                </el-popconfirm>

              </template>
            </el-table-column>
          </el-table>
          <div v-if="[RefundOrderWaitReview,RefundOrderProcessing].includes(refund.state)" class="dropdown">
            <el-popconfirm placement="top" title="您确定要全部同意吗？" @confirm="onBatchReview(refund.id, Enable)">
              <template #reference>
                <el-button type="primary">全部同意</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm placement="top" title="您确定要全部拒绝吗？" @confirm="onBatchReview(refund.id, Disable)">
              <template #reference>
                <el-button type="danger">全部拒绝</el-button>
              </template>
            </el-popconfirm>
          </div>
        </div>
      </div>

    </el-descriptions-item>
  </el-descriptions>
</template>

<style lang="scss" scoped>
.list {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 定义两列，每列占据一半的空间 */
  gap: 10px;
}

.refund-item {
  border: 1px solid #ccc;
  padding: 10px;
  box-sizing: border-box;

  .pic {
    max-width: 100px;
    max-height: 100px;
  }

  .dropdown {
    margin-top: 10px;
  }

  .status {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}

</style>