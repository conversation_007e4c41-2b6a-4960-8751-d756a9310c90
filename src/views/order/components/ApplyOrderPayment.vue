<script setup>

import {computed, onMounted, ref, watch} from "vue";
import {orderDetail, orderPay} from "../../../api/modules/order";
import {PayMethods} from "../../../utils/constmap";
import {ElNotification} from "element-plus";
import {formatMoney} from "../../../utils";

const emit = defineEmits(['close'])
const props = defineProps({
  id: Number,
})
const show = ref(true)
const form = ref({
  method: 2,
  amount: '',
  remarks: '',
  third_party_no: '',
})
const detail = ref({
  payments: [],
})
const formRef = ref(null)
const rules = computed(() => {
  return {
    method: [{required: true, message: '支付方式是必选的！'}],
    remarks: [{required: true, message: '备注是必填的！'}],
    amount: [
      {required: true, message: '付款金额必填的！'},
      {min: 0, message: '付款金额不能小于0'},
    ]
  }
})
const needPayAmount = computed(() => {
  let r = detail.value.need_pay_amount
  detail.value.payments.forEach(item => {
    r -= item.actual_pay_amount
  })

  return r
})

function handleUpload(file) {
  form.value.file_res_id = file.res_id
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {...form.value}
    data.amount *= 100
    data.order_id = props.id

    orderPay(data).then(() => {
      ElNotification.success('操作成功')
      show.value = false
    })
  })
}

watch(show, () => emit('close'))

onMounted(() => {
  orderDetail({order_id: props.id}).then(res => {
    const {data} = res
    Object.assign(detail.value, data)
  })
})

</script>

<template>
  <el-dialog v-model="show" title="订单支付">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120">
      <el-form-item label="支付方式" prop="method">
        <el-select v-model="form.method" style="width: 200px;">
          <el-option v-for="item in PayMethods" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="还需支付">
        {{ formatMoney(needPayAmount) }}
      </el-form-item>
      <el-form-item label="付款金额" prop="amount">
        <el-input v-model="form.amount" style="width: 100px"/>
      </el-form-item>
      <el-form-item label="第三方交易单号" prop="third_party_no">
        <el-input v-model="form.third_party_no"/>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input v-model="form.remarks"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
