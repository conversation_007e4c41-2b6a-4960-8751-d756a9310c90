<script setup>

import {computed, onMounted, ref, watch} from "vue";
import {tableFormatMoney} from "../../../utils";
import MyAddProductDialog from "../../../components/MyAddProductDialog.vue";
import {ElNotification} from "element-plus";
import {orderDetail, submitPickup} from "../../../api/modules/order";

const show = ref(true)
const props = defineProps({
  orderId: {
    type: Number,
    required: true
  }
})
const emit = defineEmits(['close'])

const pickup = ref({
  address: '',
  name: '',
  tel: '',
  remarks: '',
  is_self_pickup: false,
  free_freight_num: 0,
  z: [],
})
const formRef = ref(null)
const list = ref([])
const rules = computed(() => {
  const r = {}
  if (!pickup.value.is_self_pickup) {
    r.address = [
      {required: true, message: '请填写详细地址', trigger: 'blur'},
    ]
    r.name = [
      {required: true, message: '请填写联系人', trigger: 'blur'},
    ]
    r.tel = [
      {required: true, message: '请填写联系电话', trigger: 'blur'},
    ]
    r.z = [
      {required: true, message: '请选择城市', trigger: 'blur'},
    ]
  }

  return r
})

const showAddProductDialog = ref(false)

function handleAddProduct(product) {
  if (product) {
    const old = list.value.find((item) => item.product_id === product.product_id)
    if (old) {
      old.quantity = product.quantity
    } else {
      list.value.push({
        name: product.name,
        price: product.price,
        quantity: product.quantity,
        product_id: product.product_id,
      })
    }

  }

  showAddProductDialog.value = false
}

function onSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    } else if (list.value.length === 0) {
      ElNotification.error('请添加要进行发货的商品！')
      return
    }

    const data = {...pickup.value}
    data.order_id = props.orderId
    data.is_self_pickup = pickup.value.is_self_pickup ? 1 : 2
    data.products = JSON.stringify(list.value.map(item => {
      item.quantity = Number.parseInt(item.quantity)
      return item
    }))
    data.zone_id = data.z[data.z.length - 1]

    submitPickup(data).then(() => {
      ElNotification.success('操作成功')
      show.value = false
    })
  })
}

onMounted(() => {
  orderDetail({order_id: props.orderId}).then(res => {
    const {data} = res
    Object.assign(pickup.value, {
      name: data.nickname,
      tel: data.mobile,
      address: data.address,
      free_freight_num: data.free_freight_num,
    })

    list.value = data.products.map(item => {
      return {
        product_id: item.product_id,
        name: item.name,
        price: item.price,
        quantity: item.quantity - item.shipped_count,
      }
    })
  })
})

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" title="申请发货" width="60%">
    <el-form ref="formRef" :model="pickup" :rules="rules" label-width="80">
      <el-form-item label="是否自提" prop="is_self_pickup">
        <el-checkbox v-model="pickup.is_self_pickup"/>
        <div style="margin-left: 20px;">可免费发货次数：{{ pickup.free_freight_num }}</div>
      </el-form-item>

      <template v-if="!pickup.is_self_pickup">
        <el-space>
          <el-form-item label="联系人" prop="name">
            <el-input v-model="pickup.name"/>
          </el-form-item>
          <el-form-item label="联系电话" prop="tel">
            <el-input v-model="pickup.tel"/>
          </el-form-item>
        </el-space>

        <el-form-item label="城市" prop="z">
        </el-form-item>

        <el-form-item label="详细地址" prop="address">
          <el-input v-model="pickup.address" style="width: 50%"/>
        </el-form-item>
      </template>

      <el-form-item label="备注">
        <el-input v-model="pickup.remarks" :maxlength="100" show-word-limit style="width: 50%" type="textarea"/>
      </el-form-item>

    </el-form>
    <el-button type="primary" @click="showAddProductDialog = true">添加商品</el-button>
    <el-table :data="list">
      <el-table-column label="商品名称">
        <template #default="{row}">
          <el-input v-model="row.name"/>
          <el-tag v-if="row.gift" style="margin-top: 12px;" type="danger">赠品</el-tag>
        </template>
      </el-table-column>
      <el-table-column :formatter="tableFormatMoney" label="单价" prop="price"></el-table-column>
      <el-table-column label="发货数量" prop="quantity">
        <template #default="{row}">
          <el-input v-model="row.quantity" size="small" style="width: 100px;"/>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="{row, $index}">
          <el-popconfirm , title="您确定要删除吗？" @confirm="() => {
                list.splice($index, 1)
              }">
            <template #reference>
              <el-button size="small" type="danger">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <MyAddProductDialog v-if="showAddProductDialog" @close="handleAddProduct"/>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>