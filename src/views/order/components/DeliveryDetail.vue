<script setup>

import {computed, onMounted, ref, watch} from "vue";
import {orderPickup, savePickupDelivery} from "../../../api/modules/order";
import {Deliveries} from "../../../utils/constmap";
import {ElNotification} from "element-plus";

const props = defineProps({
  id: Number,
})
const emit = defineEmits(['close'])
const show = ref(true)
const pickup = ref({
  address: '',
  name: '',
  city: '',
  province: '',
  tel: '',
  remarks: '',
})
const form = ref({
  delivery_code: '',
  delivery_no: '',
})
const rules = computed(() => {
  const r = {
    delivery_code: [
      {required: true, message: '请选择承运公司！'},
    ],
  }
  if (form.value.delivery_code !== 'custom') {
    r.delivery_no = [
      {required: true, message: '运单号是必填的！'},
    ]
  }

  return r
})

function handleOk() {
  const data = {...form.value, id: props.id}

  savePickupDelivery(data).then(() => {
    ElNotification.success('操作成功')
    show.value = false
  })
}

onMounted(() => {
  orderPickup(props.id).then(res => {
    const {data} = res
    Object.assign(pickup.value, data)

    form.value.delivery_no = data.delivery_no
    form.value.delivery_code = data.delivery_code

  })
})

watch(show, () => emit('close'))

</script>

<template>

  <el-dialog v-model="show" title="填写发货信息">
    <el-descriptions title="申请信息">
      <template v-if="!pickup.is_self_pickup">
        <el-descriptions-item label="联系人">
          {{ pickup.name }}
        </el-descriptions-item>
        <el-descriptions-item label="电话">
          {{ pickup.tel }}
        </el-descriptions-item>
        <el-descriptions-item label="地址">
          {{ `${pickup.province}${pickup.city}${pickup.address}` }}
        </el-descriptions-item>
      </template>
    </el-descriptions>

    <el-form ref="formRef" :model="form" :rules="rules">
      <el-form-item label="承运方" prop="delivery_code">
        <el-select v-model="form.delivery_code" style="width: 180px;">
          <el-option v-for="item in Deliveries" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="运单号" prop="delivery_no">
        <el-input v-model="form.delivery_no" style="width:250px;"/>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </el-dialog>

</template>

<style lang="scss" scoped>

</style>
