<script setup>

import {onActivated, ref} from "vue";
import MyDateRangePicker from "@/components/MyDateRangePicker.vue";
import {refundOrderStates, RefundOrderWaitReview} from "@/utils/constmap";
import MyPagination from "@/components/MyPagination.vue";
import {deepToRaw, formatMoney, tableFormatTime} from "@/utils";
import dayjs from "dayjs";
import {refunds} from "@/api/modules/order";
import {useRouter} from "vue-router";

const search = ref({
  order_id: '',
  state: RefundOrderWaitReview,
  date: []
})

const list = ref([])
const total = ref(0)
let page = 1
const router = useRouter()

function getList() {
  const params = deepToRaw((search.value))
  params.page = page
  if (params.date.length > 0) {
    params.start_date = dayjs(params.date[0]).unix()
    params.end_date = dayjs(params.date[1]).unix()
  }

  delete params.date

  refunds(params).then(({data}) => {
    total.value = data.total
    list.value = data.list
  })
}

function onSearch() {
  page = 1
  getList()
}

onActivated(() => {
  getList()
})

</script>

<template>
  <el-card>
    <el-form inline>
      <el-form-item label="订单号">
        <el-input v-model="search.order_id" clearable placeholder="请输入订单号"></el-input>
      </el-form-item>
      <el-form-item label="退款状态">
        <el-select v-model="search.state" clearable placeholder="请选择退款状态">
          <el-option v-for="(item, index) in refundOrderStates" :key="index" :label="item.label"
                     :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间">
        <MyDateRangePicker v-model="search.date" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="list">
      <el-table-column label="退款单号" prop="id"></el-table-column>
      <el-table-column label="订单号" prop="order_id"></el-table-column>
      <el-table-column label="退款标题" prop="title"></el-table-column>
      <el-table-column label="联系人" prop="contact"/>
      <el-table-column label="联系电话" prop="phone"/>
      <el-table-column label="状态" prop="state_text"/>
      <el-table-column label="退款金额">
        <template #default="{row}">
          <el-space direction="vertical">
            <div>申请退款金额：{{ formatMoney(row.apply_refund_amount) }}</div>
            <div>实际退款金额：{{ formatMoney(row.refund_amount) }}</div>
          </el-space>

        </template>
      </el-table-column>
      <el-table-column :formatter="tableFormatTime" label="申请时间" prop="created_at"/>
      <el-table-column fixed="right" label="操作">
        <template #default="{row}">
          <el-button link type="primary" @click="router.push({
          name:'OrderDetail',
          query: {id: row.order_id}
          })">查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <MyPagination :total="total" @current-change="p => {
      page = p
      getList()
    }"/>
  </el-card>
</template>
