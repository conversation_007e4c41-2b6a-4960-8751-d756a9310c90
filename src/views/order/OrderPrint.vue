<script setup>
import {computed, onMounted, ref} from "vue";
import {useRoute} from "vue-router";
import {orderPickup} from "../../api/modules/order";
import {formatMoney, formatTime} from "../../utils";

const route = useRoute()
const pickupOrderId = ref(0)

const detail = ref({
  province: '',
  city: '',
  address: '',
  name: '',
  tel: '',
  created_at: '',
  list: [],
  remarks: '',
  order_no: '',
})

const totalAmount = computed(() => {
  let total = 0
  detail.value.list.forEach((item) => {
    total += item.quantity * item.price
  })

  return total
})

onMounted(() => {
  pickupOrderId.value = route.query.pickup_order_id
  orderPickup(pickupOrderId.value).then(res => {
    const {data} = res
    detail.value = data

    setTimeout(() => {
      window.print()
    }, 1000)

    setTimeout(() => {
      window.close()
    }, 10000)
  })
})
</script>

<template>
  <div class="print-container">
    <h2>上海寿南山生物科技有限公司</h2>
    <div class="my-address">地址：{{ detail.self_pickup_address }}<br/>电话：{{ detail.service_tel }}<br/></div>
    <div class="title">
      <div></div>
      <div class="right">
        <span>销售发货单</span>
        <span>No.{{ detail.order_no }}</span>
      </div>
    </div>
    <div class="customer-info">
      <div class="col4">客户姓名：{{ detail.name }}</div>
      <div class="col4">客户电话：{{ detail.tel }}</div>
      <div class="col4">日期：{{ formatTime(detail.created_at) }}</div>
    </div>
    <div class="customer-info address">
      送货地址：{{ detail.province + detail.city + detail.address }}
    </div>
    <table>
      <thead>
      <tr>
        <th>序号</th>
        <th>商品名称</th>
        <th>数量</th>
        <th>单价</th>
        <th>金额</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="(item, index) in detail.list" :key="index">
        <td>{{ index + 1 }}</td>
        <td>{{ item.name + (item.gift ? '（赠品）' : '') }}</td>
        <td>{{ item.quantity }}</td>
        <td>{{ formatMoney(item.price) }}</td>
        <td>{{ formatMoney(item.price * item.quantity) }}</td>
      </tr>
      <tr>
        <td colspan="4">合计：</td>
        <td>{{ formatMoney(totalAmount) }}</td>
      </tr>
      <tr>
        <td colspan="5">备注：{{ detail.remarks }}</td>
      </tr>
      </tbody>
    </table>

    <div class="footer">
      <div>客户签收人：</div>
      <div>发票签收人：</div>
      <div>制单：</div>
      <div>审核：</div>
    </div>
  </div>
</template>

<style lang="scss">
body {
  height: auto;
  font-size: 16px;
}

@media print {
  body {
    font-size: 18px;
  }
}

</style>

<style lang="scss" scoped>
@import './print';
</style>
