<script setup>
import {onActivated, provide, ref} from "vue";
import {orderDetail, sendReward} from "../../api/modules/order";
import {useRoute} from "vue-router";
import {formatMoney, formatTime, tableFormatMoney, tableFormatTime} from "../../utils";
import ApplyOrderPayment from "./components/ApplyOrderPayment.vue";
import OrderDetailRefunds from "@/views/order/components/OrderDetailRefunds.vue";
import {UserTaskRewardSend, UserTaskRewardWait} from "../../utils/constmap";
import {ElNotification} from "element-plus";

const route = useRoute()
const orderId = ref(0)
const detail = ref({
  id: '',
  contacts_name: '',
  created_at: '',
  contacts_tel: '',
  state_text: '',
  payments: [],
  details: [],
  title: '',
  total_amount: 0,
  user_id: '',
  can_pay: false,
  pay_state: 1,
})
const showPay = ref(false)
const showRefund = ref(false)
const payId = ref('')
const showSendReward = ref(false)
const rules = {
  delivery_name: [
    {required: true, message: '快递公司是必填的！', trigger: 'blur'},
  ],
  delivery_no: [
    {required: true, message: '快递单号是必填的！', trigger: 'blur'},
  ],
}
const rewardFormRef = ref(null)

provide('orderInfo', detail)

function onSendReward() {
  rewardFormRef.value.validate((valid) => {
    if (valid) {
      const data = {
        order_id: orderId.value,
        delivery_name: detail.value.reward_info.delivery_name,
        delivery_no: detail.value.reward_info.delivery_no,
      }

      sendReward(data).then(() => {
        ElNotification.success('发货成功')
        showSendReward.value = false
        getDetail()
      })
    }
  })

}

function getDetail() {
  orderDetail({order_id: orderId.value}).then(res => {
    Object.assign(detail.value, res.data)
  })
}

onActivated(() => {
  orderId.value = route.query.id
  getDetail()
})

</script>

<template>
  <el-card :header="`#${detail.id}`">
    <el-descriptions title="用户信息">
      <el-descriptions-item label="用户ID">{{ detail.user_id }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="订单信息">
      <el-descriptions-item label="联系人">{{ detail.contacts_name }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ detail.contacts_tel }}</el-descriptions-item>
      <el-descriptions-item label="下单时间">{{ formatTime(detail.created_at) }}</el-descriptions-item>
      <el-descriptions-item label="订单状态">{{ detail.state_text }}</el-descriptions-item>
      <el-descriptions-item label="支付时间">{{ formatTime(detail.pay_time) }}</el-descriptions-item>
      <el-descriptions-item label="订单金额">{{ formatMoney(detail.total_amount) }}</el-descriptions-item>
      <el-descriptions-item label="预定时间">{{ detail.date }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions column="1">
      <el-descriptions-item label="内容">{{ detail.title }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="规格信息">
      <el-descriptions-item>
        <el-table :data="detail.details">
          <el-table-column label="商品名称" prop="product_name"/>
          <el-table-column label="规格名称" prop="sku_name"/>
          <el-table-column label="类型" prop="type_text"/>
          <el-table-column label="数量" prop="quantity"/>
          <el-table-column :formatter="tableFormatMoney" label="价格" prop="price"/>
          <el-table-column label="状态" prop="state_text"/>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detail.reward_info" :column="4">
      <template #title>
        奖品信息
        <el-button v-if="detail.reward_info.reward_state === UserTaskRewardWait" size="small"
                   type="success" @click="showSendReward = true">去发货
        </el-button>
      </template>
      <el-descriptions-item label="奖品名称">{{ detail.reward_info.real_product_name }}</el-descriptions-item>
      <el-descriptions-item label="奖品数量">{{ detail.reward_info.num }}</el-descriptions-item>
      <el-descriptions-item label="收货人">{{ detail.reward_info.receiver }}</el-descriptions-item>
      <el-descriptions-item label="收货地址">{{ detail.reward_info.recv_addr }}</el-descriptions-item>
      <el-descriptions-item label="联系电话">{{ detail.reward_info.recv_phone }}</el-descriptions-item>
      <el-descriptions-item label="领取状态">
        <el-tag :type="detail.reward_info.reward_state === UserTaskRewardSend ? 'success' : 'warning'">
          {{ detail.reward_info.reward_state_text }}
        </el-tag>
      </el-descriptions-item>
      <template v-if="detail.reward_info.reward_state === UserTaskRewardSend">
        <el-descriptions-item label="快递公司">{{ detail.reward_info.delivery_name }}</el-descriptions-item>
        <el-descriptions-item label="快递单号">{{ detail.reward_info.delivery_no }}</el-descriptions-item>
      </template>
    </el-descriptions>

    <OrderDetailRefunds @refresh="getDetail()"/>

    <el-descriptions>
      <template #title>
        支付信息
        <el-button v-if="detail.can_pay" size="small" type="warning" @click="showPay = true">去支付</el-button>
      </template>
      <el-descriptions-item>
        <el-table :data="detail.payments">
          <el-table-column :formatter="tableFormatTime" label="支付时间" prop="pay_time"/>
          <el-table-column :formatter="tableFormatMoney" label="支付金额" prop="actual_pay_amount"/>
          <el-table-column label="支付方式" prop="pay_method_text"/>
          <el-table-column label="支付状态" prop="state_text"/>
          <el-table-column label="操作">
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </el-card>

  <ApplyOrderPayment v-if="showPay" :id="orderId" @close="() => {
    showPay = false
    getDetail()
  }"/>

  <el-dialog v-model="showSendReward" title="填写发货信息">
    <el-form ref="rewardFormRef" :model="detail.reward_info" :rules="rules">
      <el-form-item label="快递公司" prop="delivery_name">
        <el-input v-model="detail.reward_info.delivery_name"></el-input>
      </el-form-item>
      <el-form-item label="快递单号" prop="delivery_no">
        <el-input v-model="detail.reward_info.delivery_no"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="showSendReward = false">取消</el-button>
      <el-button type="primary" @click="onSendReward">确定</el-button>
    </template>

  </el-dialog>

</template>
