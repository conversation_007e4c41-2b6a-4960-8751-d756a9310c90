<script setup>

import EChartIndex from "../../../components/ECharts/EChartIndex.vue";
import {computed, inject} from "vue";

const data = inject('data')

const vipOptions = computed(() => {
  const options = {
    title: {
      text: '会员等级分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: data.value?.vips?.map(it => {
          return {name: it.name, value: it.num}
        }),
        label: {
          formatter: '{b}\n{d}%',
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  return options
})

</script>

<template>
  <EChartIndex :option="vipOptions"/>
</template>

<style lang="scss" scoped>

</style>
