.app-container {
  display: flex;
  flex-direction: column;
  gap: 10px;

  .el-alert {
    margin: 20px 0 0;
  }

  .el-alert:first-child {
    margin: 0;
  }

  .c {
    display: flex;
    gap: 10px;

    .star {
      :deep(.el-card__header) {
        font-weight: bold;
        font-size: 22px;
      }
    }

    .el-card {
      &:first-child {
        flex: 1;
      }

      &:last-child {
        flex: 2;
      }
    }
  }
}
