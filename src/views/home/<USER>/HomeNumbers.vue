<script setup>

import {genRandomColor} from "../../../utils";
import {ShoppingBag, Suitcase, User} from "@element-plus/icons-vue";
import {inject} from "vue";
import {useRouter} from "vue-router";

const router = useRouter()
const data = inject('data')

</script>

<template>
  <div class="number-container">
    <div class="number-item">
      <div :style="{background: genRandomColor()}">
        <el-icon color="#fff">
          <User/>
        </el-icon>
      </div>
      <div>
        <div>{{ data.vip_num }}</div>
        <div>总会员数</div>
      </div>
    </div>
    <div class="number-item can-click" @click="router.push({name: 'OrderIndex'})">
      <div :style="{background: genRandomColor()}">
        <el-icon color="#fff">
          <ShoppingBag/>
        </el-icon>
      </div>
      <div>
        <div>{{ data.order_num }}</div>
        <div>订单总数</div>
      </div>
    </div>
    <div class="number-item can-click"
         @click="router.push({name: 'OrderIndex', query: {type: 2}})">
      <div :style="{background: genRandomColor()}">
        <el-icon color="#fff">
          <ShoppingBag/>
        </el-icon>
      </div>
      <div>
        <div>{{ data.repurchase_order_num }}</div>
        <div>复购订单数</div>
      </div>
    </div>
    <div class="number-item pickups">
      <div :style="{background: genRandomColor()}">
        <el-icon color="#fff">
          <Suitcase/>
        </el-icon>
      </div>
      <div @click="router.push({name: 'DeliveryIndex'})">
        <div>{{ data.pickup_num }}</div>
        <div>待发货订单</div>
      </div>
    </div>
    <!--    <div class="number-item">-->
    <!--      <div :style="{background: genRandomColor()}">-->
    <!--        <el-icon color="#fff">-->
    <!--          <Wallet/>-->
    <!--        </el-icon>-->
    <!--      </div>-->
    <!--      <div>-->
    <!--        <div>{{ formatMoney(data.commission_amount / 10000) }}万</div>-->
    <!--        <div>返现总金额</div>-->
    <!--      </div>-->
    <!--    </div>-->
    <div class="number-item">
      <div :style="{background: genRandomColor()}">
        <el-icon color="#fff">
          <User/>
        </el-icon>
      </div>
      <div>
        <div>{{ data.month_vip }}</div>
        <div>本月升级会员数</div>
      </div>
    </div>
    <!--    <div class="number-item">-->
    <!--      <div :style="{background: genRandomColor()}">-->
    <!--        <el-icon color="#fff">-->
    <!--          <Money/>-->
    <!--        </el-icon>-->
    <!--      </div>-->
    <!--      <div>-->
    <!--        <div>{{ formatMoney(data.quarter / 1000) }}万</div>-->
    <!--        <div>本季度GMV</div>-->
    <!--      </div>-->
    <!--    </div>-->
  </div>
</template>

<style lang="scss" scoped>
@mixin center($direction: row) {
  display: flex;
  flex-direction: $direction;

  @if $direction == column {
    justify-content: center;
  }
}

.number-container {
  @include center();
  font-size: var(--el-menu-item-font-size);
  gap: 10px;
  justify-content: space-between;
  width: 100%;

  .number-item, .number-item > div:last-child {
    @include center();
  }

  .number-item {
    background: white;
    box-shadow: var(--el-box-shadow-light);
    border-radius: 4px;
    flex: 1;

    &.can-click {
      cursor: pointer;
    }

    > div {
      padding: 15px 0;
      border-radius: 4px 0 0 4px;

      &:first-child {
        padding-right: 10px;
        padding-left: 10px;
        background: #2d8cf0;
        @include center(column);
        font-size: 25px;
      }

      &:last-child {
        flex-direction: column;
        align-items: center;
        flex: 1;
        justify-content: space-between;

        > div {
          &:first-child {
            font-size: 20px;
            font-weight: bold;
          }
        }
      }
    }
  }
}

</style>
