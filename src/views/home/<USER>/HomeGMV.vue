<script setup>

import EChartIndex from "../../../components/ECharts/EChartIndex.vue";
import {computed, inject} from "vue";
import dayjs from "dayjs";

const data = inject('data')

const vipOptions = computed(() => {
  const o = {
    title: {
      text: '近三个月GMV',
      left: 'center',
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      name: '万元'
    },
    series: [
      {
        data: [],
        type: 'line',
        smooth: true,
        areaStyle: {}
      }
    ]
  }
  data.value?.last_month_3?.forEach(item => {
    o.xAxis.data.push(dayjs.unix(item.date).format('YY/MM/DD'))
    o.series[0].data.push(item.value / (100 * 10000))
  })

  return o
})

</script>

<template>
  <EChartIndex :option="vipOptions"/>
</template>

<style lang="scss" scoped>

</style>
