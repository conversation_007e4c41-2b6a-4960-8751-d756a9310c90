<script setup>

import EChartIndex from "../../../components/ECharts/EChartIndex.vue";
import {computed, inject} from "vue";

const data = inject('data')

const vipOptions = computed(() => {
  const r = {
    title: {
      text: '近三年GMV'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: new Array(12).fill(0).map((it, index) => index + 1),
      name: '月'
    },
    yAxis: {
      type: 'value',
      name: '万元'
    },
    series: []
  }
  data.value?.last_year_3?.forEach(year => {
    r.legend.data.push(`${year.year}年`)
    const item = {
      name: `${year.year}年`,
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      smooth: true,
      data: year.value.map(v => v / (100 * 10000))
    }
    r.series.push(item)
  })

  return r
})

</script>

<template>
  <EChartIndex :option="vipOptions"/>
</template>

<style lang="scss" scoped>

</style>
