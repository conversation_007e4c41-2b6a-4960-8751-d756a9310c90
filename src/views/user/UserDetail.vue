<template>
  <div class="user-detail-container">
    <el-card class="detail-card">
      <template #header>
        <div class="card-header">
          <span>用户详情</span>
          <el-button @click="goBack">返回</el-button>
        </div>
      </template>

      <el-descriptions>
        <el-descriptions-item label="用户ID">{{ userInfo.user_id }}</el-descriptions-item>
        <el-descriptions-item label="用户昵称">{{ userInfo.nickname }}</el-descriptions-item>
        <el-descriptions-item label="加入时间">{{ formatTime(userInfo.join_time) }}</el-descriptions-item>
        <el-descriptions-item label="积分">
          <el-space>
            <span>{{ userInfo.amount }}</span>
            <el-button link type="primary" @click="pointsDialogVisible = true">
              <el-icon>
                <Edit />
              </el-icon>
            </el-button>
          </el-space>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 积分调整对话框 -->
    <el-dialog v-model="pointsDialogVisible" :close-on-click-modal="false" title="调整积分">
      <el-form :model="pointsForm" label-width="100px">
        <el-form-item label="当前积分">
          <span>{{ userInfo.amount }}</span>
        </el-form-item>
        <el-form-item label="调整值">
          <el-input-number v-model.number="pointsForm.adjustValue" placeholder="输入要添加的积分值，负数表示扣除">
          </el-input-number>
          <div class="form-tip">正数表示增加积分，负数表示扣除积分</div>
        </el-form-item>
        <el-form-item label="调整后积分">
          <span>{{ calculateNewPoints }}</span>
        </el-form-item>
        <el-form-item label="备注" required>
          <el-input v-model="pointsForm.remark" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="pointsDialogVisible = false">取消</el-button>
          <el-button :disabled="!isValidAdjustment" type="primary" @click="confirmAdjustPoints">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Edit } from '@element-plus/icons-vue';
import { getUserDetail, accountOperate } from '../../api/modules/user';
import { formatTime } from "../../utils";

const route = useRoute();
const router = useRouter();

// 用户详情数据
const userInfo = ref({
  user_id: '',
  nickname: '',
  join_time: '',
  amount: 0
});

const pointsForm = ref({
  adjustValue: 0,
  remark: ''
});

const calculateNewPoints = computed(() => userInfo.value.amount + pointsForm.value.adjustValue);
const isValidAdjustment = computed(() => {
  return pointsForm.value.adjustValue !== 0 && pointsForm.value.remark.trim() !== '';
});

const confirmAdjustPoints = async () => {
  try {
    await accountOperate({
      user_id: userInfo.value.user_id,
      amount: pointsForm.value.adjustValue,
      remark: pointsForm.value.remark
    });
    ElMessage.success('积分调整成功');
    pointsDialogVisible.value = false;
    await loadData(userInfo.value.user_id);
  } catch (error) {
    ElMessage.error('积分调整失败');
    console.error(error);
  }
};

const pointsDialogVisible = ref(false);

// 获取用户详情
const loadData = async (userId) => {
  try {
    // 获取用户基本信息
    const detailRes = await getUserDetail(userId);
    userInfo.value = detailRes.data;
  } catch (error) {
    ElMessage.error('获取用户信息失败');
    console.error(error);
    router.push({ name: 'UserList' });
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 页面激活时获取数据
onActivated(() => {
  const userId = route.query.user_id;
  if (userId) {
    loadData(userId);
  } else {
    ElMessage.error('用户ID不能为空');
    router.push({ name: 'UserList' });
  }
});
</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.detail-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>