<template>
  <div class="user-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户ID">
          <el-input v-model="searchForm.user_id" clearable placeholder="请输入用户ID"/>
        </el-form-item>
        <el-form-item label="用户昵称">
          <el-input v-model="searchForm.nickname" clearable placeholder="请输入用户昵称"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户列表表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <el-button type="primary" @click="refreshData">刷新</el-button>
        </div>
      </template>

      <el-table v-loading="loading" :data="userList" style="width: 100%">
        <el-table-column label="用户ID" prop="user_id"/>
        <el-table-column label="用户昵称" prop="nickname"/>
        <el-table-column :formatter="tableFormatTime" label="加入时间" prop="join_time"/>
        <el-table-column label="积分" prop="amount"/>
        <el-table-column label="目前套餐" prop="package_name"/>
        <el-table-column :formatter="tableFormatTime" label="购买时间" prop="purchase_time"/>
        <el-table-column :formatter="tableFormatTime" label="到期时间" prop="expire_time"/>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link type="primary" @click="viewUserDetail(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <MyPagination :total="total" @current-change="handleCurrentChange"/>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, onActivated} from 'vue'
import {ElMessage} from 'element-plus'
import {useRouter} from 'vue-router'
import MyPagination from "../../components/MyPagination.vue";
import {getUserList} from "../../api/modules/user";
import {deepToRaw, tableFormatTime} from "../../utils";

const router = useRouter()

// 搜索表单数据
const searchForm = reactive({
  user_id: '',
  nickname: ''
})

// 表格数据
const userList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 获取用户列表数据
const loadData = async () => {
  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      ...deepToRaw(searchForm)
    }

    const res = await getUserList(params)
    userList.value = res.data.list
    total.value = res.data.total
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.userId = ''
  searchForm.nickname = ''
  currentPage.value = 1
  loadData()
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 查看用户详情
const viewUserDetail = (row) => {
  router.push({
    name: 'UserDetail',
    query: {user_id: row.user_id}
  })
}

// 分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  loadData()
}

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 页面加载时获取数据
onActivated(() => {
  loadData()
})
</script>

<style scoped>
.user-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
