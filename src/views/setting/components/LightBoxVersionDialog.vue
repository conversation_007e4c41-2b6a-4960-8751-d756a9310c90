<script setup>

import {computed, onMounted, ref, watch} from "vue";
import dayjs from "dayjs";
import {saveVersion, versionDetail} from "../../../api/modules/flightbox";
import {ElNotification} from "element-plus";

const props = defineProps(['id'])
const emit = defineEmits(['close'])
const show = ref(true)

const formRef = ref(null)
const form = ref({
  url: '',
  version: '',
  note: '',
  os: '',
  arch: '',
  size: '',
  pack_type: '',
})
const release_time = ref(new Date())
const packTypes = ['deb', 'rpm']

const os = [
  {
    label: 'Windows',
    value: 'windows'
  },
  {
    label: 'MacOS',
    value: 'macos'
  },
  {
    label: 'Linux',
    value: 'linux'
  }
]
const arch = computed(() => {
  switch (form.value.os) {
    case 'macos':
      return [
        {
          label: 'x86_64',
          value: 'x86_64'
        },
      ]
    default:
      return [
        {
          label: 'amd64',
          value: 'amd64'
        }
      ]
  }
})
const rules = computed(() => {
  const ret = {
    os: [
      {
        required: true,
        message: '请选择平台',
        trigger: 'blur'
      }
    ],
    arch: [
      {
        required: true,
        message: '请选择架构',
        trigger: 'blur'
      }
    ],
    version: [
      {
        required: true,
        message: '请输入版本号',
        trigger: 'blur'
      }
    ],
    url: [
      {
        required: true,
        message: '请输入下载链接',
        trigger: 'blur'
      }
    ],
    note: [
      {
        required: true,
        message: '请输入更新说明',
        trigger: 'blur'
      }
    ],
    size: [
      {
        required: true,
        message: '请输入大小',
        trigger: 'blur'
      }
    ]
  }

  if (form.value.os === 'linux') {
    ret.pack_type = [
      {
        required: true,
        message: '请选择打包方式',
        trigger: 'blur'
      }
    ]
  }

  return ret
})

function handleOk() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = {...form.value}
    data.release_time = dayjs(release_time.value).unix()

    saveVersion(data).then(() => {
      ElNotification.success('保存成功')
      show.value = false
    })
  })
}

onMounted(() => {
  if (props.id) {
    versionDetail(props.id).then(res => {
      const {data} = res

      Object.assign(form.value, data)
      release_time.value = dayjs.unix(data.release_time).toDate()
    })
  }
})

watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" title="LightBox版本">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-space>
        <el-form-item label="平台" prop="os">
          <el-select v-model="form.os">
            <el-option v-for="item in os" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.os === 'linux'" label="打包方式" prop="pack_type">
          <el-select v-model="form.pack_type">
            <el-option v-for="item in packTypes" :key="item" :label="item" :value="item"/>
          </el-select>
        </el-form-item>
      </el-space>

      <el-form-item label="架构" prop="arch">
        <el-select v-model="form.arch">
          <el-option v-for="item in arch" :key="item.value" :label="item.label" :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="version">
        <el-input v-model="form.version" placeholder="版本格式：1.0.0"></el-input>
      </el-form-item>

      <el-form-item label="下载链接" prop="url">
        <el-input v-model="form.url" style="width: 100%"></el-input>
      </el-form-item>
      <el-form-item label="大小" prop="size">
        <el-input v-model="form.size" placeholder="字节单位"></el-input>
      </el-form-item>
      <el-form-item label="发布时间">
        <el-date-picker v-model="release_time" :clearable="false"></el-date-picker>
      </el-form-item>
      <el-form-item label="更新说明" prop="note">
        <el-input v-model="form.note" :rows="4" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.el-input, .el-select {
  width: 180px;
}
</style>
