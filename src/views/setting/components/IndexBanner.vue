<script setup>

import { banners } from "../../../api/modules/setting";
import { ElNotification } from "element-plus";
import { onMounted, ref } from "vue";
import MyPagination from "../../../components/MyPagination.vue";
import BannerDetail from "./BannerDetail.vue";
import { formatTime } from '../../../utils/index'

const list = ref([])
const total = ref(0)
const page = ref(1)
const showDetail = ref(false)
const id = ref('')

function getList() {
  banners().then(res => {
    const { data } = res
    list.value = data.list
    total.value = data.total
  })
}

function handleDetail(row) {
  id.value = row.id
  showDetail.value = true
}

function handleSave(key) {
  const item = list.value.find(it => it.key === key)
  if (!item) {
    return
  }
  const data = { key }
  if (item.value instanceof Array) {
    data.value = item.value.join(',')
  } else {
    data.value = item.value
  }

  settingSave(data).then(() => {
    ElNotification.success('修改成功')
    getList()
  })
}

onMounted(() => {
  getList()
})

</script>

<template>
  <el-button type="primary" @click="() => {
    id = ''
    showDetail = true
  }">新增
  </el-button>
  <el-table :data="list">
    <el-table-column label="标题" prop="title" />
    <el-table-column label="图片">
      <template #default="{ row }">
        <el-image :preview-src-list="[row.pic]" :src="row.pic" lazy style="width: 100px;" preview-teleported />
      </template>
    </el-table-column>
    <el-table-column label="状态">
      <template #default="{ row }">
        <el-tag v-if="row.state === 1">在线</el-tag>
        <el-tag v-else type="danger">不在线</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="位置" prop="position_text" />
    <el-table-column label="链接" prop="link" />
    <el-table-column label="生效日期">
      <template #default="{ row }">
        {{ formatTime(row.start) }} <br> 至<br> {{ formatTime(row.end) }}
      </template>
    </el-table-column>
    <el-table-column label="排序" prop="sort" />
    <el-table-column label="操作">
      <template #default="{ row }">
        <el-button link type="primary" @click="handleDetail(row)">修改</el-button>
        <el-popconfirm title="您确定要删除吗？" @confirm="handleSave(row.key)">
          <template #reference>
            <el-button link type="danger">删除</el-button>
          </template>
        </el-popconfirm>

      </template>

    </el-table-column>
  </el-table>
  <MyPagination :total="total" @current-change="(p) => {
    page = p.page
    getList()
  }" />
  <BannerDetail v-if="showDetail" :id="id" @close="() => {
    showDetail = false
    getList()
  }" />
</template>

<style lang="scss" scoped></style>
