<script setup>
import {ref, onMounted} from 'vue'
import {getConfigList, getConfigDetail, saveConfig} from '../../../api/modules/setting'
import {ElMessage, ElMessageBox} from 'element-plus'
import ConfigDetail from './ConfigDetail.vue'

const configs = ref([])
const loading = ref(false)
const total = ref(0)
const query = ref({
  page: 1,
  limit: 20,
  key: ''
})
const showDetail = ref(false)
const currentKey = ref('')
const currentConfig = ref(null)

// 加载配置项列表
const loadConfigs = () => {
  loading.value = true
  getConfigList(query.value).then(res => {
    configs.value = res.data.list
    total.value = res.data.total
  }).finally(() => {
    loading.value = false
  })
}

// 处理页码变化
const handleCurrentChange = (page) => {
  query.value.page = page
  loadConfigs()
}

// 处理搜索
const handleSearch = () => {
  query.value.page = 1
  loadConfigs()
}

// 处理编辑配置项
const handleEdit = (key) => {
  currentKey.value = key
  currentConfig.value = null // 不再传递整行数据
  showDetail.value = true
}

// 处理新增配置项
const handleAdd = () => {
  currentKey.value = '' // 传入空的key表示新增
  currentConfig.value = null
  showDetail.value = true
}

// 截取显示文本(最多显示50个字符)
const truncateValue = (value) => {
  if (!value) return ''
  const strValue = String(value)
  if (strValue.length <= 50) return strValue
  return strValue.substring(0, 50) + '...'
}

// 将时间戳转换为可读日期格式
const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 根据类型字段获取类型名称
const getTypeName = (typeValue) => {
  const typeMap = {
    1: '文本',
    2: '数字',
    3: 'JSON',
    4: '布尔值',
    5: 'URL',
    6: '图片'
  }
  return typeMap[typeValue] || '未知'
}

// 组件加载时获取数据
onMounted(() => {
  loadConfigs()
})
</script>

<template>
  <div class="system-config">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-input
          v-model="query.key"
          placeholder="输入配置项名称或描述搜索"
          clearable
          @keyup.enter="handleSearch"
          style="width: 300px"
      />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button type="success" @click="handleAdd">新增配置</el-button>
    </div>

    <!-- 数据表格 -->
    <el-table :data="configs" border style="width: 100%" v-loading="loading">
      <el-table-column label="配置项名称" prop="key" min-width="120"/>
      <el-table-column label="配置项描述" prop="desc" min-width="150"/>
      <el-table-column label="类型" min-width="80">
        <template #default="{ row }">
          {{ getTypeName(row.value_type) }}
        </template>
      </el-table-column>
      <el-table-column label="配置项值" min-width="250">
        <template #default="{ row }">
          <el-tooltip :content="row.value" placement="top" :disabled="String(row.value || '').length <= 50">
            <span :class="{ 'json-value': row.value_type === 3 }">{{ truncateValue(row.value) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" min-width="180">
        <template #default="{ row }">
          {{ formatTimestamp(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="修改时间" min-width="180">
        <template #default="{ row }">
          {{ formatTimestamp(row.updated_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row.key)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
          v-model:current-page="query.page"
          :page-size="query.limit"
          :total="total"
          background
          layout="prev, pager, next, total"
          size="small"
          @current-change="handleCurrentChange"
      />
    </div>

    <!-- 配置项详情对话框 -->
    <ConfigDetail
        v-if="showDetail"
        v-model:visible="showDetail"
        :config-key="currentKey"
        :config-data="currentConfig"
        @success="loadConfigs"
    />
  </div>
</template>

<style lang="scss" scoped>
.system-config {
  padding: 20px;

  .search-container {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .json-value {
    color: #409eff;
    font-style: italic;
  }
}
</style>
