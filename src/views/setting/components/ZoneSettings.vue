<script setup>

import {onMounted, ref, watch} from "vue";
import {zones} from "../../../api/modules";
import {ZoneLevels} from "../../../utils/constmap";
import {zoneDetail, zoneUpsert} from "../../../api/modules/setting";
import {ElNotification} from "element-plus";
import MyImageUpload from "../../../components/MyImageUpload.vue";

const list = ref([])
const detail = ref({
  level: '',
  name: '',
  id: '',
  pic_id: '',
  pic: '',
})
const rules = {
  name: [
    {required: true, message: '名称是必填的！'}
  ],
  level: [
    {required: true, message: '类型是必填的！'}
  ]
}
const formRef = ref(null)
const keyword = ref('')
const treeRef = ref(null)

function handleClick(e) {
  const id = e.value

  zoneDetail(id).then(res => {
    const {data} = res

    Object.assign(detail.value, data)
    const main = document.querySelector('main')
    if (main) {
      main.scrollTo(0, 0)
    }
  })
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    } else if (!detail.value.id) {
      ElNotification.error('请选择要操作的地区！')
      return;
    }

    const data = {...detail.value}
    if (data.pic_id) {
      data.pic = data.pic_id
    }

    zoneUpsert(data).then(() => {
      ElNotification.success('操作成功！')
    })
  })
}

function filter(value, data) {
  if (!value) {
    return true
  }

  return data.label.includes(value)
}

watch(keyword, value => treeRef.value.filter(value, value))

onMounted(() => {
  zones().then(res => {
    const {data} = res

    list.value = data.list.map(province => {
      return {
        value: province.id,
        label: province.name,
        children: province.list.map(city => {
          return {
            value: city.id,
            label: city.name
          }
        })
      }
    })
  })
})

</script>

<template>
  <div class="content">
    <div class="left">
      <el-input v-model="keyword" clearable placeholder="地区搜索关键字"/>
      <el-tree ref="treeRef" :data="list" :filter-node-method="filter" @node-click="handleClick"></el-tree>
    </div>
    <div class="right">
      <el-form ref="formRef" :model="detail" :rules="rules" label-width="80px">
        <el-form-item label="地区名称" prop="name">
          <el-input v-model="detail.name"/>
        </el-form-item>
        <el-form-item label="地区类型" prop="level">
          <el-select v-model="detail.level">
            <el-option v-for="item in ZoneLevels" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="图片" prop="pic">
          <MyImageUpload v-model="detail.pic_id" v-model:url="detail.pic"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>

</template>

<style lang="scss" scoped>
.content {
  display: flex;

  .left {
    width: 40%;
    padding: 0 10px;
    box-sizing: border-box;

    :deep(.el-input) {
      margin-bottom: 10px;
    }
  }

  .right {
    flex: 1;
  }
}
</style>
