<script setup>

import {computed, onMounted, ref, toRaw, watch} from "vue";
import MyDateRangePicker from "../../../components/MyDateRangePicker.vue";
import MyUpload from "../../../components/MyUpload.vue";
import dayjs from 'dayjs'
import {bannerDetail, bannerSave} from "../../../api/modules/setting";
import {ElNotification} from "element-plus";
import {CircleClose} from "@element-plus/icons-vue";
import {BannerPositions} from "@/utils/constmap";
import MyImageUpload from "@/components/MyImageUpload.vue";

const emit = defineEmits(['close'])
const show = ref(true)
const props = defineProps({
  id: {type: [String, Number]},
})
const title = computed(() => isEdit.value ? '编辑Banner' : '新增Banner')
const isEdit = computed(() => props.id > 0)
const form = ref({
  title: '',
  link: '',
  pic_res_id: '',
  state: 2,
  date: [],
  pic: '',
  position: '',
  sort: 1,
})
const formRef = ref(null)
const rules = computed(() => {
  const r = {
    title: [{required: true, message: '标题是必填的！'}],
    date: [{required: true, message: '生效日期是必填的！'}],
    position: [{required: true, type: 'number', min: 1, message: '请选择位置'}],
  }

  if (!isEdit.value || !form.value.pic) {
    r.pic_res_id = [{required: true, message: '图片是必传的！'}]
  }

  return r
})


function handleSave() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = toRaw(form.value)
    data.start = dayjs(data.date[0]).unix()
    data.end = dayjs(data.date[1]).unix()

    bannerSave(data).then(() => {
      ElNotification.success('保存成功')
      show.value = false
    })

  })
}

onMounted(() => {
  if (isEdit.value) {
    bannerDetail(props.id).then(res => {
      const {data} = res
      Object.assign(form.value, data)

      form.value.date = [dayjs.unix(data.start).toDate(), dayjs.unix(data.end).toDate()]
    })
  }
})


watch(show, () => emit('close'))

</script>

<template>
  <el-dialog v-model="show" :title="title">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title"/>
      </el-form-item>
      <el-form-item label="是否上线">
        <el-radio-group v-model="form.state">
          <el-radio-button label="是" :value="1"></el-radio-button>
          <el-radio-button label="否" :value="2"></el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="位置" prop="position">
        <el-select v-model="form.position">
          <el-option v-for="item in BannerPositions" :key="item.value" :label="item.label"
                     :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="链接">
        <el-input v-model="form.link"/>
      </el-form-item>
      <el-form-item label="生效日期" prop="date">
        <MyDateRangePicker v-model="form.date" is-next/>
      </el-form-item>
      <el-form-item label="图片" prop="pic_res_id">
        <MyImageUpload v-model="form.pic_res_id" v-model:url="form.pic"/>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="form.sort"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.pic {
  position: relative;

  .el-icon {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 999;
    cursor: pointer;
  }
}
</style>
