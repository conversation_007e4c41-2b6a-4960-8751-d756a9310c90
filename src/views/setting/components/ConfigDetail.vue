<script setup>
import { ref, watch, defineProps, defineEmits, nextTick } from 'vue'
import { getConfigDetail, saveConfig } from '../../../api/modules/setting'
import { ElMessage } from 'element-plus'
import { Link } from '@element-plus/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  configKey: {
    type: String,
    default: ''
  },
  configData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'success'])

const loading = ref(false)
// 系统配置项类型定义
const CONFIG_TYPE = {
  TEXT: 1,
  NUMBER: 2,
  JSON: 3,
  BOOL: 4,
  URL: 5,
  IMAGE: 6
}

// 存储接口返回的原始数据
const originalConfigData = ref(null)

const form = ref({
  key: '',
  value: '',
  desc: '',
  type: CONFIG_TYPE.TEXT // 默认为文本类型
})

const valueTypes = [
  { label: '文本', value: CONFIG_TYPE.TEXT },
  { label: '数字', value: CONFIG_TYPE.NUMBER },
  { label: 'JSON', value: CONFIG_TYPE.JSON },
  { label: '布尔值', value: CONFIG_TYPE.BOOL },
  { label: 'URL', value: CONFIG_TYPE.URL },
  { label: '图片', value: CONFIG_TYPE.IMAGE }
]
const rules = {
  key: [{ required: true, message: '配置项名称不能为空', trigger: 'blur' }],
  value: [{ required: true, message: '配置项值不能为空', trigger: 'blur' }]
}
const formRef = ref(null)
// JSON相关变量
const jsonError = ref(false)

// 检测值的类型
const detectValueType = (value) => {
  if (typeof value === 'undefined' || value === null) return CONFIG_TYPE.TEXT

  // 如果值是"null"字符串，则返回JSON类型
  if (value === 'null') return CONFIG_TYPE.JSON

  // 如果值是对象或数组，返回JSON类型
  if (typeof value === 'object') return CONFIG_TYPE.JSON

  try {
    // 尝试解析为JSON
    const parsed = JSON.parse(value)
    // 不仅仅判断对象，也包括null和数组
    if (typeof parsed === 'object' || parsed === null || Array.isArray(parsed)) {
      return CONFIG_TYPE.JSON
    }
  } catch (e) {
    // 不是JSON，继续检测其他类型
  }

  // 检测是否是URL
  const urlRegex = /^(https?:\/\/)?[\w.-]+\.[a-z]{2,}([/\w.-]*)*\/?$/i
  if (urlRegex.test(value)) {
    // 检测是否是图片URL
    const imageRegex = /\.(jpg|jpeg|png|gif|webp|svg)$/i
    if (imageRegex.test(value)) {
      return CONFIG_TYPE.IMAGE
    }
    return CONFIG_TYPE.URL
  }

  // 检测数字
  if (!isNaN(Number(value)) && typeof value !== 'boolean') {
    return CONFIG_TYPE.NUMBER
  }

  // 检测布尔值
  if (value === 'true' || value === 'false' || value === true || value === false) {
    return CONFIG_TYPE.BOOL
  }

  // 默认为文本类型
  return CONFIG_TYPE.TEXT
}

// 根据数据初始化表单
const initFormWithData = (data) => {
  if (!data) return

  // 使用API返回的value_type或尝试检测
  const valueType = data.value_type || detectValueType(data.value)

  // 初始化表单数据
  form.value = {
    key: data.key,
    value: data.value,
    desc: data.desc || '',
    type: valueType
  }

  loading.value = false
  jsonError.value = false

  // 如果是JSON类型，自动格式化
  if (valueType === CONFIG_TYPE.JSON) {
    // 使用nextTick确保表单数据已经更新
    nextTick(() => {
      autoFormatJson()
    })
  }
}

// 加载配置项详情
const loadConfigDetail = (key) => {
  // 如果key为空，表示新增配置项
  if (!key) {
    // 初始化一个新的空配置
    originalConfigData.value = null
    form.value = {
      key: '',
      value: '',
      desc: '',
      type: CONFIG_TYPE.TEXT
    }
    loading.value = false
    return
  }

  // 如果有key，调用接口获取详情数据
  loading.value = true
  getConfigDetail(key)
    .then(res => {
      // 保存原始数据供保存时使用
      originalConfigData.value = res.data
      initFormWithData(res.data)
    })
    .catch(err => {
      ElMessage.error('获取配置项详情失败：' + err.message)
      loading.value = false
    })
}

// 当键或可见状态变化时，加载配置详情
watch(
  [() => props.configKey, () => props.visible],
  ([newKey, visible]) => {
    if (visible) {
      // 当对话框显示时，总是调用loadConfigDetail函数
      // 当newKey为空时表示新增配置，否则为编辑已有配置
      loadConfigDetail(newKey)
    }
  },
  { immediate: true }
)

// 监听类型变化，当切换为JSON类型时自动格式化
watch(
  () => form.value.type,
  (newType, oldType) => {
    // 只有当类型从非JSON切换为JSON，且有值时才自动格式化
    if (newType === CONFIG_TYPE.JSON && oldType !== CONFIG_TYPE.JSON && form.value.value) {
      nextTick(() => {
        autoFormatJson()
      })
    }
  }
)

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
  form.value = {
    key: '',
    value: '',
    desc: '',
    type: CONFIG_TYPE.TEXT
  }
  jsonError.value = false
}

// 保存配置项
const handleSubmit = () => {
  formRef.value.validate((valid) => {
    if (!valid) return

    // 处理不同类型的值
    let finalValue = form.value.value

    if (form.value.type === CONFIG_TYPE.JSON) {
      try {
        // 确保是有效的JSON
        const jsonObj = typeof finalValue === 'string' ? JSON.parse(finalValue) : finalValue
        finalValue = JSON.stringify(jsonObj)
      } catch (e) {
        ElMessage.error('JSON格式不正确，请检查')
        return
      }
    } else if (form.value.type === CONFIG_TYPE.NUMBER) {
      finalValue = Number(finalValue)
      if (isNaN(finalValue)) {
        ElMessage.error('请输入有效的数字')
        return
      }
    } else if (form.value.type === CONFIG_TYPE.BOOL) {
      finalValue = finalValue === true || finalValue === 'true'
    }
    
    // 处理保存数据
    // 如果originalConfigData为null，表示是新增配置
    // 否则是编辑现有配置，优先使用修改后的值
    const data = originalConfigData.value === null
      // 新增配置，必须使用表单中的所有值
      ? {
          key: form.value.key,
          value: finalValue,
          desc: form.value.desc,
          value_type: form.value.type
        }
      // 编辑现有配置，优先使用修改后的值，如果没有修改则使用原始值
      : {
          key: originalConfigData.value.key, // 编辑时key不允许修改
          value: finalValue,
          desc: form.value.desc !== undefined ? form.value.desc : (originalConfigData.value.desc || ''),
          value_type: form.value.type !== undefined ? form.value.type : (originalConfigData.value.value_type || CONFIG_TYPE.TEXT)
        }
    console.log('保存数据:', data)
    loading.value = true
    saveConfig(data)
      .then(() => {
        ElMessage.success('保存成功')
        emit('success')
        handleClose()
      })
      .catch(err => {
        ElMessage.error('保存失败：' + err.message)
      })
      .finally(() => {
        loading.value = false
      })
  })
}

// 打开URL链接
const openUrl = (url) => {
  if (!url) {
    ElMessage.warning('URL不能为空')
    return
  }
  
  // 确保URL有协议前缀
  let fullUrl = url
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    fullUrl = 'http://' + url
  }

  // 在新窗口打开URL
  window.open(fullUrl, '_blank')
}

// 自动格式化JSON数据（不显示提示消息）
const autoFormatJson = () => {
  if (form.value.type !== CONFIG_TYPE.JSON) return false

  try {
    // 特殊处理null值
    if (form.value.value === 'null' || form.value.value === '') {
      form.value.value = 'null'
      jsonError.value = false
      return true
    }

    // 尝试解析JSON
    const jsonValue = form.value.value.trim()
    let jsonObj

    // 处理空值情况
    if (!jsonValue) {
      form.value.value = ''
      return true
    }

    // 处理已经是对象的情况
    if (typeof form.value.value === 'object') {
      jsonObj = form.value.value
    } else {
      // 尝试解析JSON字符串
      try {
        jsonObj = JSON.parse(jsonValue)
      } catch (e) {
        // 尝试修复常见的JSON格式错误
        try {
          // 尝试添加引号
          const fixedJson = jsonValue
            .replace(/([{,]\s*)([a-zA-Z0-9_]+?)\s*:/g, '$1"$2":')
            .replace(/:\s*'([^']*?)'/g, ': "$1"')
            .replace(/:\s*([a-zA-Z0-9_]+?)([,}])/g, ': "$1"$2')
          jsonObj = JSON.parse(fixedJson)
        } catch (e2) {
          throw e // 修复失败，抛出原始错误
        }
      }
    }

    // 格式化并更新表单值
    form.value.value = JSON.stringify(jsonObj, null, 2)
    jsonError.value = false
    return true
  } catch (e) {
    jsonError.value = true
    console.error('JSON自动格式化错误:', e)
    return false
  }
}

// 格式化JSON数据（手动调用，显示提示消息）
const formatJson = () => {
  if (form.value.type !== CONFIG_TYPE.JSON) return

  const success = autoFormatJson()
  if (success) {
    ElMessage.success('JSON已格式化')
  } else {
    ElMessage.error('JSON格式不正确，无法格式化')
  }
}
</script>

<template>
  <el-dialog
    :title="configKey ? '编辑配置项' : '新增配置项'"
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    width="650px"
    :close-on-click-modal="false"
    @closed="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      v-loading="loading">
      <el-form-item label="配置项名称" prop="key">
        <el-input v-model="form.key" placeholder="请输入配置项名称" :disabled="!!configKey" />
      </el-form-item>

      <el-form-item label="值类型">
        <el-select v-model="form.type">
          <el-option
            v-for="item in valueTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="配置项值" prop="value">
        <!-- 根据不同的类型显示不同的输入控件 -->
        <template v-if="form.type === CONFIG_TYPE.TEXT">
          <el-input
            v-model="form.value"
            type="textarea"
            :rows="4"
            placeholder="请输入文本值"
          />
        </template>
        
        <template v-else-if="form.type === CONFIG_TYPE.NUMBER">
          <el-input-number
            v-model="form.value"
            :controls="false"
            style="width: 100%"
          />
        </template>
        
        <template v-else-if="form.type === CONFIG_TYPE.BOOL">
          <el-switch
            v-model="form.value"
            active-text="是"
            inactive-text="否"
            :active-value="true"
            :inactive-value="false"
          />
        </template>
        
        <template v-else-if="form.type === CONFIG_TYPE.JSON">
          <div class="json-editor-container">
            <el-input
              v-model="form.value"
              type="textarea"
              :rows="10"
              placeholder="请输入有效的JSON格式"
              :class="{ 'json-error': jsonError }"
              class="full-width-textarea"
            />
            <div class="json-editor-actions">
              <el-button size="small" type="primary" @click="formatJson">格式化</el-button>
            </div>
          </div>
          <div class="json-tip">
            提示：输入JSON后点击“格式化”按钮可使其更易读。保存前会自动检测JSON格式是否正确。
          </div>
        </template>
        
        <template v-else-if="form.type === CONFIG_TYPE.URL">
          <div class="url-input-container">
            <el-input
              v-model="form.value"
              placeholder="请输入URL地址"
              class="url-input">
              <template #append>
                <el-button @click="openUrl(form.value)">
                  <el-icon><Link /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
          <div class="url-tip">
            提示：输入有效的URL地址，可点击右侧图标进行浏览。
          </div>
        </template>
        
        <template v-else-if="form.type === CONFIG_TYPE.IMAGE">
          <div class="image-input-container">
            <el-input
              v-model="form.value"
              placeholder="请输入图片URL地址"
              class="image-url-input">
              <template #append>
                <el-button @click="openUrl(form.value)">
                  <el-icon><Link /></el-icon>
                </el-button>
              </template>
            </el-input>
            
            <div class="image-preview" v-if="form.value">
              <img :src="form.value" alt="图片预览" class="preview-image" />
            </div>
          </div>
          <div class="image-tip">
            提示：输入图片URL地址，下方将显示图片预览。
          </div>
        </template>
      </el-form-item>
      
      <el-form-item label="配置项描述">
        <el-input
          v-model="form.desc"
          type="textarea"
          :rows="2"
          placeholder="请输入配置项描述（可选）"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">保存</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.json-tip,
.url-tip,
.image-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.json-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  overflow: hidden;
  width: 100%;
}

.json-editor-actions {
  padding: 8px;
  background-color: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  text-align: right;
}

.json-error {
  border-color: #f56c6c;
}

.full-width-textarea {
  width: 100%;
  :deep(.el-textarea__inner) {
    width: 100%;
    min-width: 100%;
    box-sizing: border-box;
    font-family: monospace;
  }
}

.url-input-container,
.image-input-container {
  width: 100%;
  margin-bottom: 10px;
}

.image-preview {
  margin-top: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 5px;
  background-color: #f9f9f9;
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
}
</style>
