<script setup>

import { ref } from "vue";
import IndexBanner from "./components/IndexBanner.vue";
import ZoneSettings from "./components/ZoneSettings.vue";
import SystemConfig from "./components/SystemConfig.vue";

const activeTab = ref('banner')

</script>

<template>
  <el-card>
    <el-tabs v-model="activeTab">
      <el-tab-pane label="banner管理" name="banner">
        <IndexBanner v-if="activeTab === 'banner'" />
      </el-tab-pane>
      <el-tab-pane label="地区设置" name="zone">
        <ZoneSettings v-if="activeTab === 'zone'" />
      </el-tab-pane>
      <el-tab-pane label="系统配置项" name="config">
        <SystemConfig v-if="activeTab === 'config'" />
      </el-tab-pane>
    </el-tabs>

  </el-card>

</template>

<style scoped lang="scss"></style>