<script setup>

import {onActivated, ref} from "vue";
import LightBoxVersionDialog from "./components/LightBoxVersionDialog.vue";
import {versions} from "../../api/modules/flightbox";
import MyPagination from "../../components/MyPagination.vue";
import {tableFormatSize, tableFormatTime} from "../../utils";

const showVersion = ref(false)
const page = ref(1)
const total = ref(0)
const list = ref([])
const id = ref('')

function handleDetail(row) {
  id.value = row.id
  showVersion.value = true
}

function getList() {
  const params = {page: page.value}

  versions(params).then(res => {
    const {data} = res
    list.value = data.list
    total.value = data.total
  })
}

onActivated(() => {
  getList()
})

</script>

<template>
  <el-card>
    <el-button type="primary" @click="() => {
      id = 0
      showVersion = true
    }">发布新版本
    </el-button>
    <el-table :data="list">
      <el-table-column label="平台" prop="os"/>
      <el-table-column label="打包方式" prop="pack_type"/>
      <el-table-column label="架构" prop="arch"/>
      <el-table-column label="版本" prop="version"/>
      <el-table-column :formatter="tableFormatTime" label="发布时间" prop="release_time"/>
      <el-table-column :formatter="tableFormatSize" label="大小" prop="size"/>
      <el-table-column label="操作">
        <template #default="{row}">
          <el-button link type="primary" @click="handleDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="(p) => {
      page = p
      getList()
    }"/>
  </el-card>

  <LightBoxVersionDialog v-if="showVersion" :id="id" @close="() => {
    showVersion = false
    getList()
  }"/>

</template>