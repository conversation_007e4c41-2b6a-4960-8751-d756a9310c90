<template>
  <el-card>
    <el-form inline>
      <el-form-item label="工作流名称">
        <el-input v-model="search.keyword" clearable/>
      </el-form-item>
      <el-form-item label="工作流KEY">
        <el-input v-model="search.key" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="() => {
          page = 1
          getList()
        }">搜索
        </el-button>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="() => {
      id = 0; 
      showDetail = true
    }">新增工作流
    </el-button>

    <el-table :data="list" v-loading="loading">
      <el-table-column label="ID" prop="id"/>
      <el-table-column label="工作流名称" prop="name"/>
      <el-table-column label="工作流KEY" prop="key"/>
      <el-table-column :formatter="tableFormatTime" label="创建时间" prop="created_at"/>
      <el-table-column :formatter="tableFormatTime" label="更新时间" prop="updated_at"/>
      <el-table-column fixed="right" label="操作" width="120">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="() => {
            id = row.id; 
            showDetail = true
          }">编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="handlePage"/>
    <WorkflowDetail v-if="showDetail" :id="id" @close="handleDetailClose"/>
  </el-card>
</template>

<script setup>
import { ref, onMounted } from "vue";
import MyPagination from "../../components/MyPagination.vue";
import { workflows } from "../../api/modules/workflow";
import { tableFormatTime } from "../../utils";
import WorkflowDetail from './WorkflowDetail.vue';

// 数据和状态
const list = ref([])
const total = ref(0)
const loading = ref(false)
const showDetail = ref(false)
const id = ref(0)
let page = 1
const search = ref({
  keyword: '',
  key: ''
})

// 获取列表数据
const getList = async () => {
  loading.value = true
  try {
    // 实际项目中应使用API请求获取数据
    // 这里使用模拟数据展示
    // const res = await workflows({
    //   page,
    //   ...search.value
    // })
    
    // 模拟数据
    const mockData = {
      data: {
        items: [
          {
            id: 1,
            name: '订单审核流程',
            key: 'order_review',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 2,
            name: '退款申请流程',
            key: 'refund_process',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          },
          {
            id: 3,
            name: '商品上架流程',
            key: 'product_publish',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ],
        total: 3
      }
    }
    
    // 使用模拟数据
    const res = mockData
    list.value = res.data.items
    total.value = res.data.total
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 处理分页变化
const handlePage = (val) => {
  page = val
  getList()
}

// 处理详情对话框关闭
const handleDetailClose = () => {
  showDetail.value = false
  getList()
}

// 初始化加载数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
