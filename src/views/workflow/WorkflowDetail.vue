<template>
  <el-dialog
    :title="id ? '编辑工作流' : '新增工作流'"
    v-model="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      status-icon
    >
      <el-form-item label="工作流名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入工作流名称"/>
      </el-form-item>
      <el-form-item label="工作流KEY" prop="key">
        <el-input v-model="form.key" placeholder="请输入工作流KEY"/>
        <div class="form-tip">工作流KEY用于系统内部标识，请使用英文和下划线</div>
      </el-form-item>
      <el-form-item label="工作流描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入工作流描述"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { workflowDetail, workflowSave } from '../../api/modules/workflow'

const props = defineProps({
  id: {
    type: [Number, String],
    default: 0
  }
})

const emit = defineEmits(['close'])

const dialogVisible = ref(true)
const loading = ref(false)
const formRef = ref(null)

// 表单数据
const form = reactive({
  id: 0,
  name: '',
  key: '',
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入工作流名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  key: [
    { required: true, message: '请输入工作流KEY', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含英文、数字和下划线', trigger: 'blur' }
  ]
}

// 获取工作流详情
const getDetail = async () => {
  if (!props.id) return
  
  try {
    // 实际项目中应该调用API获取详情
    // const res = await workflowDetail(props.id)
    
    // 这里使用模拟数据
    const mockDetail = {
      id: props.id,
      name: `工作流${props.id}`,
      key: ['order_review', 'refund_process', 'product_publish'][props.id % 3],
      description: `这是工作流${props.id}的详细描述，用于说明工作流的用途和处理流程。`
    }
    
    // 更新表单数据
    Object.assign(form, mockDetail)
  } catch (error) {
    console.error('获取工作流详情失败', error)
    ElMessage.error('获取工作流详情失败')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    // 实际项目中应该调用API保存数据
    // await workflowSave(form)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('保存成功')
    handleClose()
  } catch (error) {
    console.error('表单验证失败', error)
    ElMessage.error('请检查表单填写是否正确')
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 初始化
onMounted(() => {
  if (props.id) {
    getDetail()
  }
})
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}
</style>
