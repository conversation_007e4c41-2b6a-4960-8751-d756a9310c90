<script setup>
import {activityDetail, activitySave} from '@/api/modules/activity';
import MyDateRangePicker from '@/components/MyDateRangePicker.vue';
import MyEditor from '@/components/MyEditor.vue';
import MyTaskSelector from '@/components/MyTaskSelector.vue';
import dayjs from 'dayjs';
import {ElMessage} from 'element-plus';
import {computed, onMounted, ref, watch} from 'vue';
import MyImageUpload from "@/components/MyImageUpload.vue";

const emits = defineEmits(['close', 'success'])
const props = defineProps({
  id: {default: 0}
})
const show = ref(true)
const form = ref({
  uuid: '',
  title: '',
  dates: [],
  task_id: 0,
  rules: '',
  mp_tpl_id: '',
  cover_res_id: 0,
  cover: '',
  link: '',
})
const title = computed(() => {
  const {id} = props
  const {title} = form.value
  if (id > 0) {
    return title
  }
  return '创建活动'
})
const formRef = ref(null)
const rules = computed(() => {
  let rules = {
    uuid: [{required: true, message: '请输入活动唯一标识'}],
    title: [{required: true, message: '请输入活动标题'}],
    dates: [{required: true, message: '请选择活动日期'}],
  }
  return rules
})

const doSubmit = async () => {
  const {dates} = form.value
  const params = {
    ...form.value,
    dates: undefined,
    start: dayjs(dates[0]).unix(),
    end: dayjs(dates[1]).unix(),
    id: props.id,
  }
  await activitySave(params)
  ElMessage.success('已保存')
  emits('success')
}

onMounted(() => {
  if (!props.id) {
    return
  }
  activityDetail(props.id).then(({data}) => {
    data.dates = [
      dayjs.unix(data.start).toDate(),
      dayjs.unix(data.end).toDate(),
    ]
    Object.assign(form.value, data)
  })
})

watch(show, (val) => {
  if (!val) {
    emits('close')
  }
})
</script>

<template>
  <el-drawer v-model="show" :title="title" size="70%">
    <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
      <el-form-item label="UUID" prop="uuid">
        <el-input v-model="form.uuid"/>
      </el-form-item>
      <el-form-item label="活动名称" prop="title">
        <el-input v-model="form.title"/>
      </el-form-item>
      <el-form-item label="活动时间" prop="dates">
        <MyDateRangePicker v-model="form.dates" is-next></MyDateRangePicker>
      </el-form-item>
      <el-form-item label="微信模板ID">
        <el-input v-model="form.mp_tpl_id"/>
      </el-form-item>
      <el-form-item label="跳转链接">
        <el-input v-model="form.link"/>
      </el-form-item>
      <el-form-item label="关联任务" prop="task_id">
        <MyTaskSelector v-model="form.task_id"></MyTaskSelector>
      </el-form-item>
      <el-form-item label="封面" prop="cover">
        <MyImageUpload v-model="form.cover_res_id" v-model:url="form.cover"/>
      </el-form-item>
      <el-form-item label="积分规则">
        <MyEditor v-model="form.rules"></MyEditor>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="doSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<style lang="scss" scoped></style>