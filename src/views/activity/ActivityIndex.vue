<script setup>
import {activityEnable, activityList} from '@/api/modules/activity';
import MyPagination from '@/components/MyPagination.vue';
import {copyText, formatTime} from '@/utils';
import {DateFmtLong, DateFmtLongFull, Disable, Enable, OnlineStates} from '@/utils/constmap';
import {onActivated, ref} from 'vue';
import {useRouter} from 'vue-router';
import ActivityDetail from './ActivityDetail.vue';


const search = ref({
  uuid: '',
  state: '',
  title: '',
})
const list = ref([])
const total = ref(0)
let page = 1
const router = useRouter()
const id = ref('')
const showDetail = ref(false)

const doLoad = async (reset = false) => {
  if (reset) {
    page = 1
  }
  let params = {
    ...search.value,
    page,
  }
  const {data} = await activityList(params)
  total.value = data.total
  list.value = data.list.map(v => v)
}
const doShowDetail = (activity_id = 0) => {
  id.value = activity_id
  showDetail.value = true
}
const doEnable = async (activity_id, state) => {
  await activityEnable({
    activity_id,
    state,
  })
  doLoad()
}

onActivated(() => {
  doLoad()
})
</script>

<template>
  <el-card>
    <el-space alignment="normal" direction="vertical">
      <div>
        <el-button type="primary" @click="doShowDetail()">添加活动</el-button>
      </div>

      <el-form inline>
        <el-form-item label="活动名称">
          <el-input v-model="search.title" clearable placeholder="请输入活动名称"/>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="search.state" clearable placeholder="请选择状态">
            <el-option v-for="item in OnlineStates" :key="item.value" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="UUID">
          <el-input v-model="search.uuid" clearable placeholder="请输入UUID"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="doLoad(true)">查询</el-button>
        </el-form-item>
      </el-form>
    </el-space>

    <el-table :data="list">
      <el-table-column fixed="left" width="60" label="ID" prop="id"/>
      <el-table-column fixed="left" width="200" label="活动名称" prop="title"/>
      <el-table-column width="200" label="主图" prop="cover">
        <template #default="{ row }">
          <el-image :src="row.cover" style="width: 80px; height: 80px;"/>
        </template>
      </el-table-column>
      <el-table-column width="80" label="状态">
        <template #default="{ row }">
          <el-tag :type="row.state === 2 ? 'danger' : 'primary'">{{ row.state_text }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column width="220" label="时间周期">
        <template #default="{ row }">
          {{ formatTime(row.start, DateFmtLong) }}~{{ formatTime(row.end, DateFmtLong) }}
        </template>
      </el-table-column>
      <el-table-column label="绑定任务" prop="task_name">
        <template #default="{ row }">
          <el-button type="primary" link @click="router.push({ name: 'TaskDetail', query: { id: row.task_id } })">{{
              row.task_name
            }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template #default="{ row }">
          <el-space wrap>
            <el-button link size="small" @click="copyText(row.uuid)">复制UUID</el-button>

            <el-button size="small" type="primary" @click="doShowDetail(row.id)">编辑</el-button>

            <el-popconfirm v-if="row.state === Enable" title="您确定要下线吗？" @confirm="doEnable(row.id, Disable)">
              <template #reference>
                <el-button size="small" type="danger">下线</el-button>
              </template>
            </el-popconfirm>
            <el-button v-else size="small" type="primary" @click="doEnable(row.id, Enable)">上线</el-button>

          </el-space>
        </template>
      </el-table-column>
    </el-table>
    <MyPagination :page="page" :total="total" @current-change="p => {
      page = p
      doLoad()
    }"/>
    <ActivityDetail v-if="showDetail" :id="id" @close="showDetail = false"
                    @success="() => { doLoad(true); showDetail = false }"/>
  </el-card>
</template>

<style lang="scss" scoped></style>