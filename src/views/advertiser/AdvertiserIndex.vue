<script setup>

import {onActivated, ref} from "vue";
import {advertisers, advertiserSave} from "../../api/modules/advertiser";
import {tableFormatTime} from "../../utils";
import MyPagination from "../../components/MyPagination.vue";
import {ElMessageBox, ElNotification} from "element-plus";

const list = ref([])
const total = ref(0)
let page = 1

function handleCreate() {
  ElMessageBox.prompt('请输入品牌方名称', '新增', {
    inputPattern: /^.+$/
  }).then(({value}) => {
    const data = {name: value}

    advertiserSave(data).then(() => {
      ElNotification.success('创建成功')
      getList()
    })
  })
}

function handleEdit(id, name) {
  ElMessageBox.prompt('请输入新方名称', `编辑 ${name} 的名称`, {
    inputPattern: /^.+$/
  }).then(({value}) => {
    const data = {name: value, id}

    advertiserSave(data).then(() => {
      ElNotification.success('修改成功')
      getList()
    })
  })
}

function getList() {
  const params = {page}

  advertisers(params).then(res => {
    const {data} = res

    list.value = data.list
    total.value = data.total
  })
}

onActivated(() => {
  getList()
})

</script>
<template>
  <el-card>
    <el-button @click="handleCreate" type="primary">新增</el-button>
    <el-table :data="list">
      <el-table-column prop="name" label="名称"/>
      <el-table-column label="创建时间" prop="created_at" :formatter="tableFormatTime"/>
      <el-table-column label="操作">
        <template #default="{row}">
          <el-button @click="handleEdit(row.id, row.name)" size="small" type="primary">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <MyPagination :total="total" @current-change="p => {
      page = p
      getList()
    }"/>

  </el-card>


</template>
