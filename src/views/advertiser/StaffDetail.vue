<script setup>

import {computed, onMounted, ref, toRaw, watch} from "vue";
import {useRouter} from "vue-router";
import MyRoleSelector from "../../components/MyRoleSelector.vue";
import {ElNotification} from "element-plus";
import {staffDetail, staffSave} from "../../api/modules/staff";

const emit = defineEmits(['close'])
const props = defineProps({
  id: Number
})
const router = useRouter()
const formRef = ref(null)
const form = ref({
  name: '',
  username: '',
  mobile: '',
  password: '',
  password2: '',
  role_id: []
})
const isEdit = computed(() => props.id > 0)
const rules = computed(() => {
  const r = {
    name: [{required: true, message: '姓名是必填的！'}],
    username: [{required: true, message: '登录名是必填的！'}],
    mobile: [{required: true, message: '手机号码是必填的！'}],
    role_id: [{required: true, message: '角色是必选的！'}],
  }

  if (!isEdit.value) {
    r['password'] = [{required: true, message: '请输入登录密码！'}]
    r['password2'] = [{required: true, message: '请在输一次密码！'}]
  }

  return r
})
const title = computed(() => isEdit.value ? '编辑员工' : '新增员工')
const show = ref(true)

function getDetail() {
  staffDetail(props.id).then(res => {
    const {data} = res

    data.role_id = data.roles
    Object.assign(form.value, data)
  })
}

function handleSubmit() {
  formRef.value.validate(valid => {
    if (!valid) {
      return
    }

    const data = toRaw(form.value)
    data.role_ids = JSON.stringify(data.role_id)

    staffSave(data).then(() => {
      ElNotification.success('保存成功')

      show.value = false
    })

  })
}

onMounted(() => {
  if (isEdit.value) {
    getDetail()
  }
})

watch(show, () => emit('close'))

</script>
<template>
  <el-drawer v-model="show" :title="title" size="70%">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="form.name"/>
      </el-form-item>
      <el-form-item label="登录名" prop="username">
        <el-input v-model="form.username"/>
      </el-form-item>
      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="form.mobile"/>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="form.password" placeholder="如需修改密码，请输入新密码"/>
      </el-form-item>
      <el-form-item label="确认密码" prop="password2">
        <el-input v-model="form.password2" placeholder="再次输入密码"/>
      </el-form-item>
      <el-form-item label="角色" prop="role_id">
        <MyRoleSelector v-model="form.role_id"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-drawer>
</template>

<style lang="scss" scoped>
.el-input {
  width: 300px;
}
</style>
