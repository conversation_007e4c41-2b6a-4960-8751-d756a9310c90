package hotel

import (
	"fmt"
	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/business/hotel_biz/hotel_asm"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
)

func Search(ctx *gin.Context) (any, error) {
	var in struct {
		beans.HotelSearchConditions
		Location string `form:"location"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	if in.Location == "" && (in.Lng == 0 || in.Lat == 0) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	radius := in.Radius
	if radius <= 0 {
		radius = constmap.SearchRadius
	}

	var lng, lat = in.Lng, in.Lat
	if in.Lng == 0 || in.Lat == 0 {
		resp, err := amap.Search(&go_amap.PlaceRequest{
			Keywords: in.Location,
			PageSize: 1,
		})
		if err != nil {
			return nil, err
		}
		if len(resp.Pois) > 0 {
			lng, lat = utils.SplitGeoLocation(resp.Pois[0].Location)
		}
	}

	if lng == 0 || lat == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	conds := []map[string]any{
		{
			"term": map[string]any{
				"type": constmap.PoiTypeHotel,
			},
		},
		{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		{
			"geo_distance": map[string]any{
				"distance": fmt.Sprintf("%.1fkm", radius),
				"location": map[string]any{
					"lat": lat,
					"lon": lng,
				},
			},
		},
	}

	sort := []map[string]any{
		{"_score": map[string]any{"order": "desc"}},
		{
			"_geo_distance": map[string]any{
				"location": map[string]any{
					"lat": lat,
					"lon": lng,
				},
				"order":         "asc",
				"distance_type": "arc",
			},
		},
		{"avg_price": map[string]any{"order": "asc"}},
	}

	if !strutil.IsBlank(in.Name) {
		conds = append(conds, map[string]any{
			"match": map[string]any{
				"name": in.Name,
			},
		})
	}

	if !strutil.IsBlank(in.BrandName) {
		conds = append(conds, map[string]any{
			"match": map[string]any{
				"brand_name": in.BrandName,
			},
		})
	}

	if in.Star > 0 {
		conds = append(conds, map[string]any{
			"range": map[string]any{
				"star": map[string]any{
					"gte": in.Star,
				},
			},
		})
	}

	if !strutil.IsBlank(in.PriceRange) {
		ranges := strings.Split(in.PriceRange, "-")
		if len(ranges) == 2 {
			var start float64
			var end float64
			start, _ = convertor.ToFloat(ranges[0])
			end, _ = convertor.ToFloat(ranges[1])
			if start == 0 && end > 0 {
				// -300
				conds = append(conds, map[string]any{
					"range": map[string]any{
						"avg_price": map[string]any{
							"lte": utils.CurrencyFloat2Int(end),
						},
					},
				})
			} else if start > 0 && end == 0 {
				// 300-
				conds = append(conds, map[string]any{
					"range": map[string]any{
						"avg_price": map[string]any{
							"gte": utils.CurrencyFloat2Int(start),
						},
					},
				})
			} else if start > 0 && end > 0 {
				conds = append(conds, map[string]any{
					"range": map[string]any{
						"avg_price": map[string]any{
							"gte": utils.CurrencyFloat2Int(start),
							"lte": utils.CurrencyFloat2Int(end),
						},
					},
				})
			}
		}
	}

	if lng > 0 && lat > 0 {
		sort = append([]map[string]any{}, sort...)
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": conds,
			},
		},
		"sort": sort,
		"size": 5,
	}
	searchResp, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "搜索查询失败")
	}

	db := utils.GetDB(ctx)

	hotelIds := slice.Map(searchResp.Hits.Hits, func(index int, item es.Hit[es2.Hotel]) uint {
		return item.Source.ObjId
	})
	segs, err := hotel_asm.BuildKnowledgeSegments(db, hotelIds)
	if err != nil {
		return nil, err
	}
	segMap := make(map[uint]*beans.DifyKnowledgeHotel)
	slice.ForEach(segs, func(index int, item *beans.DifyKnowledgeHotel) {
		segMap[item.HotelId] = item
	})

	var out struct {
		CenterPoint string                      `json:"center_point"`
		Total       int                         `json:"total"`
		List        []*beans.DifyKnowledgeHotel `json:"list"`
	}
	out.CenterPoint = fmt.Sprintf("%f,%f", lng, lat)
	out.Total = searchResp.Hits.Total.Value
	out.List = make([]*beans.DifyKnowledgeHotel, 0)

	for _, v := range searchResp.Hits.Hits {
		if hotel, ok := segMap[v.Source.ObjId]; ok {
			out.List = append(out.List, hotel)
		}
	}
	return out, nil
}
