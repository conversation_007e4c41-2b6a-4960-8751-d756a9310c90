package web

import (
	"context"
	"roadtrip-api/src/components/business/chromedp_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"strings"

	"github.com/chromedp/chromedp"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type FetchResponse struct {
	Code        int    `json:"code"`        // 状态码
	Status      string `json:"status"`      // 状态信息
	Title       string `json:"title"`       // 页面标题
	Description string `json:"description"` // 页面描述
	URL         string `json:"url"`         // 最终URL（考虑重定向后）
	Content     string `json:"content"`     // 爬取到的内容
}

func Fetch(c *gin.Context) (any, error) {
	var req struct {
		URL       string `form:"url" binding:"required"` // 要爬取的URL
		OnlyBody  int    `form:"only_body"`              // 是否只获取body部分
		StripHTML int    `form:"strip_html"`             // 是否去除HTML标签
		Query     string `form:"query"`                  // CSS选择器表达式，用于获取特定内容
	}
	if err := c.ShouldBind(&req); err != nil {
		return nil, errors.Wrap(err, constmap.ErrorMsgParam)
	}

	// 验证URL
	if !strutil.HasPrefixAny(req.URL, []string{"http://", "https://"}) {
		return nil, errors.New("无效的URL格式")
	}

	var (
		content     string
		title       string
		currentURL  string
		description string
	)

	// 由于chromedp不直接提供HTTP状态码，我们默认设置为200 OK
	statusCode := 200
	statusText := "OK"

	tasks := chromedp.Tasks{
		chromedp.WaitVisible("body", chromedp.ByQuery),

		// 获取页面标题
		chromedp.Title(&title),

		// 获取当前URL（考虑重定向后）
		chromedp.Location(&currentURL),

		// 获取页面描述（优化描述获取逻辑）
		chromedp.Evaluate(`
			(() => {
				// 尝试获取meta description
				const metaDesc = document.querySelector('meta[name="description"]');
				if (metaDesc) {
					const content = metaDesc.getAttribute('content');
					if (content && content.trim()) return content.trim();
				}
				
				// 尝试获取第一段有意义的文本
				const paragraphs = document.getElementsByTagName('p');
				for (const p of paragraphs) {
					const text = p.textContent.trim();
					if (text && text.length > 20) { // 确保文本有足够长度
						return text;
					}
				}
				
				// 如果还没有找到，尝试获取第一个主要内容区域的文本
				const mainContent = document.querySelector('main, article, #content, .content');
				if (mainContent) {
					const text = mainContent.textContent.trim();
					if (text) {
						// 返回前200个字符作为描述
						return text.slice(0, 200);
					}
				}
				
				return '';
			})()
		`, &description),

		// 获取页面内容
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error

			// 移除所有script标签
			if err := chromedp.Evaluate(`
				(() => {
					const scripts = document.getElementsByTagName('script');
					while(scripts.length > 0) {
						scripts[0].parentNode.removeChild(scripts[0]);
					}
				})()
			`, nil).Do(ctx); err != nil {
				return errors.Wrap(err, "移除script标签失败")
			}

			// 如果提供了查询表达式，优先使用它获取特定内容
			if req.Query != "" {
				// 先检查元素是否存在
				var exists bool
				if err := chromedp.Evaluate(`document.querySelector(`+"`"+req.Query+"`"+`) !== null`, &exists).Do(ctx); err != nil {
					return errors.Wrap(err, "执行查询表达式失败")
				}

				if !exists {
					return errors.New("未找到匹配查询表达式的元素")
				}

				// 获取匹配元素的HTML
				err = chromedp.OuterHTML(req.Query, &content, chromedp.ByQuery).Do(ctx)
			} else if req.OnlyBody == constmap.Enable {
				// 只获取body内容
				err = chromedp.OuterHTML("body", &content).Do(ctx)
			} else {
				// 获取整个页面内容
				err = chromedp.OuterHTML("html", &content).Do(ctx)
			}

			if err != nil {
				return errors.Wrap(err, "获取页面内容失败")
			}
			return nil
		}),
	}

	if err := chromedp_biz.ChromeRun(req.URL, tasks, true, "", 0); err != nil {
		return nil, errors.Wrap(err, "爬取页面失败")
	}

	// 如果需要去除HTML标签
	if req.StripHTML == constmap.Enable {
		content = utils.StripHTML(content)
		// 清理多余的空白字符
		content = strings.Join(strings.Fields(content), " ")

		// 如果描述为空或者也需要清理HTML标签
		if description != "" {
			description = utils.StripHTML(description)
			description = strings.Join(strings.Fields(description), " ")
		}
	}

	return FetchResponse{
		Code:        statusCode,
		Status:      statusText,
		Title:       title,
		Description: description,
		URL:         currentURL,
		Content:     content,
	}, nil
}
