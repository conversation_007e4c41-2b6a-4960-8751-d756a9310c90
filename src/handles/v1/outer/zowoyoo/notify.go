package zowoyoo

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"io"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Notify(ctx *gin.Context) (any, error) {
	ctx.Set(constmap.ContextHttpRaw, true)
	var err error
	var in struct {
		Apikey             string   `json:"apikey"`             //分销商密钥
		CancelId           string   `json:"cancelId"`           //取消审核id，与申请取消时返回的cancelId一致
		CancelMoney        float64  `json:"cancelMoney"`        //取消的金额，取消通知必有值
		CancelNum          int      `json:"cancelNum"`          //取消的数量，取消通知必有值
		CancelState        int      `json:"cancelState"`        //取消审核状态 1同意取消 2拒绝取消，取消通知必有值
		ConfirmState       int      `json:"confirmState"`       //1确认成功，2确认失败，确认通知必有值
		CustId             string   `json:"custId"`             //分销商帐号
		ExpressNo          string   `json:"expressNo"`          //物流单号，快递商品出票通知必有值
		FinishCodes        []string `json:"finishCodes"`        //已核销的凭证码集合，核销通知必有
		FinishNum          int      `json:"finishNum"`          //核销的总数量，核销通知必有值
		LogisticsCompanyNo string   `json:"logisticsCompanyNo"` //物流公司编码，快递商品出票通知必有值
		Method             string   `json:"method"`             //回调通知接口方法，confirm/确认通知(人工确认的产品需要接收，确认通过后才能调用支付，一般是酒店产品用到)；print/出票通知；cancel/取消通知；finish/核销通知；product/产品通知
		Num                int      `json:"num"`                //确认的订单数量，确认通知必有值
		OrderId            int64    `json:"orderId"`            //自我游订单号，订单相关的通知必有值
		OrderMoney         float64  `json:"orderMoney"`         //确认的订单结算总额，确认通知必有值
		OrderSourceId      string   `json:"orderSourceId"`      //第三方订单号，订单相关的通知必有值
		PrintMessage       string   `json:"printMessage"`       //出票失败原因，出票通知必有值
		PrintState         int      `json:"printState"`         //1出票成功，2出票失败，出票通知必有值
		ProductId          int64    `json:"productId"`          //自我游产品id,产品变更通知必有值
		ProductType        int      `json:"productType"`        //产品变更类别,1价格变更，2基本信息变更，3下架，4上架，产品变更通知必有值
		SaleMoney          float64  `json:"saleMoney"`          //确认的订单零售总额，确认通知必有值
		Vouchers           []struct {
			Code          string `json:"code"`          //凭证码
			CreditNo      string `json:"creditNo"`      //证件号码
			Name          string `json:"name"`          //姓名
			PdfUrl        string `json:"pdfUrl"`        //pdf凭证链接
			QrcodeUrl     string `json:"qrcodeUrl"`     //二维码凭证链接
			Type          int    `json:"type"`          //凭证类型 0凭证码 1二维码链接 2pdf 3普通链接
			VoucherStatus int    `json:"voucherStatus"` //凭证类型 0 正常 1 已核销 2 已取消 3申请取消中 4取消处理中
			VoucherUrl    string `json:"voucherUrl"`    //3普通链接
		} `json:"vouchers"` //凭证信息，出票通知必有值
	}
	type out struct {
		State int    `json:"state"` //状态 1成功 0失败
		Msg   string `json:"msg"`
	}
	var outData = new(out)
	var body, _ = io.ReadAll(ctx.Request.Body)
	defer func() {
		my_logger.Infof("[zwy]notify", zap.ByteString("input", body), zap.Any("output", outData))
		ctx.JSON(200, outData)
	}()
	setResponse := func(err error) (any, error) {
		if err == nil {
			outData.State = 1
		} else {
			outData.Msg = err.Error()
		}
		return nil, nil
	}
	if err = json.Unmarshal(body, &in); err != nil {
		return setResponse(nil)
	}
	//交由定时任务统一处理
	return setResponse(nil)

	if in.Apikey != config.Config.Ota.Zwy.ApiKey || in.CustId != convertor.ToString(config.Config.Ota.Zwy.AgentId) {
		return setResponse(utils.NewErrorStr(constmap.ErrorParam, "参数校验错误"))
	}
	db := utils.GetDB(ctx)
	getThirdOrder := func() (*models.OrderThirdOrder, error) {
		var thirdOrder models.OrderThirdOrder
		db.Model(&thirdOrder).Preload("OrderDetail").Take(&thirdOrder, models.OrderThirdOrder{
			ThirdOrderId:   convertor.ToString(in.OrderId),
			ThirdOrderType: constmap.ThirdOrderTypeZwy,
		})
		if thirdOrder.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "第三方订单不存在")
		}
		return &thirdOrder, nil
	}
	if in.Method == "confirm" {
		// 确认通知(人工确认的产品需要接收，确认通过后才能调用支付，一般是酒店产品用到)
		// - 确认成功:订单已支付，三方订单支付处理
		// - 确认失败:子订单标记失败，主订单停留在处理中状态
		thirdOrder, err := getThirdOrder()
		if err != nil {
			return setResponse(err)
		}
		var order models.Order
		db.Model(&order).Where("id=?", thirdOrder.OrderId).Take(&order)
		if in.ConfirmState == 2 {
			db.Model(&thirdOrder.OrderDetail).Updates(models.OrderDetail{State: constmap.OrderDetailStateFail, Remark: "确认失败"})
		} else if in.ConfirmState == 1 {
			if order.PayState == constmap.PayStatePayed {
				if err = db.Transaction(func(tx *gorm.DB) error {
					if thirdOrder.ThirdPayState == constmap.ThirdPayStateNeedConfirm {
						tx.Model(&thirdOrder).Updates(models.OrderThirdOrder{ThirdPayState: constmap.ThirdPayStateWaitPay})
					}
					return nil
				}); err != nil {
					return setResponse(err)
				}
				if err = orders.ThirdOrderPay(db, order.ID); err != nil {
					return setResponse(err)
				}
				return setResponse(err)
			}
		}
	} else if in.Method == "print" {
		// 出票通知
		// - 出票成功:子订单标记成功，全部子订单处理成功后标记主订单处理成功
		// - 出票失败:子订单标记失败，主订单停留在处理中状态
		thirdOrder, err := getThirdOrder()
		if err != nil {
			return setResponse(err)
		}
		if in.PrintState == 2 {
			errMsg := "出票失败:" + in.PrintMessage
			db.Model(&thirdOrder).Omit(clause.Associations).Updates(&models.OrderThirdOrder{ThirdPayState: constmap.ThirdPayStatePrintFail, Remark: errMsg})
			db.Model(&thirdOrder.OrderDetail).Updates(models.OrderDetail{State: constmap.OrderDetailStateFail, Remark: errMsg})
		} else if in.PrintState == 1 {
			err = db.Transaction(func(tx *gorm.DB) error {
				tx.Model(&thirdOrder).Omit(clause.Associations).Updates(&models.OrderThirdOrder{ThirdPayState: constmap.ThirdPayStatePayed})
				tx.Model(&thirdOrder.OrderDetail).Updates(models.OrderDetail{State: constmap.OrderDetailStateSuccess})
				var vouchers []models.OrderVoucher
				tx.Model(&vouchers).Take(&vouchers, models.OrderVoucher{OrderId: thirdOrder.OrderId, OrderDetailId: thirdOrder.OrderDetailId})
				if len(vouchers) == 0 && len(in.Vouchers) > 0 { //补充票证内容
					stateMap := map[int]constmap.VoucherState{
						0: constmap.VoucherStateNormal,
						1: constmap.VoucherStateUsed,
						2: constmap.VoucherStateCancel,
						3: constmap.VoucherStateCanceling,
						4: constmap.VoucherStateCanceling,
					}
					for _, v := range in.Vouchers {
						b, _ := json.Marshal(v)
						vouchers = append(vouchers, models.OrderVoucher{
							OrderId:   thirdOrder.OrderId,
							State:     stateMap[v.VoucherStatus],
							VoucherNo: v.Code,
							Pdf:       v.PdfUrl,
							Qr:        v.QrcodeUrl,
							Url:       v.VoucherUrl,
							Ext:       string(b),
						})
					}
					if err = tx.Create(vouchers).Error; err != nil {
						return fmt.Errorf("创建核销数据失败:%v", err)
					}
				}
				return orders.OrderDetailStateChange(tx, thirdOrder.OrderId, constmap.OrderDetailStateSuccess)
			})
			return setResponse(err)
		}

	} else if in.Method == "cancel" {
		// 取消通知
		thirdOrder, err := getThirdOrder()
		if err != nil {
			return setResponse(err)
		}
		var refund models.RefundPayment
		db.Model(&refund).Take(&refund, models.RefundPayment{OrderId: thirdOrder.OrderId, State: constmap.RefundStateThirdRefund})
		if refund.ID == 0 {
			return setResponse(utils.NewErrorStr(constmap.ErrorParam, "不存在【三方OTA退款处理中】的退款单"))
		}
		if in.CancelState == 1 {
			orders.RefundCheck(db, refund)
		} else if in.CancelState == 2 {
			// 拒绝取消,退款单标记为退款异常
			db.Model(&refund).
				Omit(clause.Associations).Updates(models.RefundPayment{
				State: constmap.RefundStateAbnormal,
				Resp:  "[zwy]第三方订单退款拒绝",
			})
		}
	} else if in.Method == "finish" {
		// 核销通知
		thirdOrder, err := getThirdOrder()
		if err != nil {
			return setResponse(err)
		}
		if len(in.FinishCodes) > 0 {
			err = db.Transaction(func(tx *gorm.DB) error {
				tx.Model(&models.OrderVoucher{}).
					Where(models.OrderVoucher{
						OrderId:       thirdOrder.OrderId,
						OrderDetailId: thirdOrder.OrderDetailId,
					}).
					Where("voucher_no in ?", in.FinishCodes).
					Updates(models.OrderVoucher{State: constmap.VoucherStateUsed})
				return orders.VoucherStateChange(tx, thirdOrder.OrderDetail, constmap.VoucherStateUsed)
			})
			return setResponse(err)
		}
	} else if in.Method == "product" {
		// 产品通知
	}
	return setResponse(nil)
}
