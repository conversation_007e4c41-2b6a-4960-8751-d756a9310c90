package advertiser

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Save(ctx *gin.Context) (any, error) {
	var in struct {
		Id   uint   `form:"id"`
		Name string `form:"name" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)
	var err error

	if in.Id == 0 {
		record := models.Advertiser{
			Name: in.Name,
		}

		err = db.Create(&record).Error
	} else {
		var record models.Advertiser
		if err = db.Take(&record, in.Id).Error; err != nil {
			return nil, utils.NewError(err)
		}

		if err = db.Model(&record).Updates(models.Advertiser{Name: in.Name}).Error; err != nil {

		}
	}

	return nil, err
}
