package advertiser

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type item struct {
		Id        uint   `json:"id"`
		Name      string `json:"name"`
		CreatedAt int64  `json:"created_at"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	whereStr := strings.Builder{}
	whereArgs := []any{}

	db.Model(&models.Advertiser{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []models.Advertiser
	db.Where(whereStr.String(), whereArgs...).Offset((page - 1) * pageSize).
		Limit(pageSize).Order("id desc").Find(&list)

	for _, v := range list {
		data.List = append(data.List, item{
			Id:        v.ID,
			Name:      v.Name,
			CreatedAt: v.CreatedAt.Unix(),
		})
	}

	return data, nil
}
