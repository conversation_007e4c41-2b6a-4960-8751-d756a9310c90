package wish

import (
	"fmt"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

// Finish 标记心愿单用户已去过（管理后台）
func Finish(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"` // 心愿单ID
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	// 查询心愿单
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 前置校验：只有已达成的心愿单才能标记为已完成
	if wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前状态的心愿单不可标记为已完成")
	}

	// 执行标记完成操作，使用WHERE条件确保只有已达成状态才能转换为已完成
	result := db.Model(&wish).Where(models.Wish{
		State: constmap.WishStateSuccess,
	}).Updates(models.Wish{
		State: constmap.WishStateFinished,
	})
	if result.Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "标记完成失败，请稍后重试")
	}
	if result.RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单状态已改变，无法标记为已完成")
	}

	// 同步到ES
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": fmt.Sprintf("%d", in.WishId),
	})

	var out struct {
		WishId uint               `json:"wish_id"`
		State  constmap.WishState `json:"state"`
	}
	out.WishId = wish.ID
	out.State = wish.State

	return out, nil
}
