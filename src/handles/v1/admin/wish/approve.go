package wish

import (
	"roadtrip-api/src/components/business/wish_biz/wish_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

// 审核通过心愿单（管理后台）
func Approve(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"` // 心愿单ID
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	// 查询心愿单
	var wish models.Wish
	if err := db.Preload("Medias").Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 前置校验：只有已驳回的心愿单才能审核通过
	if wish.State != constmap.WishStateRejected {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前状态的心愿单不可审核通过")
	}

	// 调用现有的审核通过逻辑
	if err := wish_asm.SetRiskCheckOk(db, &wish, wish.Medias); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "审核通过失败")
	}

	return gin.H{
		"message": "心愿单审核通过成功",
	}, nil
}
