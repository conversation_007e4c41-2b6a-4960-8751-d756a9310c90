package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// MemberUpdate 更新同行人信息（管理后台）
func MemberUpdate(ctx *gin.Context) (any, error) {
	var in struct {
		MemberId    uint                     `form:"member_id" binding:"required"`
		FollowState constmap.WishFollowState `form:"follow_state"`
		Budget      string                   `form:"budget"` // 预算（可选）
		Remark      string                   `form:"remark"` // 备注（可选）
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	// 检查同行人是否存在
	var member models.WishMember
	if err := db.Take(&member, in.MemberId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "同行人不存在")
	}

	// 构建更新数据
	updateData := models.WishMember{}
	hasUpdate := false

	if in.Budget != "" {
		updateData.Budget = in.Budget
		hasUpdate = true
	}

	if in.Remark != "" {
		updateData.Remark = in.Remark
		hasUpdate = true
	}

	if in.FollowState != 0 {
		updateData.FollowState = in.FollowState
		hasUpdate = true
	}

	if !hasUpdate {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "没有需要更新的数据")
	}

	// 执行更新操作，使用WHERE条件确保同行人存在
	result := db.Model(&models.WishMember{}).Where("id = ?", in.MemberId).Updates(updateData)
	if result.Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新同行人信息失败")
	}
	if result.RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "同行人不存在或数据未改变")
	}

	// 更新本地数据，避免重新查询数据库
	if in.Budget != "" {
		member.Budget = in.Budget
	}
	if in.Remark != "" {
		member.Remark = in.Remark
	}

	var out struct {
		MemberId        uint                     `json:"member_id"`
		Budget          string                   `json:"budget"`
		Remark          string                   `json:"remark"`
		State           constmap.WishMemberState `json:"state"`
		StateText       string                   `json:"state_text"`
		FollowState     constmap.WishFollowState `json:"follow_state"`
		FollowStateText string                   `json:"follow_state_text"`
	}
	out.MemberId = member.ID
	out.Budget = member.Budget
	out.Remark = member.Remark
	out.State = member.State
	out.StateText = business.WishMemberStateText(member.State)
	out.FollowState = member.FollowState
	out.FollowStateText = business.WishFollowStateAdminText(member.FollowState)

	return out, nil
}
