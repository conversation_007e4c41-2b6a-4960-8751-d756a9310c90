package wish

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// MemberReview 审核同行人（管理后台）
func MemberReview(ctx *gin.Context) (any, error) {
	var in struct {
		MemberId     uint   `form:"member_id" binding:"required"`  // 同行人ID
		IsApprove    int    `form:"is_approve" binding:"required"` // 是否通过：1-通过，2-拒绝
		RejectReason string `form:"reject_reason"`                 // 拒绝原因（拒绝时必填）
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 验证是否通过参数
	if in.IsApprove != constmap.Enable && in.IsApprove != constmap.Disable {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无效的审核参数")
	}

	// 拒绝时必须提供拒绝原因
	if in.IsApprove == constmap.Disable && in.RejectReason == "" {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "拒绝时必须提供拒绝原因")
	}

	db := utils.GetDB(ctx)

	// 先查询同行人信息获取心愿单ID
	var member models.WishMember
	if err := db.Take(&member, in.MemberId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "同行人不存在")
	}

	// 验证同行人状态
	if member.State != constmap.WishMemberStateWaitReview {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "同行人状态不可审核")
	}

	// 查询心愿单信息并验证状态
	var wish models.Wish
	if err := db.Take(&wish, member.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 验证心愿单状态（只有进行中的心愿单才能审核同行人）
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前心愿单状态不允许审核同行人")
	}

	// 确定新状态和更新数据
	var newState constmap.WishMemberState
	updateData := models.WishMember{}

	if in.IsApprove == constmap.Enable {
		newState = constmap.WishMemberStateApproved
		updateData.State = newState
	} else {
		newState = constmap.WishMemberStateRejected
		updateData.State = newState
		updateData.Remark = in.RejectReason
	}

	// 使用事务确保数据一致性
	err := db.Transaction(func(tx *gorm.DB) error {
		// 执行审核操作（只更新待审核状态的同行人）
		if tx.Model(&models.WishMember{}).Where("id = ? AND state = ?", in.MemberId, constmap.WishMemberStateWaitReview).Updates(updateData).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "审核失败，同行人不存在或状态不可审核")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	var out struct {
		MemberId     uint                     `json:"member_id"`
		IsApprove    int                      `json:"is_approve"`
		State        constmap.WishMemberState `json:"state"`
		StateText    string                   `json:"state_text"`
		WishAchieved bool                     `json:"wish_achieved"` // 心愿单是否达成
	}
	out.MemberId = in.MemberId
	out.IsApprove = in.IsApprove
	out.State = newState
	out.StateText = business.WishMemberStateText(newState)

	return out, nil
}
