package wish

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// UpdateFollowState 更新心愿单跟进状态（管理后台）
func UpdateFollowState(ctx *gin.Context) (any, error) {
	var in struct {
		WishId      uint                     `form:"wish_id" binding:"required"`
		MemberId    uint                     `form:"member_id" binding:"required"`
		FollowState constmap.WishFollowState `form:"follow_state" binding:"required"`
		Remark      string                   `form:"remark"` // 跟进备注（可选）
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 验证跟进状态值
	validStates := []constmap.WishFollowState{
		constmap.WishFollowStateDefault,
		constmap.WishFollowStateWait,
		constmap.WishFollowStateProcessing,
		constmap.WishFollowStateTransFinished,
		constmap.WishFollowStateTransCancelled,
	}
	if !slice.Contain(validStates, in.FollowState) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无效的跟进状态")
	}

	db := utils.GetDB(ctx)

	// 检查心愿单是否存在
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	var wishMember models.WishMember
	if err := db.Where(models.WishMember{
		WishId: in.WishId,
	}).Take(&wishMember, in.MemberId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单成员不存在")
	}

	if wishMember.State != constmap.WishMemberStateApproved {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单成员状态不是已通过")
	}

	// 更新跟进状态，使用WHERE条件确保心愿单存在且可更新
	result := db.Model(&wishMember).Updates(models.WishMember{FollowState: in.FollowState})
	if result.Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新跟进状态失败")
	}
	if result.RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "更新失败")
	}

	var out struct {
		WishId          uint                     `json:"wish_id"`
		MemberId        uint                     `json:"member_id"`
		FollowState     constmap.WishFollowState `json:"follow_state"`
		FollowStateText string                   `json:"follow_state_text"`
		Remark          string                   `json:"remark"`
	}
	out.WishId = in.WishId
	out.FollowState = in.FollowState
	out.FollowStateText = business.WishFollowStateAdminText(in.FollowState)
	out.MemberId = in.MemberId
	out.Remark = in.Remark

	return out, nil
}
