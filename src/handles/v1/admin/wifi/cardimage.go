package wifi

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func CardImage(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var card models.FlowCard
	if err := db.Take(&card, in.Id).Error; err != nil {
		return nil, err
	} else if card.Type != constmap.FlowCardTypeDefault {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	ctx.Set(constmap.ContextHttpRaw, true)

	buf, err2 := wifis.GenCardImage(&card, nil, true)
	if err2 != nil {
		return nil, err2
	}

	ctx.Data(200, "image/png", buf.Bytes())

	return nil, nil
}
