package wifi

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Rollback(ctx *gin.Context) (any, error) {
	var inn struct {
		RelId uint   `form:"rel_id"`
		Code  string `form:"code"`
	}
	_ = ctx.ShouldBind(&inn)

	if inn.RelId == 0 && inn.Code == "" {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var rel models.FlowCardRelCode
	if db.Joins("Code").Take(&rel, inn.RelId).Error != nil {
		if err := db.Joins("Code").Where("Code.code=?", inn.Code).Take(&rel).Error; err != nil {
			return nil, err
		}
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Unscoped().Delete(&rel).Error; err != nil {
			return err
		}
		code := rel.Code
		updateCode := models.FlowCardCode{
			GotState: constmap.GotStateDefault,
		}
		if tx.Model(&code).Updates(updateCode).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}

		//更新唯一码
		if rel.RewardId > 0 {
			if tx.Model(models.FlowCardRewardCode{}).Where("id=?", rel.RewardId).
				Updates(models.FlowCardRewardCode{State: constmap.Enable}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
		}

		//更新库存
		var stock models.FlowCardAirline
		if err := tx.Where("flow_card_id=? and code=?", rel.FlowCardId, rel.Airline).Take(&stock).Error; err != nil {
			return err
		}
		updateStock := map[string]any{
			"remaining_stock": gorm.Expr("remaining_stock+1"),
		}
		if tx.Model(&stock).Updates(updateStock).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}

		return nil
	})

	return nil, err
}
