package wifi

import (
	"archive/zip"
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/fs"
	"os"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

type zipFileInfo struct {
	name    string
	mod     time.Time
	content []byte
}

func (z zipFileInfo) Name() string {
	return z.name
}

func (z zipFileInfo) Size() int64 {
	return int64(len(z.content))
}

func (z zipFileInfo) Mode() fs.FileMode {
	return os.FileMode(0644)
}

func (z zipFileInfo) ModTime() time.Time {
	return z.mod
}

func (z zipFileInfo) IsDir() bool {
	return false
}

func (z zipFileInfo) Sys() any {
	return nil
}

func Export(ctx *gin.Context) (any, error) {
	var inn struct {
		Id   uint   `form:"id"`
		Code string `form:"code"`
	}
	_ = ctx.ShouldBind(&inn)

	var card models.FlowCard
	db := utils.GetDB(ctx)
	if err := db.Joins("Advertiser").Take(&card, inn.Id).Error; err != nil {
		return nil, err
	}

	var rewards []models.FlowCardRewardCode
	db.Where("flow_card_id = ?", card.ID).Find(&rewards)

	var zipBuf bytes.Buffer
	zipWriter := zip.NewWriter(&zipBuf)

	for _, reward := range rewards {
		buf, err := wifis.GenCardImage(&card, &reward, false)
		if err != nil {
			continue
		}
		header, err := zip.FileInfoHeader(&zipFileInfo{
			name:    fmt.Sprintf("%s.png", reward.Code),
			mod:     time.Now(),
			content: buf.Bytes(),
		})
		if err != nil {
			continue
		}

		header.Method = zip.Deflate
		writer, err := zipWriter.CreateHeader(header)
		if err != nil {
			continue
		}
		if _, err = writer.Write(buf.Bytes()); err != nil {
			continue
		}
	}
	_ = zipWriter.Close()

	err := business.Download(
		fmt.Sprintf("%s-%s.zip", utils.If(card.Title == "", card.Advertiser.Name, card.Title), time.Now().Format("20060102")),
		ctx,
		func(ctx2 *gin.Context) error {
			_, err := ctx.Writer.Write(zipBuf.Bytes())
			return err
		})

	return nil, err
}
