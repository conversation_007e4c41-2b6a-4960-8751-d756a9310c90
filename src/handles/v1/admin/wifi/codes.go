package wifi

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func Codes(ctx *gin.Context) (any, error) {
	var in struct {
		Airline string `form:"airline"`
		State   int    `form:"state"`
		Code    string `form:"code"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type item struct {
		Id           uint   `json:"id"`
		Code         string `json:"code"`
		State        int    `json:"state"`
		StateText    string `json:"state_text"`
		GetState     int    `json:"get_state"`
		GetStateText string `json:"get_state_text"`
		GetAt        int64  `json:"get_at"`
		CreatedAt    int64  `json:"created_at"`
		EndAt        int64  `json:"end_at"`
		AirlineName  string `json:"airline_name"`
		AirlineCode  string `json:"airline_code"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	whereStr := strings.Builder{}
	whereStr.WriteString("1=1")
	whereArgs := []any{}

	if !strutil.IsBlank(in.Airline) {
		whereStr.WriteString(" and airline=?")
		whereArgs = append(whereArgs, in.Airline)
	}
	if in.State > 0 {
		whereStr.WriteString(" and state=?")
		whereArgs = append(whereArgs, in.State)
	}
	if !strutil.IsBlank(in.Code) {
		whereStr.WriteString(" and code=?")
		whereArgs = append(whereArgs, in.Code)
	}

	db.Model(&models.FlowCardCode{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []models.FlowCardCode
	db.Where(whereStr.String(), whereArgs...).Order("id desc").Offset((page - 1) * pageSize).Limit(pageSize).Find(&list)
	data.List = make([]item, len(list))

	for i, code := range list {
		data.List[i] = item{
			Id:           code.ID,
			Code:         code.Code,
			State:        code.State,
			StateText:    wifis.FlowcardStateText(code.State),
			GetState:     code.GotState,
			GetStateText: wifis.FlowCardCodeGetStateText(code.GotState),
			AirlineCode:  code.Airline,
			CreatedAt:    code.CreatedAt.Unix(),
			EndAt:        code.EndAt.Unix(),
		}
	}

	airlineCodes := utils.SliceColumn(data.List, func(val item) string {
		return val.AirlineCode
	})
	ids := utils.SliceColumn(data.List, func(val item) uint {
		return val.Id
	})

	if len(airlineCodes) > 0 {
		var airlines []models.Airline
		db.Where("code in ?", maputil.Keys(airlineCodes)).Find(&airlines)
		for _, airline := range airlines {
			for _, ints := range airlineCodes {
				for _, i := range ints {
					data.List[i].AirlineName = airline.Name
				}
			}
		}

		var rels []models.FlowCardRelCode
		db.Where("flow_card_code_id in ?", maputil.Keys(ids)).Find(&rels)
		for _, rel := range rels {
			for _, ints := range ids {
				for _, i := range ints {
					data.List[i].GetAt = rel.CreatedAt.Unix()
				}
			}
		}
	}

	return data, nil
}
