package wifi

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func Import(ctx *gin.Context) (any, error) {
	db := utils.GetDB(ctx)
	file, err := ctx.FormFile("file")
	airline := ctx.PostForm("airline")
	if err != nil {
		return nil, err
	} else if strutil.IsBlank(airline) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if err != nil {
		return nil, err
	}
	fs, err := file.Open()
	if err != nil {
		return nil, err
	}
	headMap := map[string]struct{}{
		"上网码": {},
		"有效期": {},
	}
	f, err := excelize.OpenReader(fs)
	if err != nil {
		return nil, err
	}

	defer f.Close()

	sheet := f.GetSheetName(0)
	rows, err := f.Rows(sheet)
	if err != nil {
		return nil, err
	}

	now := time.Now()

	if !rows.Next() {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "excel纸张页内容错误")
	}
	var heads []string
	var lineCnt = 1
	if heads, err = rows.Columns(); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "excel头部信息解析失败")
	} else {
		var headCnt int
		slice.ForEach(heads, func(_ int, item string) {
			if _, ok := headMap[item]; ok {
				headCnt++
			}
		})
		if headCnt != len(headMap) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "excel缺少必要列内容")
		}
	}

	var list []models.FlowCardCode

	for rows.Next() {
		lineCnt++
		row, err := rows.Columns()
		if err != nil {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d行内容解析失败", lineCnt))
		}
		var cntMap = make(map[string]string)
		for i, item := range row {
			if i > len(heads)-1 {
				continue
			}
			if _, ok := headMap[heads[i]]; ok {
				if strutil.IsBlank(item) {
					return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d行第%d列内容不能为空", lineCnt, i+1))
				}
				cntMap[heads[i]] = item
			}
		}
		if len(cntMap) != len(headMap) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d行缺少必要列内容", lineCnt))
		}

		endAt, err1 := time.Parse(constmap.DateFmtLong, cntMap["有效期"])
		if err1 != nil || endAt.IsZero() {
			continue
		}

		list = append(list, models.FlowCardCode{
			Airline: airline,
			Code:    cntMap["上网码"],
			EndAt:   endAt,
			StartAt: now,
		})
	}

	for _, codes := range slice.Chunk(list, 100) {
		db.Create(codes)
	}

	return nil, err
}
