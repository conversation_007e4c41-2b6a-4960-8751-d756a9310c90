package wifi

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func FlowCards(ctx *gin.Context) (any, error) {
	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type air struct {
		Code       string `json:"code"`
		Name       string `json:"name"`
		TotalStock int    `json:"total_stock"`
		LeftStock  int    `json:"left_stock"`
	}

	type item struct {
		Code           string `json:"code"`
		Title          string `json:"title"`
		Id             uint   `json:"id"`
		Type           int    `json:"type"`
		TypeText       string `json:"type_text"`
		State          int    `json:"state"`
		StateText      string `json:"state_text"`
		StartTime      int64  `json:"start_time"`
		EndTime        int64  `json:"end_time"`
		CreatedAt      int64  `json:"created_at"`
		AdvertiserName string `json:"advertiser_name"`

		Airs []air `json:"airs"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	data.List = make([]item, 0)
	db.Model(&models.FlowCard{}).Count(&data.Total)

	var list []models.FlowCard
	db.Offset((page - 1) * pageSize).
		Preload("Airlines.Air").
		Joins("Advertiser").Limit(pageSize).
		Order("flow_cards.id desc").Find(&list)

	for _, card := range list {
		it := item{
			Code:           card.Code,
			Title:          card.Title,
			Id:             card.ID,
			Type:           card.Type,
			TypeText:       wifis.FlowcardTypeText(card.Type),
			State:          card.State,
			StateText:      wifis.FlowcardStateText(card.State),
			StartTime:      card.StartTime.Unix(),
			EndTime:        card.EndTime.Unix(),
			CreatedAt:      card.CreatedAt.Unix(),
			AdvertiserName: card.Advertiser.Name,
			Airs: slice.Map(card.Airlines, func(index int, item models.FlowCardAirline) air {
				return air{
					Code:       item.Air.Code,
					Name:       item.Air.Name,
					TotalStock: item.TotalStock,
					LeftStock:  item.RemainingStock,
				}
			}),
		}

		data.List = append(data.List, it)
	}

	return data, nil
}
