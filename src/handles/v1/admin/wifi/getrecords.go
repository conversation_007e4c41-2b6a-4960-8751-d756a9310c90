package wifi

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func GetRecords(ctx *gin.Context) (any, error) {
	var in struct {
		FlowcardId uint `form:"flowcard_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	type item struct {
		Id          uint   `json:"id"`
		UserId      uint   `json:"user_id"`
		Name        string `json:"name"`
		Airline     string `json:"airline"`
		AirlineName string `json:"airline_name"`
		Ip          string `json:"ip"`
		CreatedAt   int64  `json:"created_at"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	whereStr := "flow_card_id=?"
	whereArgs := []any{in.FlowcardId}

	db.Model(&models.FlowCardRelCode{}).Where(whereStr, whereArgs...).Count(&data.Total)

	var list []models.FlowCardRelCode
	db.Model(&models.FlowCardRelCode{}).
		Joins("Air").Joins("User").
		Where(whereStr, whereArgs...).
		Limit(pageSize).Offset((page - 1) * pageSize).
		Order("flow_card_rel_codes.id desc").
		Find(&list)
	data.List = make([]item, 0)
	for _, code := range list {
		data.List = append(data.List, item{
			Id:          code.ID,
			UserId:      code.UserId,
			Name:        code.User.Nickname,
			Airline:     code.Airline,
			AirlineName: code.Air.Name,
			Ip:          code.Ip,
			CreatedAt:   code.CreatedAt.Unix(),
		})
	}

	return data, nil
}
