package admin

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Zones(ctx *gin.Context) (any, error) {
	var in struct {
		Level int `form:"level"`
	}

	_ = ctx.ShouldBind(&in)

	levels := []int{
		constmap.ZoneLevelCity,
		constmap.ZoneLevelProvince,
	}
	if in.Level == constmap.ZoneLevelProvince {
		levels = []int{in.Level}
	}

	var list []models.Zone

	db := utils.GetDB(ctx)
	db.Where("state=? and level in ?", constmap.Enable, levels).Find(&list)

	type z struct {
		Id     uint   `json:"id"`
		Name   string `json:"name"`
		Level  int    `json:"level"`
		Pinyin string `json:"pinyin"`
		List   []z    `json:"list"`
	}

	var data struct {
		List []z `json:"list"`
	}

	provinces := slice.Filter(list, func(index int, zone models.Zone) bool {
		return zone.Level == constmap.ZoneLevelProvince
	})
	cities := slice.Filter(list, func(index int, zone models.Zone) bool {
		return zone.Level == constmap.ZoneLevelCity
	})

	data.List = make([]z, len(provinces))
	provinceIdMap := make(map[uint]int)

	for i, p := range provinces {
		data.List[i] = z{
			Name:   p.Name,
			Level:  p.Level,
			Id:     p.ID,
			Pinyin: p.Pinyin,
			List:   make([]z, 0),
		}
		provinceIdMap[p.ID] = i
	}
	for _, c := range cities {
		if index, ok := provinceIdMap[c.ParentId]; ok {
			data.List[index].List = append(data.List[index].List, z{
				Name:   c.Name,
				Level:  c.Level,
				Id:     c.ID,
				Pinyin: c.Pinyin,
				List:   []z{},
			})
		}
	}

	return data, nil
}
