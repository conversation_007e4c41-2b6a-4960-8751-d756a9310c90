package pkg

import (
	"errors"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

// Enable 开启或关闭套餐
func Enable(ctx *gin.Context) (any, error) {
	var in struct {
		Id    uint `form:"id" binding:"required"`
		State int  `form:"state" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	if in.State != constmap.Enable && in.State != constmap.Disable {
		return nil, utils.NewError(errors.New("state状态值错误"))
	}

	db := utils.GetDB(ctx)

	if db.Model(&models.Package{}).Where("id=?", in.Id).Updates(models.Package{
		State: in.State,
	}).RowsAffected == 0 {
		return nil, utils.NewError(errors.New("更新失败"))
	}

	var out struct {
		Id    uint `json:"id"`
		State int  `json:"state"`
	}
	out.Id = in.Id
	out.State = in.State
	return out, nil
}
