package pkg

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var pkg models.Package
	if err := db.Take(&pkg, in.Id).Error; err != nil {
		return nil, err
	}

	var data struct {
		Id           uint                    `json:"id"`
		Name         string                  `json:"name"`
		Desc         string                  `json:"desc"`
		Price        int64                   `json:"price"`          // 价格：分
		RealPrice    int64                   `json:"real_price"`     // 实际价格：分
		Amount       int                     `json:"amount"`         // 给予金额
		ExpNum       int                     `json:"exp_num"`        // 到期数字
		ExpUnit      constmap.PackageExpUnit `json:"exp_unit"`       // 到期数字单位
		ExpUnitText  string                  `json:"exp_unit_text"`  // 到期单位文本
		NewUsable    int                     `json:"new_usable"`     // 首充可用
		NewRealPrice int64                   `json:"new_real_price"` // 首充实际价格：分
		Tags         string                  `json:"tags"`
		State        int                     `json:"state"`
		Sort         int                     `json:"sort"`
		CreatedAt    int64                   `json:"created_at"`
		UpdatedAt    int64                   `json:"updated_at"`
	}

	data.Id = pkg.ID
	data.Name = pkg.Name
	data.Desc = pkg.Desc
	data.Price = pkg.Price
	data.RealPrice = pkg.RealPrice
	data.Amount = pkg.Amount
	data.ExpNum = pkg.ExpNum
	data.ExpUnit = pkg.ExpUnit
	data.ExpUnitText = business.PackageExpUnitText(pkg.ExpUnit)
	data.NewUsable = pkg.NewUsable
	data.NewRealPrice = pkg.NewRealPrice
	data.State = pkg.State
	data.Sort = pkg.Sort
	data.Tags = pkg.Tags
	data.CreatedAt = pkg.CreatedAt.Unix()
	data.UpdatedAt = pkg.UpdatedAt.Unix()

	return data, nil
}
