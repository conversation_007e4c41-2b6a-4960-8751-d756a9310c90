package pkg

import (
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type saveData struct {
	Id           uint                    `form:"id"`
	Name         string                  `form:"name" binding:"required"`
	Desc         string                  `form:"desc"`
	Price        int64                   `form:"price" binding:"required"`      // 价格：分
	RealPrice    int64                   `form:"real_price" binding:"required"` // 实际价格：分
	Amount       int                     `form:"amount" binding:"required"`     // 给予金额
	ExpNum       int                     `form:"exp_num" binding:"required"`    // 到期数字
	ExpUnit      constmap.PackageExpUnit `form:"exp_unit" binding:"required"`   // 到期数字单位
	NewUsable    int                     `form:"new_usable"`                    // 首充可用
	NewRealPrice int64                   `form:"new_real_price"`                // 首充实际价格：分
	Sort         int                     `form:"sort"`
	Tags         string                  `form:"tags"`
}

func Save(ctx *gin.Context) (any, error) {
	var in saveData

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	// 验证到期单位的有效性
	if !isValidPackageExpUnit(in.ExpUnit) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无效的到期单位")
	}

	// 验证价格逻辑
	if in.Price < in.RealPrice {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "价格不能小于实际价格")
	}

	if in.NewUsable == constmap.Enable {
		if in.NewRealPrice < 1 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请填写首充实际价格")
		}
	}

	db := utils.GetDB(ctx)

	isEdit := in.Id > 0
	if isEdit {
		return edit(&in, db)
	} else {
		return create(&in, db)
	}
}

func edit(in *saveData, db *gorm.DB) (any, error) {
	var pkg models.Package
	if err := db.Take(&pkg, in.Id).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "套餐不存在")
	}

	update := models.Package{
		Name:         in.Name,
		Desc:         in.Desc,
		Price:        in.Price,
		RealPrice:    in.RealPrice,
		Amount:       in.Amount,
		ExpNum:       in.ExpNum,
		ExpUnit:      in.ExpUnit,
		NewUsable:    in.NewUsable,
		NewRealPrice: in.NewRealPrice,
		Sort:         in.Sort,
		Tags:         in.Tags,
	}

	omitCols := typeset.NewTypeSet(false, clause.Associations, "ID", "CreatedAt", "State")
	err := db.Model(&pkg).Select("*").Omit(omitCols.Values()...).Updates(update).Error
	if err != nil {
		return nil, err
	}
	var out struct {
		Id    uint   `json:"id"`
		Name  string `json:"name"`
		State int    `json:"state"`
	}
	out.Id = pkg.ID
	out.Name = pkg.Name
	out.State = pkg.State
	return out, nil
}

func create(in *saveData, db *gorm.DB) (any, error) {
	pkg := models.Package{
		Name:         in.Name,
		Desc:         in.Desc,
		Price:        in.Price,
		RealPrice:    in.RealPrice,
		Amount:       in.Amount,
		ExpNum:       in.ExpNum,
		ExpUnit:      in.ExpUnit,
		NewUsable:    in.NewUsable,
		NewRealPrice: in.NewRealPrice,
		State:        constmap.Disable,
		Sort:         in.Sort,
		Tags:         in.Tags,
	}

	err := db.Create(&pkg).Error
	if err != nil {
		return nil, err
	}
	var out struct {
		Id    uint   `json:"id"`
		Name  string `json:"name"`
		State int    `json:"state"`
	}
	out.Id = pkg.ID
	out.Name = pkg.Name
	out.State = pkg.State
	return out, nil
}

// 验证套餐到期单位是否有效
func isValidPackageExpUnit(unit constmap.PackageExpUnit) bool {
	switch unit {
	case constmap.PackageExpUnitWeek,
		constmap.PackageExpUnitMonth,
		constmap.PackageExpUnitSeason,
		constmap.PackageExpUnitYear:
		return true
	default:
		return false
	}
}
