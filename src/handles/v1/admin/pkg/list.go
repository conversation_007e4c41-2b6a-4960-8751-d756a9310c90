package pkg

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		Name  string `form:"name"`  // 套餐名称搜索
		State int    `form:"state"` // 状态筛选，默认为0表示不筛选
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	type item struct {
		Id           uint                    `json:"id"`
		Name         string                  `json:"name"`
		Desc         string                  `json:"desc"`
		Price        int64                   `json:"price"`          // 价格：分
		RealPrice    int64                   `json:"real_price"`     // 实际价格：分
		Amount       int                     `json:"amount"`         // 给予金额
		ExpNum       int                     `json:"exp_num"`        // 到期数字
		ExpUnit      constmap.PackageExpUnit `json:"exp_unit"`       // 到期数字单位
		ExpUnitText  string                  `json:"exp_unit_text"`  // 到期单位文本
		NewUsable    int                     `json:"new_usable"`     // 首充可用
		NewRealPrice int64                   `json:"new_real_price"` // 首充实际价格：分
		State        int                     `json:"state"`
		Sort         int                     `json:"sort"`
		Tags         string                  `json:"tags"`
		CreatedAt    int64                   `json:"created_at"`
		UpdatedAt    int64                   `json:"updated_at"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	// 构建查询条件
	query := db.Model(&models.Package{})

	if in.Name != "" {
		query = query.Where("name LIKE ?", "%"+in.Name+"%")
	}

	if in.State > 0 {
		query = query.Where("state = ?", in.State)
	}

	// 获取总数
	query.Count(&data.Total)

	// 获取列表数据
	var list []models.Package
	query.Order("id DESC").Limit(pageSize).Offset((page - 1) * pageSize).Find(&list)

	data.List = make([]item, len(list))
	for i, pkg := range list {
		it := item{
			Id:           pkg.ID,
			Name:         pkg.Name,
			Desc:         pkg.Desc,
			Price:        pkg.Price,
			RealPrice:    pkg.RealPrice,
			Amount:       pkg.Amount,
			ExpNum:       pkg.ExpNum,
			ExpUnit:      pkg.ExpUnit,
			ExpUnitText:  business.PackageExpUnitText(pkg.ExpUnit),
			NewUsable:    pkg.NewUsable,
			NewRealPrice: pkg.NewRealPrice,
			State:        pkg.State,
			Sort:         pkg.Sort,
			Tags:         pkg.Tags,
			CreatedAt:    pkg.CreatedAt.Unix(),
			UpdatedAt:    pkg.UpdatedAt.Unix(),
		}
		data.List[i] = it
	}

	return data, nil
}
