package common

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/handles/v1/admin/common/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func CateList(ctx *gin.Context) (any, error) {
	var err error
	const (
		typeScenic = "scenic"
		typeTuan   = "tuan"
	)
	var in struct {
		Type    string `form:"type"` //scenic/tuan
		Keyword string `form:"keyword"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.Type == "" {
		in.Type = typeScenic
	}
	var (
		where     strings.Builder
		whereArgs []any
	)
	where.WriteString("1=1")
	if in.Keyword != "" {
		switch in.Type {
		case typeScenic:
			where.WriteString(" AND value like ?")
		case typeTuan:
			where.WriteString(" AND name like ?")
		}
		whereArgs = append(whereArgs, "%"+in.Keyword+"%")
	}
	db := utils.GetDB(ctx)
	var rsp = def.OptionList{
		List: make([]def.Option, 0),
	}
	switch in.Type {
	case typeScenic:
		var cates []models.ScenicCate
		db.Where(where.String(), whereArgs...).Find(&cates)
		for _, v := range cates {
			rsp.List = append(rsp.List, def.Option{
				Id:    v.ID,
				Value: v.Value,
			})
		}
	case typeTuan:
		var cates []models.TuanCate
		db.Where(where.String(), whereArgs...).Find(&cates)
		for _, v := range cates {
			rsp.List = append(rsp.List, def.Option{
				Id:    v.ID,
				Value: v.Name,
			})
		}
	}

	return rsp, nil
}
