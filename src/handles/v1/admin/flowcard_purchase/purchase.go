package flowcard_purchase

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

type airlineStock struct {
	Code  string `json:"code"`
	Stock int    `json:"stock"`
}

func Purchase(ctx *gin.Context) (any, error) {
	var in struct {
		Title        string `form:"title" binding:"required"`
		AdvertiserId uint   `form:"advertiser_id" binding:"required"`
		Type         int    `form:"type" binding:"required"`
		StartTime    int64  `form:"start_time" binding:"required"`
		EndTime      int64  `form:"end_time" binding:"required"`
		Airlines     string `form:"airlines" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var airlines []airlineStock
	if err := json.Unmarshal([]byte(in.Airlines), &airlines); err != nil {
		return nil, err
	} else if len(airlines) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	airlineCodes := map[string]bool{}
	for _, item := range airlines {
		if _, ok := airlineCodes[item.Code]; ok {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgAirlineRepeat)
		}
		airlineCodes[item.Code] = true
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		flowCard := models.FlowCard{
			AdvertiserId: in.AdvertiserId,
			Title:        in.Title,
			Type:         in.Type,
			StartTime:    time.Unix(in.StartTime, 0),
			EndTime:      time.Unix(in.EndTime, 0),
			Code:         utils.Md5(utils.UUID()),
		}
		flowCard.StartTime = datetime.BeginOfDay(flowCard.StartTime)
		flowCard.EndTime = datetime.EndOfDay(flowCard.EndTime)

		if err := tx.Create(&flowCard).Error; err != nil {
			return err
		}

		stocks := slice.Map(airlines, func(index int, item airlineStock) models.FlowCardAirline {
			return models.FlowCardAirline{
				FlowCardId:     flowCard.ID,
				Code:           item.Code,
				TotalStock:     item.Stock,
				RemainingStock: item.Stock,
			}
		})
		if err := tx.Create(&stocks).Error; err != nil {
			return err
		}

		if flowCard.Type == constmap.FlowCardTypeUnique {
			totalStock := slice.ReduceBy(airlines, 0, func(index int, item airlineStock, agg int) int {
				return item.Stock + agg
			})
			var rewards []models.FlowCardRewardCode
			for i := 0; i < totalStock; i++ {
				rewards = append(rewards, models.FlowCardRewardCode{
					FlowCardId: flowCard.ID,
					Code:       utils.Md5(utils.UUID()),
				})
			}

			group := slice.Chunk(rewards, 200)
			for _, g := range group {
				if err := tx.Create(&g).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})

	return nil, err
}
