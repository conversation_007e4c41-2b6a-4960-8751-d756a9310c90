package flowcard_purchase

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Enable(ctx *gin.Context) (any, error) {
	var in struct {
		Id    uint `form:"id" binding:"required"`
		State int  `form:"state" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	var record models.FlowCard

	if err := db.Take(&record, in.Id).Error; err != nil {
		return nil, err
	}

	if err := db.Model(&record).Updates(models.FlowCard{State: in.State}).Error; err != nil {
		return nil, err
	}

	return nil, nil
}
