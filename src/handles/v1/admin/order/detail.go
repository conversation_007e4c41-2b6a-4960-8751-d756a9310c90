package order

import (
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

type refundOrder struct {
	Id                uint                      `json:"id"`
	CreatedAt         int64                     `json:"created_at"`
	State             constmap.RefundOrderState `json:"state"`
	StateText         string                    `json:"state_text"`
	ApplyRefundAmount int64                     `json:"apply_refund_amount"`
	Subs              []refundSub               `json:"subs"`
}

type refundSub struct {
	RefundOrderId    uint                         `json:"refund_order_id"`
	Id               uint                         `json:"id"`
	Pic              string                       `json:"pic"`
	Type             constmap.OrderSubType        `json:"type"`
	TypeText         string                       `json:"type_text"`
	Title            string                       `json:"title"`
	SkuName          string                       `json:"sku_name"`
	Quantity         int                          `json:"quantity"`
	State            constmap.RefundSubOrderState `json:"state"`
	StateText        string                       `json:"state_text"`
	StartDate        int64                        `json:"start_date"`
	EndDate          int64                        `json:"end_date"`
	RefundAmount     int64                        `json:"refund_amount"`
	OtaRefundAmount  int64                        `json:"ota_refund_amount"`
	OtaPenaltyAmount int64                        `json:"ota_penalty_amount"`
	RejectReason     string                       `json:"reject_reason"`
}

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId uint `form:"order_id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	type p struct {
		Id              uint   `json:"id"`
		State           int    `json:"state"`
		StateText       string `json:"state_text"`
		PayMethod       int    `json:"pay_method"`
		PayTime         int64  `json:"pay_time"`
		PayMethodText   string `json:"pay_method_text"`
		ActualPayAmount int64  `json:"actual_pay_amount"`
		CanRefund       bool   `json:"can_refund"`
		RefundAmount    int64  `json:"refund_amount"`
	}

	type d struct {
		ProductName string                `json:"product_name"`
		SkuName     string                `json:"sku_name"`
		Quantity    int                   `json:"quantity"`
		Price       int64                 `json:"price"`
		Type        constmap.OrderSubType `json:"type"`
		TypeText    string                `json:"type_text"`
		StateText   string                `json:"state_text"`
	}

	type reward struct {
		RealProductName string                       `json:"real_product_name"`
		Num             int                          `json:"num"`
		Receiver        string                       `json:"receiver"`
		RecvPhone       string                       `json:"recv_phone"`
		RecvAddr        string                       `json:"recv_addr"`
		DeliveryName    string                       `json:"delivery_name"`
		DeliveryNo      string                       `json:"delivery_no"`
		RewardState     constmap.UserTaskRewardState `json:"reward_state"`
		RewardStateText string                       `json:"reward_state_text"`
	}

	var data struct {
		OrderId       uint          `json:"id"`
		ContactsName  string        `json:"contacts_name"`
		ContactsTel   string        `json:"contacts_tel"`
		PayState      int           `json:"pay_state"`
		PayStateText  string        `json:"pay_state_text"`
		State         int           `json:"state"`
		StateText     string        `json:"state_text"`
		Title         string        `json:"title"`
		Date          string        `json:"date"`
		CreatedAt     int64         `json:"created_at"`
		PayTime       int64         `json:"pay_time"`
		UserId        uint          `json:"user_id"`
		Payments      []p           `json:"payments"`
		TotalAmount   int64         `json:"total_amount"`
		NeedPayAmount int64         `json:"need_pay_amount"`
		Details       []d           `json:"details"`
		CanPay        bool          `json:"can_pay"`
		RefundOrders  []refundOrder `json:"refund_orders"`
		RewardInfo    *reward       `json:"reward_info"`
	}

	db := utils.GetDB(ctx)

	var order models.Order
	if err := db.Preload("Details").Take(&order, in.OrderId).Error; err != nil {
		return nil, err
	}

	data.ContactsName = order.ContactsName
	data.ContactsTel = order.ContactsTel
	data.Title = order.Title
	data.OrderId = order.ID
	data.StateText = orders.OrderStateText(order.State)
	data.State = order.State
	data.PayState = order.PayState
	data.PayStateText = orders.OrderPayStateText(order.PayState)
	data.CreatedAt = order.CreatedAt.Unix()
	data.Date = datetime.FormatTimeToStr(order.Date, "yyyy-mm-dd")
	data.UserId = order.UserId
	data.TotalAmount = order.TotalAmount
	data.NeedPayAmount = order.NeedPayAmount
	data.PayTime = order.PayTime.Unix()
	data.Details = slice.Map(order.Details, func(index int, item models.OrderDetail) d {
		return d{
			ProductName: item.ProductName,
			SkuName:     item.SkuName,
			Quantity:    item.Quantity,
			Price:       item.Amount,
			Type:        item.OrderSubType,
			TypeText:    business.OrderSubTypeText(item.OrderSubType),
			StateText:   business.SubOrderStateText(item.State),
		}
	})
	data.CanPay = orders.CanPay(&order)

	var payments []models.OrderPayment
	db.Where("order_id = ? and state=?", in.OrderId, constmap.PayStatePayed).Find(&payments)
	data.Payments = slice.Map(payments, func(index int, item models.OrderPayment) p {
		return p{
			Id:              item.ID,
			State:           item.State,
			StateText:       orders.OrderPayStateText(item.State),
			PayMethod:       item.PayMethod,
			PayTime:         item.PayTime.Unix(),
			PayMethodText:   orders.PayMethodText(item.PayMethod),
			ActualPayAmount: item.ActualPayAmount,
			CanRefund:       orders.CanRefund(&item, &order),
			RefundAmount:    item.RefundAmount,
		}
	})
	data.RefundOrders = orderDetailRefunds(db, &order)

	var r models.UserTaskReward
	if db.Where("order_id=?", order.ID).Take(&r).Error == nil {
		data.RewardInfo = &reward{
			RealProductName: r.RealProductName,
			Num:             r.Num,
			Receiver:        r.Receiver,
			RecvPhone:       r.RecvPhone,
			RecvAddr:        r.RecvAddr,
			DeliveryName:    r.DeliveryName,
			DeliveryNo:      r.DeliveryNo,
			RewardState:     r.RewardState,
			RewardStateText: business.UserTaskRewardStateText(r.RewardState),
		}
	}

	return data, nil
}

func orderDetailRefunds(db *gorm.DB, order *models.Order) []refundOrder {
	var refundOrders []models.RefundOrder
	db.Preload("RefundSuborders.OrderDetail").
		Where("order_id = ?", order.ID).
		Find(&refundOrders)

	list := slice.Map(refundOrders, func(index int, item models.RefundOrder) refundOrder {
		return refundOrder{
			Id:                item.ID,
			CreatedAt:         item.CreatedAt.Unix(),
			State:             item.State,
			StateText:         business.RefundOrderStateText(item.State),
			ApplyRefundAmount: item.ApplyRefundAmount,
			Subs: slice.Map(item.RefundSuborders, func(index int, sub models.RefundSuborder) refundSub {
				it := refundSub{
					RefundOrderId:    sub.RefundOrderId,
					Id:               sub.ID,
					Pic:              utils.StaticUrl(sub.OrderDetail.Pic),
					Type:             sub.OrderDetail.OrderSubType,
					TypeText:         business.OrderSubTypeText(sub.OrderDetail.OrderSubType),
					Title:            sub.OrderDetail.ProductName,
					SkuName:          sub.OrderDetail.SkuName,
					StartDate:        sub.OrderDetail.DateStart,
					EndDate:          sub.OrderDetail.DateEnd,
					Quantity:         sub.RefundQuantity,
					State:            sub.State,
					StateText:        business.RefundSubOrderStateText(sub.State),
					RefundAmount:     sub.RefundAmount,
					OtaRefundAmount:  sub.OtaRefundAmount,
					OtaPenaltyAmount: sub.OtaPenaltyAmount,
					RejectReason:     sub.RejectReason,
				}

				if sub.OrderDetail.OrderSubType == constmap.OrderSubTypeTuan {
					it.StartDate = order.Date.Unix()
					it.EndDate = it.StartDate
				}

				return it
			}),
		}
	})

	return list
}
