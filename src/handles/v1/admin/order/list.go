package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId   uint   `form:"order_id"`
		Phone     string `form:"phone"`
		Type      string `form:"type"`
		State     string `form:"state"`
		StartTime int64  `form:"start_time"`
		EndTime   int64  `form:"end_time"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	page, pageSize := utils.GetPage(ctx)

	type detail struct {
		Id       uint   `json:"id"`
		Title    string `json:"title"`
		Amount   int64  `json:"amount"`
		Quantity int    `json:"quantity"`
	}

	type item struct {
		OrderId      uint     `json:"order_id"`
		Title        string   `json:"title"`
		ContactsName string   `json:"contacts_name"`
		ContactsTel  string   `json:"contacts_tel"`
		CreatedAt    int64    `json:"created_at"`
		Date         int64    `json:"date"`
		State        int      `json:"state"`
		StateText    string   `json:"state_text"`
		Details      []detail `json:"details"`
		TotalAmount  int64    `json:"total_amount"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	whereStr := strings.Builder{}

	whereStr.WriteString("1=1")
	whereArgs := []any{}
	if in.OrderId > 0 {
		whereStr.WriteString(" and orders.id=?")
		whereArgs = append(whereArgs, in.OrderId)
	}
	if !strutil.IsBlank(in.Phone) {
		whereStr.WriteString(" and orders.contacts_tel=?")
		whereArgs = append(whereArgs, in.Phone)
	}
	if !strutil.IsBlank(in.Type) {
		whereStr.WriteString(" and orders.order_type=?")
		whereArgs = append(whereArgs, in.Type)
	}
	if !strutil.IsBlank(in.State) {
		whereStr.WriteString(" and orders.state in ?")
		whereArgs = append(whereArgs, strings.Split(in.State, ","))
	}
	if in.StartTime > 0 && in.EndTime > 0 {
		whereStr.WriteString(" and orders.created_at between ? and ?")
		whereArgs = append(whereArgs, time.Unix(in.StartTime, 0), time.Unix(in.EndTime, 0))
	}

	db := utils.GetDB(ctx)
	db.Model(&models.Order{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []models.Order
	db.Order("orders.id desc").
		Preload("Details").
		Limit(pageSize).
		Offset((page-1)*pageSize).
		Where(whereStr.String(), whereArgs...).Find(&list)

	data.List = make([]item, len(list))
	for i, order := range list {
		it := item{
			TotalAmount:  order.TotalAmount,
			OrderId:      order.ID,
			Title:        order.Title,
			ContactsName: order.ContactsName,
			ContactsTel:  order.ContactsTel,
			CreatedAt:    order.CreatedAt.Unix(),
			Date:         order.Date.Unix(),
			State:        order.State,
			StateText:    orders.OrderStateText(order.State),
			Details: slice.Map(order.Details, func(index int, item models.OrderDetail) detail {
				return detail{
					Id:       item.ID,
					Title:    item.SkuName,
					Amount:   item.Amount,
					Quantity: item.Quantity,
				}
			}),
		}
		data.List[i] = it
	}

	return data, nil
}
