package order

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func SendReward(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId      uint   `form:"order_id" binding:"required"`
		DeliveryName string `form:"delivery_name" binding:"required"`
		DeliveryNo   string `form:"delivery_no" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var order models.Order
	if err := db.Take(&order, in.OrderId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	var r models.UserTaskReward
	if db.Where("order_id=? and reward_state=?", order.ID, constmap.UserTaskRewardWait).Take(&r).Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "用户没有领取奖励或者已经发货了")
	}

	if db.Model(&r).Where(models.UserTaskReward{RewardState: r.RewardState}).
		Updates(models.UserTaskReward{
			DeliveryName: in.DeliveryName,
			DeliveryNo:   in.DeliveryNo,
			RewardState:  constmap.UserTaskRewardSend,
		}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "更新失败")
	}

	return nil, nil
}
