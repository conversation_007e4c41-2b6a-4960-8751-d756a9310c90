package order

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func ApplyRefund(ctx *gin.Context) (any, error) {
	var in struct {
		PayId        uint   `form:"pay_id" binding:"required"`
		RefundAmount int64  `form:"refund_amount" binding:"required"`
		RefundReason string `form:"refund_reason" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var payment models.OrderPayment
	if err := db.Joins("Order").First(&payment, in.PayId).Error; err != nil {
		return nil, err
	} else if payment.State != constmap.PayStatePayed {
		return nil, utils.NewErrorStr(constmap.ErrorState, constmap.ErrorMsgState)
	} else if in.RefundAmount+payment.RefundAmount > payment.ActualPayAmount {
		return nil, utils.NewErrorStr(constmap.ErrorApplyRefund, "总退款金额大于支付金额")
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		refund := models.RefundPayment{
			PayId:        payment.ID,
			OrderId:      payment.OrderId,
			RefundAmount: in.RefundAmount,
			State:        constmap.RefundStateApproved,
			RefundReason: in.RefundReason,
			OutRefundNo:  utils.GenOrderNo(),
		}

		if err := tx.Create(&refund).Error; err != nil {
			return err
		}
		affected := tx.Model(&payment).Omit(clause.Associations).Updates(map[string]any{
			"refund_amount": gorm.Expr("refund_amount+?", in.RefundAmount),
		}).RowsAffected

		if affected == 0 {
			return utils.NewErrorStr(constmap.ErrorApplyRefund, constmap.ErrorMsgApplyRefund)
		}

		order := payment.Order

		if tx.Model(&order).Omit(clause.Associations).Updates(models.Order{
			State: constmap.OrderStateRefund,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorApplyRefund, constmap.ErrorMsgApplyRefund)
		}

		if tx.Model(&models.OrderDetail{}).Where(models.OrderDetail{OrderId: order.ID}).
			Updates(models.OrderDetail{State: constmap.OrderDetailStateRefund}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorApplyRefund, constmap.ErrorMsgApplyRefund)
		}

		return nil
	})

	return nil, err
}
