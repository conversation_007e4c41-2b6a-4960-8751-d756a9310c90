package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

func RefundList(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId   uint  `form:"order_id"`
		State     int   `form:"state"`
		StartTime int64 `form:"start_time"`
		EndTime   int64 `form:"end_time"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type item struct {
		Id                uint                      `json:"id"`
		OrderId           uint                      `json:"order_id"`
		ApplyRefundAmount int64                     `json:"apply_refund_amount"`
		RefundAmount      int64                     `json:"refund_amount"`
		State             constmap.RefundOrderState `json:"state"`
		StateText         string                    `json:"state_text"`
		CreatedAt         int64                     `json:"created_at"`
		Title             string                    `json:"title"`
		Contact           string                    `json:"contact"`
		Phone             string                    `json:"phone"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	whereStr := strings.Builder{}
	whereStr.WriteString("1=1")
	whereArgs := []any{}

	if in.OrderId > 0 {
		whereStr.WriteString(" and order_id = ?")
		whereArgs = append(whereArgs, in.OrderId)
	}

	if in.State > 0 {
		whereStr.WriteString(" and refund_orders.state =?")
		whereArgs = append(whereArgs, in.State)
	}
	if in.StartTime > 0 && in.EndTime > 0 {
		whereStr.WriteString(" and refund_orders.created_at between ? and ?")
		whereArgs = append(whereArgs, time.Unix(in.StartTime, 0), time.Unix(in.EndTime, 0))
	}

	db.Model(&models.RefundOrder{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []models.RefundOrder
	db.Model(&models.RefundOrder{}).
		Joins("Order").
		Where(whereStr.String(), whereArgs...).
		Order("refund_orders.id desc").
		Offset((page - 1) * pageSize).
		Limit(pageSize).Find(&list)

	data.List = slice.Map(list, func(index int, it models.RefundOrder) item {
		return item{
			Id:                it.ID,
			OrderId:           it.OrderId,
			ApplyRefundAmount: it.ApplyRefundAmount,
			RefundAmount:      it.RefundAmount,
			State:             it.State,
			StateText:         business.RefundOrderStateText(it.State),
			CreatedAt:         it.CreatedAt.Unix(),
			Title:             it.Order.Title,
			Contact:           it.Order.ContactsName,
			Phone:             it.Order.ContactsTel,
		}
	})

	return data, nil
}
