package order

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func Pay(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId      uint   `form:"order_id" binding:"required"`
		PayMethod    int    `form:"method" binding:"required"`
		Amount       int64  `form:"amount" binding:"required"`
		Remarks      string `form:"remarks" binding:"required"`
		ThirdPartyNo string `form:"third_party_no"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var order models.Order
	if err := db.Take(&order, in.OrderId).Error; err != nil {
		return nil, err
	} else if order.State == constmap.OrderStateCancel {
		return nil, utils.NewErrorStr(constmap.ErrorState, constmap.ErrorMsgState)
	}

	payment := models.OrderPayment{
		OrderId:         order.ID,
		UserId:          order.UserId,
		NeedPayAmount:   in.Amount,
		ActualPayAmount: in.Amount,
		OutTradeNo:      in.Remarks,
		ThirdPartyNo:    in.ThirdPartyNo,
		PayMethod:       in.PayMethod,
		Remarks:         in.Remarks,
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&payment).Error; err != nil {
			return err
		}

		if err := orders.PayComplete(tx, &payment, &order, "", payment.ThirdPartyNo, time.Now()); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	_ = my_queue.Light(constmap.EventOrderPay, gin.H{
		"ids": fmt.Sprintf("%d", order.ID),
	})

	return nil, err
}
