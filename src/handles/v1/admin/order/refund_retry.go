package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func RefundRetry(ctx *gin.Context) (any, error) {
	var in struct {
		RefundOrderId    uint `form:"refund_order_id" binding:"required"`
		RefundSuborderId uint `form:"refund_suborder_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var refund = new(models.RefundOrder)
	db.Preload("RefundSuborders.OrderDetail").Take(&refund, in.RefundOrderId)

	subOrders := slice.Filter(refund.RefundSuborders, func(index int, item models.RefundSuborder) bool {
		if item.State != constmap.RefundSubOrderAbnormal {
			return false
		}
		if in.RefundSuborderId != 0 && item.ID != in.RefundSuborderId {
			return false
		}

		return true
	})
	if len(subOrders) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "没有可审核的退款单")
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		for _, refundSubOrder := range subOrders {
			updateRefundSubOrder := models.RefundSuborder{
				State: constmap.RefundSubOrderWaitRefund,
			}

			refundSubPayment := models.RefundSubPayment{
				RefundSuborderId: refundSubOrder.ID,
				State:            constmap.RefundSubPaymentWaitRefund,
				RefundNo:         utils.GenOrderNo(),
				RefundAmount:     refundSubOrder.RefundAmount,
				//OtaRefundAmount:  refundSubOrder.OtaRefundAmount,
			}
			if err := tx.Create(&refundSubPayment).Error; err != nil {
				return err
			}
			updateRefundSubOrder.RefundSubPaymentId = refundSubPayment.ID

			if tx.Model(&refundSubOrder).Where(models.RefundSuborder{
				State: refundSubOrder.State,
			}).Updates(updateRefundSubOrder).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "子退款单更新失败")
			}
		}

		// 退款订单状态
		if tx.Model(refund).Where(models.RefundOrder{
			State: refund.State,
		}).Omit(clause.Associations).Updates(models.RefundOrder{
			State: constmap.RefundOrderProcessing,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "退款单更新失败")
		}

		return orders.RefundSubordersFinalDo(tx, refund)
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		RefundOrderId uint `json:"refund_order_id"`
	}
	out.RefundOrderId = refund.ID

	return out, nil
}
