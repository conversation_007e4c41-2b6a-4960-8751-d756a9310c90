package order

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Payment(ctx *gin.Context) (any, error) {
	var in struct {
		PayId uint `form:"pay_id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var payment models.OrderPayment
	if err := db.Joins("Order").First(&payment, in.PayId).Error; err != nil {
		return nil, err
	}

	var data struct {
		PayId     uint  `json:"pay_id"`
		PayPrice  int64 `json:"pay_price"`
		State     int   `json:"state"`
		CanRefund bool  `json:"can_refund"`
	}

	data.PayId = payment.ID
	data.PayPrice = payment.NeedPayAmount
	data.State = payment.State
	data.CanRefund = orders.CanRefund(&payment, &payment.Order)

	return data, nil
}
