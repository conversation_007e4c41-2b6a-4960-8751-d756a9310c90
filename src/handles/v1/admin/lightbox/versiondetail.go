package lightbox

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func VersionDetail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var data struct {
		Arch        string `json:"arch"`
		Id          uint   `json:"id"`
		Os          string `json:"os"`
		Version     string `json:"version"`
		Url         string `json:"url"`
		Size        int    `json:"size"`
		ReleaseTime int64  `json:"release_time"`
		Note        string `json:"note"`
		PackType    string `json:"pack_type"`
	}

	db := utils.GetDB(ctx)

	var lbVersion models.LightboxVersion

	if err := db.Take(&lbVersion, in.Id).Error; err != nil {
		return nil, err
	}
	data.Arch = lbVersion.Arch
	data.Id = lbVersion.ID
	data.Os = lbVersion.Os
	data.Version = lbVersion.Version
	data.Url = lbVersion.Url
	data.Size = lbVersion.Size
	data.ReleaseTime = lbVersion.ReleaseTime.Unix()
	data.Note = lbVersion.Note
	data.PackType = lbVersion.PackType

	return data, nil
}
