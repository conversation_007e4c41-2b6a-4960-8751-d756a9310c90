package lightbox

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Versions(ctx *gin.Context) (any, error) {

	type item struct {
		Id          uint   `json:"id"`
		Os          string `json:"os"`
		Arch        string `json:"arch"`
		Version     string `json:"version"`
		Size        int    `json:"size"`
		ReleaseTime int64  `json:"release_time"`
		PackType    string `json:"pack_type"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	db.Model(&models.LightboxVersion{}).Count(&data.Total)

	var list []models.LightboxVersion
	db.Limit(pageSize).Offset((page - 1) * pageSize).Order("id desc").Find(&list)
	data.List = make([]item, len(list))
	for i, version := range list {
		data.List[i] = item{
			Os:          version.Os,
			Arch:        version.Arch,
			Version:     version.Version,
			Size:        version.Size,
			ReleaseTime: version.ReleaseTime.Unix(),
			Id:          version.ID,
			PackType:    version.PackType,
		}
	}

	return data, nil
}
