package lightbox

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

type editPost struct {
	Version     string `form:"version" binding:"required"`
	Os          string `form:"os" binding:"required"`
	ReleaseTime int64  `form:"release_time" binding:"required"`
	Note        string `form:"note"`
	Size        int    `form:"size" binding:"required"`
	Url         string `form:"url" binding:"required"`
	Arch        string `form:"arch" binding:"required"`
	PackType    string `form:"pack_type"`
	Id          uint   `form:"id"`
}

func SaveVersion(ctx *gin.Context) (any, error) {
	var in editPost

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	} else if !regexp.MustCompile("^\\d+\\.\\d+\\.\\d+$").MatchString(in.Version) {
		return nil, utils.NewErrorStr(constmap.ErrorVersion, constmap.ErrorMsgVersion)
	} else if in.Os == "linux" {
		if strutil.IsBlank(in.PackType) {
			return nil, utils.NewErrorStr(constmap.ErrorPackType, constmap.ErrorMsgPackType)
		}
	}

	isEdit := in.Id > 0

	db := utils.GetDB(ctx)
	var err error

	if !isEdit {
		_, err = create(db, &in)
	} else {
		var version models.LightboxVersion
		if err = db.Take(&version, in.Id).Error; err != nil {
			return nil, err
		}
		err = edit(db, &version, &in)
	}

	return nil, err
}

func edit(db *gorm.DB, version *models.LightboxVersion, in *editPost) error {
	update := models.LightboxVersion{
		Version:     in.Version,
		BuildNumber: buildNumber(in.Version),
		Os:          in.Os,
		ReleaseTime: time.Unix(in.ReleaseTime, 0),
		Note:        in.Note,
		Size:        in.Size,
		Url:         in.Url,
		Arch:        in.Arch,
		PackType:    in.PackType,
	}

	if db.Model(&version).Updates(update).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	return nil
}

func create(db *gorm.DB, in *editPost) (*models.LightboxVersion, error) {
	version := models.LightboxVersion{
		Version:     in.Version,
		BuildNumber: buildNumber(in.Version),
		Os:          in.Os,
		ReleaseTime: time.Unix(in.ReleaseTime, 0),
		Note:        in.Note,
		Size:        in.Size,
		Url:         in.Url,
		Arch:        in.Arch,
		PackType:    in.PackType,
	}
	if err := db.Create(&version).Error; err != nil {
		return nil, err
	}

	return &version, nil
}

func buildNumber(v string) int {
	b, _ := convertor.ToInt(strings.ReplaceAll(v, ".", ""))

	return int(b)
}
