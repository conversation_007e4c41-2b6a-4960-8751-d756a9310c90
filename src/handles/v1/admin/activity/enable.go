package activity

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Enable(ctx *gin.Context) (any, error) {
	var in struct {
		ActivityId uint `form:"activity_id" binding:"required"`
		State      int  `form:"state" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)

	var activity models.Activity
	db.Take(&activity, in.ActivityId)

	if db.Model(&activity).Updates(models.Activity{
		State: in.State,
	}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
	}
	var out struct {
		Id    uint `json:"id"`
		State int  `json:"state"`
	}
	out.Id = activity.ID
	out.State = activity.State
	return out, nil
}
