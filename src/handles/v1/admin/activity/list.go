package activity

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid  string `form:"uuid"`
		State int    `form:"state"`
		Title string `form:"title"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	where := strings.Builder{}
	whereArgs := make([]any, 0)

	where.WriteString("1 ")
	if !strutil.IsBlank(in.Uuid) {
		where.WriteString(" AND uuid=?")
		whereArgs = append(whereArgs, strutil.Trim(in.Uuid))
	}
	if in.State > 0 {
		where.WriteString(" AND state=?")
		whereArgs = append(whereArgs, in.State)
	}
	if !strutil.IsBlank(in.Title) {
		where.WriteString(" AND title like ?")
		whereArgs = append(whereArgs, "%"+strutil.Trim(in.Title)+"%")
	}

	type vitem struct {
		Id        uint   `json:"id"`
		Title     string `json:"title"`
		Uuid      string `json:"uuid"`
		State     int    `json:"state"`
		StateText string `json:"state_text"`
		Start     int64  `json:"start"`
		End       int64  `json:"end"`
		TaskId    uint   `json:"task_id"`
		TaskName  string `json:"task_name"`
		Cover     string `json:"cover"`
		Link      string `json:"link"`
	}
	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}
	var list []models.Activity
	db.Model(&models.Activity{}).Where(where.String(), whereArgs...).
		Count(&out.Total).
		Offset((page - 1) * pageSize).Limit(pageSize).
		Order("id desc").
		Find(&list)
	taskMap := task_biz.LoadRelateTasks(db, slice.Map(list, func(index int, item models.Activity) uint {
		return item.ID
	}), constmap.TaskRelateActivity)
	out.List = slice.Map(list, func(index int, item models.Activity) vitem {
		v := vitem{
			Id:        item.ID,
			Title:     item.Title,
			Uuid:      item.Uuid,
			State:     item.State,
			StateText: business.EnableStateText(item.State),
			Start:     item.Start.Unix(),
			End:       item.End.Unix(),
			TaskId:    taskMap[item.ID].ID,
			TaskName:  taskMap[item.ID].Name,
			Cover:     utils.StaticUrl(item.Cover),
			Link:      item.Link,
		}
		return v
	})

	return out, nil
}
