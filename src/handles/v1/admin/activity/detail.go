package activity

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		ActivityId uint `form:"activity_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var activity models.Activity
	db.Preload("Ext").Take(&activity, in.ActivityId)
	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}
	taskMap := task_biz.LoadRelateTasks(db, []uint{activity.ID}, constmap.TaskRelateActivity)

	var out struct {
		Id       uint   `json:"id"`
		Title    string `json:"title"`
		Uuid     string `json:"uuid"` //活动标识
		State    int    `json:"state"`
		Start    int64  `json:"start"`
		End      int64  `json:"end"`
		TaskId   uint   `json:"task_id"` //关联主任务
		TaskName string `json:"task_name"`
		Rules    string `json:"rules"`
		MpTplId  string `json:"mp_tpl_id"`
		Cover    string `json:"cover"`
		Link     string `json:"link"`
	}
	out.Id = activity.ID
	out.Title = activity.Title
	out.Uuid = activity.Uuid
	out.State = activity.State
	out.Start = activity.Start.Unix()
	out.End = activity.End.Unix()
	out.TaskId = taskMap[activity.ID].ID
	out.TaskName = taskMap[activity.ID].Name
	out.Rules = activity.Ext.Rules
	out.MpTplId = activity.MpTplId
	out.Cover = utils.StaticUrl(activity.Cover)
	out.Link = activity.Link
	return out, nil
}
