package activity

import (
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"
)

func Save(ctx *gin.Context) (any, error) {
	var in struct {
		Id         uint   `form:"id"`
		Title      string `form:"title" binding:"required"`
		Start      int64  `form:"start" binding:"required"`
		End        int64  `form:"end" binding:"required"`
		Rules      string `form:"rules"`
		TaskId     uint   `form:"task_id"`
		Uuid       string `form:"uuid"`
		MpTplId    string `form:"mp_tpl_id"`
		CoverResId uint   `form:"cover_res_id"`
		Link       string `form:"link"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var activity = new(models.Activity)
	if in.Id > 0 {
		db.Preload("Ext").Take(activity, in.Id)
		if activity.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
		}
	}

	update := &models.Activity{
		Title:   in.Title,
		Start:   datetime.BeginOfDay(time.Unix(in.Start, 0)),
		End:     utils.GetEndOfDay(time.Unix(in.End, 0)),
		MpTplId: in.MpTplId,
		Link:    in.Link,
	}
	updateExt := models.ActivityExt{
		Rules: in.Rules,
	}

	omits := typeset.NewTypeSet(false, clause.Associations, "ID", "State", "CreatedAt", "Uuid")
	if in.CoverResId == 0 {
		omits.Add("Cover")
	} else {
		if c, err := business.MoveUploadFile(db, in.CoverResId); err != nil {
			return nil, err
		} else {
			update.Cover = utils.UnWrapStaticUrl(c)
		}
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if activity.ID > 0 {
			if tx.Model(activity).
				Select("*").Omit(omits.Values()...).
				Updates(update).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			if strutil.IsBlank(in.Uuid) {
				return utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
			}
			var cnt int64
			if tx.Model(&models.Activity{}).Where(models.Activity{Uuid: in.Uuid}).Count(&cnt); cnt > 0 {
				return utils.NewErrorStr(constmap.ErrorParam, "UUID冲突")
			}
			update.Uuid = in.Uuid
			update.State = constmap.Disable
			if err := tx.Omit(clause.Associations).Create(&update).Error; err != nil {
				return utils.NewError(err)
			}
			activity = update
		}
		if activity.Ext.ID > 0 {
			if tx.Model(&activity.Ext).
				Updates(updateExt).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			updateExt.ActivityId = activity.ID
			if err := tx.Create(&updateExt).Error; err != nil {
				return utils.NewError(err)
			}
			activity.Ext = updateExt
		}

		if err := task_biz.SaveTaskRelate(tx, in.TaskId, activity.ID, constmap.TaskRelateActivity); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Id uint `json:"id"`
	}
	out.Id = activity.ID
	return out, nil
}
