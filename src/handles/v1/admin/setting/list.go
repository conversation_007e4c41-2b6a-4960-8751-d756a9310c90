package setting

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		Key  string `form:"key"`
		Keys string `form:"keys"`
		Desc string `form:"desc"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	var where strings.Builder
	var whereArgs []any
	where.WriteString("1=1")

	if !strutil.IsBlank(in.Key) {
		where.WriteString(" and (`key` like ? or `desc` like ?)")
		whereArgs = append(whereArgs, "%"+strutil.Trim(in.Key)+"%", "%"+strutil.Trim(in.Key)+"%")
	}

	if !strutil.IsBlank(in.Desc) {
		where.WriteString(" and `desc` like ?")
		whereArgs = append(whereArgs, "%"+strutil.Trim(in.Desc)+"%")
	}

	if !strutil.IsBlank(in.Keys) {
		where.WriteString(" and `key` in (?)")
		whereArgs = append(whereArgs, strings.Split(in.Keys, ","))
	}

	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type item struct {
		Id        uint                        `json:"id"`
		Key       string                      `json:"key"`
		Desc      string                      `json:"desc"`
		Value     string                      `json:"value"`
		ValueType constmap.SysConfigValueType `json:"value_type"`
		CreatedAt int64                       `json:"created_at"`
		UpdatedAt int64                       `json:"updated_at"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	db.Model(&models.SysConfig{}).Where(where.String(), whereArgs...).Count(&data.Total)

	var list []models.SysConfig
	db.Limit(pageSize).Offset((page-1)*pageSize).Where(where.String(), whereArgs...).Order("id desc").Find(&list)

	data.List = make([]item, len(list))
	for i, config := range list {
		it := item{
			Id:        config.ID,
			Key:       config.Key,
			Desc:      config.Desc,
			Value:     config.Value,
			ValueType: config.ValueType,
			CreatedAt: config.CreatedAt.Unix(),
			UpdatedAt: config.UpdatedAt.Unix(),
		}
		data.List[i] = it
	}

	return data, nil
}
