package setting

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Save(ctx *gin.Context) (any, error) {
	var in struct {
		Key       string                      `form:"key" binding:"required"`
		Value     string                      `form:"value"`
		Desc      string                      `form:"desc"`
		ValueType constmap.SysConfigValueType `form:"value_type"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	in.Key = strutil.Trim(in.Key)
	if strutil.IsBlank(in.Key) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	var err error

	var sysConfig models.SysConfig
	if err = db.Where("`key`=?", in.Key).Take(&sysConfig).Error; err != nil {
		sysConfig = models.SysConfig{
			Key:       in.Key,
			Value:     in.Value,
			Desc:      in.Desc,
			ValueType: in.ValueType,
		}
		if err = db.Create(&sysConfig).Error; err != nil {
			return nil, err
		}
	} else if db.Model(&sysConfig).Select("*").Omit("ID", "CreatedAt", "Key").Updates(models.SysConfig{
		Value:     in.Value,
		Desc:      in.Desc,
		ValueType: in.ValueType,
	}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "保存失败")
	}

	// 清除缓存
	sys_config_biz.ClearCache(in.Key)

	var out struct {
		Id        uint                        `json:"id"`
		Key       string                      `json:"key"`
		Value     string                      `json:"value"`
		Desc      string                      `json:"desc"`
		ValueType constmap.SysConfigValueType `json:"value_type"`
		CreatedAt int64                       `json:"created_at"`
		UpdatedAt int64                       `json:"updated_at"`
	}
	out.Id = sysConfig.ID
	out.Key = sysConfig.Key
	out.Value = sysConfig.Value
	out.Desc = sysConfig.Desc
	out.ValueType = sysConfig.ValueType
	out.CreatedAt = sysConfig.CreatedAt.Unix()
	out.UpdatedAt = sysConfig.UpdatedAt.Unix()
	return out, nil
}
