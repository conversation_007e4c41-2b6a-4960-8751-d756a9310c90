package user

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
)

func Detail(ctx *gin.Context) (any, error) {
	// 定义输入参数结构
	var in struct {
		UserId uint `form:"user_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	// 查询用户基础信息
	var user models.User
	if err := db.Take(&user, in.UserId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "用户不存在")
	}

	// 查询用户积分账户
	var account models.UserAccount
	db.Where("user_id = ? AND currency = ?", in.UserId, constmap.CurrencyIntegral).Take(&account)

	// 查询用户套餐汇总信息
	var summary models.UserPackageSummary
	db.Where("user_id = ?", in.UserId).Take(&summary)

	// 查询最新的用户套餐购买记录
	var latestPackage models.UserPackage
	db.Where("user_id = ? AND state = ?", in.UserId, constmap.UserPackageStateUsing).
		Order("created_at DESC").
		Take(&latestPackage)

	// 定义返回数据结构
	var data struct {
		// 用户详情
		UserId   uint   `json:"user_id"`
		Nickname string `json:"nickname"`
		JoinTime int64  `json:"join_time"`
		Amount   int    `json:"amount"`

		// 用户套餐信息
		PackageName  string `json:"package_name"`
		PurchaseTime int64  `json:"purchase_time"`
		ExpireTime   int64  `json:"expire_time"`
		PayPrice     int64  `json:"pay_price"`
	}

	// 填充用户基础信息
	data.UserId = user.ID
	data.Nickname = user.Nickname
	data.JoinTime = user.CreatedAt.Unix()
	data.Amount = account.Amount

	// 填充套餐信息
	if summary.ID > 0 {
		data.PackageName = summary.PackageName
		data.ExpireTime = summary.ExpireAt.Unix()
	}

	if latestPackage.ID > 0 {
		if data.PackageName == "" {
			data.PackageName = latestPackage.PackageName
		}
		data.PurchaseTime = latestPackage.CreatedAt.Unix()
		data.PayPrice = latestPackage.PayPrice
	}

	// 如果没有购买时间，使用加入时间
	if data.PurchaseTime == 0 {
		data.PurchaseTime = data.JoinTime
	}

	return data, nil
}
