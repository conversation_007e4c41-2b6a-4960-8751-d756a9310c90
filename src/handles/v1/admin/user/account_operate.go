package user

import (
	"fmt"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 积分操作
func AccountOperate(ctx *gin.Context) (any, error) {
	var in struct {
		UserId       uint                  `form:"user_id" binding:"required"`
		CurrencyType constmap.CurrencyType `form:"currency_type"`
		Amount       int64                 `form:"amount" binding:"required"`
		Remark       string                `form:"remark" binding:"required"` //备注
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	// 参数验证
	if in.Amount == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "金额不能为0")
	}

	// 默认货币类型为积分
	if in.CurrencyType == 0 {
		in.CurrencyType = constmap.CurrencyIntegral
	}

	session, _ := business.GetAdminLoginUser(ctx)

	db := utils.GetDB(ctx)

	var transactionNo string

	// 在事务中执行积分操作
	err := db.Transaction(func(tx *gorm.DB) error {
		// 确定账户日志类型（增加或减少）
		var accountLogType constmap.AccountLog
		var amount int

		if in.Amount > 0 {
			// 正数表示增加积分
			accountLogType = constmap.AccountLogIncrAmount
			amount = int(in.Amount)
		} else {
			// 负数表示减少积分
			accountLogType = constmap.AccountLogDecrAmount
			amount = int(-in.Amount) // 转为正数
		}

		// 创建账户交易流水
		trans, err := account_biz.CreateAccountTransaction(tx, in.UserId, in.CurrencyType, amount,
			accountLogType, constmap.AccountLogSubAdminOperate, fmt.Sprintf("[%d]%s", session.UserId, in.Remark))
		if err != nil {
			return err
		}

		// 执行账户交易
		if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
			return err
		}

		transactionNo = trans.TransactionNo
		return nil
	})

	if err != nil {
		return nil, err
	}

	// 返回执行结果
	var out struct {
		TransactionNo string `json:"transaction_no"` // 交易流水号
		Success       bool   `json:"success"`        // 执行结果
		Message       string `json:"message"`        // 执行消息
	}

	out.TransactionNo = transactionNo
	out.Success = true
	out.Message = "积分操作成功"

	return out, nil
}
