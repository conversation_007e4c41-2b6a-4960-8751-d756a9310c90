package user

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) (any, error) {
	// 定义输入参数结构
	var in struct {
		UserId   uint   `form:"user_id"`  // 用户ID搜索
		Nickname string `form:"nickname"` // 用户昵称搜索
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	// 获取分页参数
	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	// 定义返回数据项结构
	type item struct {
		UserId           uint   `json:"user_id"`            // 用户ID
		Nickname         string `json:"nickname"`           // 用户昵称
		Avatar           string `json:"avatar"`             // 头像
		InviteCode       string `json:"invite_code"`        // 邀请码
		JoinTime         int64  `json:"join_time"`          // 加入时间
		PackageName      string `json:"package_name"`       // 套餐名称
		TotalAmount      int    `json:"amount"`             // 总积分
		PurchaseTime     int64  `json:"purchase_time"`      // 购买时间
		ExpireTime       int64  `json:"expire_time"`        // 到期时间
		PackageState     int    `json:"package_state"`      // 套餐状态
		PackageStateText string `json:"package_state_text"` // 套餐状态文本
	}

	// 定义返回数据结构
	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	// 构建查询条件
	query := db.Model(&models.User{})

	// 处理搜索条件
	if in.UserId > 0 {
		query = query.Where("id = ?", in.UserId)
	}

	if !strutil.IsBlank(in.Nickname) {
		query = query.Where("nickname LIKE ?", "%"+strutil.Trim(in.Nickname)+"%")
	}

	// 获取总数
	query.Count(&data.Total)

	// 获取用户列表
	var users []models.User
	query.Order("id DESC").
		Limit(pageSize).
		Offset((page - 1) * pageSize).
		Find(&users)

	// 获取用户ID列表
	userIds := slice.Map(users, func(index int, user models.User) uint {
		return user.ID
	})

	// 批量查询用户积分账户
	var accounts []models.UserAccount
	var accountMap map[uint]models.UserAccount
	if len(userIds) > 0 {
		db.Where("user_id IN ? AND currency = ?", userIds, constmap.CurrencyIntegral).Find(&accounts)
		accountMap = slice.KeyBy(accounts, func(account models.UserAccount) uint {
			return account.UserId
		})
	}

	// 批量查询用户套餐汇总信息
	var packageSummaries []models.UserPackageSummary
	var summaryMap map[uint]models.UserPackageSummary
	if len(userIds) > 0 {
		db.Where("user_id IN ?", userIds).Find(&packageSummaries)
		summaryMap = slice.KeyBy(packageSummaries, func(summary models.UserPackageSummary) uint {
			return summary.UserId
		})
	}

	// 批量查询用户套餐记录，在Go中找最新记录
	var allPackages []models.UserPackage
	var packageMap map[uint]models.UserPackage
	if len(userIds) > 0 {
		// 查询所有用户的套餐记录，只查询使用中的套餐
		db.Where("user_id IN ? AND state = ?", userIds, constmap.UserPackageStateUsing).
			Order("user_id, created_at DESC").
			Find(&allPackages)

		// 在Go代码中找每个用户的最新套餐记录
		packageMap = make(map[uint]models.UserPackage)
		for _, pkg := range allPackages {
			if _, exists := packageMap[pkg.UserId]; !exists {
				packageMap[pkg.UserId] = pkg // 第一条记录就是最新的（因为按created_at DESC排序）
			}
		}
	}

	// 处理查询结果并格式化输出数据
	data.List = make([]item, len(users))
	for i, user := range users {
		account, hasAccount := accountMap[user.ID]
		summary, hasSummary := summaryMap[user.ID]
		latestPkg, hasLatestPkg := packageMap[user.ID]

		data.List[i] = item{
			UserId:       user.ID,
			Nickname:     user.Nickname,
			Avatar:       utils.AvatarUrl(user.Avatar),
			InviteCode:   user.InviteCode,
			JoinTime:     user.CreatedAt.Unix(),
			PackageName:  utils.If(hasSummary, summary.PackageName, ""),
			TotalAmount:  utils.If(hasAccount, account.Amount, 0),
			PurchaseTime: utils.If(hasLatestPkg, latestPkg.CreatedAt.Unix(), 0),
			ExpireTime:   utils.If(hasSummary, summary.ExpireAt.Unix(), 0),
			PackageState: int(utils.If(hasLatestPkg, latestPkg.State, constmap.UserPackageStateDefault)),
			PackageStateText: business.UserPackageStateText(
				utils.If(hasLatestPkg, latestPkg.State, constmap.UserPackageStateDefault)),
		}
	}

	return data, nil
}
