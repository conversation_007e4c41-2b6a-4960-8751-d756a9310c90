package admin

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

import (
	"os"
	"path"
)

func Upload(ctx *gin.Context) (any, error) {
	var in struct {
		IsTmp int `form:"is_tmp"`
	}
	_ = ctx.ShouldBind(&in)
	file, err := ctx.FormFile("file")

	if err != nil {
		return nil, err
	}

	dir := config.Config.App.UploadTmpDir
	if _, err = os.Stat(dir); err != nil && os.IsNotExist(err) {
		if err = os.MkdirAll(dir, os.ModePerm); err != nil {
			return nil, err
		}
	}
	ext := path.Ext(file.Filename)
	filename := utils.UUID()

	dest := path.Join(dir, filename+ext)
	if err = ctx.SaveUploadedFile(file, dest); err != nil {
		return nil, err
	}

	defer func() {
		if err != nil {
			_ = os.Remove(dest)
		}
	}()

	var data struct {
		ResId uint   `json:"res_id"`
		Url   string `json:"url"`
	}

	res := models.UploadTmp{Url: dest}
	if err = utils.GetDB(ctx).Create(&res).Error; err == nil {
		data.ResId = res.ID

		if in.IsTmp == constmap.Disable {
			if url, err := business.UploadToOss(&res); err != nil {
				return nil, err
			} else {
				data.Url = utils.StaticUrl(url)
			}
		}

	}

	return data, err
}
