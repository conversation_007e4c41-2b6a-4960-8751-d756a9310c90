package tuan

import (
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Prices(ctx *gin.Context) (any, error) {
	var in struct {
		Id    uint   `form:"id" binding:"required"`
		Month string `form:"month" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	d, err := datetime.FormatStrToTime(in.Month+"-01", "yyyy-mm-dd")
	if err != nil {
		return nil, err
	}

	start := datetime.BeginOfMonth(d)
	end := datetime.EndOfMonth(d)

	var list []models.TuanDate
	db.Where("product_id=? and date>=? and date<=?", in.Id, start, end).Find(&list)

	var data struct {
		List []int64 `json:"list"`
	}
	data.List = slice.Map(list, func(index int, item models.TuanDate) int64 {
		return item.Date.Unix()
	})

	return data, nil
}
