package tuan

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 【废弃】
func SaveDate(ctx *gin.Context) (any, error) {
	return map[any]any{}, nil

	var in struct {
		Id    uint   `form:"id" binding:"required"`
		List  string `form:"list" binding:"required"`
		Month string `form:"month" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	d, err := datetime.FormatStrToTime(in.Month+"-01", "yyyy-mm-dd")
	if err != nil {
		return nil, err
	}

	start := datetime.BeginOfMonth(d)
	end := datetime.EndOfMonth(d)

	var product models.Tuan
	if err = db.Take(&product, in.Id).Error; err != nil {
		return nil, err
	}
	var list []int64
	if err = json.Unmarshal([]byte(in.List), &list); err != nil {
		return nil, err
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		var dates []models.TuanDate

		if err = tx.Unscoped().Delete(&models.TuanDate{},
			"product_id = ? and date between ? and ?",
			product.ID, start, end).Error; err != nil {
			return err
		}

		for _, i := range slice.Unique(list) {
			dates = append(dates, models.TuanDate{
				ProductId: product.ID,
				Date:      time.Unix(i, 0),
			})
		}
		if len(dates) > 0 {
			if err = tx.Create(dates).Error; err != nil {
				return err
			}
		}

		return nil
	})

	return nil, err
}
