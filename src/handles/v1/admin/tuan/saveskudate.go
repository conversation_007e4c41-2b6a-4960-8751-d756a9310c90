package tuan

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
	"time"
)

type datesku struct {
	SkuId           uint `json:"sku_id"`
	Price           int  `json:"price"`
	SettlementPrice int  `json:"settlement_price"`
}
type tuandate struct {
	Start int64     `json:"start"`
	End   int64     `json:"end"`
	Weeks string    `json:"weeks"`
	Stock int       `json:"stock"`
	Skus  []datesku `json:"skus"`
}

// 保存团期
func SaveSkuDate(ctx *gin.Context) (any, error) {
	var in struct {
		TuanId uint   `form:"tuan_id" binding:"required"`
		Dates  string `form:"dates" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var dates []tuandate

	if err := json.Unmarshal([]byte(in.Dates), &dates); err != nil {
		return nil, utils.NewError(err)
	} else if len(dates) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请保证至少有一条团期")
	}

	daySet := typeset.NewTypeSet[string](false)
	skuMaps := make(map[uint]*typeset.TypeSet[string])
	for _, v := range dates {
		if v.Start > v.End {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请保证团期区间开始时间小于结束时间")
		}
		if len(v.Skus) == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "每个区间至少包含一个sku")
		}
		if v.Stock <= 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入库存")
		}
		weeks := slice.ReduceBy(strings.Split(v.Weeks, ","), typeset.NewTypeSet[time.Weekday](false), func(_ int, item string, agg *typeset.TypeSet[time.Weekday]) *typeset.TypeSet[time.Weekday] {
			n, _ := convertor.ToInt(item)
			if n == 7 {
				agg.Add(time.Sunday)
			} else {
				agg.Add(time.Weekday(n))
			}
			return agg
		})

		day := time.Unix(v.Start, 0)
		end := time.Unix(v.End, 0).Format(constmap.DateFmtLong)
		days := make([]string, 0)

		for {
			dayStr := day.Format(constmap.DateFmtLong)
			if weeks.Has(day.Weekday()) {
				days = append(days, dayStr)
				if daySet.Has(dayStr) {
					return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("日期冲突[%s]，日期不能同时出现在多个日期区间内", dayStr))
				}
				daySet.Add(dayStr)
			}
			if dayStr == end {
				break
			}
			day = day.Add(constmap.TimeDur1d)
		}

		for _, vv := range v.Skus {
			if vv.Price <= 0 || vv.SettlementPrice <= 0 {
				return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入价格和结算价")
			}
			if _, ok := skuMaps[vv.SkuId]; !ok {
				skuMaps[vv.SkuId] = typeset.NewTypeSet[string](false)
			}
			for _, str := range days {
				if skuMaps[vv.SkuId].Has(str) {
					return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("Sku[%d]日期冲突[%s]", vv.SkuId, str))
				}
			}
		}
	}

	var cnt int64
	db.Model(&models.TuanSku{}).Where(models.TuanSku{TuanId: in.TuanId}).Where("id in ?", maputil.Keys(skuMaps)).Count(&cnt)
	if int(cnt) != len(skuMaps) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "sku不存在")
	}

	var tuanDates []models.TuanDate
	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Unscoped().Where(models.TuanDate{ProductId: in.TuanId}).Delete(&models.TuanDate{}).Error; err != nil {
			return err
		}
		if err := tx.Unscoped().Where(models.TuanDateSku{ProductId: in.TuanId}).Delete(&models.TuanDateSku{}).Error; err != nil {
			return err
		}

		var productPrice int
		for _, v := range dates {
			var tuanDate = models.TuanDate{
				ProductId: in.TuanId,
				Start:     time.Unix(v.Start, 0),
				End:       time.Unix(v.End, 0),
				Weeks:     v.Weeks,
				Stock:     v.Stock,
			}
			if err := tx.Create(&tuanDate).Error; err != nil {
				return err
			}
			tuanDates = append(tuanDates, tuanDate)
			var tuanDateSkus []models.TuanDateSku
			for _, vv := range v.Skus {
				if productPrice == 0 || productPrice > vv.Price {
					productPrice = vv.Price
				}
				tuanDateSkus = append(tuanDateSkus, models.TuanDateSku{
					ProductId:       in.TuanId,
					TuanDateId:      tuanDate.ID,
					SkuId:           vv.SkuId,
					Price:           vv.Price,
					SettlementPrice: vv.SettlementPrice,
				})
			}

			if err := tx.Create(&tuanDateSkus).Error; err != nil {
				return err
			}
		}

		if tx.Where("id=?", in.TuanId).Updates(&models.Tuan{Price: productPrice}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "产品价格更新失败")
		}

		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Ids []uint `json:"ids"`
	}
	out.Ids = slice.Map(tuanDates, func(index int, item models.TuanDate) uint {
		return item.ID
	})
	return out, nil
}
