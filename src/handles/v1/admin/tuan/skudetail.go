package tuan

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func SkuDetail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"sku_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	var tuanSku models.TuanSku

	if err := db.Take(&tuanSku, in.Id).Error; err != nil {
		return nil, err
	}

	var data struct {
		Id          uint   `json:"id"`
		Name        string `json:"name"`
		AdultNum    int    `json:"adult_num"`
		ChildrenNum int    `json:"children_num"`
		Price       int    `json:"price"`
		IsRequire   int    `json:"is_require"`
	}

	data.Id = tuanSku.ID
	data.Name = tuanSku.Name
	data.AdultNum = tuanSku.AdultNum
	data.Price = tuanSku.Price
	data.ChildrenNum = tuanSku.ChildrenNum
	data.IsRequire = tuanSku.IsRequire

	return data, nil
}
