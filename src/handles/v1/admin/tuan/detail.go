package tuan

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	var product models.Tuan

	if err := db.Where("tuans.id=?", in.Id).
		Joins("Desc").
		Preload("Pics").
		Preload("Nodes").
		Preload("Skus").
		Preload("Dates").
		Preload("Dates.Skus").
		Take(&product).Error; err != nil {
		return nil, err
	}

	var data struct {
		Id            uint                   `json:"id"`
		Name          string                 `json:"name"`
		State         int                    `json:"state"`
		MainPic       string                 `json:"main_pic"`
		Pics          []string               `json:"pics"`
		Desc          string                 `json:"desc"`
		ShortDesc     string                 `json:"short_desc"`
		Days          int                    `json:"days"`
		ZoneId        uint                   `json:"zone_id"`
		Price         int                    `json:"price"`
		AgeLimit      string                 `json:"age_limit"`     //年龄限制
		MemberLimit   string                 `json:"member_limit"`  //人数限制
		Base          string                 `json:"base"`          //集散/解散地
		Customization string                 `json:"customization"` //定制化
		Nodes         []string               `json:"nodes"`
		Schedules     []beans.TuanSchedule   `json:"schedules"`
		Skus          []beans.Sku            `json:"skus"`
		Cate          []string               `json:"cate"`
		DatesSkus     []tuandate             `json:"date_skus"`
		CanRefund     int                    `json:"can_refund"`
		RefundRules   []beans.TuanRefundRule `json:"refund_rules"`
		TaskId        uint                   `json:"task_id"`
		NeedPeople    int                    `json:"need_people"`
	}

	data.Id = product.ID
	data.Name = product.Name
	data.State = product.State
	data.Desc = product.Desc.Content
	data.MainPic = utils.StaticUrl(product.Pic)
	data.ShortDesc = product.ShortDesc
	data.Days = product.Days
	data.ZoneId = product.ZoneId
	data.Cate = strutil.SplitAndTrim(product.Cate, ",")
	data.Price = product.Price
	data.AgeLimit = product.AgeLimit
	data.MemberLimit = product.MemberLimit
	data.Base = product.Base
	data.Customization = product.Customization
	data.NeedPeople = product.NeedPeople

	tasks := task_biz.LoadRelateTasks(db, []uint{product.ID}, constmap.TaskRelateTuan)
	if task, ok := tasks[product.ID]; ok {
		data.TaskId = task.ID
	}

	data.Nodes = slice.Map(product.Nodes, func(index int, item models.TuanTripNode) string {
		return item.Name
	})
	data.Schedules = slice.Map(product.Nodes, func(index int, item models.TuanTripNode) beans.TuanSchedule {
		return beans.TuanSchedule{
			Name:      item.Name,
			ShortDesc: item.ShortDesc,
			Content:   item.Content,
		}
	})
	data.Skus = slice.Map(product.Skus, func(index int, item models.TuanSku) beans.Sku {
		return beans.Sku{
			Id:              item.ID,
			AdultNum:        item.AdultNum,
			ChildrenNum:     item.ChildrenNum,
			IsRequire:       item.IsRequire,
			Name:            item.Name,
			Price:           item.Price,
			SettlementPrice: item.SettlementPrice,
		}
	})
	data.Pics = slice.Map(product.Pics, func(index int, item models.TuanPic) string {
		return utils.StaticUrl(item.Url)
	})
	data.DatesSkus = slice.Map(product.Dates, func(index int, item models.TuanDate) tuandate {
		v := tuandate{
			Start: item.Start.Unix(),
			End:   item.End.Unix(),
			Weeks: item.Weeks,
			Stock: item.Stock,
			Skus: slice.Map(item.Skus, func(index int, item models.TuanDateSku) datesku {
				return datesku{
					SkuId:           item.SkuId,
					Price:           item.Price,
					SettlementPrice: item.SettlementPrice,
				}
			}),
		}
		return v
	})
	data.CanRefund = product.CanRefund

	if err := json.Unmarshal([]byte(product.RefundRules), &data.RefundRules); err != nil {
		data.RefundRules = []beans.TuanRefundRule{}
	}

	return data, nil
}
