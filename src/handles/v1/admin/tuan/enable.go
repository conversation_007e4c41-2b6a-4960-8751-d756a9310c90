package tuan

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Enable(ctx *gin.Context) (any, error) {
	var in struct {
		Id    uint `form:"id" binding:"required"`
		State int  `form:"state" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	var product models.Tuan

	if err := db.Where("id=?", in.Id).First(&product).Error; err != nil {
		return nil, err
	}

	update := models.Tuan{State: in.State}

	err := db.Model(&product).Updates(update).Error
	if err != nil {
		return nil, err
	}
	if err = my_queue.Light(constmap.EventTuanUpdate, gin.H{
		"ids": fmt.Sprintf("%d", in.Id),
	}); err != nil {
		return nil, fmt.Errorf("同步消息发送失败:%v", err)
	}

	return nil, err
}
