package tuan

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		State   int    `form:"state"`
		Keyword string `form:"keyword"`
		ZoneId  string `form:"zone_id"`
		Cate    string `form:"cate"`
	}
	_ = ctx.ShouldBind(&in)

	page, pageSize := utils.GetPage(ctx)

	type item struct {
		ID           uint        `json:"id"`
		Name         string      `json:"name"`
		CreatedAt    int64       `json:"created_at"`
		Pic          string      `json:"pic"`
		State        int         `json:"state"`
		ZoneName     string      `json:"zone_name"`
		Price        int         `json:"price"`
		Skus         []beans.Sku `json:"skus"`
		Cate         []string    `json:"cate"`
		HasSceneSign bool        `json:"has_scene_sign"`
		TaskId       uint        `json:"task_id"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	db := utils.GetDB(ctx)

	whereStr := strings.Builder{}
	whereStr.WriteString("1=1")
	whereArg := []any{}
	if in.State > 0 {
		whereStr.WriteString(" and tuans.state=?")
		whereArg = append(whereArg, in.State)
	}
	if !strutil.IsBlank(in.Keyword) {
		whereStr.WriteString(" and tuans.name like ?")
		whereArg = append(whereArg, "%"+in.Keyword+"%")
	}
	if in.Cate != "" {
		whereStr.WriteString(" and (0 ")
		for _, v := range strutil.SplitAndTrim(in.Cate, ",") {
			whereStr.WriteString(" or find_in_set(?, tuans.cate)")
			whereArg = append(whereArg, v)
		}
		whereStr.WriteString(")")
	}
	if in.ZoneId != "" {
		whereStr.WriteString(" and tuans.zone_id in ?")
		whereArg = append(whereArg, strutil.SplitAndTrim(in.ZoneId, ","))
	}

	db.Model(&models.Tuan{}).Where(whereStr.String(), whereArg...).Count(&data.Total)

	var list []models.Tuan
	db.Offset((page-1)*pageSize).
		Limit(pageSize).
		Where(whereStr.String(), whereArg...).
		Preload("Skus").
		Joins("Zone").
		Order("tuans.id desc").Find(&list)

	data.List = make([]item, len(list))

	for i, product := range list {
		data.List[i] = item{
			ID:        product.ID,
			Name:      product.Name,
			CreatedAt: product.CreatedAt.Unix(),
			State:     product.State,
			Pic:       utils.StaticUrl(product.Pic),
			Price:     product.Price,
			Skus: slice.Map(product.Skus, func(index int, item models.TuanSku) beans.Sku {
				return beans.Sku{
					Name:            item.Name,
					Price:           item.Price,
					SettlementPrice: item.SettlementPrice,
				}
			}),
			ZoneName: product.Zone.Name,
			Cate:     strutil.SplitAndTrim(product.Cate, ","),
		}
	}

	tuanIds := utils.SliceColumn(data.List, func(val item) uint {
		return val.ID
	})

	if len(tuanIds) > 0 {
		tasks := task_biz.LoadRelateTasks(db, maputil.Keys(tuanIds), constmap.TaskRelateTuan)
		for index, task := range tasks {
			for _, i := range tuanIds[index] {
				data.List[i].HasSceneSign = true
				data.List[i].TaskId = task.ID
			}
		}
	}

	return data, nil
}
