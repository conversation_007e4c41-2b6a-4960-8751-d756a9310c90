package tuan

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func SaveTripNode(ctx *gin.Context) (any, error) {
	var in struct {
		TuanId    uint   `form:"tuan_id" binding:"required"`
		Schedules string `form:"schedules" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	var schedules []beans.TuanSchedule
	if err := json.Unmarshal([]byte(in.Schedules), &schedules); err != nil {
		return nil, utils.NewError(err)
	}
	if len(schedules) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "至少保证填写一项")
	}
	var c int64
	if db.Model(&models.Tuan{}).Where("id=?", in.TuanId).Count(&c); c == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	nodes := slice.Map(schedules, func(index int, item beans.TuanSchedule) models.TuanTripNode {
		return models.TuanTripNode{
			TuanId:    in.TuanId,
			Name:      item.Name,
			ShortDesc: item.ShortDesc,
			Content:   item.Content,
		}
	})

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where(models.TuanTripNode{TuanId: in.TuanId}).Delete(&models.TuanTripNode{}).Error; err != nil {
			return err
		}
		if err := tx.Create(&nodes).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		Ids []uint `json:"ids"`
	}
	out.Ids = slice.Map(nodes, func(index int, item models.TuanTripNode) uint {
		return item.ID
	})
	return out, nil
}
