package tuan

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
)

type editData struct {
	Id            uint   `form:"id"`
	Name          string `form:"name" binding:"required"`
	ChildrenPrice *int   `form:"children_price" binding:"required"`
	Price         *int   `form:"price"`
	Desc          string `form:"desc" binding:"required"`
	MainPic       uint   `form:"main_pic_res_id"`
	Pics          string `form:"pics"`
	DeletePics    string `form:"delete_pics"`
	Days          int    `form:"days" binding:"required"`
	ShortDesc     string `form:"short_desc"`
	TripNode      string `form:"trip_node" binding:"required"`
	Skus          string `form:"skus" binding:"required"`
	ZoneId        uint   `form:"zone_id" binding:"required"`
	Cate          string `form:"cate"`
	AgeLimit      string `form:"age_limit"`     //年龄限制
	MemberLimit   string `form:"member_limit"`  //人数限制
	Base          string `form:"base"`          //集散/解散地
	Customization string `form:"customization"` //定制化
	CanRefund     int    `form:"can_refund"`
	RefundRules   string `form:"refund_rules"`
	TaskId        uint   `form:"task_id"`
	NeedPeople    int    `form:"need_people"`
}

func Save(ctx *gin.Context) (any, error) {
	var in editData

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var product models.Tuan
	db := utils.GetDB(ctx)

	if in.Price == nil {
		*in.Price = 0
	}
	if in.ChildrenPrice == nil {
		*in.ChildrenPrice = 0
	}

	if in.TaskId > 0 {
		if err := taskIsExists(db, in.TaskId); err != nil {
			return nil, err
		}
	}

	var err error
	isEdit := utils.If(in.Id > 0, true, false)
	if !isEdit {
		err = create(&product, db, &in)
	} else {
		err = edit(&product, db, &in)
	}
	if err != nil {
		return nil, err
	}

	if err = my_queue.Light(constmap.EventTuanUpdate, gin.H{
		"ids": fmt.Sprintf("%d", product.ID),
	}); err != nil {
		return nil, fmt.Errorf("同步消息发送失败:%v", err)
	}

	return nil, err
}

func taskIsExists(db *gorm.DB, taskId uint) error {
	var c int64
	db.Model(&models.Task{}).Where("id=? and parent_task_id=0", taskId).Count(&c)

	if c == 0 {
		return utils.NewErrorStr(constmap.ErrorParam, "任务不存在，或者该任务不是主任务")
	}
	return nil
}

// 返回 (需要新建的类型model数组，团游类型字段值)
func getTuanCate(db *gorm.DB, inCate string) ([]models.TuanCate, string) {
	cate := strutil.SplitAndTrim(inCate, ",")
	var all []models.TuanCate
	db.Where("name in ?", cate).Find(&all)
	cateSet := typeset.NewTypeSet(false, cate...)
	cate = cateSet.Values()
	slice.ForEach(all, func(_ int, v models.TuanCate) {
		cateSet.Del(v.Name)
	})
	return slice.ReduceBy(cateSet.Values(), make([]models.TuanCate, 0), func(_ int, v string, agg []models.TuanCate) []models.TuanCate {
		return append(agg, models.TuanCate{Name: v})
	}), strings.Join(cate, ",")
}

func edit(product *models.Tuan, db *gorm.DB, data *editData) error {
	if err := db.Preload("Skus").Where("tuans.id=?", data.Id).Joins("Desc").First(product).Error; err != nil {
		return err
	}

	oldSkus := product.Skus

	createCate, cate := getTuanCate(db, data.Cate)
	update := models.Tuan{
		Name:          data.Name,
		Days:          data.Days,
		ShortDesc:     data.ShortDesc,
		ZoneId:        data.ZoneId,
		Cate:          cate,
		AgeLimit:      data.AgeLimit,
		MemberLimit:   data.MemberLimit,
		Base:          data.Base,
		Customization: data.Customization,
		CanRefund:     data.CanRefund,
		NeedPeople:    data.NeedPeople,
	}
	var refundRules []beans.TuanRefundRule
	if err := json.Unmarshal([]byte(data.RefundRules), &refundRules); err != nil {
		return err
	}
	update.RefundRules, _ = convertor.ToJson(refundRules)

	if data.MainPic > 0 {
		var tmp models.UploadTmp
		if err := db.Take(&tmp, data.MainPic).Error; err == nil {
			if p, err := business.UploadToOss(&tmp); err != nil {
				return err
			} else {
				update.Pic = utils.UnWrapStaticUrl(p)
			}
		}
	}
	var skus []beans.Sku
	if err := json.Unmarshal([]byte(data.Skus), &skus); err != nil {
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error {
		if len(createCate) > 0 {
			if err := tx.Create(&createCate).Error; err != nil {
				return err
			}
		}
		if err := tx.Model(&product).Updates(update).Error; err != nil {
			return err
		}
		//更新零值字段
		if err := tx.Model(&product).Updates(map[string]any{
			"cate":          update.Cate,
			"age_limit":     update.AgeLimit,
			"member_limit":  update.MemberLimit,
			"base":          update.Base,
			"customization": update.Customization,
		}).Error; err != nil {
			return err
		}

		if err := saveNode(data, tx, product); err != nil {
			return err
		}

		if err := tx.Model(&product.Desc).Updates(models.TuanDesc{Content: data.Desc}).Error; err != nil {
			return err
		}

		var removeSkus []models.TuanSku
		var updateSkus []beans.Sku

		for _, oldSku := range oldSkus {
			var newSku *beans.Sku
			var ok bool

			for _, sku := range skus {
				if sku.Id == oldSku.ID {
					ok = true
					newSku = &sku
					break
				}
			}
			if ok {
				if skuIsChange(&oldSku, newSku) {
					updateSkus = append(updateSkus, *newSku)
				}
			} else {
				removeSkus = append(removeSkus, oldSku)
			}
		}

		if len(removeSkus) > 0 {
			ids := slice.Map(removeSkus, func(index int, item models.TuanSku) uint {
				return item.ID
			})
			if tx.Delete(&models.TuanSku{}, "id in ?", ids).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			if err := tx.Unscoped().Delete(&models.TuanDateSku{}, "product_id=? AND sku_id in ?", product.ID, ids).Error; err != nil {
				return utils.NewError(err)
			}
		}

		newSkus := slice.Map(slice.Filter(skus, func(index int, item beans.Sku) bool {
			return item.Id == 0
		}), func(index int, item beans.Sku) models.TuanSku {
			return models.TuanSku{
				TuanId:          product.ID,
				Price:           item.Price,
				SettlementPrice: item.SettlementPrice,
				Name:            item.Name,
				IsRequire:       item.IsRequire,
				AdultNum:        item.AdultNum,
				ChildrenNum:     item.ChildrenNum,
			}
		})
		if len(newSkus) > 0 {
			if err := tx.Create(&newSkus).Error; err != nil {
				return err
			}
		}
		if len(updateSkus) > 0 {
			for _, sku := range updateSkus {
				if tx.Model(&models.TuanSku{}).
					Select("*").Omit("id,tuan_id,created_at").
					Where("id=? and tuan_id=?", sku.Id, product.ID).Updates(models.TuanSku{
					Price:           sku.Price,
					SettlementPrice: sku.SettlementPrice,
					Name:            sku.Name,
					IsRequire:       sku.IsRequire,
					AdultNum:        sku.AdultNum,
					ChildrenNum:     sku.ChildrenNum,
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
				}
			}
		}

		if err := task_biz.SaveTaskRelate(tx, data.TaskId, product.ID, constmap.TaskRelateTuan); err != nil {
			return err
		}

		return saveImage(data, tx, product)
	})
}

func create(product *models.Tuan, db *gorm.DB, data *editData) error {
	createCate, cate := getTuanCate(db, data.Cate)
	product = &models.Tuan{
		Name:          data.Name,
		Days:          data.Days,
		ShortDesc:     data.ShortDesc,
		ZoneId:        data.ZoneId,
		Cate:          cate,
		AgeLimit:      data.AgeLimit,
		MemberLimit:   data.MemberLimit,
		Base:          data.Base,
		Customization: data.Customization,
		CanRefund:     data.CanRefund,
		NeedPeople:    data.NeedPeople,
	}
	var refundRules []beans.TuanRefundRule
	if err := json.Unmarshal([]byte(data.RefundRules), &refundRules); err != nil {
		return err
	}
	product.RefundRules, _ = convertor.ToJson(refundRules)

	if data.MainPic > 0 {
		var tmp models.UploadTmp
		if err := db.Take(&tmp, data.MainPic).Error; err == nil {
			if p, err := business.UploadToOss(&tmp); err != nil {
				return err
			} else {
				product.Pic = utils.UnWrapStaticUrl(p)
			}
		}
	}

	var skus []beans.Sku
	if err := json.Unmarshal([]byte(data.Skus), &skus); err != nil {
		return err
	}

	return db.Transaction(func(tx *gorm.DB) error {
		if len(createCate) > 0 {
			if err := tx.Create(&createCate).Error; err != nil {
				return err
			}
		}
		if err := tx.Create(product).Error; err != nil {
			return err
		}

		desc := models.TuanDesc{
			ProductId: product.ID,
			Content:   data.Desc,
		}

		if err := tx.Create(&desc).Error; err != nil {
			return err
		}
		if err := saveNode(data, tx, product); err != nil {
			return err
		}

		tuanSkus := slice.Map(skus, func(index int, item beans.Sku) models.TuanSku {
			return models.TuanSku{
				TuanId:          product.ID,
				Price:           item.Price,
				SettlementPrice: item.SettlementPrice,
				Name:            item.Name,
				IsRequire:       item.IsRequire,
				AdultNum:        item.AdultNum,
				ChildrenNum:     item.ChildrenNum,
			}
		})
		if err := tx.Create(&tuanSkus).Error; err != nil {
			return err
		}

		if err := task_biz.SaveTaskRelate(tx, data.TaskId, product.ID, constmap.TaskRelateTuan); err != nil {
			return err
		}

		return saveImage(data, tx, product)
	})
}

func saveNode(data *editData, tx *gorm.DB, tuan *models.Tuan) error {
	return nil
	var nodes []string
	if err := json.Unmarshal([]byte(data.TripNode), &nodes); err != nil {
		return err
	}
	if err := tx.Unscoped().
		Delete(&models.TuanTripNode{}, "tuan_id=?", tuan.ID).
		Error; err != nil {
		return err
	}

	var list []models.TuanTripNode
	for _, node := range nodes {
		list = append(list, models.TuanTripNode{
			TuanId: tuan.ID,
			Name:   node,
		})
	}

	if len(list) > 0 {
		return tx.Create(&list).Error
	}
	return nil
}

func saveImage(data *editData, db *gorm.DB, product *models.Tuan) error {
	if data.Pics != "" {
		pics := strings.Split(data.Pics, ",")

		var all []models.TuanPic
		for _, s := range pics {
			id, _ := convertor.ToInt(s)

			if u, err := business.MoveUploadFile(db, uint(id)); err == nil {
				all = append(all, models.TuanPic{
					ProductId: product.ID,
					Url:       u,
				})
			}
		}

		if len(all) > 0 {
			if err := db.Create(&all).Error; err != nil {
				return err
			}
		}
	}

	if data.DeletePics != "" {
		var pics []string
		if err := json.Unmarshal([]byte(data.DeletePics), &pics); err != nil {
			return err
		}

		pics = slice.Unique(pics)
		for _, pic := range pics {
			pic = utils.UnWrapStaticUrl(pic)
			db.Unscoped().Delete(&models.TuanPic{}, "product_id=? and url=?", product.ID, pic)
		}
	}

	return nil
}

func skuIsChange(sku *models.TuanSku, sku2 *beans.Sku) bool {
	return sku.Name != sku2.Name ||
		sku.SettlementPrice != sku2.SettlementPrice ||
		sku.Price != sku2.Price ||
		sku.AdultNum != sku2.AdultNum ||
		sku.ChildrenNum != sku2.ChildrenNum ||
		sku.IsRequire != sku2.IsRequire
}
