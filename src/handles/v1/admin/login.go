package admin

import (
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Login(ctx *gin.Context) (interface{}, error) {
	var in struct {
		UserName string `form:"username" binding:"required"`
		Password string `form:"password" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var user models.AdminUser
	if err := db.Where("username=? or mobile=?", in.UserName, in.UserName).
		Take(&user).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
	} else if bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(in.Password)) != nil {
		return nil, utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
	} else if user.State != constmap.Enable {
		return nil, utils.NewErrorStr(constmap.ErrorNotPermission, constmap.ErrorMsgNotPermission)
	}

	var authUser struct {
		beans.AuthTokenInfo
		IsAdmin bool `json:"is_admin"`
	}

	authUser.Name = user.Nickname
	authUser.Token = utils.UUID()

	session := models.AdminSession{
		Username: user.Username,
		Token:    authUser.Token,
		UserId:   user.ID,
	}

	if err := db.Create(&session).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
	}

	ctx.SetCookie("admin_token", session.Token, 7*24*3600, "", "", false, false)

	return authUser, nil
}
