package task

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Save(ctx *gin.Context) (any, error) {
	var in struct {
		Id              uint                  `form:"id"`
		Name            string                `form:"name" binding:"required"`
		ParentTaskId    uint                  `form:"parent_task_id"`
		IntervalType    constmap.TaskInterval `form:"interval_type" binding:"required"`
		MaxTimes        int                   `form:"max_times" binding:"required"`
		CondType        constmap.TaskCond     `form:"cond_type" binding:"required"`
		CondAmount      int                   `form:"cond_amount" binding:"required"`
		RewardAmount    int                   `form:"reward_amount" binding:"required"`
		RewardId        string                `form:"reward_id"`
		RewardType      constmap.TaskReward   `form:"reward_type" binding:"required"`
		ShorDesc        string                `form:"short_desc"`
		Tutorial        string                `form:"tutorial"`
		ScenicId        uint                  `form:"scenic_id"`
		CoverId         uint                  `form:"cover_id"`
		RankSettleNum   int                   `form:"rank_settle_num"`
		RewardDuplicate int                   `form:"reward_duplicate"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var task = new(models.Task)
	if in.Id > 0 {
		db.Joins("Ext").Take(task, in.Id)
		if task.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "任务不存在")
		}
	}
	if in.ParentTaskId > 0 {
		var cnt int64
		if db.Model(&models.Task{}).Where("id=?", in.ParentTaskId).Count(&cnt); cnt == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "父级任务不存在")
		}
	}

	update := &models.Task{
		Name:            in.Name,
		ParentTaskId:    in.ParentTaskId,
		IntervalType:    in.IntervalType,
		MaxTimes:        in.MaxTimes,
		CondType:        in.CondType,
		CondAmount:      in.CondAmount,
		RewardAmount:    in.RewardAmount,
		RewardId:        in.RewardId,
		RewardType:      in.RewardType,
		ShortDesc:       in.ShorDesc,
		ScenicId:        in.ScenicId,
		Cover:           task.Cover,
		RankSettleNum:   in.RankSettleNum,
		RewardDuplicate: utils.If(in.RewardDuplicate == constmap.Enable, constmap.Enable, constmap.Disable),
	}
	updateExt := models.TaskExt{
		Tutorial: in.Tutorial,
	}
	if in.CoverId > 0 {
		var cover models.UploadTmp
		if err := db.Take(&cover, in.CoverId).Error; err == nil {
			if p, err := business.UploadToOss(&cover); err != nil {
				return nil, err
			} else {
				update.Cover = utils.UnWrapStaticUrl(p)
			}
		}
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		if task.ID > 0 {
			if tx.Model(&task).
				Select("*").Omit(clause.Associations, "ID", "State", "CreatedAt").
				Updates(update).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			update.State = constmap.Disable
			if err := tx.Create(&update).Error; err != nil {
				return utils.NewError(err)
			}
			task = update
		}
		if task.Ext.ID > 0 {
			if tx.Model(&task.Ext).Updates(updateExt).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			updateExt.TaskId = task.ID
			if err := tx.Create(&updateExt).Error; err != nil {
				return utils.NewError(err)
			}
			task.Ext = updateExt
		}

		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Id uint `json:"id"`
	}
	out.Id = task.ID
	return out, nil
}
