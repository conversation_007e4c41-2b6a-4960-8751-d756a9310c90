package task

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
)

func Options(ctx *gin.Context) (any, error) {
	var out struct {
		Conds         []beans.SelectOption[constmap.TaskCond]     `json:"conds"`
		IntervalTypes []beans.SelectOption[constmap.TaskInterval] `json:"interval_types"`
		RewardTypes   []beans.SelectOption[constmap.TaskReward]   `json:"reward_types"`
	}

	out.Conds = slice.Map(maputil.Keys(business.TaskCondTextMap), func(index int, item constmap.TaskCond) beans.SelectOption[constmap.TaskCond] {
		return beans.SelectOption[constmap.TaskCond]{
			Label: business.TaskCondText(item),
			Value: item,
		}
	})
	out.RewardTypes = slice.Map(maputil.Keys(business.TaskRewardTextMap), func(index int, item constmap.TaskReward) beans.SelectOption[constmap.TaskReward] {
		return beans.SelectOption[constmap.TaskReward]{
			Label: business.TaskRewardText(item),
			Value: item,
		}
	})
	out.IntervalTypes = slice.Map(maputil.Keys(business.TaskIntervalTextMap), func(index int, item constmap.TaskInterval) beans.SelectOption[constmap.TaskInterval] {
		return beans.SelectOption[constmap.TaskInterval]{
			Label: business.TaskIntervalText(item),
			Value: item,
		}
	})
	return out, nil
}
