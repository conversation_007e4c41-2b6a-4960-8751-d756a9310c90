package task

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Enable(ctx *gin.Context) (any, error) {
	var in struct {
		TaskId uint `form:"task_id" binding:"required"`
		State  int  `form:"state" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)

	var task models.Task
	db.Take(&task, in.TaskId)
	if task.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "任务不存在")
	}

	if db.Model(&task).Updates(models.Task{
		State: in.State,
	}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
	}
	var out struct {
		Id    uint `json:"id"`
		State int  `json:"state"`
	}
	out.Id = task.ID
	out.State = task.State
	return out, nil
}
