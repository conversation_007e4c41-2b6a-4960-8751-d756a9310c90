package task

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		Id         uint   `form:"id"`
		Name       string `form:"name"`
		State      int    `form:"state"`
		OnlyParent int    `form:"only_parent"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	where := strings.Builder{}
	whereArgs := make([]any, 0)

	where.WriteString("1 ")

	if in.Id > 0 {
		where.WriteString(" AND id=?")
		whereArgs = append(whereArgs, in.Id)
	}
	if !strutil.IsBlank(in.Name) {
		where.WriteString(" AND name like ?")
		whereArgs = append(whereArgs, fmt.Sprintf("%%%s%%", strutil.Trim(in.Name)))
	}
	if in.State > 0 {
		where.WriteString(" AND state=?")
		whereArgs = append(whereArgs, in.State)
	}
	if in.OnlyParent == constmap.Enable {
		where.WriteString(" AND parent_task_id=?")
		whereArgs = append(whereArgs, 0)
	}

	type vitem struct {
		Id               uint                  `json:"id"`
		Name             string                `json:"name"`
		State            int                   `json:"state"`
		StateText        string                `json:"state_text"`
		ParentTaskId     uint                  `json:"parent_task_id"`
		ParentTaskName   string                `json:"parent_task_name"`
		IntervalType     constmap.TaskInterval `json:"interval_type"`
		IntervalTypeText string                `json:"interval_type_text"`
		MaxTimes         int                   `json:"max_times"`
		CondType         constmap.TaskCond     `json:"cond_type"`
		CondTypeText     string                `json:"cond_type_text"`
		CondAmount       int                   `json:"cond_amount"`
		RewardAmount     int                   `json:"reward_amount"`
		RewardId         string                `json:"reward_id"`
		RewardType       constmap.TaskReward   `json:"reward_type"`
		RewardTypeText   string                `json:"reward_type_text"`
	}
	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}

	var list []models.Task
	db.Model(&list).Where(where.String(), whereArgs...).Count(&out.Total).
		Offset((page - 1) * pageSize).Limit(pageSize).
		Order("id desc").
		Find(&list)

	parentTaskIds := slice.Map(list, func(index int, item models.Task) uint {
		return item.ParentTaskId
	})

	taskMap := make(map[uint]models.Task)

	if len(parentTaskIds) > 0 {
		var tasks []models.Task
		db.Find(&tasks, parentTaskIds)
		taskMap = slice.ReduceBy(tasks, taskMap, func(index int, item models.Task, agg map[uint]models.Task) map[uint]models.Task {
			agg[item.ID] = item
			return agg
		})
	}

	out.List = slice.Map(list, func(index int, item models.Task) vitem {
		v := vitem{
			Id:               item.ID,
			Name:             item.Name,
			State:            item.State,
			StateText:        business.EnableStateText(item.State),
			ParentTaskId:     item.ParentTaskId,
			ParentTaskName:   taskMap[item.ParentTaskId].Name,
			IntervalType:     item.IntervalType,
			IntervalTypeText: business.TaskIntervalText(item.IntervalType),
			MaxTimes:         item.MaxTimes,
			CondType:         item.CondType,
			CondTypeText:     business.TaskCondText(item.CondType),
			CondAmount:       item.CondAmount,
			RewardAmount:     item.RewardAmount,
			RewardId:         item.RewardId,
			RewardType:       item.RewardType,
			RewardTypeText:   business.TaskRewardText(item.RewardType),
		}
		return v
	})

	return out, nil
}
