package task

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func SignTasks(ctx *gin.Context) (any, error) {
	var in struct {
		ParentTaskId uint `form:"parent_task_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	type item struct {
		TaskId    uint   `json:"task_id"`
		SceneName string `json:"scene_name"`
		SceneId   uint   `json:"scene_id"`
	}

	var data struct {
		Tasks []item `json:"tasks"`
	}

	var tasks []models.Task
	db.Where(models.Task{ParentTaskId: in.ParentTaskId}).Find(&tasks)
	data.Tasks = make([]item, len(tasks))

	for i, task := range tasks {
		data.Tasks[i] = item{
			TaskId:  task.ID,
			SceneId: task.ScenicId,
		}
	}
	scenicIds := utils.SliceColumn(data.Tasks, func(val item) uint {
		return val.SceneId
	})
	if len(scenicIds) > 0 {
		var scenics []models.Scenic
		db.Where("id in ?", maputil.Keys(scenicIds)).Find(&scenics)
		for _, scenic := range scenics {
			for _, i := range scenicIds[scenic.ID] {
				data.Tasks[i].SceneName = scenic.Name
			}
		}
	}

	return data, nil
}
