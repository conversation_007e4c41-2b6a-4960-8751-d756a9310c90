package task

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		TaskId uint `form:"task_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var task models.Task
	db.Joins("Ext").Take(&task, in.TaskId)

	if task.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "任务不存在")
	}

	type vitem struct {
		Id               uint                  `json:"id"`
		Name             string                `json:"name"`
		State            int                   `json:"state"`
		StateText        string                `json:"state_text"`
		MaxTimes         int                   `json:"max_times"`
		IntervalType     constmap.TaskInterval `json:"interval_type"`
		IntervalTypeText string                `json:"interval_type_text"`
		CondType         constmap.TaskCond     `json:"cond_type"`
		CondTypeText     string                `json:"cond_type_text"`
		CondAmount       int                   `json:"cond_amount"`
		RankSettleNum    int                   `json:"rank_settle_num"` //排行榜任务前N个排名有奖励
		RewardAmount     int                   `json:"reward_amount"`
		RewardId         string                `json:"reward_id"`
		RewardType       constmap.TaskReward   `json:"reward_type"`
		RewardTypeText   string                `json:"reward_type_text"`
		RewardDuplicate  int                   `json:"reward_duplicate"` //是否可重叠奖励。否时奖励未领取时不再生成新奖励
		ShortDesc        string                `json:"short_desc"`
		Tutorial         string                `json:"tutorial"`
		ScenicId         uint                  `json:"scenic_id"`
		Cover            string                `json:"cover"`
	}

	var out struct {
		vitem
		ParentTaskId   uint    `json:"parent_task_id"`
		ParentTaskName string  `json:"parent_task_name"`
		Children       []vitem `json:"children"`
	}

	var parent models.Task
	var children []models.Task
	if task.ParentTaskId > 0 {
		db.Preload("Ext").Take(&parent, task.ParentTaskId)
	}
	db.Preload("Ext").Where(models.Task{ParentTaskId: task.ID}).Find(&children)

	foo := func(task models.Task) vitem {
		return vitem{
			Id:               task.ID,
			Name:             task.Name,
			State:            task.State,
			StateText:        business.EnableStateText(task.State),
			IntervalType:     task.IntervalType,
			IntervalTypeText: business.TaskIntervalText(task.IntervalType),
			MaxTimes:         task.MaxTimes,
			CondType:         task.CondType,
			CondTypeText:     business.TaskCondText(task.CondType),
			CondAmount:       task.CondAmount,
			RewardAmount:     task.RewardAmount,
			RewardId:         task.RewardId,
			RewardType:       task.RewardType,
			RewardTypeText:   business.TaskRewardText(task.RewardType),
			ShortDesc:        task.ShortDesc,
			Tutorial:         task.Ext.Tutorial,
			ScenicId:         task.ScenicId,
			Cover:            utils.StaticUrl(task.Cover),
			RankSettleNum:    task.RankSettleNum,
			RewardDuplicate:  task.RewardDuplicate,
		}
	}

	out.vitem = foo(task)
	out.ParentTaskId = task.ParentTaskId
	out.ParentTaskName = parent.Name
	out.Children = slice.Map(children, func(index int, item models.Task) vitem {
		return foo(item)
	})
	return out, nil
}
