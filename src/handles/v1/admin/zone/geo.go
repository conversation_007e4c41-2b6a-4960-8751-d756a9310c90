package zone

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/utils"
)

func Geo(ctx *gin.Context) (any, error) {
	var in struct {
		Address string `form:"address" binding:"required"`
		City    string `form:"city"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	resp, err := amap.Geo(in.Address, in.City)
	if err != nil {
		return nil, utils.NewError(err)
	}

	var data struct {
		Lng     float64 `json:"lng"`
		Lat     float64 `json:"lat"`
		Address string  `json:"address"`
	}

	if len(resp.GeoCodes) > 0 {
		data.Lng, data.Lat = utils.SplitGeoLocation(resp.GeoCodes[0].Location)
		data.Address = resp.GeoCodes[0].FormattedAddress
	}

	return data, nil
}
