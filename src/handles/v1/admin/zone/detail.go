package zone

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/handles/v1/admin/zone/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var zone models.Zone
	db := utils.GetDB(ctx)
	err := db.Where("id=?", in.Id).Take(&zone).Error
	if err != nil {
		return nil, err
	}

	return def.ZoneDetail{
		Id:       zone.ID,
		Name:     zone.Name,
		Level:    zone.Level,
		AdCode:   zone.AdCode,
		ParentId: zone.ParentId,
		State:    zone.State,
		Lng:      zone.Lng,
		Lat:      zone.Lat,
		Pic:      utils.StaticUrl(zone.Pic),
	}, nil
}
