package zone

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func Upsert(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Id       uint    `form:"id"`
		Name     string  `form:"name" binding:"required"`
		Level    int     `form:"level"`
		AdCode   string  `form:"ad_code"`
		ParentId uint    `form:"parent_id"`
		State    int     `form:"state"`
		Lng      float64 `form:"lng"`
		Lat      float64 `form:"lat"`
		Pic      uint    `form:"pic"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.Name == "" {
		return nil, errors.New("parameter error")
	}
	var (
		db   = utils.GetDB(ctx)
		zone = models.Zone{
			Name:     in.Name,
			Level:    in.Level,
			AdCode:   in.AdCode,
			ParentId: in.ParentId,
			State:    in.State,
			Lng:      in.Lng,
			Lat:      in.Lat,
		}
	)
	zone.ID = in.Id
	zone.UpdatedAt = time.Now()
	if in.Pic > 0 {
		var tmp models.UploadTmp
		if err := db.Take(&tmp, in.Pic).Error; err == nil {
			if p, err := business.UploadToOss(&tmp); err != nil {
				return nil, err
			} else {
				zone.Pic = utils.UnWrapStaticUrl(p)
			}
		}
	}

	if zone.ID > 0 {
		err = db.Where("id=?", zone.ID).Omit(clause.Associations).Updates(zone).Error
	} else {
		err = db.Omit(clause.Associations).Create(zone).Error
	}

	if err != nil {
		return nil, err
	}

	my_cache.Remove(constmap.RKZones)

	return zone, err
}
