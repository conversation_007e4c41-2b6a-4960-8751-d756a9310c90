package plan

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	page, pageSize := utils.GetPage(ctx)

	type item struct {
		ContactsName string `json:"contacts_name"`
		ContactsTel  string `json:"contacts_tel"`
		CreatedAt    int64  `json:"created_at"`
		FromZoneName string `json:"from_zone_name"`
		ToZoneName   string `json:"to_zone_name"`
		Peoples      int    `json:"peoples"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	whereStr := strings.Builder{}

	whereArgs := []any{}

	db := utils.GetDB(ctx)
	db.Model(&models.Plan{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []models.Plan
	db.Order("id desc").
		Limit(pageSize).
		Offset((page-1)*pageSize).
		Where(whereStr.String(), whereArgs...).Find(&list)

	data.List = make([]item, len(list))
	for i, order := range list {
		it := item{
			ContactsName: order.ContactName,
			ContactsTel:  order.ContactTel,
			CreatedAt:    order.CreatedAt.Unix(),
			FromZoneName: order.From,
			ToZoneName:   order.To,
			Peoples:      order.Peoples,
		}

		data.List[i] = it
	}

	return data, nil
}
