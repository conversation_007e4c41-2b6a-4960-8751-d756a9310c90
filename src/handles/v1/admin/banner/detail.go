package banner

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	db := utils.GetDB(ctx)

	var banner models.Banner

	if err := db.Take(&banner, in.Id).Error; err != nil {
		return nil, err
	}

	var data struct {
		Id           uint                    `json:"id"`
		Title        string                  `json:"title"`
		Link         string                  `json:"link"`
		State        int                     `json:"state"`
		Start        int64                   `json:"start"`
		End          int64                   `json:"end"`
		Pic          string                  `json:"pic"`
		Position     constmap.BannerPosition `json:"position"`
		PositionText string                  `json:"position_text"`
		Sort         int                     `json:"sort"`
	}

	data.End = banner.End.Unix()
	data.Start = banner.Start.Unix()
	data.Id = banner.ID
	data.Title = banner.Title
	data.Link = banner.Link
	data.Pic = utils.StaticUrl(banner.Pic)
	data.State = banner.State
	data.Position = banner.Position
	data.PositionText = business.BannerPositionText(banner.Position)
	data.Sort = banner.Sort

	return data, nil
}
