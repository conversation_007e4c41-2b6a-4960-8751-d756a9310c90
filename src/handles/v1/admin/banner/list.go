package banner

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func List(ctx *gin.Context) (any, error) {
	page, pageSize := utils.GetPage(ctx)

	db := utils.GetDB(ctx)

	type item struct {
		Id           uint                    `json:"id"`
		Title        string                  `json:"title"`
		Link         string                  `json:"link"`
		State        int                     `json:"state"`
		Start        int64                   `json:"start"`
		End          int64                   `json:"end"`
		Pic          string                  `json:"pic"`
		Position     constmap.BannerPosition `json:"position"`
		PositionText string                  `json:"position_text"`
		Sort         int                     `json:"sort"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	db.Model(&models.Banner{}).Count(&data.Total)

	var list []models.Banner
	db.Limit(pageSize).Offset((page - 1) * pageSize).Find(&list)

	data.List = make([]item, len(list))
	for i, banner := range list {
		it := item{
			Id:           banner.ID,
			Title:        banner.Title,
			Link:         banner.Link,
			Start:        banner.Start.Unix(),
			State:        banner.State,
			End:          banner.End.Unix(),
			Pic:          utils.StaticUrl(banner.Pic),
			Position:     banner.Position,
			PositionText: business.BannerPositionText(banner.Position),
			Sort:         banner.Sort,
		}
		data.List[i] = it
	}

	return data, nil
}
