package banner

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"
)

type editData struct {
	Id       uint                    `form:"id"`
	Title    string                  `form:"title" binding:"required"`
	Link     string                  `form:"link"`
	State    int                     `form:"state"`
	Start    int64                   `form:"start" binding:"required"`
	End      int64                   `form:"end" binding:"required"`
	PicResId uint                    `form:"pic_res_id"`
	Position constmap.BannerPosition `form:"position" binding:"required"`
	Sort     int                     `form:"sort"`
}

func Save(ctx *gin.Context) (any, error) {
	var in editData

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var err error
	db := utils.GetDB(ctx)

	isEdit := in.Id > 0
	if isEdit {
		err = edit(&in, db)
	} else {
		err = create(&in, db)
	}

	return nil, err
}

func edit(in *editData, db *gorm.DB) (err error) {
	var banner models.Banner
	if err = db.Take(&banner, in.Id).Error; err != nil {
		return err
	}

	update := models.Banner{
		Title:    in.Title,
		Start:    time.Unix(in.Start, 0),
		End:      time.Unix(in.End, 0),
		Link:     in.Link,
		State:    in.State,
		Position: in.Position,
		Sort:     in.Sort,
	}
	omitCols := typeset.NewTypeSet(false, "ID", "CreatedAt", "Pic")
	if in.PicResId > 0 {
		if update.Pic, err = business.MoveUploadFile(db, in.PicResId); err != nil {
			return err
		}
		update.Pic = utils.UnWrapStaticUrl(update.Pic)
		omitCols.Del("Pic")
	}

	err = db.Model(&banner).Omit(omitCols.Values()...).Updates(update).Error

	return err
}

func create(in *editData, db *gorm.DB) error {
	var err error

	banner := models.Banner{
		Title:    in.Title,
		Start:    time.Unix(in.Start, 0),
		End:      time.Unix(in.End, 0),
		Link:     in.Link,
		State:    in.State,
		Position: in.Position,
		Sort:     in.Sort,
	}
	if banner.Pic, err = business.MoveUploadFile(db, in.PicResId); err != nil {
		return err
	}
	banner.Pic = utils.UnWrapStaticUrl(banner.Pic)

	err = db.Create(&banner).Error

	return err
}
