package scenic

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 上下线
func Enable(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Id    uint `form:"id" binding:"required"`
		State int  `form:"state" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.State != constmap.Enable && in.State != constmap.Disable {
		return nil, utils.NewError(errors.New("state状态值错误"))
	}
	db := utils.GetDB(ctx)
	if db.Model(&models.Scenic{}).Where("id=?", in.Id).Updates(models.Scenic{
		State: in.State,
	}).RowsAffected == 0 {
		return nil, utils.NewError(errors.New("更新失败"))
	}
	if err = my_queue.Light(constmap.EventScenicUpdate, gin.H{
		"ids": fmt.Sprintf("%d", in.Id),
	}); err != nil {
		return nil, fmt.Errorf("同步消息发送失败:%v", err)
	}
	return make(map[string]any), nil
}
