package scenic

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/handles/v1/admin/scenic/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func TagList(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Keyword string `form:"keyword"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var (
		where     strings.Builder
		whereArgs []any
	)
	where.WriteString("1=1")
	if in.Keyword != "" {
		where.WriteString(" AND value like ?")
		whereArgs = append(whereArgs, "%"+in.Keyword+"%")
	}
	db := utils.GetDB(ctx)
	var tags []models.ScenicTag
	db.Where(where.String(), whereArgs...).Find(&tags)
	var rsp = def.ScenicTagList{
		List: make([]def.ScenicTag, 0),
	}
	for _, v := range tags {
		rsp.List = append(rsp.List, def.ScenicTag{
			Id:    v.ID,
			Value: v.Value,
		})
	}
	return rsp, nil
}
