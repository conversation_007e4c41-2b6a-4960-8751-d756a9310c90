package scenic

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func OtaList(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ScenicId uint `form:"scenic_id" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	db := utils.GetDB(ctx)
	type outItem struct {
		Id          uint   `json:"id"`
		OtaId       string `json:"ota_id"`
		OtaCode     string `json:"ota_code"`
		OtaCodeText string `json:"ota_code_text"`
	}
	var out struct {
		List []outItem `json:"list"`
	}
	var otas []models.ScenicOta
	db.Where(models.ScenicOta{ScenicId: in.ScenicId}).Find(&otas)

	out.List = slice.Map(otas, func(index int, item models.ScenicOta) outItem {
		return outItem{
			Id:          item.ID,
			OtaId:       item.OtaId,
			OtaCode:     item.OtaCode,
			OtaCodeText: constmap.OtaCodeMap[item.OtaCode],
		}
	})

	return out, nil
}
