package def

type ScenicList struct {
	Total int              `json:"total"`
	List  []ScenicListItem `json:"list"`
}

type ScenicListItem struct {
	Id        uint                `json:"id"`
	Name      string              `json:"name"`
	Pic       string              `json:"pic"`
	Tags      []string            `json:"tags"`
	State     int                 `json:"state"`
	StateText string              `json:"state_text"`
	Llm       int                 `json:"llm"`
	LlmText   string              `json:"llm_text"`
	ZoneId    uint                `json:"zone_id"`
	ZoneName  string              `json:"zone_name"`
	Level     int                 `json:"level"`
	Cate      []string            `json:"cate"`
	Ota       []string            `json:"ota"`
	OtaMap    map[string][]string `json:"ota_map"`
}

type ScenicPic struct {
	Id  uint   `json:"id"`
	Url string `json:"url"`
}

type ScenicDetail struct {
	Id          uint        `json:"id"`
	Name        string      `json:"name"`
	Pic         string      `json:"pic"`
	Tags        []string    `json:"tags"`
	State       int         `json:"state"`
	Llm         int         `json:"llm"`
	ZoneId      uint        `json:"zone_id"`
	ZoneName    string      `json:"zone_name"`
	ZoneLng     float64     `json:"zone_lng"`
	ZoneLat     float64     `json:"zone_lat"`
	Lng         float64     `json:"lng"`
	Lat         float64     `json:"lat"`
	Level       int         `json:"level"`
	Address     string      `json:"address"`
	Pics        []ScenicPic `json:"pics"`
	Cate        []string    `json:"cate"`
	Altitude    string      `json:"altitude"`
	OpenTime    string      `json:"open_time"`
	SuggestHour string      `json:"suggest_hour"`
	CostText    string      `json:"cost_text"`
	Desc        string      `json:"desc"`
}

type OptionList struct {
	List []Option `json:"list"`
}

type Option struct {
	Id    uint   `json:"id"`
	Value string `json:"value"`
}

type ScenicTagList struct {
	List []ScenicTag `json:"list"`
}
type ScenicTag struct {
	Id    uint   `json:"id"`
	Value string `json:"value"`
}

type ScenicSave struct {
	Id uint `json:"id"`
}
