package scenic

import (
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/admin/scenic/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
)

func Save(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Id          uint    `form:"id"`
		Name        string  `form:"name" binding:"required"`
		ZoneId      uint    `form:"zone_id" binding:"required"`
		Lng         float64 `form:"lng" binding:"required"`
		Lat         float64 `form:"lat" binding:"required"`
		Address     string  `form:"address" binding:"required"`
		Tel         string  `form:"tel"`
		CostText    string  `form:"cost_text"`
		Pic         uint    `form:"pic"`
		Level       int     `form:"level"`
		Llm         int     `form:"llm"`
		State       int     `form:"state"`
		Tags        string  `form:"tags"`
		Cate        string  `form:"cate"`
		Altitude    string  `form:"altitude"`
		OpenTime    string  `form:"open_time"`
		SuggestHour string  `form:"suggest_hour"`
		Desc        string  `form:"desc"`
		Pics        string  `form:"pics"`
		RemovePics  string  `form:"remove_pics"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	db := utils.GetDB(ctx)

	var (
		tags, cate []string
		tagList    []*models.ScenicTag
		cateList   []*models.ScenicCate
	)

	if in.Tags != "" {
		tagsSet := typeset.NewTypeSet(false, strings.Split(in.Tags, ",")...)
		tags = tagsSet.Values()
		db.Where("value in ?", tags).Find(&tagList)
		for _, v := range tagList {
			tagsSet.Del(v.Value)
		}
		tagList = tagList[:0]
		for _, v := range tagsSet.Values() {
			tagList = append(tagList, &models.ScenicTag{
				Value: v,
			})
		}
	}
	if in.Cate != "" {
		cateSet := typeset.NewTypeSet(false, strings.Split(in.Cate, ",")...)
		cate = cateSet.Values()
		db.Where("value in ?", cate).Find(&cateList)
		for _, v := range cateList {
			cateSet.Del(v.Value)
		}
		cateList = cateList[:0]
		for _, v := range cateSet.Values() {
			cateList = append(cateList, &models.ScenicCate{
				Value: v,
			})
		}
	}

	var scenic = &models.Scenic{}
	if in.Id > 0 {
		db.Where("id=?", in.Id).Take(&scenic)
		if scenic.ID < 1 {
			return nil, fmt.Errorf("景点[%d]不存在", in.Id)
		}
	}

	////检测冲突
	//var cnt int64
	//if db.Model(&models.Scenic{}).Where("zone_id=? AND name like ? AND id<>?", in.ZoneId, "%"+strutil.Trim(in.Name)+"%", scenic.ID).Count(&cnt); cnt > 0 {
	//	return nil, utils.NewErrorStr(constmap.ErrorParam, "景点已存在")
	//}

	scenic.Name = in.Name
	scenic.ZoneId = in.ZoneId
	scenic.Lng = in.Lng
	scenic.Lat = in.Lat
	scenic.Address = in.Address
	scenic.Tel = in.Tel
	scenic.CostText = in.CostText
	if in.Pic > 0 {
		var tmp models.UploadTmp
		if err := db.Take(&tmp, in.Pic).Error; err == nil {
			if p, err := business.UploadToOss(&tmp); err != nil {
				return nil, err
			} else {
				scenic.Pic = utils.UnWrapStaticUrl(p)
			}
		}
	}
	scenic.Level = in.Level
	scenic.State = utils.If(in.State == constmap.Enable || in.State == constmap.Disable,
		in.State,
		utils.If(scenic.ID > 0, scenic.State, constmap.Disable))
	scenic.Llm = utils.If(in.Llm == constmap.Enable || in.Llm == constmap.Disable,
		in.Llm,
		utils.If(scenic.ID > 0, scenic.Llm, constmap.Disable))
	scenic.Tags = strings.Join(tags, ",")
	scenic.Cate = strings.Join(cate, ",")
	scenic.Altitude = in.Altitude
	scenic.OpenTime = in.OpenTime
	scenic.SuggestHour = in.SuggestHour
	scenic.CostText = in.CostText
	scenic.Desc = in.Desc

	err = db.Transaction(func(tx *gorm.DB) error {
		if len(tagList) > 0 {
			if err = tx.Create(&tagList).Error; err != nil {
				return err
			}
		}
		if len(cateList) > 0 {
			if err = tx.Create(&cateList).Error; err != nil {
				return err
			}
		}
		if scenic.ID > 0 {
			err = tx.Omit(clause.Associations, "id").Where("id=?", scenic.ID).Updates(scenic).Error
			if err != nil {
				return err
			}
			//更新零值字段
			if err = tx.Model(&scenic).Updates(map[string]any{
				"level": scenic.Level,
				"tags":  scenic.Tags,
				"cate":  scenic.Cate,
			}).Error; err != nil {
				return err
			}
		} else {
			err = tx.Omit(clause.Associations).Create(&scenic).Error
		}

		//添加图片
		if !strutil.IsBlank(in.Pics) {
			newPicIds, _ := utils.ToArray[uint](in.Pics, ",")
			var newPics []models.ScenicPic

			for _, id := range newPicIds {
				if url, err := business.MoveUploadFile(tx, id); err != nil {
					return err
				} else {
					newPics = append(newPics, models.ScenicPic{
						ScenicId: scenic.ID,
						Url:      utils.UnWrapStaticUrl(url),
					})
				}
			}

			if len(newPics) > 0 {
				if err = tx.Create(&newPics).Error; err != nil {
					return err
				}
			}
		}

		//删除图片
		if !strutil.IsBlank(in.RemovePics) {
			removePicIds, _ := utils.ToArray[uint](in.RemovePics, ",")
			if err = tx.Unscoped().Where("id in ?", removePicIds).Delete(&models.ScenicPic{}).Error; err != nil {
				return err
			}
		}

		return err
	})

	if err != nil {
		return nil, utils.NewError(err)
	}
	if err = my_queue.Light(constmap.EventScenicUpdate, gin.H{
		"ids": fmt.Sprintf("%d", scenic.ID),
	}); err != nil {
		return nil, fmt.Errorf("同步消息发送失败:%v", err)
	}

	var rsp def.ScenicSave
	rsp.Id = scenic.ID
	return rsp, nil
}
