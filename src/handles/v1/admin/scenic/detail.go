package scenic

import (
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/handles/v1/admin/scenic/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func Detail(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var scenic models.Scenic
	db := utils.GetDB(ctx)
	db.Preload("Pics").Preload("Zone").Where("id=?", in.Id).Take(&scenic)
	if scenic.ID < 1 {
		return nil, errors.New("景点不存在")
	}
	var rsp = def.ScenicDetail{
		Id:       scenic.ID,
		Name:     scenic.Name,
		Pic:      utils.StaticUrl(scenic.Pic),
		State:    scenic.State,
		Llm:      scenic.Llm,
		ZoneId:   scenic.ZoneId,
		ZoneName: scenic.Zone.Name,
		ZoneLng:  scenic.Zone.Lng,
		ZoneLat:  scenic.Zone.Lat,
		Lng:      scenic.Lng,
		Lat:      scenic.Lat,
		Level:    scenic.Level,
		Address:  scenic.Address,
		Pics: slice.Map(scenic.Pics, func(index int, item models.ScenicPic) def.ScenicPic {
			return def.ScenicPic{
				Id:  item.ID,
				Url: utils.StaticUrl(item.Url),
			}
		}),
		Altitude:    scenic.Altitude,
		OpenTime:    scenic.OpenTime,
		SuggestHour: scenic.SuggestHour,
		CostText:    scenic.CostText,
		Desc:        scenic.Desc,
		Tags:        make([]string, 0),
		Cate:        make([]string, 0),
	}
	if scenic.Tags != "" {
		rsp.Tags = strings.Split(scenic.Tags, ",")
	}
	if scenic.Cate != "" {
		rsp.Cate = strings.Split(scenic.Cate, ",")
	}

	return rsp, nil
}
