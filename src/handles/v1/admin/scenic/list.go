package scenic

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/admin/scenic/internal/def"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Id     uint   `form:"id"`
		Level  string `form:"level"` //【多选】景点等级
		Name   string `form:"name"`
		ZoneId string `form:"zone_id"` //【多选】城市ID
		Tags   string `form:"tags"`    //【多选】标签
		State  int    `form:"state"`
		Cate   string `form:"cate"` //【多选】分类
		Ota    string `form:"ota"`
		OtaId  string `form:"ota_id"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	page, psize := utils.GetPage(ctx)

	var must = []map[string]any{
		{"term": map[string]any{"type": constmap.PoiTypeScenic}},
	}
	if in.Id > 0 {
		must = append(must, map[string]any{"term": map[string]any{"obj_id": in.Id}})
	}
	if in.Name != "" {
		must = append(must, map[string]any{"match_phrase": map[string]any{"name": in.Name}})
	}
	if in.Level != "" {
		levelList, _ := utils.ToArray[int](in.Level, ",")
		must = append(must, map[string]any{"terms": map[string]any{"level": levelList}})
	}
	if in.Tags != "" {
		must = append(must, map[string]any{"terms": map[string]any{"tags": strings.Split(in.Tags, ",")}})
	}
	if in.ZoneId != "" {
		zoneIds, _ := utils.ToArray[uint](in.ZoneId, ",")
		must = append(must, map[string]any{"terms": map[string]any{"zone_id": zoneIds}})
	}
	if in.State > 0 {
		must = append(must, map[string]any{"term": map[string]any{"state": in.State}})
	}
	if in.Cate != "" {
		must = append(must, map[string]any{"terms": map[string]any{"cate": strings.Split(in.Cate, ",")}})
	}
	if in.Ota != "" {
		must = append(must, map[string]any{"term": map[string]any{"ota": in.Ota}})
	}
	if in.OtaId != "" {
		must = append(must, map[string]any{"term": map[string]any{"ota_id": in.OtaId}})
	}
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": must,
			},
		},
		"sort": []map[string]any{
			{"_score": map[string]any{"order": "desc"}},
			{"obj_id": map[string]any{"order": "desc"}},
		},
		"track_total_hits": true,
		"from":             (page - 1) * psize,
		"size":             psize,
	}
	res, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)
	if err != nil {
		return nil, err
	}
	var rsp = def.ScenicList{
		List: make([]def.ScenicListItem, 0),
	}
	rsp.Total = res.Hits.Total.Value
	if len(res.Hits.Hits) == 0 {
		return rsp, nil
	}

	var sortMap = make(map[uint]int)
	var scenicIds []uint
	for i, v := range res.Hits.Hits {
		sortMap[v.Source.ObjId] = i
		scenicIds = append(scenicIds, v.Source.ObjId)
	}
	var scenics []models.Scenic
	db.Joins("Zone").Where("scenics.id in ?", scenicIds).Find(&scenics)

	var (
		otas   []models.ScenicOta
		otaMap = make(map[uint][]models.ScenicOta)
	)
	db.Where("scenic_id in ?", scenicIds).Find(&otas)
	for _, v := range otas {
		otaMap[v.ScenicId] = append(otaMap[v.ScenicId], v)
	}

	for _, v := range scenics {
		var (
			tags = make([]string, 0)
			cate = make([]string, 0)
		)
		if v.Tags != "" {
			tags = strings.Split(v.Tags, ",")
		}
		if v.Cate != "" {
			cate = strings.Split(v.Cate, ",")
		}
		vitem := def.ScenicListItem{
			Id:        v.ID,
			Name:      v.Name,
			Pic:       utils.StaticUrl(v.Pic),
			Tags:      tags,
			State:     v.State,
			StateText: constmap.StateMap[v.State],
			Llm:       v.Llm,
			LlmText:   constmap.StateMap[v.Llm],
			ZoneId:    v.ZoneId,
			ZoneName:  v.Zone.Name,
			Level:     v.Level,
			Cate:      cate,
			Ota: slice.FlatMap(otaMap[v.ID], func(index int, item models.ScenicOta) []string {
				return []string{item.OtaCode}
			}),
			OtaMap: slice.ReduceBy(otaMap[v.ID], make(map[string][]string), func(_ int, v models.ScenicOta, agg map[string][]string) map[string][]string {
				if ids, ok := agg[v.OtaCode]; ok {
					agg[v.OtaCode] = append(ids, v.OtaId)
				} else {
					agg[v.OtaCode] = []string{v.OtaId}
				}
				return agg
			}),
		}
		rsp.List = append(rsp.List, vitem)
	}
	// 重新按ES结果顺序排序
	slice.SortBy(rsp.List, func(a, b def.ScenicListItem) bool {
		return sortMap[a.Id] < sortMap[b.Id]
	})
	return rsp, nil
}
