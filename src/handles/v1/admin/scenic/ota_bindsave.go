package scenic

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 绑定OTA
func OtaBindSave(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ScenicId uint   `form:"scenic_id" binding:"required"`
		Otas     string `form:"otas"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	type otaData struct {
		OtaId   string `json:"ota_id"`
		OtaCode string `json:"ota_code"`
	}
	var otas []otaData
	err = json.Unmarshal([]byte(in.Otas), &otas)
	if err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	err = db.Transaction(func(tx *gorm.DB) error {
		var createMap = make(map[string]models.ScenicOta)
		for _, v := range otas {
			v.OtaId = strutil.Trim(v.OtaId)
			v.OtaCode = strutil.Trim(v.OtaCode)
			if _, ok := constmap.OtaCodeMap[v.OtaCode]; !ok {
				return utils.NewErrorStr(constmap.ErrorParam, "未知ota_code")
			}
			var ota models.ScenicOta
			tx.Model(&models.ScenicOta{}).Where(models.ScenicOta{OtaId: v.OtaId, OtaCode: v.OtaCode}).Take(&ota)
			if ota.ID > 0 && ota.ScenicId != in.ScenicId {
				return utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("OTA冲突，OTA已绑定景点[%d]", ota.ScenicId))
			}
			createMap[fmt.Sprintf("%s:%s", v.OtaCode, v.OtaId)] = models.ScenicOta{
				ScenicId: in.ScenicId,
				OtaId:    v.OtaId,
				OtaCode:  v.OtaCode,
			}
		}
		if err = tx.Omit(clause.Associations).Unscoped().Model(&models.ScenicOta{}).
			Where(models.ScenicOta{ScenicId: in.ScenicId}).Delete(nil).Error; err != nil {
			return utils.NewError(err)
		}
		if len(createMap) > 0 {
			if err = tx.Omit(clause.Associations).Model(&models.ScenicOta{}).Create(maputil.Values(createMap)).Error; err != nil {
				return utils.NewError(err)
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return struct{}{}, nil
}
