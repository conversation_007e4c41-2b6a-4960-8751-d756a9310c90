package my

import (
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func ResetPwd(ctx *gin.Context) (interface{}, error) {
	var in struct {
		OldPassword string `form:"old_password" binding:"required"`
		Password    string `form:"new_password" binding:"required"`
		Password2   string `form:"confirm_password" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	} else if in.Password2 != in.Password {
		return nil, utils.NewErrorStr(constmap.ErrorTwoPass, constmap.ErrorMsgTwoPass)
	}

	db := utils.GetDB(ctx)
	p, _ := bcrypt.GenerateFromPassword([]byte(in.Password), bcrypt.DefaultCost)

	session, _ := business.GetAdminLoginUser(ctx)

	if bcrypt.CompareHashAndPassword([]byte(session.User.Password), []byte(in.OldPassword)) != nil {
		return nil, utils.NewErrorStr(constmap.ErrorOldPwd, constmap.ErrorMsgOldPwd)
	}

	err := db.Model(&session.User).Omit(clause.Associations).Updates(models.AdminUser{Password: string(p)}).Error

	return nil, err
}
