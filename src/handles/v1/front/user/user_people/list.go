package user_people

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		IsDefault int `form:"is_default"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	where := strings.Builder{}
	whereArgs := make([]any, 0)

	where.WriteString("user_id=?")
	whereArgs = append(whereArgs, session.UserId)

	if in.IsDefault > 0 {
		where.WriteString(" and is_default=?")
		whereArgs = append(whereArgs, in.IsDefault)
	}

	type vitem struct {
		Id         uint            `json:"id"`
		Name       string          `json:"name"`
		Phone      string          `json:"phone"`
		IdType     constmap.IdType `json:"id_type"`
		IdTypeText string          `json:"id_type_text"`
		IdNo       string          `json:"id_no"`
		IsDefault  int             `json:"is_default"`
	}
	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}

	var peoples []models.UserPeople
	db.Model(&peoples).Where(where.String(), whereArgs...).
		Count(&out.Total).
		Order("id desc").Limit(pageSize).Offset((page - 1) * pageSize).
		Find(&peoples)

	if len(peoples) == 0 {
		out.List = make([]vitem, 0)
		return out, nil
	}

	out.List = slice.Map(peoples, func(index int, item models.UserPeople) vitem {
		return vitem{
			Id:         item.ID,
			Name:       item.Name,
			Phone:      item.Phone,
			IdType:     item.IdType,
			IdTypeText: business.IdTypeText(item.IdType),
			IdNo:       item.IdNo,
			IsDefault:  item.IsDefault,
		}
	})
	return out, nil
}
