package user_people

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"roadtrip-api/src/validators"
)

func Save(ctx *gin.Context) (any, error) {
	var in struct {
		Id        uint            `form:"id"`
		Name      string          `form:"name" binding:"required"`
		Phone     string          `form:"phone"`
		IdType    constmap.IdType `form:"id_type" binding:"required"`
		IdNo      string          `form:"id_no" binding:"required"`
		IsDefault int             `form:"is_default"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var people models.UserPeople
	if in.Id > 0 {
		db.Where(models.UserPeople{UserId: session.UserId}).Take(&people, in.Id)
		if people.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "数据不存在")
		}
	}

	var hasPeople models.UserPeople
	db.Where("id<>?", in.Id).
		Where(models.UserPeople{
			UserId: session.UserId,
			IdType: in.IdType,
			IdNo:   in.IdNo,
		}).Take(&hasPeople)

	if hasPeople.ID > 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "证件信息已存在")
	}

	if !typeset.NewTypeSet(false, constmap.ValidIdType...).Has(in.IdType) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请选择有效证件类型")
	}

	if in.IdType == constmap.IdTypeIdCard {
		if !validators.IsIdCardNo(in.IdNo) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入有效的身份证号码")
		}
	}

	if !strutil.IsBlank(in.Phone) {
		if !validators.IsMobile(in.Phone) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入有效的手机号码")
		}
	}

	people.UserId = session.UserId
	people.Name = in.Name
	people.Phone = in.Phone
	people.IdType = in.IdType
	people.IdNo = in.IdNo
	people.IsDefault = utils.If(in.IsDefault == constmap.Enable, constmap.Enable, constmap.Disable)

	err := db.Transaction(func(tx *gorm.DB) error {
		if people.IsDefault == constmap.Enable {
			tx.Where(models.UserPeople{UserId: session.UserId}).Updates(&models.UserPeople{
				IsDefault: constmap.Disable,
			})
		}
		if people.ID > 0 {
			if tx.Model(&people).
				Select("*").Omit(clause.Associations, "ID", "CreatedAt").
				Updates(&people).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			if err := tx.Omit(clause.Associations).Create(&people).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		Id        uint `json:"id"`
		IsDefault int  `json:"is_default"`
	}
	out.Id = people.ID
	out.IsDefault = people.IsDefault
	return out, nil
}
