package user_people

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Del(ctx *gin.Context) (any, error) {
	var in struct {
		PeopleId uint `form:"people_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var people models.UserPeople

	db.Where(models.UserPeople{UserId: session.UserId}).Take(&people, in.PeopleId)

	if people.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "出行人信息不存在")
	}

	if err := db.Unscoped().Delete(&people).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, err.Error())
	}

	var out struct {
		Id uint `json:"id"`
	}
	out.Id = people.ID
	return out, nil
}
