package user_people

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
)

func Options(ctx *gin.Context) (any, error) {
	var out struct {
		IdTypes []beans.SelectOption[constmap.IdType] `json:"id_types"`
	}

	out.IdTypes = slice.Map(constmap.ValidIdType, func(_ int, item constmap.IdType) beans.SelectOption[constmap.IdType] {
		return beans.SelectOption[constmap.IdType]{
			Label: business.IdTypeText(item),
			Value: item,
		}
	})
	return out, nil
}
