package user

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func PlanList(ctx *gin.Context) (any, error) {
	type outItem struct {
		Id            uint                      `json:"id"`
		From          string                    `json:"from"`
		Days          int                       `json:"days"`
		SceneNum      int                       `json:"scene_num"`
		To            string                    `json:"to"`
		PromptOptions beans.TravelPromptOptions `json:"prompt_options"`
		CreatedAt     int64                     `json:"created_at"`
		Subject       string                    `json:"subject"`
		PdfState      constmap.PlanPdfState     `json:"pdf_state"`
		PdfUrl        string                    `json:"pdf_url"`
		PdfCover      string                    `json:"pdf_cover"`
	}
	type out struct {
		Total int64     `json:"total"`
		List  []outItem `json:"list"`
	}
	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	var rsp = out{
		List: make([]outItem, 0),
	}
	var plans []models.Plan
	db.Model(&plans).
		Where("user_id=? and state=?", session.UserId, constmap.Enable).
		Count(&rsp.Total).
		Joins("Ext").
		Preload("Sections").
		Order("plans.id DESC").
		Offset((page - 1) * pageSize).Limit(pageSize).Find(&plans)
	for _, v := range plans {
		var options beans.TravelPromptOptions
		if v.Ext.PromptOptions.Data != nil {
			options = *v.Ext.PromptOptions.Data
		}

		var rspItem = outItem{
			Id:            v.ID,
			Days:          v.CostDay,
			SceneNum:      v.SceneCnt,
			From:          v.From,
			To:            v.To,
			PromptOptions: options,
			CreatedAt:     v.CreatedAt.Unix(),
			Subject:       v.Subject,
			PdfState:      v.Ext.PdfState,
			PdfCover:      utils.StaticUrl(v.Ext.PdfCover),
			PdfUrl:        utils.StaticUrl(v.Ext.PdfUrl),
		}
		rspItem.PdfUrl = strings.ReplaceAll(rspItem.PdfUrl, "http://", "https://")

		rsp.List = append(rsp.List, rspItem)
	}
	return rsp, nil
}
