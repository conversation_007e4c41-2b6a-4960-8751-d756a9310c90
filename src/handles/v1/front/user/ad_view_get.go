package user

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func AdViewGet(ctx *gin.Context) (any, error) {
	key := utils.RandomComplexStr(16)
	if err := my_cache.Set(fmt.Sprintf(constmap.RKAdView, key), 1, constmap.TimeDur5m); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	var out struct {
		Key string `json:"key"`
	}
	out.Key = key
	return out, nil
}
