package user

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
)

// 我参与的活动
func Activities(ctx *gin.Context) (any, error) {
	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	type activityItem struct {
		ActivityId uint                       `json:"activity_id"`
		Title      string                     `json:"title"`
		Cover      string                     `json:"cover"`
		Start      int64                      `json:"start"`
		End        int64                      `json:"end"`
		State      int                        `json:"state"`
		Link       string                     `json:"link"`
		Rewards    []beans.UserActivityReward `json:"rewards"`
	}

	var out struct {
		Total int64          `json:"total"`
		List  []activityItem `json:"list"`
	}

	// 查询用户参与的活动
	var userActivities []models.UserActivity
	query := db.Model(&userActivities).Where(models.UserActivity{UserId: session.UserId})
	query.Count(&out.Total)

	if out.Total == 0 {
		out.List = []activityItem{}
		return out, nil
	}

	query.Order("created_at DESC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&userActivities)

	// 批量查询活动信息
	activityIds := slice.Map(userActivities, func(_ int, ua models.UserActivity) uint {
		return ua.ActivityId
	})

	var activities []models.Activity
	db.Find(&activities, activityIds)

	activityMap := slice.KeyBy(activities, func(a models.Activity) uint {
		return a.ID
	})

	// 构建返回数据
	out.List = slice.FilterMap(userActivities, func(_ int, ua models.UserActivity) (activityItem, bool) {
		activity, exists := activityMap[ua.ActivityId]
		if !exists {
			return activityItem{}, false
		}

		rewards := []beans.UserActivityReward{}
		if ua.Ext.Data != nil && len(ua.Ext.Data.Rewards) > 0 {
			rewards = ua.Ext.Data.Rewards
		}

		return activityItem{
			ActivityId: activity.ID,
			Title:      activity.Title,
			Cover:      utils.StaticUrl(activity.Cover),
			Start:      activity.Start.Unix(),
			End:        activity.End.Unix(),
			State:      activity.State,
			Link:       activity.Link,
			Rewards:    rewards,
		}, true
	})

	return out, nil
}
