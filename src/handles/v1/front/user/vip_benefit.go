package user

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 会员权益
func VipBenefit(ctx *gin.Context) (any, error) {
	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)
	var out struct {
		ShowRenew   bool              `json:"show_renew"` //是否显示续费
		VipInfo     beans.UserVipInfo `json:"vip_info"`
		VipConf     beans.VipConf     `json:"vip_conf"`
		BenefitUser struct {
			RealName  string `json:"real_name"`
			FirstName string `json:"first_name"`
			LastName  string `json:"last_name"`
			IdCard    string `json:"id_card"`
		} `json:"benefit_user"`
	}
	out.VipInfo.UserType = constmap.UserTypeNormal
	out.VipInfo.State = constmap.Disable

	var userVip models.UserVip
	var vipConf models.VipConf
	db.Where(models.UserVip{UserId: session.UserId}).Last(&userVip)
	db.Where(models.VipConf{State: constmap.Enable}).Last(&vipConf)
	if userVip.ID > 0 {
		out.BenefitUser.RealName = userVip.RealName
		out.BenefitUser.FirstName = userVip.FirstName
		out.BenefitUser.LastName = userVip.LastName
		out.BenefitUser.IdCard = userVip.IdCard
	}
	var userVipBenefitMap = make(map[uint]models.UserVipBenefit)
	if user_biz.IsUserVipValid(&userVip) {
		extra, hasChange := user_biz.IsUserVipChanged(&userVip, &vipConf)
		out.ShowRenew = !hasChange

		db.Take(&vipConf, extra.VipConfId)
		out.VipInfo = beans.UserVipInfo{
			UserType: constmap.UserTypeVip,
			State:    constmap.Enable,
			VipName:  vipConf.Name,
			VipIcon:  vipConf.Icon,
			VipStart: userVip.Start.Format(constmap.DateFmtLong),
			VipEnd:   userVip.End.Format(constmap.DateFmtLong),
			RealName: userVip.RealName,
		}

		var ub []models.UserVipBenefit
		db.Find(&ub, models.UserVipBenefit{UserVipId: userVip.ID})
		userVipBenefitMap = slice.KeyBy(ub, func(v models.UserVipBenefit) uint { return v.BenefitId })
	}
	var benefit []models.VipBenefit
	db.Where(models.VipBenefit{VipConfId: vipConf.ID}).Find(&benefit)
	out.VipConf = beans.VipConf{
		Id:          vipConf.ID,
		Name:        vipConf.Name,
		Icon:        vipConf.Icon,
		Desc:        vipConf.Desc,
		SalePrice:   vipConf.SalePrice,
		MarketPrice: vipConf.MarketPrice,
		Benefit: slice.Map(benefit, func(_ int, v models.VipBenefit) beans.VipBenefit {
			b := beans.VipBenefit{
				Code:     v.Code,
				Name:     v.Name,
				Desc:     v.Desc,
				Icon:     v.Icon,
				Type:     v.BenefitType,
				Discount: v.Discount,
				Times:    v.Times,
			}
			if uv, ok := userVipBenefitMap[v.ID]; ok {
				b.RemainTimes = uv.RemainTimes
			}
			return b
		}),
	}

	return out, nil
}
