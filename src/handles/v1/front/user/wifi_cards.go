package user

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func WifiCards(ctx *gin.Context) (any, error) {
	db := utils.GetDB(ctx)

	session, _ := business.GetFrontLoginUser(ctx)

	type card struct {
		RelId              uint   `json:"rel_id"`
		Code               string `json:"code"`
		AirlineName        string `json:"airline_name"`
		AirlineProductName string `json:"airline_product_name"`
		AdvertiserName     string `json:"advertiser_name"`
		ExpireTime         int64  `json:"expire_time"`
		GotStateText       string `json:"got_state_text"`
		GotState           int    `json:"got_state"`
		Logo               string `json:"logo"`
	}

	var list []models.FlowCardRelCode
	db.Joins("Air").
		Joins("Advertiser").Joins("Code").
		Where("user_id=?", session.UserId).
		Order("flow_card_rel_codes.id desc").
		Find(&list)

	var data struct {
		List  []card `json:"list"`
		Total int    `json:"total"`
	}

	now := time.Now()

	data.List = slice.Map(list, func(index int, item models.FlowCardRelCode) card {
		it := card{
			RelId:              item.ID,
			Code:               item.Code.Code,
			AirlineName:        item.Air.Name,
			AirlineProductName: item.Air.ProductName,
			AdvertiserName:     item.Advertiser.Name,
			ExpireTime:         item.Code.EndAt.Unix(),
			Logo:               utils.StaticUrl(item.Air.Logo),
			GotStateText:       wifis.FlowCardCodeGetStateText(item.Code.GotState),
			GotState:           item.Code.GotState,
		}
		if now.After(item.Code.EndAt) {
			it.GotStateText = wifis.FlowCardCodeGetStateText(constmap.GotStateExpired)
		}

		return it
	})
	data.Total = len(data.List)

	return data, nil
}
