package user

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/validators"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 购买会员
func VipPay(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		RealName  string `form:"real_name" binding:"required"`
		FirstName string `form:"first_name" binding:"required"`
		LastName  string `form:"last_name" binding:"required"`
		IdCard    string `form:"id_card" binding:"required"`
		Method    int    `form:"method"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.Method == 0 {
		in.Method = constmap.PayMethodWeixin
	}
	if !validators.IsIdCardNo(in.IdCard) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "身份证号格式不正确")
	}
	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "vipay", convertor.ToString(session.UserId)), constmap.TimeDur5s)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgConcurrency)
	}
	defer unlocker()

	var userVip models.UserVip
	var vipConf models.VipConf
	var payment = models.OrderPayment{
		UserId:      session.UserId,
		PayMethod:   in.Method,
		PaymentType: constmap.PaymentTypeVip,
	}
	var payExtra beans_asm.PaymentExtra
	db.Where(models.UserVip{UserId: session.UserId, State: constmap.Enable}).Take(&userVip)
	db.Where(models.VipConf{State: constmap.Enable}).Last(&vipConf)
	if vipConf.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "会员权益配置错误")
	}
	if user_biz.IsUserVipValid(&userVip) {
		//续费
		if _, hasChange := user_biz.IsUserVipChanged(&userVip, &vipConf); hasChange {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "会员权益内容有变更，不可续费")
		}
		payExtra.VipId = userVip.ID
	}
	payExtra.RealName = in.RealName
	payExtra.FirstName = in.FirstName
	payExtra.LastName = in.LastName
	payExtra.IdCard = in.IdCard
	payExtra.VipConfId = vipConf.ID
	b, _ := json.Marshal(payExtra)
	payment.Extra = string(b)
	payment.NeedPayAmount = int64(vipConf.SalePrice)
	var resp *beans.PayStr
	err = db.Transaction(func(tx *gorm.DB) error {
		resp, err = orders.CreatePayment(db, &payment, &session.User, vipConf.Name)
		return err
	})
	return resp, nil
}
