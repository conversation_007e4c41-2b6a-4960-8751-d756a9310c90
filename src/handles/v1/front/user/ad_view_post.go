package user

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 看广告得积分
func AdViewPost(ctx *gin.Context) (any, error) {
	var in struct {
		Pos  string `form:"pos" binding:"required"` //广告位
		Sign string `form:"sign" binding:"required"`
		Data string `form:"data" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	// 本地生成签名对比
	if !utils.VerifyHMAC(constmap.AdIntegralPassword, in.Data, in.Sign) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	ckey := fmt.Sprintf(constmap.RKAdView, in.Data)

	if my_cache.Exists(ckey) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请求已失效")
	}

	my_cache.Remove(ckey)

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	val, err := sys_config_biz.GetConfig(db, constmap.SysConfigAdConfig)
	if err != nil {
		my_logger.Errorf("GetConfig SysConfigAdConfig", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	cfg, _ := val.(*beans.SysAdConfig)
	var amount int64
	switch in.Pos {
	case "integral_pop":
		amount = cfg.IntegralPop.Amount
	}
	if amount < 1 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		trans, err := account_biz.CreateAccountTransaction(tx, session.UserId, constmap.CurrencyIntegral, int(amount),
			constmap.AccountLogIncrAmount, constmap.AccountLogSubAdView, "")
		if err != nil {
			return err
		}
		return account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil)
	})
	if err != nil {
		return nil, err
	}
	var out struct {
		Amount int64 `json:"amount"`
	}
	out.Amount = amount
	return out, nil
}
