package user

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Center(ctx *gin.Context) (any, error) {
	session, _ := business.GetFrontLoginUser(ctx)

	var data struct {
		Avatar         string                `json:"avatar"`
		ID             uint                  `json:"id"`
		Nickname       string                `json:"nickname"`
		InviteCode     string                `json:"invite_code"`
		BindInviteCode string                `json:"bind_invite_code"`
		CanBindInvite  bool                  `json:"can_bind_invite"`
		WifiCardRelId  uint                  `json:"wifi_card_rel_id"`
		VipInfo        beans.UserVipInfo     `json:"vip_info"`
		VipConf        beans.VipConf         `json:"vip_conf"`
		PackageInfo    beans.UserPackageInfo `json:"package_info"`
	}

	db := utils.GetDB(ctx)

	var user models.User
	db.Take(&user, session.UserId)

	data.VipInfo.UserType = constmap.UserTypeNormal
	//data.Avatar = utils.AvatarUrl(session.User.Avatar)
	data.Avatar = utils.StaticUrl(session.User.Avatar)
	data.ID = session.UserId
	data.Nickname = session.User.Nickname
	data.InviteCode = user.InviteCode
	data.BindInviteCode = user.BindInviteCode
	data.CanBindInvite = user.BindInviteCode == ""

	var rel models.FlowCardRelCode
	db.Where("user_id=?", session.UserId).Order("id desc").Limit(1).Take(&rel)
	data.WifiCardRelId = rel.ID

	var userVip models.UserVip
	var vipConf models.VipConf
	db.Where(models.UserVip{UserId: session.UserId}).Last(&userVip)
	if userVip.ID > 0 {
		var extra beans.UserVipExtra
		_ = json.Unmarshal([]byte(userVip.Extra), &extra)
		db.Take(&vipConf, extra.VipConfId)

		data.VipInfo = beans.UserVipInfo{
			UserType: constmap.UserTypeVip,
			State:    utils.If(user_biz.IsUserVipValid(&userVip), constmap.Enable, constmap.Disable),
			VipName:  vipConf.Name,
			VipIcon:  vipConf.Icon,
			VipStart: userVip.Start.Format(constmap.DateFmtLong),
			VipEnd:   userVip.End.Format(constmap.DateFmtLong),
			RealName: userVip.RealName,
		}
	} else {
		db.Where(models.VipConf{State: constmap.Enable}).Last(&vipConf)
	}
	data.VipConf = beans.VipConf{
		Id:          vipConf.ID,
		Name:        vipConf.Name,
		Icon:        vipConf.Icon,
		Desc:        vipConf.Desc,
		SalePrice:   vipConf.SalePrice,
		MarketPrice: vipConf.MarketPrice,
	}

	var upkgSummary = new(models.UserPackageSummary)
	db.Where(models.UserPackageSummary{UserId: user.ID}).Take(&upkgSummary)
	data.PackageInfo = beans.UserPackageInfo{
		PackageId:   upkgSummary.PackageId,
		PackageName: upkgSummary.PackageName,
		ExpireAt:    upkgSummary.ExpireAt.Unix(),
	}
	if acct, err := account_biz.GetAccount(db, user.ID, constmap.CurrencyIntegral); err == nil {
		data.PackageInfo.Amount = acct.Amount
	}

	return data, nil
}
