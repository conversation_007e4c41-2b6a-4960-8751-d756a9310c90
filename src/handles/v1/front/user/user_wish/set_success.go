package user_wish

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 标记心愿单已达成
func SetSuccess(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 查询心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 2. 验证权限：只有发起人可以标记达成
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以标记心愿单达成")
	}

	// 3. 验证心愿单状态：只有进行中的心愿单可以标记达成
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有进行中的心愿单可以标记达成")
	}

	// 4. 更新心愿单状态为已达成
	if err := db.Model(&wish).Update("state", constmap.WishStateSuccess).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "标记达成失败，请稍后重试")
	}

	// 5. 同步到ES
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": fmt.Sprintf("%d", in.WishId),
	})

	// 6. 组装返回数据
	var out struct {
		WishId uint   `json:"wish_id"`
		Status string `json:"status"`
	}
	out.WishId = in.WishId
	out.Status = "success"

	return out, nil
}
