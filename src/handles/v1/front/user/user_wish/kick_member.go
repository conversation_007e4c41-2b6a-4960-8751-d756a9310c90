package user_wish

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"unicode/utf8"
)

// 踢出成员
func KickMember(ctx *gin.Context) (any, error) {
	var in struct {
		MemberId   uint   `form:"member_id" binding:"required"`
		KickReason string `form:"kick_reason"` // 踢出原因，可选
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 验证踢出原因长度
	if utf8.RuneCountInString(in.KickReason) > 200 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "踢出原因不能超过200个字")
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 防并发处理
	mutexKey := fmt.Sprintf(constmap.RKMutex, "kickMember", fmt.Sprintf("%d:%d", session.UserId, in.MemberId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, err
	}
	defer unlocker()

	// 2. 查询成员记录
	var member models.WishMember
	if err := db.Take(&member, in.MemberId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "成员记录不存在")
	}

	// 3. 查询心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, member.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 4. 验证权限：只有发起人可以踢出成员
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以踢出成员")
	}

	// 5. 验证不能踢出自己
	if member.UserId == session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "不能踢出自己")
	}

	// 6. 验证成员状态：只有已通过的成员可以被踢出
	if member.State != constmap.WishMemberStateApproved {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有已通过的成员可以被踢出")
	}

	// 7. 验证心愿单状态：只有进行中或成功的心愿单可以踢出成员
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前心愿单状态不允许踢出成员")
	}

	// 8. 使用事务处理踢出和消息发送
	err = db.Transaction(func(tx *gorm.DB) error {
		// 更新成员状态为踢出
		updateData := models.WishMember{
			State:  constmap.WishMemberStateKickOff,
			Remark: in.KickReason, // 保存踢出原因
		}

		if err := tx.Model(&member).Updates(updateData).Error; err != nil {
			return err
		}

		// 发送踢出通知消息
		if err := sendKickMemberMessageInTx(tx, &wish, session.User.Nickname, member.UserId, in.KickReason); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "踢出失败，请稍后重试")
	}

	// 9. 清除相关缓存（事务成功后清理）
	message_biz.ClearUserMessageCacheByTypes(member.UserId, []constmap.MessageType{constmap.MessageTypeTeam})

	// 10. 组装返回数据
	var out struct {
		WishId     uint   `json:"wish_id"`
		MemberId   uint   `json:"member_id"`
		UserId     uint   `json:"user_id"`
		Status     string `json:"status"`
		KickReason string `json:"kick_reason,omitempty"`
	}
	out.WishId = wish.ID
	out.MemberId = member.ID
	out.UserId = member.UserId
	out.Status = "kicked"
	out.KickReason = in.KickReason

	return out, nil
}

// 在事务中发送踢出成员消息
func sendKickMemberMessageInTx(tx *gorm.DB, wish *models.Wish, fromUserNickname string, toUserId uint, kickReason string) error {
	// 构建消息内容
	title := "您已被踢出心愿单"
	var content string
	if kickReason != "" {
		content = fmt.Sprintf("%s 将您踢出了心愿单「%s」。原因：%s", fromUserNickname, wish.Title, kickReason)
	} else {
		content = fmt.Sprintf("%s 将您踢出了心愿单「%s」", fromUserNickname, wish.Title)
	}

	// 创建消息模板
	messageTpl := &models.MessageTpl{
		Type:         constmap.MessageTypeTeam,
		SubType:      constmap.MessageSubTypeText,
		Title:        title,
		Content:      content,
		WishId:       wish.ID,
		WishMemberId: 0, // 踢出消息不关联具体的成员记录
	}

	if err := tx.Create(messageTpl).Error; err != nil {
		return err
	}

	// 创建用户消息记录（未读状态）
	message := &models.Message{
		MessageTplId: messageTpl.ID,
		UserId:       toUserId,
		IsRead:       constmap.Disable, // 未读
	}

	if err := tx.Create(message).Error; err != nil {
		return err
	}

	return nil
}
