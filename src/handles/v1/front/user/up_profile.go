package user

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func UpProfile(ctx *gin.Context) (any, error) {
	var in struct {
		Nickname string `form:"nickname"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	session, _ := business.GetFrontLoginUser(ctx)

	db := utils.GetDB(ctx)

	update := models.User{}

	if in.Nickname != "" {
		update.Nickname = in.Nickname
	}
	if err := db.Model(&session.User).Omit(clause.Associations).Updates(update).Error; err != nil {
		return nil, err
	}

	return nil, nil
}
