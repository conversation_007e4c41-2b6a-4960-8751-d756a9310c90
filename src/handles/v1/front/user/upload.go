package user

import (
	"github.com/gin-gonic/gin"
	"os"
	"path/filepath"
	"roadtrip-api/src/config"
	"roadtrip-api/src/utils"
	"time"
)

func Upload(ctx *gin.Context) (interface{}, error) {
	file, err := ctx.FormFile("file")
	if err != nil {
		return nil, err
	}
	dir := filepath.Join("resources", "avatar", time.Now().Format("2006/01"))
	absDir := filepath.Join(config.Config.App.UploadDir, dir)
	ext := filepath.Ext(file.Filename)

	if _, err := os.Stat(absDir); err != nil {
		if err = os.Mkdir<PERSON>ll(absDir, os.ModePerm); err != nil {
			return nil, err
		}
	}
	filename := utils.UUID() + ext
	path := filepath.Join(dir, filename)
	absPath := filepath.Join(absDir, filename)
	if err = ctx.SaveUploadedFile(file, absPath); err != nil {
		return nil, err
	}

	path = utils.StaticUrl(path)

	return gin.H{"url": path}, nil
}
