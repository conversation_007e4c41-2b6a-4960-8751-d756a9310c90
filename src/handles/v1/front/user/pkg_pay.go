package user

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 购买套餐
func PkgPay(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		Method    int  `form:"method"`
		PackageId uint `form:"package_id" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.Method == 0 {
		in.Method = constmap.PayMethodMpWeixin
	}
	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)

	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "packagePay", convertor.ToString(session.UserId)), constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgConcurrency)
	}
	defer unlocker()

	var pkg = new(models.Package)
	db.Where(models.Package{State: constmap.Enable}).Take(&pkg, in.PackageId)

	if pkg.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "套餐不存在")
	}

	var userPkgSummary = new(models.UserPackageSummary)
	db.Where(models.UserPackageSummary{
		UserId: session.UserId,
	}).Take(&userPkgSummary)

	var upkgs []*models.UserPackage
	db.Where(models.UserPackage{
		UserId: session.UserId,
		State:  constmap.UserPackageStateUsing,
	}).Find(&upkgs)

	if !user_biz.CanChangePackage(userPkgSummary, upkgs, in.PackageId) {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "当前套餐使用中不可购买其他套餐")
	}

	var cnt int64
	db.Model(&models.UserPackage{}).Where(models.UserPackage{
		UserId: session.UserId,
	}).Where("state <> ?", constmap.UserPackageStateAbnormal).Count(&cnt)

	var payExtra beans_asm.PaymentExtra

	payExtra.PackageIsNew = constmap.Disable
	payExtra.PackageId = in.PackageId
	payExtra.Package = pkg

	var payAmount int64
	var upkgCnt = user_biz.GetUserPackageCount(db, session.UserId)
	if user_biz.CanUsePackageNew(upkgCnt, pkg) {
		// 首充
		if pkg.NewRealPrice < 1 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "支付金额校验失败")
		}
		payExtra.PackageIsNew = constmap.Enable
		payAmount = pkg.NewRealPrice
	} else {
		// 非首充
		if pkg.RealPrice < 1 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "支付金额校验失败")
		}
		payAmount = pkg.RealPrice
	}

	var payment = models.OrderPayment{
		UserId:        session.UserId,
		PayMethod:     in.Method,
		PaymentType:   constmap.PaymentTypePackage,
		NeedPayAmount: payAmount,
	}

	b, _ := json.Marshal(payExtra)
	payment.Extra = string(b)

	var resp *beans.PayStr
	err = db.Transaction(func(tx *gorm.DB) error {
		resp, err = orders.CreatePayment(db, &payment, &session.User, pkg.Name)
		return err
	})
	if err != nil {
		return nil, err
	}
	return resp, nil
}
