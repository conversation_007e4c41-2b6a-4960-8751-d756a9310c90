package user

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func PlanRemove(ctx *gin.Context) (any, error) {
	var in struct {
		PlanId uint `form:"plan_id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var plan models.Plan
	if err := db.Where("id = ? and user_id = ?", in.PlanId, session.UserId).Take(&plan).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	}

	if db.Model(&plan).Updates(&models.Plan{
		State: constmap.Disable,
	}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作失败")
	}

	var out struct {
		Id    uint `json:"id"`
		State int  `json:"state"`
	}
	out.Id = plan.ID
	out.State = plan.State
	return out, nil
}
