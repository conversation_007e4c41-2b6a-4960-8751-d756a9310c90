package user

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func BindInviteCode(ctx *gin.Context) (any, error) {
	var in struct {
		InviteCode string `form:"invite_code" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	user := session.User

	if user.BindInviteCode != "" {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您已绑定了邀请码，不能重复绑定")
	}

	var bindUser models.User
	db.Where(models.User{InviteCode: in.InviteCode}).Take(&bindUser)

	if bindUser.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "邀请码无效")
	}

	if bindUser.ID == user.ID {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "不能绑定自己的邀请码")
	}

	if bindUser.BindInviteCode != "" && bindUser.BindInviteCode == user.InviteCode {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "邀请码不能互相绑定")
	}

	var amount int64
	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigBindInviteCodeAmount); err != nil {
		return nil, utils.NewError(err)
	} else {
		amount = val.(int64)
	}

	if err := db.Transaction(func(tx *gorm.DB) error {

		if tx.Model(&user).Updates(models.User{
			BindInviteCode: bindUser.InviteCode,
			BindUserId:     bindUser.ID,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "保存邀请码失败")
		}

		trans, err := account_biz.CreateAccountTransaction(tx, user.ID, constmap.CurrencyIntegral, int(amount),
			constmap.AccountLogIncrAmount, constmap.AccountLogSubBindInviteCode, "")
		if err != nil {
			return err
		}
		if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Amount int64 `json:"amount"`
	}
	out.Amount = amount

	return out, nil
}
