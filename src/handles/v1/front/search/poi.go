package search

import (
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/utils/typeset"
	"sync"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// 定义响应结构
type searchItem struct {
	Id       uint   `json:"id"`
	Name     string `json:"name"`
	Type     int    `json:"type"`      // POI类型：1=景点，2=酒店，3=城市
	TypeText string `json:"type_text"` // 类型文本：景点、酒店、城市
	Cover    string `json:"cover"`     // 封面
	Address  string `json:"address"`   // 地址信息
	Score    string `json:"score"`     //酒店评分
	Level    int    `json:"level"`     //景区评级
	ZoneId   uint   `json:"zone_id"`
	ZoneName string `json:"zone_name"`
	Province string `json:"province"` // 省份
	Location string `json:"location"`
}

// Poi 聚合搜索接口 - 搜索城市、景点、酒店
func Poi(ctx *gin.Context) (any, error) {
	var in struct {
		Q string `form:"q" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if strutil.IsBlank(in.Q) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "搜索关键词不能为空")
	}

	db := utils.GetDB(ctx)

	// 并行查询城市、景点、酒店
	tasks := parallel_task.NewPool(3)
	defer tasks.Release()

	var mu sync.Mutex
	var zoneItems []searchItem
	var scenicItems []searchItem
	var hotelItems []searchItem

	// 查询城市
	tasks.AddTask(func() error {
		items := searchZones(db, in.Q)
		mu.Lock()
		zoneItems = items
		mu.Unlock()
		return nil
	})

	// 查询景点
	tasks.AddTask(func() error {
		items := searchScenics(db, in.Q)
		mu.Lock()
		scenicItems = items
		mu.Unlock()
		return nil
	})

	// 查询酒店
	//tasks.AddTask(func() error {
	//	items := searchHotels(db, in.Q)
	//	mu.Lock()
	//	hotelItems = items
	//	mu.Unlock()
	//	return nil
	//})

	_ = tasks.Wait()

	// 拼装结果
	result := struct {
		List []searchItem `json:"list"`
	}{}

	result.List = append(result.List, zoneItems...)
	result.List = append(result.List, scenicItems...)
	result.List = append(result.List, hotelItems...)

	// 批量填充zone信息
	fillZoneInfo(db, result.List)

	return result, nil
}

// 搜索城市数据并返回标准化结果
func searchZones(db *gorm.DB, keyword string) []searchItem {

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"type": constmap.PoiTypeZone,
						},
					},
					{
						"term": map[string]any{
							"level": constmap.ZoneLevelCity,
						},
					},
					{
						"match": map[string]any{
							"keywords": keyword,
						},
					},
				},
			},
		},
		"size": 1,
		"sort": []map[string]any{
			{"level": map[string]any{"order": "asc"}}, // 城市级别优先
			{"_score": map[string]any{"order": "desc"}},
		},
	}

	searchResp, err := es.Search[es2.Zone](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("城市ES搜索失败", zap.String("keyword", keyword), zap.Error(err))
		return []searchItem{}
	}

	zoneIds := slice.Map(searchResp.Hits.Hits, func(index int, item es.Hit[es2.Zone]) uint {
		return item.Source.ObjId
	})

	if len(zoneIds) == 0 {
		return []searchItem{}
	}

	var zones []models.Zone
	db.Where("id IN ?", zoneIds).Find(&zones)

	// 创建ID到Zone的映射
	zoneMap := slice.KeyBy(zones, func(zone models.Zone) uint { return zone.ID })

	// 按ES返回顺序构建结果
	return slice.FilterMap(zoneIds, func(index int, zoneId uint) (searchItem, bool) {
		zone, exists := zoneMap[zoneId]
		if !exists {
			return searchItem{}, false
		}
		return searchItem{
			Id:       zone.ID,
			Name:     zone.Name,
			Type:     constmap.PoiTypeZone,
			Cover:    utils.StaticUrl(zone.Pic),
			Address:  zone.Name,
			ZoneId:   zone.ID,   // 城市的ZoneId就是自己的ID
			ZoneName: zone.Name, // 城市的ZoneName就是自己的名称
			Location: utils.JoinPoi(zone.Lng, zone.Lat),
		}, true
	})
}

// 搜索景点数据并返回标准化结果
func searchScenics(db *gorm.DB, keyword string) []searchItem {

	mustConds := []map[string]any{
		{
			"term": map[string]any{
				"type": constmap.PoiTypeScenic,
			},
		},
		{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		{
			"term": map[string]any{
				"llm": constmap.Enable,
			},
		},
		{
			"match": map[string]any{
				"keywords": keyword,
			},
		},
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": mustConds,
			},
		},
		"size": 10,
		"sort": []map[string]any{
			{"level": map[string]any{"order": "desc"}}, // 景点级别优先
			{"_score": map[string]any{"order": "desc"}},
		},
	}

	searchResp, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("景点ES搜索失败", zap.String("keyword", keyword), zap.Error(err))
		return []searchItem{}
	}

	scenicIds := slice.Map(searchResp.Hits.Hits, func(index int, item es.Hit[es2.Scenic]) uint {
		return item.Source.ObjId
	})

	if len(scenicIds) == 0 {
		return []searchItem{}
	}

	var scenics []models.Scenic
	db.Where("id IN ?", scenicIds).Find(&scenics)

	// 创建ID到Scenic的映射
	scenicMap := slice.KeyBy(scenics, func(scenic models.Scenic) uint { return scenic.ID })

	// 按ES返回顺序构建结果
	return slice.FilterMap(scenicIds, func(index int, scenicId uint) (searchItem, bool) {
		scenic, exists := scenicMap[scenicId]
		if !exists {
			return searchItem{}, false
		}
		return searchItem{
			Id:       scenic.ID,
			Name:     scenic.Name,
			Type:     constmap.PoiTypeScenic,
			Cover:    utils.StaticUrl(scenic.Pic),
			Address:  scenic.Address,
			Level:    scenic.Level,
			ZoneId:   scenic.ZoneId,
			Location: utils.JoinPoi(scenic.Lng, scenic.Lat),
		}, true
	})
}

// 搜索酒店数据并返回标准化结果
func searchHotels(db *gorm.DB, keyword string) []searchItem {

	mustConds := []map[string]any{
		{
			"term": map[string]any{
				"type": constmap.PoiTypeHotel,
			},
		},
		{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		{
			"match": map[string]any{
				"keywords": keyword,
			},
		},
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": mustConds,
			},
		},
		"size": 10,
		"sort": []map[string]any{
			{"_score": map[string]any{"order": "desc"}}, // 相关性优先
			{"score": map[string]any{"order": "desc"}},  // 评分优先
			{"star": map[string]any{"order": "desc"}},   // 星级优先
		},
	}

	searchResp, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("酒店ES搜索失败", zap.String("keyword", keyword), zap.Error(err))
		return []searchItem{}
	}

	hotelIds := slice.Map(searchResp.Hits.Hits, func(index int, item es.Hit[es2.Hotel]) uint {
		return item.Source.ObjId
	})

	if len(hotelIds) == 0 {
		return []searchItem{}
	}

	var hotels []models.Hotel
	db.Where("id IN ?", hotelIds).Find(&hotels)

	// 创建ID到Hotel的映射
	hotelMap := slice.KeyBy(hotels, func(hotel models.Hotel) uint { return hotel.ID })

	// 按ES返回顺序构建结果
	return slice.FilterMap(hotelIds, func(index int, hotelId uint) (searchItem, bool) {
		hotel, exists := hotelMap[hotelId]
		if !exists {
			return searchItem{}, false
		}
		return searchItem{
			Id:       hotel.ID,
			Name:     hotel.Name,
			Type:     constmap.PoiTypeHotel,
			Cover:    utils.StaticUrl(hotel.Pic),
			Address:  hotel.Address,
			Score:    utils.FormatNumber(hotel.Score, 1),
			ZoneId:   hotel.ZoneId,
			Location: utils.JoinPoi(hotel.Lng, hotel.Lat),
		}, true
	})
}

// fillZoneInfo 批量填充zone信息
func fillZoneInfo(db *gorm.DB, items []searchItem) {
	if len(items) == 0 {
		return
	}

	// 收集并去重所有需要查询的ZoneId
	zoneIdSet := typeset.NewTypeSet[uint](false)
	for _, item := range items {
		if item.ZoneId > 0 {
			zoneIdSet.Add(item.ZoneId)
		}
	}

	if zoneIdSet.Len() == 0 {
		return
	}

	// 批量查询zone信息，查询到省级
	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIdSet.Values(), constmap.ZoneLevelProvince)

	// 填充zone信息
	for i := range items {
		if items[i].ZoneId > 0 {
			if zone, exists := zoneMap[items[i].ZoneId]; exists {
				items[i].ZoneName = zone.Name
				items[i].Province = getProvinceFromZoneWithParent(zone)
			}
		}
	}
}

// getProvinceFromZoneWithParent 从zone及其父级关系中获取省份信息
func getProvinceFromZoneWithParent(zone *models.Zone) string {
	for current := zone; current != nil; current = current.Parent {
		if current.Level == constmap.ZoneLevelProvince {
			return current.Name
		}
	}
	return ""
}
