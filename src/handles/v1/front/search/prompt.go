package search

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
)

func Prompt(ctx *gin.Context) (any, error) {
	var in struct {
		Prompt string `form:"text" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var data struct {
		//Content string `json:"content"`
		Id string `json:"id"`
	}

	var aiI = ai.Doubao{}

	resp, err := aiI.ChatCompletionMessage(in.Prompt)
	if err != nil {
		return nil, err
	}

	cacheKey := fmt.Sprintf(constmap.RKAiResp, resp.RequestId)

	if err = my_cache.SetDefault(cacheKey, resp); err != nil {
		return nil, err
	}

	//output := blackfriday.Run([]byte(resp.Content))

	data.Id = resp.RequestId
	//data.Content = string(output)

	return data, nil
}
