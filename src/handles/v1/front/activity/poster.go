package activity

import (
	"bytes"
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"golang.org/x/image/draw"
	"golang.org/x/image/font/opentype"
	"image"
	"image/color"
	"image/png"
	"net/http"
	"roadtrip-api/src/assets"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Poster(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid      string `form:"uuid" binding:"required"`
		UserId    uint   `form:"user_id"`
		ReturnUrl int    `form:"return_url"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var activity models.Activity
	var user models.User
	db.Where(models.Activity{Uuid: in.Uuid}).Take(&activity)
	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}
	if in.UserId > 0 {
		db.Take(&user, in.UserId)
	}

	file := constmap.QiniuPrefix + fmt.Sprintf("/tmp/%s/act_%d_%d.png", utils.If(config.IsProduction(), "prod", "dev"), activity.ID, user.ID)
	if config.IsProduction() && qiniu_biz.IsExists(file) {
		if in.ReturnUrl == constmap.Enable {
			return gin.H{"url": utils.StaticUrl(file)}, nil
		}

		ctx.Set(constmap.ContextHttpRaw, true)
		ctx.Redirect(301, utils.StaticUrl(file))
		return nil, nil
	}

	var avatar image.Image
	var bg image.Image
	var err error

	loadDefaultAvatar := func() {
		avatar, _, _ = image.Decode(bytes.NewReader(assets.UserDefaultAvatar))
	}

	if strutil.IsBlank(user.Avatar) {
		loadDefaultAvatar()
	} else if rsp, err := http.Get(utils.StaticUrl(user.Avatar)); err != nil {
		loadDefaultAvatar()
	} else if avatar, _, err = image.Decode(rsp.Body); err != nil {
		loadDefaultAvatar()
	}

	if bg, _, err = image.Decode(bytes.NewReader(assets.ActLaxinBg)); err != nil {
		return nil, err
	}
	const cwidth = 750
	const cheight = 750
	var x, y int
	var clr *color.RGBA
	avatarBound := avatar.Bounds()
	fontLoader, err := utils.NewFontLoader()
	if err != nil {
		return nil, err
	}
	defer fontLoader.Close()

	cv := image.NewRGBA(image.Rect(0, 0, cwidth, cheight))

	// 背景色
	utils.DrawRect(cv, cv.Rect, color.Black)

	// 缩放背景图到画布大小
	bgDst := utils.ImageResize(bg, cwidth, 0)
	draw.Draw(cv, bgDst.Bounds(), bgDst, image.Point{}, draw.Over)

	//画头像
	avatarWidth, avatarHeight := 120, 120
	avatarDst := utils.ImageResize(avatar, //以短边缩放
		utils.If(avatarBound.Dx() > avatarBound.Dy(), 0, avatarWidth),
		utils.If(avatarBound.Dx() > avatarBound.Dy(), avatarHeight, 0))
	sp := image.Point{ //平移到指定位置
		X: 325,
		Y: 296,
	}
	avatarBound = image.Rect(0, 0, avatarWidth, avatarHeight).Add(sp)
	mask := utils.CreateRoundedRectMask(float64(avatarWidth), float64(avatarHeight), 60)
	draw.DrawMask(cv, avatarBound, avatarDst, utils.ImageCenterInRect(avatarDst, avatarWidth, avatarHeight), mask, image.Point{}, draw.Over)

	face28 := "28"
	if err := fontLoader.LoadFace(face28, &opentype.FaceOptions{Size: 28}); err != nil {
		return nil, err
	}

	text := utils.If(strutil.IsBlank(user.Nickname), "义伴AI", user.Nickname)
	_, textHeight := utils.MeasureString(text, fontLoader, face28)
	x = avatarBound.Max.X + 8
	y = avatarBound.Min.Y + (avatarBound.Dy()-textHeight.Round())/2
	clr, _ = utils.ColorHex("#022BAB")
	utils.DrawText(cv, x, y, text, fontLoader, face28, clr)

	text = "邀请你参与活动，领好礼！"
	x = 341
	y = avatarBound.Max.Y + 33
	clr, _ = utils.ColorHex("#333333")
	utils.DrawText(cv, x, y, text, fontLoader, face28, clr)

	// 输出到临时文件缓存并响应
	buff := &bytes.Buffer{}
	if err := png.Encode(buff, cv); err != nil {
		return nil, utils.NewError(err)
	}

	if err := qiniu_biz.UploadReader(buff, file); err != nil {
		return nil, err
	}

	if in.ReturnUrl == constmap.Enable {
		return gin.H{"url": utils.StaticUrl(file)}, nil
	}
	ctx.Set(constmap.ContextHttpRaw, true)
	ctx.Redirect(301, utils.StaticUrl(file))

	return nil, nil
}
