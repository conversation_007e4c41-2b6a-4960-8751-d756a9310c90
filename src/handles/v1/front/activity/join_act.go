package activity

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 参与活动
func JoinAct(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid string `form:"uuid" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	unlocker, err := my_cache.Mutex(
		fmt.Sprintf(constmap.RKMutex, "joinact", convertor.ToString(session.UserId)),
		constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgConcurrency)
	}
	defer unlocker()

	var activity models.Activity
	db.Where(models.Activity{
		Uuid: in.Uuid,
	}).Take(&activity)

	now := time.Now()
	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	} else if activity.State != constmap.Enable {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动已下架")
	} else if activity.Start.After(now) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动未开始")
	} else if activity.End.Before(now) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动已结束")
	}

	var userActivity = models.UserActivity{
		UserId:     session.UserId,
		ActivityId: activity.ID,
	}

	db.Where(userActivity).Take(&userActivity)
	if userActivity.ID > 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您已报名了此活动")
	}

	var out struct {
		UserActivityId uint                `json:"user_activity_id"`
		Name           string              `json:"name"`
		RewardType     constmap.TaskReward `json:"reward_type"`
		RewardAmount   int                 `json:"reward_amount"`
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&userActivity).Error; err != nil {
			return err
		}
		if o, err := task_asm.DoActivityTask(tx, task_asm.DoActivityTaskReq{
			UserId:     session.UserId,
			ActivityId: activity.ID,
			Cond:       constmap.TaskCondJoinAct,
		}); err != nil {
			return err
		} else {
			out.RewardType = o.RewardType
			out.RewardAmount = o.RewardAmount
		}
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	out.Name = activity.Title
	out.UserActivityId = userActivity.ID

	return out, nil
}
