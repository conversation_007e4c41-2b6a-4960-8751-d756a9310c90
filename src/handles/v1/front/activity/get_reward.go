package activity

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func GetReward(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid   string `form:"uuid" binding:"required"`
		TaskId uint   `form:"task_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	unlocker, err := my_cache.Mutex(
		fmt.Sprintf(constmap.RKMutex, "actGetReward", convertor.ToString(session.UserId)),
		constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgConcurrency)
	}
	defer unlocker()

	var activity models.Activity
	db.Where(models.Activity{Uuid: in.Uuid}).Take(&activity)
	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}
	taskMap := task_biz.LoadRelateTasks(db, []uint{activity.ID}, constmap.TaskRelateActivity)
	if len(taskMap) == 0 || in.TaskId != taskMap[activity.ID].ID {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "活动未关联有效任务")
	}
	var cnt int64
	if db.Model(&models.UserActivity{}).Where(models.UserActivity{
		UserId:     session.UserId,
		ActivityId: activity.ID,
	}).Count(&cnt); cnt == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您还未报名活动，请先报名")
	}

	var task = taskMap[activity.ID]

	var utask models.UserTask
	db.Where(models.UserTask{
		UserId: session.UserId,
		TaskId: in.TaskId,
	}).Take(&utask)
	if utask.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "用户关联任务异常")
	}

	if utask.State == constmap.UserTaskDone || utask.RewardTimes >= utask.MaxTimes {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您已经领取过奖励了")
	}
	if task.MaxTimes <= utask.RewardTimes {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "没有待领取的奖励")
	}

	if task.RewardType != constmap.TaskRewardAirWifi {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "奖励无需手动领取")
	}

	//var flowCard models.FlowCard
	//db.Preload("Airlines").Preload("Airlines.Air").Where(models.FlowCard{
	//	Code:  task.RewardId,
	//	State: constmap.Enable,
	//}).Take(&flowCard)
	//if flowCard.ID == 0 {
	//	return nil, utils.NewErrorStr(constmap.ErrorParam, "奖励不存在或已下架")
	//}

	type airline struct {
		Code        string `json:"code"`
		Name        string `json:"name"`
		ProductName string `json:"product_name"`
		Logo        string `json:"logo"`
		LogoActive  string `json:"logo_active"`
		Desc        string `json:"desc"`
		Scope       string `json:"scope"`
	}

	var out struct {
		RewardType       constmap.TaskReward `json:"reward_type"`
		RewardCode       string              `json:"reward_code"`
		RedPackPic       string              `json:"red_pack_pic"`
		FlowCardAirlines []airline           `json:"flow_card_airlines"`
	}

	out.RedPackPic = "https://rp.yjsoft.com.cn/yiban/static/activity/new/hongbao.png"
	out.RewardType = task.RewardType
	out.RewardCode = task.RewardId
	return out, nil
	//out.FlowCardAirlines = slice.Map(flowCard.Airlines, func(index int, item models.FlowCardAirline) airline {
	//	return airline{
	//		Code:        item.Code,
	//		Name:        item.Air.Name,
	//		ProductName: item.Air.ProductName,
	//		Logo:        utils.StaticUrl(item.Air.Logo),
	//		LogoActive:  utils.StaticUrl(item.Air.LogoActive),
	//		Desc:        item.Air.Desc,
	//		Scope:       item.Air.Scope,
	//	}
	//})

	//updateUtask := map[string]any{
	//	"reward_times": gorm.Expr("reward_times+1"),
	//	"version":      utils.IncrVersion(utask.Version),
	//}
	//err = db.Transaction(func(tx *gorm.DB) error {
	//	if flowCard.Type == constmap.FlowCardTypeDefault {
	//		updateUtask["reward_code"] = flowCard.Code
	//		out.RewardCode = flowCard.Code
	//	} else if flowCard.Type == constmap.FlowCardTypeUnique {
	//		var airRewardCode models.FlowCardRewardCode
	//		tx.Where(models.FlowCardRewardCode{
	//			FlowCardId: flowCard.ID,
	//			State:      constmap.Enable,
	//		}).Order("id asc").Take(&airRewardCode)
	//		if airRewardCode.ID == 0 {
	//			return utils.NewErrorStr(constmap.ErrorSystem, "可用库存不足")
	//		}
	//		updateUtask["reward_code"] = airRewardCode.Code
	//		out.RewardCode = airRewardCode.Code
	//	}
	//	if utask.RewardTimes+1 >= utask.MaxTimes {
	//		updateUtask["state"] = constmap.UserTaskDone
	//	}
	//	if tx.Model(&utask).Where("version=?", utask.Version).Updates(updateUtask).RowsAffected == 0 {
	//		return utils.NewErrorStr(constmap.ErrorSystem, "更新任务状态失败")
	//	}
	//	if err := tx.Create(&models.UserTaskLog{
	//		UserTaskId: utask.ID,
	//		UserId:     utask.UserId,
	//		TaskId:     utask.TaskId,
	//		Type:       constmap.UserTaskLogReward,
	//		ActivityId: activity.ID,
	//	}).Error; err != nil {
	//		return utils.NewError(err)
	//	}
	//	return nil
	//})
	//if err != nil {
	//	return nil, utils.NewError(err)
	//}
	//return out, nil
}
