package activity

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 任务记录
func TaskLogs(ctx *gin.Context) (any, error) {
	var in struct {
		TaskId uint `form:"task_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	type vitem struct {
		Name   string `json:"name"`
		Time   int64  `json:"time"`
		Amount int    `json:"amount"`
	}

	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}

	var list []models.UserTaskLog
	db.Model(&list).Where(models.UserTaskLog{
		UserId: session.UserId,
		TaskId: in.TaskId,
		Type:   constmap.UserTaskLogDo,
	}).Count(&out.Total).
		Order("id desc").Offset((page - 1) * pageSize).Limit(pageSize).
		Find(&list)

	out.List = slice.Map(list, func(index int, item models.UserTaskLog) vitem {
		var b beans.UserTaskLogExtra
		_ = json.Unmarshal([]byte(item.Extra), &b)
		return vitem{
			Name:   b.TaskName,
			Amount: b.RewardAmount,
			Time:   item.CreatedAt.Unix(),
		}
	})
	return out, nil
}
