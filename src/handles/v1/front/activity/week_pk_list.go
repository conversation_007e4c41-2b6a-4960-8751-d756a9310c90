package activity

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// PK排行榜
func WeekPkList(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid string `form:"uuid" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}
	db := utils.GetDB(ctx)

	var activity = new(models.Activity)

	db.Where(models.Activity{Uuid: in.Uuid}).Take(&activity)

	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}

	page, pageSize := utils.GetPage(ctx)
	var out beans.WeekPKRankList

	var rankList []*models.ActivityPlan

	query := db.Model(&rankList).Where(&models.ActivityPlan{
		ActivityId: activity.ID,
	})

	ulist := getUserList(db, activity, userId, 1)
	if len(ulist) > 0 {
		query.Where("id NOT IN ?", slice.Map(ulist, func(index int, item *models.ActivityPlan) uint {
			return item.ID
		}))
	}

	query.Count(&out.Total)
	query.Order("id ASC").
		Offset((page - 1) * pageSize).
		Limit(pageSize).
		Find(&rankList)

	if page == 1 {
		rankList = append(ulist, rankList...)
	}
	out.Total += int64(len(ulist))

	if len(rankList) == 0 {
		out.List = make([]beans.WeekPkRankListItem, 0)
		return out, nil
	}
	out.HasMore = out.Total > int64(page*pageSize)

	// 创建 ActivityPlan 映射表和用户信息映射表
	planIds := slice.Map(rankList, func(_ int, activityPlan *models.ActivityPlan) uint {
		return activityPlan.PlanId
	})

	userIds := slice.Map(rankList, func(_ int, activityPlan *models.ActivityPlan) uint {
		return activityPlan.UserId
	})
	userMap := user_biz.LoadUsers(db, userIds)
	planMap := plan_biz.LoadPlans(db, planIds)

	// 组装返回数据
	out.List = slice.Map(rankList, func(i int, rank *models.ActivityPlan) beans.WeekPkRankListItem {
		item := beans.WeekPkRankListItem{
			UserId:         rank.UserId,
			PlanId:         rank.PlanId,
			Poster:         utils.StaticUrl(rank.Poster),
			ActivityPlanId: rank.ID,
		}
		if user, userExists := userMap[rank.UserId]; userExists {
			item.Nickname = user.Nickname
			item.Avatar = utils.AvatarUrl(user.Avatar)
		}
		if plan, exists := planMap[rank.PlanId]; exists {
			item.Score = int64(plan.Likes)
		}

		return item
	})

	return out, nil
}

func getUserList(db *gorm.DB, act *models.Activity, userId uint, limit int) []*models.ActivityPlan {
	if userId < 1 {
		return nil
	}
	var rankList []*models.ActivityPlan

	db.Model(&rankList).Where(&models.ActivityPlan{
		ActivityId: act.ID,
	}).Order("id DESC").Limit(limit).Find(&rankList)
	return rankList
}
