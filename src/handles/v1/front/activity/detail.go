package activity

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

type inDetail struct {
	Uuid     string `form:"uuid" binding:"required"`
	ShareUid uint   `form:"share_uid"`
}

func Detail(ctx *gin.Context) (any, error) {
	var in inDetail
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var activity = new(models.Activity)

	db.Joins("Ext").Where(models.Activity{Uuid: in.Uuid}).Take(&activity)

	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}

	taskMap := task_biz.LoadRelateTasks(db, []uint{activity.ID}, constmap.TaskRelateActivity)
	var childrenTasks []models.Task
	if len(taskMap) > 0 {
		db.Preload("Ext").Where(models.Task{
			ParentTaskId: taskMap[activity.ID].ID,
			State:        constmap.Enable,
		}).Find(&childrenTasks)
	}

	if in.Uuid == constmap.ActWeekPk {
		return detailWeekPk(ctx, in, activity, taskMap[activity.ID], childrenTasks)
	}

	type Vutask struct {
		State           constmap.UserTaskState `json:"state"`             //用户任务状态
		CondTimes       int                    `json:"cond_times"`        //条件达成次数
		CondAccAmount   int                    `json:"cond_acc_amount"`   //条件累计数额
		RewardTimes     int                    `json:"reward_times"`      //奖励领取次数
		RewardAccAmount int                    `json:"reward_acc_amount"` //奖励累计领取数额
	}
	type vtask struct {
		Id           uint                `json:"id"`
		Name         string              `json:"name"`
		MaxTimes     int                 `json:"max_times"`
		CondType     constmap.TaskCond   `json:"cond_type"`
		CondAmount   int                 `json:"cond_amount"`
		RewardAmount int                 `json:"reward_amount"`
		RewardType   constmap.TaskReward `json:"reward_type"`
		ShortDesc    string              `json:"short_desc"`
		Tutorial     string              `json:"tutorial"` //任务教程
		Children     []vtask             `json:"children"`
		Utask        *Vutask             `json:"utask"`
	}

	var out struct {
		Title      string   `json:"title"`
		State      int      `json:"state"`
		Start      int64    `json:"start"`
		End        int64    `json:"end"`
		HasJoin    bool     `json:"has_join"`
		Rules      string   `json:"rules"` //积分规则
		TaskId     uint     `json:"task_id"`
		Task       vtask    `json:"task"`
		SubsTplIds []string `json:"subs_tpl_ids"` //订阅模板id
	}

	foo := func(task models.Task) vtask {
		return vtask{
			Id:           task.ID,
			Name:         task.Name,
			MaxTimes:     task.MaxTimes,
			CondType:     task.CondType,
			CondAmount:   task.CondAmount,
			RewardAmount: task.RewardAmount,
			RewardType:   task.RewardType,
			ShortDesc:    task.ShortDesc,
			Tutorial:     task.Ext.Tutorial,
			Children:     make([]vtask, 0),
		}
	}

	bar := func(utasks []*models.UserTask, task models.Task) *Vutask {
		if len(utasks) == 0 {
			return nil
		}
		var utask *models.UserTask
		switch task.IntervalType {
		default:
			break
		case constmap.TaskIntervalOnce:
			utask = utasks[0]
		case constmap.TaskIntervalDay:
			nowDay := time.Now().Format(constmap.DateFmtShort)
			for _, v := range utasks {
				if v.Date.Format(constmap.DateFmtShort) == nowDay {
					utask = v
					break
				}
			}
		case constmap.TaskIntervalWeek:
			nowWeek := datetime.BeginOfWeek(time.Now()).Format(constmap.DateFmtShort)
			for _, v := range utasks {
				if v.Date.Format(constmap.DateFmtShort) == nowWeek {
					utask = v
					break
				}
			}
		}
		if utask == nil {
			return nil
		}
		v := &Vutask{
			State:           utask.State,
			CondTimes:       utask.CondTimes,
			CondAccAmount:   utask.CondAccAmount,
			RewardTimes:     utask.RewardTimes,
			RewardAccAmount: utask.RewardAccAmount,
		}
		return v
	}

	out.Title = activity.Title
	out.Start = activity.Start.Unix()
	out.End = activity.End.Unix()
	out.TaskId = taskMap[activity.ID].ID
	out.State = activity.State
	out.Rules = activity.Ext.Rules
	out.SubsTplIds = []string{}
	if activity.MpTplId != "" {
		out.SubsTplIds = append(out.SubsTplIds, activity.MpTplId)
	}
	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}
	if userId > 0 {
		var cnt int64
		db.Model(&models.UserActivity{}).Where(models.UserActivity{
			ActivityId: activity.ID,
			UserId:     userId,
		}).Count(&cnt)
		out.HasJoin = cnt > 0
	}
	if len(taskMap) > 0 {
		out.Task = foo(taskMap[activity.ID])
		inTaskMap := make(map[uint]models.Task)
		out.Task.Children = slice.Map(childrenTasks, func(index int, item models.Task) vtask {
			inTaskMap[item.ID] = item
			return foo(item)
		})
		if userId > 0 && out.HasJoin {
			var taskIds = make([]uint, 0)
			taskIds = append(taskIds, taskMap[activity.ID].ID)
			slice.ForEach(childrenTasks, func(index int, item models.Task) {
				taskIds = append(taskIds, item.ID)
			})

			var utasks []*models.UserTask
			db.Where("task_id in ? and user_id=?", taskIds, userId).Find(&utasks)
			utaskMap := slice.ReduceBy(utasks, make(map[uint][]*models.UserTask), func(_ int, item *models.UserTask, agg map[uint][]*models.UserTask) map[uint][]*models.UserTask {
				agg[item.TaskId] = append(agg[item.TaskId], item)
				return agg
			})

			out.Task.Utask = bar(utaskMap[taskMap[activity.ID].ID], taskMap[activity.ID])
			out.Task.Children = slice.Map(out.Task.Children, func(index int, item vtask) vtask {
				item.Utask = bar(utaskMap[item.Id], inTaskMap[item.Id])
				return item
			})
		}
	}

	// 分享人得积分
	if userId > 0 && in.ShareUid > 0 && userId != in.ShareUid {
		_ = my_queue.Light(constmap.EventTaskAction, gin.H{
			"task_event_req": convertor.ToString(task_asm.DoActivityTaskReq{
				Cond:      constmap.TaskCondShareAct,
				UserId:    in.ShareUid,
				HelperUid: userId,
			}),
		})
	}

	return out, nil
}

func detailWeekPk(ctx *gin.Context, in inDetail, activity *models.Activity, ptask models.Task, childrenTasks []models.Task) (any, error) {
	db := utils.GetDB(ctx)
	var lastWeek time.Time
	now := time.Now()
	if now.After(activity.End) {
		lastWeek = utils.BeginOfWeek(activity.End)
	} else {
		lastWeek = utils.BeginOfWeek(now.AddDate(0, 0, -7))
	}

	type reward struct {
		RewardType constmap.TaskReward    `json:"reward_type"`
		State      constmap.UserTaskState `json:"state"`
		RewardId   string                 `json:"reward_id"`
		Amount     int64                  `json:"amount"`
	}

	buildReward := func(task models.Task, utask *models.UserTask) reward {
		var item = reward{
			RewardType: task.RewardType,
			State:      constmap.UserTaskProcessing,
			Amount:     int64(task.RewardAmount),
		}
		if utask != nil && utask.Date.Equal(utils.BeginOfWeek(time.Now().AddDate(0, 0, -7))) { //上周的数据才有效
			item.State = utask.State
			if utask.State == constmap.UserTaskWaitReward {
				item.RewardId = task.RewardId
			}
		}
		return item
	}

	var out struct {
		ActivityId uint                  `json:"activity_id"`
		Uuid       string                `json:"uuid"`
		Start      int64                 `json:"start"`
		End        int64                 `json:"end"`
		Title      string                `json:"title"`
		Cover      string                `json:"cover"`
		Rules      string                `json:"rules"`
		HasJoin    bool                  `json:"has_join"`
		LastWeekPK *beans.WeekPKRankList `json:"last_week_pk"`
		Rewards    []reward              `json:"rewards"`
	}

	out.Start = activity.Start.Unix()
	out.End = activity.End.Unix()
	out.ActivityId = activity.ID
	out.Uuid = activity.Uuid
	out.Title = activity.Title
	out.Cover = utils.StaticUrl(activity.Cover)
	out.Rules = activity.Ext.Rules
	out.LastWeekPK = getWeekPkSettleList(db, activity, lastWeek)

	taskIds := []uint{ptask.ID}
	slice.ForEach(childrenTasks, func(index int, item models.Task) {
		taskIds = append(taskIds, item.ID)
	})
	var utasks map[uint]*models.UserTask
	if session, err := business.GetFrontLoginUser(ctx); err != nil {
		utasks = make(map[uint]*models.UserTask)
	} else {
		utasks = task_biz.LoadUserTasks(db, session.UserId, taskIds, nil)

		var cnt int64
		db.Model(&models.UserActivity{}).Where(models.UserActivity{
			ActivityId: activity.ID,
			UserId:     session.UserId,
		}).Count(&cnt)
		out.HasJoin = cnt > 0
	}

	out.Rewards = append(out.Rewards, buildReward(ptask, utasks[ptask.ID]))
	slice.ForEach(childrenTasks, func(index int, item models.Task) {
		out.Rewards = append(out.Rewards, buildReward(item, utasks[item.ID]))
	})

	return out, nil
}
