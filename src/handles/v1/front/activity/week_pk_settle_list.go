package activity

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func WeekPkSettleList(ctx *gin.Context) (any, error) {
	var in struct {
		Uuid string `form:"uuid" binding:"required"`
		Week int64  `form:"week" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var activity = new(models.Activity)

	db.Where(models.Activity{Uuid: in.Uuid}).Take(&activity)

	if activity.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	}

	week := utils.BeginOfWeek(time.Unix(in.Week, 0))

	return getWeekPkSettleList(db, activity, week), nil
}

func getWeekPkSettleList(db *gorm.DB, activity *models.Activity, week time.Time) *beans.WeekPKRankList {
	if week.After(activity.End) {
		week = utils.BeginOfWeek(activity.End)
	} else if week.Before(activity.Start) {
		week = utils.BeginOfWeek(activity.Start)
	} else {
		week = utils.BeginOfWeek(week)
	}

	var out = new(beans.WeekPKRankList)
	out.Week = week.Unix()

	actStartWeek := utils.BeginOfWeek(activity.Start)
	if actStartWeek.After(week) { //活动未开始
		out.WeekNum = 1
	} else {
		out.WeekNum = int(week.Unix()-actStartWeek.Unix())/86400/7 + 1
	}

	var rankList []*models.ActivityPlanRankSettle

	query := db.Model(&rankList).Where(&models.ActivityPlanRankSettle{
		ActivityId: activity.ID,
		Date:       week,
	})
	query.Order("score DESC, id ASC").
		Find(&rankList)

	if len(rankList) == 0 {
		out.List = make([]beans.WeekPkRankListItem, 0)
		return out
	}

	// 提取 ActivityPlanId 列表，批量查询 ActivityPlan
	activityPlanIds := slice.Map(rankList, func(_ int, rank *models.ActivityPlanRankSettle) uint {
		return rank.ActivityPlanId
	})

	var activityPlans []*models.ActivityPlan
	db.Find(&activityPlans, activityPlanIds)

	// 创建 ActivityPlan 映射表和用户信息映射表
	activityPlanMap := slice.KeyBy(activityPlans, func(activityPlan *models.ActivityPlan) uint {
		return activityPlan.ID
	})

	userIds := slice.Map(activityPlans, func(_ int, activityPlan *models.ActivityPlan) uint {
		return activityPlan.UserId
	})
	userMap := user_biz.LoadUsers(db, userIds)

	// 组装返回数据
	out.List = slice.Map(rankList, func(i int, rank *models.ActivityPlanRankSettle) beans.WeekPkRankListItem {
		item := beans.WeekPkRankListItem{
			ActivityPlanId: rank.ActivityPlanId,
			RankId:         rank.ID,
			Score:          rank.Score,
			Rank:           i + 1,
		}

		if activityPlan, exists := activityPlanMap[rank.ActivityPlanId]; exists {
			item.UserId = activityPlan.UserId
			item.PlanId = activityPlan.PlanId
			item.Poster = utils.StaticUrl(activityPlan.Poster)

			if user, userExists := userMap[activityPlan.UserId]; userExists {
				item.Nickname = user.Nickname
				item.Avatar = utils.AvatarUrl(user.Avatar)
			}
		}

		return item
	})

	return out
}
