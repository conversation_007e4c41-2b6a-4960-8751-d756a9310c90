package activity

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func DoTask(ctx *gin.Context) (any, error) {
	var in struct {
		Cond    constmap.TaskCond `form:"cond" binding:"required"`
		PlanId  uint              `form:"plan_id"`
		AiReqid string            `form:"ai_reqid"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	switch in.Cond {
	default:
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	case constmap.TaskCondSavePlan:
		if in.PlanId <= 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
	case constmap.TaskCondMakePlan:
		if strutil.IsBlank(in.AiReqid) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	type reward struct {
		ActivityId   uint                `json:"activity_ids"`
		Name         string              `json:"name"`
		RewardType   constmap.TaskReward `json:"reward_type"`
		RewardAmount int                 `json:"reward_amount"`
	}

	var out struct {
		Rewards []*reward `json:"rewards"`
	}
	out.Rewards = make([]*reward, 0)

	uacts := activity_biz.FindValidUserActs(db, session.UserId, nil)

	for _, act := range uacts {
		err := db.Transaction(func(tx *gorm.DB) error {
			rsp, err := task_asm.DoActivityTask(tx, task_asm.DoActivityTaskReq{
				UserId:     session.UserId,
				ActivityId: act.ActivityId,
				PlanId:     in.PlanId,
				AiReqid:    in.AiReqid,
				Cond:       in.Cond,
			})
			if err != nil {
				return err
			}
			out.Rewards = append(out.Rewards, &reward{
				ActivityId:   act.ActivityId,
				RewardType:   rsp.RewardType,
				RewardAmount: rsp.RewardAmount,
			})
			return nil
		})
		if err != nil {
			my_logger.Errorf("DoTask DoActivityTask", zap.Error(err))
		}
	}
	activityIds := slice.ReduceBy(out.Rewards, make([]uint, 0), func(index int, item *reward, agg []uint) []uint {
		return append(agg, item.ActivityId)
	})
	activityMap := make(map[uint]models.Activity)
	if len(activityIds) > 0 {
		var acts []models.Activity
		db.Find(&acts, activityIds)
		activityMap = slice.ReduceBy(acts, make(map[uint]models.Activity), func(index int, item models.Activity, agg map[uint]models.Activity) map[uint]models.Activity {
			agg[item.ID] = item
			return agg
		})
	}

	slice.ForEach(out.Rewards, func(index int, item *reward) {
		item.Name = activityMap[item.ActivityId].Title
	})

	return out, nil
}
