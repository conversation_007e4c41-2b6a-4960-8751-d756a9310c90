package index

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"image/png"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func Qrcode(ctx *gin.Context) (any, error) {
	var in struct {
		Text string `form:"text" binding:"required"`
		Size int    `form:"size"`
	}

	ctx.Set(constmap.ContextHttpRaw, true)
	var buf = new(bytes.Buffer)

	err := func() error {
		if err := ctx.ShouldBind(&in); err != nil {
			return utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
		if in.Size < 50 {
			in.Size = 200
		}
		qrimg, err := business.EncodeQrCode(in.Text, in.Size, in.Size)
		if err != nil {
			return utils.NewError(err)
		}

		if err = png.Encode(buf, qrimg); err != nil {
			return utils.NewError(err)
		}
		return nil
	}()

	if err != nil {
		ctx.Data(500, "text/plain", []byte(err.Error()))
		return nil, nil
	}

	ctx.Data(200, "image/png", buf.Bytes())
	return nil, nil
}
