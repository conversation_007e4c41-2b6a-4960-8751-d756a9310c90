package def

import (
	"fmt"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

func SeasonColor() (title, color string) {
	season := utils.Season(time.Now().Month())
	title = fmt.Sprintf("%s精选", season)
	switch season != "" {
	case strings.Contains(season, "春"):
		color = "#52C41A"
	case strings.Contains(season, "夏"):
		color = "#1890FF"
	case strings.Contains(season, "秋"):
		color = "#FACA14"
	case strings.Contains(season, "冬"):
		color = "#FF4D4F"
	}
	return
}
