package index

import (
	"github.com/gin-gonic/gin"
	"path"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func UploadToken(ctx *gin.Context) (any, error) {
	var in struct {
		ExpDays int `form:"exp_days"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	var err error
	// 转换为qiniu_biz的选项结构
	size, _ := utils.ParseFileSizeStr("20M")
	var tokenExpires int64 = constmap.TimeSec1h
	options := &qiniu_biz.UploadTokenOptions{
		Expires: tokenExpires,
		MaxSize: size,
	}

	var tokenResp *qiniu_biz.UploadTokenResponse

	// 生成上传令牌
	options.ExpDays = in.ExpDays
	tokenResp, err = qiniu_biz.GenerateUploadToken(options)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "生成上传令牌失败: "+err.Error())
	}

	var out struct {
		Token        string `json:"token"`
		Bucket       string `json:"bucket"`
		Domain       string `json:"domain"`
		Prefix       string `json:"prefix"`
		TokenExpires int64  `json:"token_expires"`
	}
	out.TokenExpires = tokenExpires - 600
	out.Token = tokenResp.Token
	out.Bucket = tokenResp.Bucket
	out.Domain = config.Config.App.StaticHost
	out.Prefix = path.Join(constmap.QiniuPrefix, utils.If(config.IsProduction(), "prod", "dev"))
	return out, nil
}
