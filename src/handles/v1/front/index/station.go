package index

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 机场、车站搜索
func Station(ctx *gin.Context) (any, error) {
	var in struct {
		Type    constmap.JTimelineType `form:"type" binding:"required"`
		Keyword string                 `form:"keyword" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)
	rsp, err := amap.SearchStation(db, amap.SearchStationRequest{
		Keyword:  in.Keyword,
		PageSize: pageSize,
		PageNum:  page,
		Type:     in.Type,
	})
	if err != nil {
		my_logger.Errorf("search station", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "查询失败")
	}

	type vitem struct {
		Id       string  `json:"id"`
		Lng      float64 `json:"lng"`
		Lat      float64 `json:"lat"`
		Name     string  `json:"name"`
		ZoneId   uint    `json:"zone_id"`
		ZoneName string  `json:"zone_name"`
	}
	var out struct {
		Total int     `json:"total"`
		List  []vitem `json:"list"`
	}
	out.Total = rsp.Count
	out.List = make([]vitem, 0)

	var zoneCodes []string
	for _, v := range rsp.Tips {
		zoneCodes = append(zoneCodes, v.Adcode)
	}
	zoneMap := make(map[string]*models.Zone)
	if len(zoneCodes) > 0 {
		var zones []models.Zone
		var adcodeMap = make(map[uint]string)
		var zoneIds = make([]uint, 0)
		db.Select("id,ad_code").Where("ad_code in ?", zoneCodes).Find(&zones)
		slice.ForEach(zones, func(index int, item models.Zone) {
			adcodeMap[item.ID] = item.AdCode
			zoneIds = append(zoneIds, item.ID)
		})
		zmap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, constmap.ZoneLevelCity)
		maputil.ForEach(zmap, func(zid uint, value *models.Zone) {
			if value.Parent != nil {
				value = value.Parent
			}
			if adcode, ok := adcodeMap[zid]; ok {
				zoneMap[adcode] = value
			}
		})
	}

	for _, v := range rsp.Tips {
		zone, ok := zoneMap[v.Adcode]
		if !ok {
			continue
		}
		poi, _ := utils.ToArray[float64](v.Location, ",")
		var locLng, locLat float64
		if len(poi) == 2 {
			locLng, locLat = poi[0], poi[1]
		}
		out.List = append(out.List, vitem{
			Id:       v.Id,
			Lng:      locLng,
			Lat:      locLat,
			Name:     v.Name,
			ZoneId:   zone.ID,
			ZoneName: zone.Name,
		})
	}

	return out, nil
}
