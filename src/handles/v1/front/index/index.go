package index

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/banner_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/index/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Index(ctx *gin.Context) (any, error) {
	var in struct {
		ZoneId uint `form:"zone_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	type Vtype string
	const (
		TypeHot         Vtype = "hot"          //热门出行
		TypeMoreOptions Vtype = "more_options" //更多提示词
		TypeMyPlan      Vtype = "my_plan"      //我的行程
		TypeHotel       Vtype = "hotel"        //酒店搜索
		TypeScenic      Vtype = "scenic"       //景点搜索
	)
	type hotTravel struct {
		From string `json:"from"`
		To   string `json:"to"`
	}
	type vitem struct {
		Type  Vtype          `json:"type"`
		Title string         `json:"title"`
		Hots  []hotTravel    `json:"hots"`
		Query map[string]any `json:"query"`
	}
	type activity struct {
		Id    uint   `json:"id"`
		Uuid  string `json:"uuid"`
		Title string `json:"title"`
		Pic   string `json:"pic"`
	}

	var out struct {
		Prompts    *constmap.GenericList[string] `json:"prompts"`
		Texts      []vitem                       `json:"texts"`
		Activities []activity                    `json:"activities"`
		HotTags    *constmap.GenericList[string] `json:"hot_tags"`
		TitleColor string                        `json:"title_color"`
		Banners    []beans.Banner                `json:"banners"`
	}
	out.Texts = make([]vitem, 0)
	out.Activities = make([]activity, 0)
	banners := banner_biz.LoadBanners(db, []constmap.BannerPosition{constmap.BannerPositionHome})
	out.Banners = banners[constmap.BannerPositionHome]

	out.Texts = append(out.Texts, vitem{
		Type:  TypeMoreOptions,
		Title: "我想自驾游去北京玩3天",
		Hots:  make([]hotTravel, 0),
		Query: map[string]any{
			"to":   "北京",
			"days": "3天",
		},
	})

	out.Texts = append(out.Texts, vitem{
		Type:  TypeScenic,
		Title: "云南有哪些景点好玩？",
		Hots:  make([]hotTravel, 0),
		Query: map[string]any{
			"tab":      "scenic",
			"zone_ids": "101,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393",
		},
	})

	out.Texts = append(out.Texts, vitem{
		Type:  TypeHotel,
		Title: "上海徐家汇有哪些酒店",
		Hots:  make([]hotTravel, 0),
		Query: map[string]any{
			"tab":      "hotel",
			"zone_ids": "7",
			"location": "121.440641,31.193908",
		},
	})

	acts := activity_biz.FindValidActs(db, "uuid=?", []any{constmap.ActLaxin})
	out.Activities = slice.FilterMap(acts, func(index int, item models.Activity) (activity, bool) {
		v := activity{
			Id:    item.ID,
			Uuid:  item.Uuid,
			Title: item.Title,
		}
		if item.Uuid == constmap.ActLaxin {
			v.Pic = "https://rp.yjsoft.com.cn/yiban/static/activity/new/activity-popup.png?v=1"
		}
		return v, true
	})

	zoneId := utils.If(in.ZoneId == 0, constmap.DefaultZoneId, in.ZoneId)
	out.Prompts = utils.NewGenericList[string](nil)
	my_cache.Get(fmt.Sprintf(constmap.RKRecPrompts, zoneId), out.Prompts)

	out.HotTags = utils.NewGenericList[string](nil)
	my_cache.Get(fmt.Sprintf(constmap.RKPlanHotTags, zoneId), out.HotTags)
	title, color := def.SeasonColor()
	out.HotTags = utils.NewGenericList(utils.Unshift(*out.HotTags, title))
	out.TitleColor = color

	return out, nil
}
