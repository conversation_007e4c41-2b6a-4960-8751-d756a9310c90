package index

import (
	"context"
	"encoding/json"
	"fmt"
	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

type geoData struct {
	Address string  `json:"address"`
	Lng     float64 `json:"lng"`
	Lat     float64 `json:"lat"`
	City    string  `json:"city"`
	ZoneId  uint    `json:"zone_id"`
}

func (c *geoData) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, c)
}

func (c *geoData) MarshalBinary() (data []byte, err error) {
	return json.Marshal(c)
}

func Geo(ctx *gin.Context) (any, error) {
	var in struct {
		Address string  `form:"address"`
		Lng     float64 `form:"query_lng"`
		Lat     float64 `form:"query_lat"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	} else if strutil.IsBlank(in.Address) && in.Lng == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	var data = new(geoData)

	db := utils.GetDB(ctx)

	var reGeo *go_amap.ReGeoResponse
	var err error

	if in.Lng > 0 && in.Lat > 0 {
		c, unlocker := context.WithCancel(context.Background())
		my_cache.RedisLock(c, fmt.Sprintf(constmap.RKSpin, "geo", convertor.ToString(in.Lng)+convertor.ToString(in.Lat)), constmap.TimeDur1m)
		defer unlocker()

		ckey := fmt.Sprintf(constmap.RKGeo, convertor.ToString(in.Lng), convertor.ToString(in.Lat))
		if my_cache.Get(ckey, data) {
			return data, nil
		}
		reGeo, err = amap.ReGeo(in.Lng, in.Lat)
		if err != nil {
			return nil, err
		}

		var zone models.Zone
		if db.
			Where(models.Zone{AdCode: reGeo.ReGeoCode.AddressComponent.Adcode}).
			Take(&zone).Error == nil {
			res := zone_biz.NewZoneBiz().GetZonesToTop(db, []uint{zone.ID}, constmap.ZoneLevelCity)

			if res[zone.ID].Parent != nil {
				data.ZoneId = res[zone.ID].Parent.ID
			}
		}

		data.City = convertor.ToString(reGeo.ReGeoCode.AddressComponent.City)
		data.Lng = in.Lng
		data.Lat = in.Lat
		data.Address = reGeo.ReGeoCode.FormattedAddress
		_ = my_cache.Set(ckey, data, constmap.TimeDur1m)
	}
	if err != nil {
		return nil, err
	}

	return data, nil
}
