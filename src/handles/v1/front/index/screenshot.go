package index

import (
	"context"
	"github.com/chromedp/chromedp"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/chromedp_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func Screenshot(ctx *gin.Context) (any, error) {
	var in struct {
		Url            string `form:"url" bind:"required"`
		Selector       string `form:"selector"`
		Size           string `form:"size"`
		WaitVisible    string `form:"wait_visible"`
		WaitNotVisible string `form:"wait_not_visible"`
	}

	ctx.Set(constmap.ContextHttpRaw, true)

	err := func() error {
		var err error
		if err = ctx.ShouldBind(&in); err != nil {
			return err
		}

		if _, err = business.GetFrontLoginUser(ctx); err != nil {
			return err
		}

		var screenshot []byte

		wait := utils.If(strutil.IsBlank(in.WaitVisible), "body", in.WaitVisible)

		tasks := chromedp.Tasks{
			chromedp.WaitVisible(wait, chromedp.ByQuery),
			chromedp.ScrollIntoView("body", chromedp.ByQuery),
		}

		if !strutil.IsBlank(in.WaitNotVisible) {
			tasks = append(tasks, chromedp.WaitNotVisible(in.WaitNotVisible, chromedp.ByQuery))
		}

		tasks = append(tasks, chromedp.ActionFunc(func(ctx context.Context) error {
			if strutil.IsBlank(in.Selector) {
				if err = chromedp.FullScreenshot(&screenshot, 90).Do(ctx); err != nil {
					return err
				}
			} else {
				if err = chromedp.Screenshot(in.Selector, &screenshot, chromedp.NodeVisible, chromedp.ByQuery).Do(ctx); err != nil {
					return err
				}
			}

			return nil
		}))

		size := utils.If(strutil.IsBlank(in.Size), "375*812", in.Size)

		if err = chromedp_biz.ChromeRun(in.Url, tasks, true, size, 2); err != nil {
			return err
		}

		ctx.Data(200, "image/png", screenshot)
		return nil
	}()

	if err != nil {
		var errCode = 500
		if e, ok := err.(constmap.AppError); ok && e.Code == constmap.ErrorNotLogin {
			errCode = 403
		}
		ctx.Data(errCode, "text/plain", []byte(err.Error()))
	}

	return nil, nil
}
