package index

import (
	response2 "github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth/response"
	"github.com/duke-git/lancet/v2/condition"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/business/user_biz/user_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

type loginReq struct {
	Code     string
	Nickname string
	AvatarId uint
}

func Login(ctx *gin.Context) (any, error) {
	var in struct {
		Code     string `form:"code" binding:"required"`
		Nickname string `form:"nickname"`
		AvatarId uint   `form:"avatar_id"`
		ShareUid uint   `form:"share_uid"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var (
		user *models.User
		auth *models.UserOauth
	)
	var err error
	var req = loginReq{
		Code:     in.Code,
		Nickname: in.Nickname,
		AvatarId: in.AvatarId,
	}

	switch business.GetSourceType(ctx) {
	case constmap.SourceWeixinH5:
		user, _, err = loginWithH5(in.Code, db)
	default:
		user, auth, err = loginDefault(req, db)
	}

	if err != nil {
		return nil, err
	}

	session := models.Session{
		Token:  utils.UUID(),
		UserId: user.ID,
	}

	if err = db.Create(&session).Error; err != nil {
		return nil, err
	}

	type resp struct {
		beans.FrontAuthToken

		NeedNickname bool `json:"need_nickname"`
	}

	data := resp{
		FrontAuthToken: beans.FrontAuthToken{
			Token:    session.Token,
			Nickname: user.Nickname,
			Avatar:   utils.StaticUrl(user.Avatar),
			UserId:   user.ID,
		},
	}
	if auth != nil {
		data.OpenId = auth.OpenId
	}

	if in.ShareUid > 0 {
		_ = my_queue.Light(constmap.EventTaskAction, gin.H{
			"task_event_req": convertor.ToString(task_asm.DoActivityTaskReq{
				Cond:      constmap.TaskCondShareMini,
				UserId:    in.ShareUid,
				HelperUid: data.UserId,
			}),
		})
	}

	return data, err
}

func loginWithH5(code string, db *gorm.DB) (*models.User, *models.UserOauth, error) {
	resp, err := my_wechat.UserFromCode(code)
	if err != nil {
		return nil, nil, err
	}

	openid := resp.GetOpenID()
	nickname := resp.GetNickname()
	if strutil.IsBlank(openid) {
		return nil, nil, utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
	}

	var entry models.UserOauth
	var user models.User

	err = db.Transaction(func(tx *gorm.DB) error {

		if err = tx.Joins("User").Where("user_oauths.open_id=? and type=?",
			openid, constmap.OauthTypeWeixinH5,
		).First(&entry).Error; err != nil {
			entry = models.UserOauth{
				OpenId: openid,
				Type:   constmap.OauthTypeWeixinH5,
			}
			if err = tx.Omit(clause.Associations).Create(&entry).Error; err != nil {
				return err
			}
			user = models.User{
				Nickname: condition.TernaryOperator(strutil.IsBlank(nickname), "微信用户", nickname),
			}
			if err = tx.Create(&user).Error; err != nil {
				return err
			}

			if tx.Model(&entry).Omit(clause.Associations).Updates(models.UserOauth{UserId: user.ID}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
			}
		} else {
			user = entry.User
		}

		return nil
	})

	return &user, &entry, err
}

func loginDefault(in loginReq, db *gorm.DB) (*models.User, *models.UserOauth, error) {
	var code2Session *response2.ResponseCode2Session
	var entry models.UserOauth
	var user models.User
	var err error

	if code2Session, err = my_wechat.GetOpenIdByCode(in.Code); err != nil {
		return nil, nil, err
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if err = tx.Joins("User").Where("user_oauths.open_id=? and type=?", code2Session.OpenID, constmap.OauthTypeMpWeixin).First(&entry).Error; err != nil {
			entry = models.UserOauth{
				UserId: 0,
				OpenId: code2Session.OpenID,
				Type:   constmap.OauthTypeMpWeixin,
			}
			if err = tx.Omit(clause.Associations).Create(&entry).Error; err != nil {
				return err
			}
			user = models.User{
				Nickname: "微信用户" + utils.RandomStr(8),
			}
			if err = tx.Create(&user).Error; err != nil {
				return err
			}

			if tx.Model(&entry).Omit(clause.Associations).Updates(models.UserOauth{UserId: user.ID}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorLoginFail, constmap.ErrorMsgLoginFail)
			}
		} else {
			user = entry.User
		}

		var updateUser models.User
		var doUpdate bool
		if !strutil.IsBlank(in.Nickname) {
			updateUser.Nickname = in.Nickname
			doUpdate = true
		}
		if in.AvatarId > 0 {
			if url, err := business.MoveUploadFile(tx, in.AvatarId); err != nil {
				return err
			} else {
				updateUser.Avatar = utils.UnWrapStaticUrl(url)
				doUpdate = true
			}
		}
		if doUpdate {
			if tx.Model(&user).Updates(updateUser).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "基础信息更新失败")
			}
		}

		return nil
	})

	if user.InviteCode == "" {
		if err := user_asm.GenerateInviteCode(db, &user); err != nil {
			my_logger.Errorf("GenerateInviteCode", zap.Uint("uid", user.ID), zap.Error(err))
		}
	}

	return &user, &entry, nil
}
