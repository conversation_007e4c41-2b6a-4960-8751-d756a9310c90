package index

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/holiday_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

func Holiday(ctx *gin.Context) (any, error) {
	var in struct {
		Start int64 `form:"start" binding:"required"`
		End   int64 `form:"end" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	holidays, err := holiday_biz.GetHoliday(time.Unix(in.Start, 0), time.Unix(in.End, 0))
	if err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		Holidays []beans.Holiday `json:"holidays"`
	}
	out.Holidays = holidays
	return out, nil
}
