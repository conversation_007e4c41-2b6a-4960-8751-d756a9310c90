package index

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Cities(ctx *gin.Context) (any, error) {

	db := utils.GetDB(ctx)

	type z struct {
		Id       uint    `json:"id"`
		Name     string  `json:"name"`
		Pinyin   string  `json:"pinyin"`
		Lng      float64 `json:"lng"`
		Lat      float64 `json:"lat"`
		Province string  `json:"province"`
	}
	var out struct {
		Hots []z `json:"hots"`
		List []z `json:"list"`
	}
	out.Hots = make([]z, 0)

	var hots = utils.NewGenericList(make([]beans.HotZone, 0))
	if my_cache.Get(constmap.RKHotZones, hots) {
		out.Hots = slice.Map(*hots, func(index int, item beans.HotZone) z {
			return z{
				Id:   item.Id,
				Name: item.Name,
				Lng:  item.Lng,
				Lat:  item.Lat,
			}
		})
	}

	var zones []models.Zone
	db.Where("`level` in ?", []int{constmap.ZoneLevelCity, constmap.ZoneLevelProvince}).Find(&zones)

	provinces := make(map[uint]string)
	list := slice.Filter(zones, func(index int, item models.Zone) bool {
		if item.Level == constmap.ZoneLevelCity {
			return true
		}
		provinces[item.ID] = item.Name
		return false
	})
	slice.SortBy(list, func(a, b models.Zone) bool {
		return a.Pinyin < b.Pinyin
	})
	list = zone_biz.NewZoneBiz().NormalizeZoneNames(list)
	out.List = slice.Map(list, func(index int, item models.Zone) z {
		ins := z{
			Id:       item.ID,
			Name:     item.Name,
			Pinyin:   item.Pinyin,
			Lng:      item.Lng,
			Lat:      item.Lat,
			Province: provinces[item.ParentId],
		}
		return ins
	})

	return out, nil
}
