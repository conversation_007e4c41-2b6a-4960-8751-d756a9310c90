package index

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/banner_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 发现
func Finder(ctx *gin.Context) (any, error) {
	var in struct{}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	type tab struct {
		Label string `json:"label"`
		Value string `json:"value"`
	}
	var out struct {
		Banners []beans.Banner `json:"banners"`
		Tabs    []tab          `json:"tabs"`
	}

	banners := banner_biz.LoadBanners(db, []constmap.BannerPosition{constmap.BannerPositionFaXian})
	out.Banners = banners[constmap.BannerPositionFaXian]

	out.Tabs = []tab{
		{Label: "全部", Value: "all"},
		{Label: "团游", Value: "tuan"},
	}

	return out, nil
}
