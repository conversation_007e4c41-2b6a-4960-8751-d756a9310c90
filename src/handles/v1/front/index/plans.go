package index

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/index/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Plans(ctx *gin.Context) (any, error) {
	var in struct {
		ZoneId uint   `form:"zone_id" binding:"required"`
		HotTag string `form:"hot_tag"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	title, color := def.SeasonColor()
	if in.HotTag == title {
		in.HotTag = ""
	}

	db := utils.GetDB(ctx)
	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}

	type outItem struct {
		*beans.PlanCard
		CostAvg1 int64 `json:"cost_avg1"`
		PlanId   uint  `json:"plan_id"`
		IsOwn    bool  `json:"is_own"`
	}
	plans := make([]*outItem, 0)
	reqIds := make([]string, 0)
	ckey := fmt.Sprintf(constmap.RLRobotPlan, in.ZoneId, in.HotTag)
	for _, v := range my_cache.RedisClient().SMembers(ckey).Val() {
		if v == "" {
			continue
		}
		plan := &beans.PlanCard{}
		if err := json.Unmarshal([]byte(v), plan); err != nil {
			my_logger.Errorf("PlanCard.Unmarshal", zap.Error(err))
			continue
		}
		plans = append(plans, &outItem{
			PlanCard: plan,
			CostAvg1: utils.CurrencyFloat2Int(plan.CostAvg),
		})
		reqIds = append(reqIds, plan.AiReqid)
	}

	reqMap := make(map[string]models.Plan)
	costMap := make(map[string]*beans.PlanCost)
	if len(reqIds) > 0 {
		if userId > 0 {
			var list []models.Plan
			db.Where(models.Plan{
				UserId: userId,
				State:  constmap.Enable,
			}).Where("ai_reqid IN ?", reqIds).Find(&list)
			slice.ForEach(list, func(index int, item models.Plan) {
				reqMap[item.AiReqid] = item
			})
		}

		var planCostDetails []models.PlanCostDetail
		db.Where("ai_reqid in ? AND state=?", reqIds, constmap.PlanCostDetailStateDone).Find(&planCostDetails)
		slice.ForEach(planCostDetails, func(index int, item models.PlanCostDetail) {
			costMap[item.AiReqid] = item.Cost.Data
		})
	}
	plans = slice.Map(plans, func(index int, item *outItem) *outItem {
		if p, ok := reqMap[item.AiReqid]; ok {
			item.PlanId = p.ID
			item.IsOwn = true
		}
		if c, ok := costMap[item.AiReqid]; ok {
			item.CostAvg1 = c.Total
		}
		return item
	})
	var out struct {
		Title string     `json:"title"`
		Color string     `json:"color"`
		List  []*outItem `json:"list"`
	}
	out.Title, out.Color = title, color
	out.List = plans
	if !strutil.IsBlank(in.HotTag) {
		out.Title = in.HotTag
	}
	return out, nil
}
