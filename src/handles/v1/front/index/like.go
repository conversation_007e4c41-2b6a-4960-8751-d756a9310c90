package index

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Like(ctx *gin.Context) (any, error) {
	var in struct {
		Type  constmap.LikeType `form:"type" binding:"required"`
		ResId uint              `form:"res_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var cnt int64
	switch in.Type {
	default:
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	case constmap.LikeWish:
		db.Model(&models.Wish{}).Where("id=?", in.ResId).Count(&cnt)
	}
	if cnt == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "目标不存在")
	}

	var out struct {
		IsLike bool  `json:"is_like"`
		Likes  int64 `json:"likes"`
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		if isLike, likes, err := like_biz.DoLike(tx, in.Type, in.ResId, session.UserId); err != nil {
			return err
		} else {
			out.IsLike = isLike
			out.Likes = likes
		}
		return nil
	})
	if err != nil {
		my_logger.Errorf("Like", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作失败")
	}

	switch in.Type {
	case constmap.LikeWish:
		// 异步同步到ES
		_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
			"ids": convertor.ToString(in.ResId),
		})
	}

	return out, nil
}
