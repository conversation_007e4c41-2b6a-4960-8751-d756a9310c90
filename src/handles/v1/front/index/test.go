package index

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Test(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if !config.Config.App.Debug {
		return "deny", nil
	}

	db := utils.GetDB(ctx)

	var plan models.Plan

	if err := db.Preload("Sections").Joins("Ext").Take(&plan, in.Id).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	buf, err := plan_asm.RenderPlanHtml(models.New(), &plan)
	if err != nil {
		return nil, err
	}

	ctx.Set(constmap.ContextHttpRaw, true)

	ctx.Data(200, "text/html", buf.Bytes())

	return nil, nil
}
