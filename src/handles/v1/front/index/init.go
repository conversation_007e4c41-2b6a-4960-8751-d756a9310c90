package index

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Init(ctx *gin.Context) (any, error) {
	type shareMiniTask struct {
		TaskId    uint   `json:"task_id"`
		ShortDesc string `json:"short_desc"`
		Value     int    `json:"value"`
	}

	var data struct {
		JsSdkConfig       any                `json:"js_sdk_config"`
		ZoneId            uint               `json:"zone_id"`
		ZoneName          string             `json:"zone_name"`
		Lat               float64            `json:"lat"`
		Lng               float64            `json:"lng"`
		ServiceWx         string             `json:"service_wx"`
		ServiceEmail      string             `json:"service_email"`
		ShareMiniTask     *shareMiniTask     `json:"share_mini_task"`
		PlanCostAmount    int64              `json:"plan_cost_amount"`     //行程规划扣除积分值
		BindInviteGive    int64              `json:"bind_invite_give"`     //绑定赠送积分值
		PlanPdfCostAmount int64              `json:"plan_pdf_cost_amount"` //线路PDF生成积分值
		PackageBuyService string             `json:"package_buy_service"`  //套餐购买服务条款
		AdConfig          *beans.SysAdConfig `json:"ad_config"`            //广告配置
		Sws               *beans.SysSwitch   `json:"sws"`                  //系统开关
	}

	data.PackageBuyService = "https://rp.yjsoft.com.cn/yiban/static/service.html"
	data.ServiceWx = "yiban701"
	data.ServiceEmail = "<EMAIL>"

	db := utils.GetDB(ctx)
	zone, _ := zone_biz.NewZoneBiz().IpLocation(ctx, db)
	data.ZoneId = zone.ID
	data.ZoneName = zone.Name
	data.Lat = zone.Lat
	data.Lng = zone.Lng

	var task models.Task
	if err := db.Where(models.Task{
		CondType:   constmap.TaskCondShareMini,
		RewardType: constmap.TaskRewardAccountIntegral,
		State:      constmap.Enable}).Take(&task).Error; err == nil {

		data.ShareMiniTask = &shareMiniTask{
			TaskId:    task.ID,
			ShortDesc: task.ShortDesc,
			Value:     task.RewardAmount,
		}
	}

	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigBindInviteCodeAmount); err == nil {
		data.BindInviteGive, _ = val.(int64)
	}

	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigPlanCostAmount); err == nil {
		data.PlanCostAmount, _ = val.(int64)
	}

	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigGeneratePlanPdfCostAmount); err == nil {
		data.PlanPdfCostAmount, _ = val.(int64)
	}

	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigAdConfig); err == nil {
		data.AdConfig, _ = val.(*beans.SysAdConfig)
	}

	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigSysSwitch); err == nil {
		data.Sws, _ = val.(*beans.SysSwitch)
	}

	switch business.GetSourceType(ctx) {
	case constmap.SourceWeixinH5:
		data.JsSdkConfig, _ = my_wechat.JsSdkConfig()
	}

	return data, nil
}
