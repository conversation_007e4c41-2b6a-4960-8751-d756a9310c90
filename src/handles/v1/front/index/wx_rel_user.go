package index

import (
	response2 "github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth/response"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// openid反查用户
func ThirdRelUser(ctx *gin.Context) (any, error) {
	var in struct {
		Code string `form:"code" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	var code2Session *response2.ResponseCode2Session
	var err error
	if code2Session, err = my_wechat.GetOpenIdByCode(in.Code); err != nil {
		return nil, utils.NewError(err)
	}

	db := utils.GetDB(ctx)

	var oauth models.UserOauth
	db.Preload("User").Where(models.UserOauth{
		OpenId: code2Session.OpenID,
		Type:   constmap.OauthTypeMpWeixin,
	}).Take(&oauth)

	var out struct {
		UserId   uint   `json:"user_id"`
		Nickname string `json:"nickname"`
		Avatar   string `json:"avatar"`
	}
	if oauth.UserId == 0 {
		return out, nil
	}
	out.UserId = oauth.UserId
	out.Nickname = oauth.User.Nickname
	out.Avatar = utils.StaticUrl(oauth.User.Avatar)
	return out, nil
}
