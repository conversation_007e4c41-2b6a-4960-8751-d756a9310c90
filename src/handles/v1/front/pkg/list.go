package pkg

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}

	var pkgs []*models.Package
	db.Where(models.Package{State: constmap.Enable}).Order("sort DESC,ID DESC").Find(&pkgs)

	type vitem struct {
		Id           uint                    `json:"id"`
		Name         string                  `json:"name"`
		Desc         string                  `json:"desc"`
		Price        int64                   `json:"price"`          // 价格：分
		RealPrice    int64                   `json:"real_price"`     // 实际价格：分
		Amount       int                     `json:"amount"`         // 给予金额
		ExpNum       int                     `json:"exp_num"`        // 到期数字
		ExpUnit      constmap.PackageExpUnit `json:"exp_unit"`       // 到期数字单位
		ExpUnitText  string                  `json:"exp_unit_text"`  // 到期单位文本
		NewUsable    int                     `json:"new_usable"`     // 首充可用
		NewRealPrice int64                   `json:"new_real_price"` // 首充实际价格：分
		Tags         []string                `json:"tags"`
		CanUseNew    bool                    `json:"can_use_new"` //是否可用首充档位配置
		CanBuy       bool                    `json:"can_buy"`     //是否可选中
	}

	var out struct {
		List []vitem `json:"list"`
	}
	var upkgCnt int64
	var upkgSummary *models.UserPackageSummary
	var upkgs []*models.UserPackage
	if userId > 0 {
		upkgCnt = user_biz.GetUserPackageCount(db, userId)
		upkgSummary = new(models.UserPackageSummary)
		db.Where(models.UserPackageSummary{UserId: userId}).Take(&upkgSummary)
		db.Where(models.UserPackage{
			UserId: userId,
			State:  constmap.UserPackageStateUsing,
		}).Find(&upkgs)
	}

	out.List = slice.Map(pkgs, func(index int, item *models.Package) vitem {
		return vitem{
			Id:           item.ID,
			Name:         item.Name,
			Desc:         item.Desc,
			Price:        item.Price,
			RealPrice:    item.RealPrice,
			Amount:       item.Amount,
			ExpNum:       item.ExpNum,
			ExpUnit:      item.ExpUnit,
			ExpUnitText:  business.PackageExpUnitText(item.ExpUnit),
			NewUsable:    item.NewUsable,
			NewRealPrice: item.NewRealPrice,
			Tags:         strutil.SplitEx(item.Tags, ",", true),
			CanUseNew:    userId > 0 && user_biz.CanUsePackageNew(upkgCnt, item),
			CanBuy:       userId == 0 || user_biz.CanChangePackage(upkgSummary, upkgs, item.ID),
		}
	})
	return out, nil
}
