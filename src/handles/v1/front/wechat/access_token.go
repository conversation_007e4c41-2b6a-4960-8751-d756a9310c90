package wechat

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_wechat"
)

func AccessToken(ctx *gin.Context) (any, error) {
	var data struct {
		AccessToken string  `json:"access_token"`
		ExpiresIn   float64 `json:"expires_in"`
	}

	token, err := my_wechat.Official().AccessToken.GetToken(false)
	if err != nil {
		return nil, err
	}
	data.AccessToken = token.AccessToken
	data.ExpiresIn = token.ExpiresIn

	return data, nil
}
