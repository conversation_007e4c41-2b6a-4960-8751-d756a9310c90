package product

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/product/internal/def"
	"roadtrip-api/src/utils"
)

// 价格日历
func Calendar(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ProductId   string               `form:"product_id" binding:"required"`
		ProductType constmap.ProductType `form:"product_type" binding:"required"`
		Start       string               `form:"start" binding:"required"`
		End         string               `form:"end"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var rsp = def.CalendarPriceResp{
		List: make([]def.CalendarPrice, 0),
	}
	if in.ProductType == constmap.ProductTypeZwyTicket {
		productId, err := convertor.ToInt(in.ProductId)
		if err != nil {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
		cli := my_ota.NewZwy()
		res, err := cli.QueryProductPrice(my_ota.ZwyProductPriceReq{
			ProductNo:     productId,
			TravelDate:    in.Start,
			EndTravelDate: in.End,
		})
		if err != nil {
			my_logger.Errorf("QueryProductPrice err", zap.Error(err))
			return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}
		for _, v := range res.TicketPrices {
			rsp.List = append(rsp.List, def.CalendarPrice{
				Date:        v.Date,
				MarketPrice: utils.CurrencyFloat2Int(v.MarketPrice),
				SalePrice:   utils.CurrencyFloat2Int(v.SalePrice),
				Num:         v.Num,
			})
		}
	}
	return rsp, nil
}
