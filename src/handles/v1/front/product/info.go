package product

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"strings"
)

// 产品详情
func Info(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ProductId   string               `form:"product_id" binding:"required"`   //产品ID
		ProductType constmap.ProductType `form:"product_type" binding:"required"` //产品类型
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	switch in.ProductType {

	case constmap.ProductTypeZwyTicket:
		return preorderZwyTicket(ctx, in.ProductId)
	}
	return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
}

func preorderZwyTicket(ctx *gin.Context, productId string) (any, error) {
	var err error
	cli := my_ota.NewZwy()
	productNo, err := convertor.ToInt(productId)
	if err != nil {
		my_logger.Errorf("convertor.ToInt(productId) fail", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	res, err := cli.QueryProductDetail(my_ota.ZwyProductDetailReq{
		ProductNo: productNo,
	})
	if err != nil {
		my_logger.Errorf("cli.QueryProductDetail fail", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	if res.TreeId != 0 {
		my_logger.Errorf("preorderZwyTicket: product not ticket", zap.String("productId", productId), zap.Int("treeId", res.TreeId))
		return nil, utils.NewErrorStr(constmap.ErrorParam, "产品非门票类型")
	}
	type idCardOption struct {
		Name  string          `json:"name"`
		Value constmap.IdType `json:"value"`
	}
	var rsp struct {
		Title       string         `json:"title"` //名称
		MarketPrice int64          `json:"market_price"`
		SalePrice   int64          `json:"sale_price"`
		IdCards     []idCardOption `json:"id_cards"` //支持的证件类型
		BuyNote     struct {
			ChargeInclude   string `json:"charge_include"`    //费用包含
			ChargeNoInclude string `json:"charge_no_include"` //费用不包含
			RefundNote      string `json:"refund_note"`       //退款说明
			CancelDay       int    `json:"cancel_day"`        //距离游玩日期多少天可以直退，值为0表示当日之前可退；值为正数，表示游玩日期前多少天可以退；值为负数，表示游玩日期后多少天以内可以退,跟退款规则字段有关
			CancelRules     any    `json:"cancel_rules"`      //退款规则
			ImportantNote   string `json:"important_note"`    //重要提示
		} `json:"buy_note"` //购买须知
		BuyMaxNum    int    `json:"buy_max_num"`    //最大预定数 0不限制
		BuyMinNum    int    `json:"buy_min_num"`    //最小预定数 0不限制
		BuyStartDay  int    `json:"buy_start_day"`  //提前多少天预定
		BuyStartDate string `json:"buy_start_date"` //最早游玩日期
		BuyMaxDay    int    `json:"buy_max_day"`    //最大可预订天数，0不限制，如果是具体的天数，预定的日期不能超过该值
		Peoples      struct {
			AdultNum int `json:"adult_num"` //成人数
			ChildNum int `json:"child_num"` //儿童数
			OldNum   int `json:"old_num"`   //老人数
		} `json:"peoples"` //人数配置
	}
	rsp.Title = res.ProductName
	rsp.MarketPrice = utils.CurrencyFloat2Int(res.MarketPrice)
	rsp.SalePrice = utils.CurrencyFloat2Int(res.SalePrice)
	rsp.IdCards = slice.FilterMap(strings.Split(res.CreditTypeSet, ","), func(index int, item string) (idCardOption, bool) {
		i, _ := convertor.ToInt(item)
		typ := my_ota.IdCardZwy2Yj(int(i))
		return idCardOption{
			Name:  typ.ToString(),
			Value: typ,
		}, typ > -1
	})
	rsp.BuyNote.ChargeInclude = res.ChargeInclude
	rsp.BuyNote.ChargeNoInclude = res.ChargeNoInclude
	rsp.BuyNote.RefundNote = res.RefundNote
	rsp.BuyNote.ImportantNote = res.ImportantNote
	rsp.BuyNote.CancelRules = res.CancelRules
	rsp.BuyNote.CancelDay = res.CancelDay
	rsp.BuyMaxNum = res.MaxNum
	rsp.BuyMinNum = res.StartNum
	rsp.BuyStartDay = res.StartDay
	rsp.BuyStartDate = res.StartDate
	rsp.BuyMaxDay = res.StartMaxDay
	rsp.Peoples.AdultNum = res.PeopleNum
	rsp.Peoples.ChildNum = res.ChildrenNum
	rsp.Peoples.OldNum = res.OldmanNum
	return rsp, nil
}
