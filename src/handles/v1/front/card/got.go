package card

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Got(ctx *gin.Context) (any, error) {
	var in struct {
		RelId string `form:"rel_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var rel models.FlowCardRelCode
	if err := db.Joins("Air").
		Joins("Advertiser").Joins("Code").
		Where("flow_card_rel_codes.user_id=? and flow_card_rel_codes.id=?", session.UserId, in.RelId).Take(&rel).Error; err != nil {
		return nil, err
	}

	var data struct {
		Code               string `json:"code"`
		AirlineName        string `json:"airline_name"`
		AirlineProductName string `json:"airline_product_name"`
		AdvertiserName     string `json:"advertiser_name"`
		ExpireTime         int64  `json:"expire_time"`
		Scope              string `json:"scope"`
		Desc               string `json:"desc"`
		Logo               string `json:"logo"`
	}

	data.Code = rel.Code.Code
	data.AirlineProductName = rel.Air.ProductName
	data.AirlineName = rel.Air.Name
	data.AdvertiserName = rel.Advertiser.Name
	data.ExpireTime = rel.Code.EndAt.Unix()
	data.Scope = rel.Air.Scope
	data.Desc = rel.Air.Desc
	data.Logo = utils.StaticUrl(rel.Air.Logo)

	return data, nil
}
