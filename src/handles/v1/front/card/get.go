package card

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Get(ctx *gin.Context) (any, error) {
	var in struct {
		Code    string `form:"code" binding:"required"`
		Airline string `form:"airline" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var user *models.User

	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		user = &session.User
	}

	db := utils.GetDB(ctx)

	card, err := wifis.GetCardByCode(db, in.Code, user)
	if err != nil {
		return nil, err
	}

	var data struct {
		RelId uint `json:"rel_id"`
	}

	rel, err := wifis.Get(db, user, card, in.Airline, ctx.ClientIP())
	if err != nil {
		return nil, err
	}

	data.RelId = rel.ID

	if user != nil {
		_ = my_queue.Light(constmap.EventAirCodeReward, gin.H{
			"code":    in.Code,
			"user_id": user.ID,
		})
	}

	return data, err
}
