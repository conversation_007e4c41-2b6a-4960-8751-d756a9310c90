package card

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/wifis"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Info(ctx *gin.Context) (any, error) {
	var in struct {
		Code string `form:"code" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var user *models.User

	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		user = &session.User
	}

	db := utils.GetDB(ctx)

	card, err := wifis.GetCardByCode(db, in.Code, user)
	if err != nil {
		return nil, err
	}

	type airlineItem struct {
		Name       string `json:"name"`
		Code       string `json:"code"`
		Logo       string `json:"logo"`
		LogoActive string `json:"logo_active"`
		Scope      string `json:"scope"`
		Desc       string `json:"desc"`
		Stock      int    `json:"stock"`
	}

	var data struct {
		Code           string        `json:"code"`
		AdvertiserName string        `json:"advertiser_name"`
		EndTime        int64         `json:"end_time"`
		StartTime      int64         `json:"start_time"`
		Airlines       []airlineItem `json:"airlines"`
		State          int           `json:"state"`
		GotState       int           `json:"got_state"`
		GetByMy        bool          `json:"get_by_my"`
		RelId          uint          `json:"rel_id"`
	}

	data.GetByMy = card.GetByMy
	data.Code = card.Card.Code
	data.AdvertiserName = card.Card.Advertiser.Name
	data.EndTime = card.Card.EndTime.Unix()
	data.StartTime = card.Card.StartTime.Unix()
	data.Airlines = make([]airlineItem, 0)
	data.State = card.Card.State
	data.GotState = card.GotState

	var airlines []models.FlowCardAirline
	db.Where("flow_card_id=?", card.Card.ID).Joins("Air").Find(&airlines)
	for _, airline := range airlines {
		data.Airlines = append(data.Airlines, airlineItem{
			Code:  airline.Code,
			Logo:  utils.StaticUrl(airline.Air.Logo),
			Scope: airline.Air.Scope,
			Desc:  airline.Air.Desc,
			Stock: airline.RemainingStock,
			Name:  airline.Air.Name,
		})
	}

	if data.GetByMy {
		data.RelId = card.RelId
	}

	return data, nil
}
