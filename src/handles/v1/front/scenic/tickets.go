package scenic

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/concurrent_slice"
	"roadtrip-api/src/utils/parallel_task"
)

// 门票列表
func Tickets(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ScenicId int `form:"scenic_id" binding:"required"`
	}
	type outItem struct {
		Ota         string               `json:"ota"`
		Title       string               `json:"title"` //套餐标题
		TicketId    string               `json:"ticket_id"`
		TicketType  string               `json:"ticket_type"`  //门票类型
		SalePrice   int64                `json:"sale_price"`   //零售价
		MarketPrice int64                `json:"market_price"` //市场价
		IsCancel    bool                 `json:"is_cancel"`    //是否支持退款
		CancelDay   int                  `json:"cancel_day"`   //支持退款天数
		ProductType constmap.ProductType `json:"product_type"` //产品类型
		Peoples     struct {
			AdultNum    int `json:"adult_num"` //成人数
			OldNum      int `json:"old_num"`   //老人数
			ChildrenNum int `json:"children_num"`
		} `json:"peoples"`
	}
	type out struct {
		ScenicId int       `json:"scenic_id"`
		List     []outItem `json:"list"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var rsp = out{
		ScenicId: in.ScenicId,
		List:     make([]outItem, 0),
	}
	pool := parallel_task.NewPool(5)
	defer pool.Release()
	db := utils.GetDB(ctx)
	otas := make([]models.ScenicOta, 0)
	db.Where("scenic_id=?", in.ScenicId).Find(&otas)
	if len(otas) == 0 {
		return rsp, nil
	}
	var ch = concurrent_slice.NewChan[outItem](len(otas) * 1000)
	//var chPrice = concurrent_slice.NewChan[float64](len(otas) * 1000)
	var otaZwy = my_ota.NewZwy()
	//var otaPrices = make([]float64, 0)
	for _, v := range otas {
		pool.AddTask(func(v models.ScenicOta) func() error {
			return func() error {
				if v.OtaCode == constmap.OtaCodeZwy {
					res, err := otaZwy.QueryProductList(my_ota.ZwyProductListReq{
						ViewId:    v.OtaId,
						TreeId:    "0",
						ResultNum: 20,
					})
					if err != nil {
						return fmt.Errorf("[zwy]%v", err)
					}
					for _, vv := range res.Results {
						var rspItem = outItem{
							Ota:         v.OtaCode,
							Title:       vv.ProductName,
							TicketId:    convertor.ToString(vv.ProductNo),
							SalePrice:   utils.CurrencyFloat2Int(vv.SalePrice),
							MarketPrice: utils.CurrencyFloat2Int(vv.MarketPrice),
							IsCancel:    vv.IsChangeask == 1,
							TicketType:  vv.TicketTypeName,
							ProductType: constmap.ProductTypeZwyTicket,
						}

						rspItem.Peoples.AdultNum = vv.PeopleNum
						rspItem.Peoples.OldNum = vv.OldmanNum
						rspItem.Peoples.ChildrenNum = vv.ChildrenNum

						if rspItem.IsCancel {
							rspItem.CancelDay = vv.CancelDay
						}

						//if vv.TicketTypeName == "普通票" {
						//	chPrice.Append(vv.SalePrice)
						//}
						ch.Append(rspItem)

					}
				}
				return nil
			}
		}(v))
	}
	if err = pool.Wait(); err != nil {
		my_logger.Errorf("parallel_task wait error", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	ch.ForEach(func(val outItem) {
		rsp.List = append(rsp.List, val)
	})
	//chPrice.ForEach(func(val float64) {
	//	otaPrices = append(otaPrices, val)
	//})
	//go func() {
	//	var scenic models.Scenic
	//	db.Select("id,cost_text").Where("id=?", in.ScenicId).Take(&scenic)
	//	if scenic.ID > 0 && len(otaPrices) > 0 {
	//		price := mathutil.Average(otaPrices...)
	//		scenic.CostText = fmt.Sprintf("%.2f", price)
	//		if db.Updates(scenic).RowsAffected == 0 {
	//			my_logger.Errorf("scenic cost_text update error", zap.Uint("id", scenic.ID), zap.Error(err))
	//		}
	//	}
	//}()
	return rsp, nil
}
