package def

import "roadtrip-api/src/beans"

type SearchResult struct {
	List  []SearchResultItem `json:"list"`
	Total int                `json:"total"`
}

type SearchSceneItem struct {
	IsFree bool               `json:"is_free"`
	Prices beans.ScenicPrices `json:"prices"`
}
type SearchResultItem struct {
	Id           uint             `json:"id"`
	Name         string           `json:"name"`
	Level        int              `json:"level"`
	ZoneId       uint             `json:"zone_id"`
	Lng          float64          `json:"lng"`
	Lat          float64          `json:"lat"`
	CityName     string           `json:"city_name"`
	Address      string           `json:"address"`
	CostText     string           `json:"cost_text"`
	Pic          string           `json:"pic"`
	ZoneDistance float64          `json:"zone_distance"` //距离市中心
	OpenTime     string           `json:"open_time"`     //开放时间
	Scene        *SearchSceneItem `json:"scene"`
	Tags         []string         `json:"tags"`
}
