package scenic

import (
	"encoding/json"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/scenic/internal/def"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
)

func Search(ctx *gin.Context) (any, error) {
	var in struct {
		Ids     string `form:"ids"`
		Keyword string `form:"keyword"`
		ZoneIds string `form:"zone_ids"`
		Tags    string `form:"tags"`
		Level   int    `form:"level"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	page, pageSize := utils.GetPage(ctx)

	conds := []any{
		map[string]any{
			"term": map[string]any{
				"type": constmap.PoiTypeScenic,
			},
		},
		map[string]any{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		map[string]any{
			"range": map[string]any{
				"level": map[string]any{
					"gt": 0,
				},
			},
		},
	}

	if !strutil.IsBlank(in.Keyword) {
		conds = append(conds, map[string]any{
			"match": map[string]any{
				"name": in.Keyword,
			},
		})
	}

	if !strutil.IsBlank(in.ZoneIds) {
		zoneIds, err := utils.ToArray[uint](in.ZoneIds, ",")
		if err != nil {
			return nil, utils.NewError(err)
		}
		conds = append(conds, map[string]any{
			"terms": map[string]any{
				"zone_id": zoneIds,
			},
		})
	}

	if !strutil.IsBlank(in.Tags) {
		tags := strutil.SplitEx(in.Tags, ",", true)
		if len(tags) > 0 {
			conds = append(conds, map[string]any{
				"terms": map[string]any{
					"tags": tags,
				},
			})
		}
	}

	if in.Level > 0 {
		conds = append(conds, map[string]any{
			"term": map[string]any{
				"level": in.Level,
			},
		})
	}

	if !strutil.IsBlank(in.Ids) {
		ids, err := utils.ToArray[uint](in.Ids, ",")
		if err != nil {
			return nil, utils.NewError(err)
		}
		conds = append(conds, map[string]any{
			"terms": map[string]any{
				"obj_id": ids,
			},
		})
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": conds,
			},
		},
		"sort": []map[string]any{
			{"level": map[string]any{"order": "desc"}},
			{"_score": map[string]any{"order": "desc"}},
		},
		"from": (page - 1) * pageSize,
		"size": pageSize,
	}
	searchResp, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return nil, utils.NewError(errors.New("搜索查询失败"))
	}

	rsp := def.SearchResult{
		Total: searchResp.Hits.Total.Value,
		List:  make([]def.SearchResultItem, 0),
	}

	var ids []uint
	for _, v := range searchResp.Hits.Hits {
		ids = append(ids, v.Source.ObjId)
	}
	db := utils.GetDB(ctx)
	var itemMap = make(map[uint]models.Scenic)
	if len(ids) > 0 {
		var items []models.Scenic
		db.Where("id in ?", ids).Find(&items)
		for _, v := range items {
			itemMap[v.ID] = v
		}
	}
	for _, v := range searchResp.Hits.Hits {
		ritem := def.SearchResultItem{
			Id:       v.Source.ObjId,
			Name:     v.Source.Name,
			Lat:      v.Source.Location[1],
			Lng:      v.Source.Location[0],
			CityName: v.Source.CityName,
			ZoneId:   v.Source.ZoneId,
			Level:    v.Source.Level,
			Scene:    new(def.SearchSceneItem),
			Tags:     make([]string, 0),
		}
		if vitem, ok := itemMap[v.Source.ObjId]; ok {
			ritem.CostText = vitem.CostText
			ritem.Pic = utils.StaticUrl(vitem.Pic)
			ritem.Address = vitem.Address
			ritem.OpenTime = vitem.OpenTime
			ritem.Scene.IsFree = vitem.IsFree == constmap.Enable
			if vitem.ScenicPrices != "" {
				_ = json.Unmarshal([]byte(vitem.ScenicPrices), &ritem.Scene.Prices)
			}

			if !strutil.IsBlank(vitem.Tags) {
				ritem.Tags = strutil.SplitEx(vitem.Tags, ",", true)
			}
		}
		rsp.List = append(rsp.List, ritem)
	}
	zoneIds := slice.Map(rsp.List, func(index int, item def.SearchResultItem) uint {
		return item.ZoneId
	})
	zoneMap := zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)

	slice.ForEach(rsp.List, func(index int, item def.SearchResultItem) {
		if z, ok := zoneMap[item.ZoneId]; ok {
			item.CityName = z.Name
			item.ZoneDistance = utils.CalculateDistance(z.Lng, z.Lat, item.Lng, item.Lat)
		}
		rsp.List[index] = item
	})
	return rsp, nil
}
