package scenic

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var scenic = new(models.Scenic)

	if err := db.Preload("Pics").Take(&scenic, in.Id).Error; err != nil {
		return nil, err
	}

	var data struct {
		CostText  string   `json:"cost_text"`
		OpenTime  string   `json:"open_time"`
		LevelText string   `json:"level_text"`
		Name      string   `json:"name"`
		Pic       string   `json:"pic"`
		Pics      []string `json:"pics"`
		Height    string   `json:"height"`
		Address   string   `json:"address"`
		Lng       float64  `json:"lng"`
		Lat       float64  `json:"lat"`
		Weather   struct {
			Temperature string `json:"temperature"`
			Desc        string `json:"desc"`
		} `json:"weather"`
		Desc           string                `json:"desc"`
		SuggestHour    string                `json:"suggest_hour"`
		PlayCosts      beans.ScenicPlayCosts `json:"play_costs"`
		Prices         beans.ScenicPrices    `json:"prices"`
		IsBooking      bool                  `json:"is_booking"` //需要预约
		BestPlayMonths []int                 `json:"best_play_months"`
		Id             uint                  `json:"id"`
		State          int                   `json:"state"`
		Tel            string                `json:"tel"`
		AvgPrice       int64                 `json:"avg_price"`
		IsFree         bool                  `json:"is_free"`
		Tags           []string              `json:"tags"`
		Scene          *beans.JTimelineScene `json:"scene"`
	}

	data.State = scenic.State
	data.Tel = scenic.Tel
	data.Id = scenic.ID
	data.CostText = scenic.CostText
	data.LevelText = scenics_biz.LevelText(scenic.Level)
	data.Lng = scenic.Lng
	data.Lat = scenic.Lat
	data.Name = scenic.Name
	data.Address = scenic.Address
	data.Desc = scenic.Desc
	data.OpenTime = scenic.OpenTime
	data.SuggestHour = scenic.SuggestHour
	data.IsBooking = scenic.IsBooking == constmap.Enable
	if scenic.PlayCosts != "" {
		_ = json.Unmarshal([]byte(scenic.PlayCosts), &data.PlayCosts)
	}
	if scenic.ScenicPrices != "" {
		_ = json.Unmarshal([]byte(scenic.ScenicPrices), &data.Prices)
	}
	data.BestPlayMonths = slice.FilterMap(strings.Split(scenic.BestPlayMonths, ","), func(index int, item string) (int, bool) {
		if item == "" {
			return 0, false
		} else if i, err := convertor.ToInt(item); err != nil {
			return 0, false
		} else {
			return int(i), true
		}
	})
	data.Pics = slice.Map(scenic.Pics, func(index int, item models.ScenicPic) string {
		return utils.StaticUrl(item.Url)
	})
	data.Pic = utils.StaticUrl(scenic.Pic)
	if data.Pic == "" && len(data.Pics) > 0 {
		data.Pic = data.Pics[0]
		go func() {
			db.Model(&scenic).Updates(models.Scenic{
				Pic: utils.UnWrapStaticUrl(data.Pic),
			})
		}()
	}
	data.Tags = slice.FilterMap(strings.Split(scenic.Tags, ","), func(index int, item string) (string, bool) {
		return item, !strutil.IsBlank(item)
	})
	data.IsFree = scenic.IsFree == constmap.Enable
	data.Scene = plan_biz.BuildJTimelineScene(scenic)
	if data.Scene.IsFree {
		data.Tags = utils.Unshift(data.Tags, "免费")
	} else {
		data.AvgPrice = utils.CurrencyFloat2Int(data.Prices.Adult.MonthlyPriceAvg)
	}

	return data, nil
}
