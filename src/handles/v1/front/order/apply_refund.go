package order

import (
	"encoding/json"
	"fmt"
	"math"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 申请退款
func ApplyRefund(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId     uint   `form:"order_id" binding:"required"`
		Details     string `form:"details" binding:"required"`
		ApplyReason string `form:"apply_reason"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	type refundDetail struct {
		OrderDetailId uint `json:"order_detail_id"`
		Quantity      int  `json:"quantity"`
	}

	var refunds []refundDetail
	if err := json.Unmarshal([]byte(in.Details), &refunds); err != nil {
		return nil, utils.NewError(err)
	}
	if len(refunds) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "applyRefund", convertor.ToString(in.OrderId)), constmap.TimeDur1m)
	defer unlocker()

	var order = new(models.Order)
	db.Preload("Details").Where(models.Order{
		UserId: session.UserId,
	}).Take(&order, in.OrderId)

	if order.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单不存在")
	}

	order.Details = beans_asm.OrderDetailLoadExt(db, order.ID, order.Details)
	canRefundOrder, err := orders.CanRefundDetails(db, order)
	if err != nil {
		return nil, utils.NewError(err)
	}
	if len(canRefundOrder.List) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单没有可退款的子项")
	}
	detailMap := slice.ReduceBy(order.Details, make(map[uint]models.OrderDetail), func(index int, item models.OrderDetail, agg map[uint]models.OrderDetail) map[uint]models.OrderDetail {
		agg[item.ID] = item
		return agg
	})
	refundOrderDetailMap := slice.ReduceBy(canRefundOrder.List, make(map[uint]beans.CanRefundDetail), func(index int, item beans.CanRefundDetail, agg map[uint]beans.CanRefundDetail) map[uint]beans.CanRefundDetail {
		agg[item.OrderDetailId] = item
		return agg
	})

	var applyRefundAmount int64
	var refundAmount int64
	var refundSubOrders []models.RefundSuborder
	for _, v := range refunds {
		if d, ok := refundOrderDetailMap[v.OrderDetailId]; !ok {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("订单子项%d不可退款", v.OrderDetailId))
		} else if v.Quantity > d.CanRefundQuantity {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("订单子项%d可退款数量为%d", v.OrderDetailId, d.CanRefundQuantity))
		} else {
			applyAmount := d.SkuPrice * int64(v.Quantity)
			amount := applyAmount
			detail := detailMap[v.OrderDetailId]
			if detail.OrderSubType == constmap.OrderSubTypeTuan && detail.Ext != nil {
				if ota, err := ota_asm.NewOta(constmap.OtaCodeYb); err != nil {
					//nothing
				} else if extTuan, err := ota.UnmarshalOrderExtTuan(detail.Ext.Ext); err != nil {
					//nothing
				} else {
					if extTuan.CanRefund != constmap.Enable {
						return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("订单子项%d不可退款", v.OrderDetailId))
					}
					days := int(utils.GetStartOfDay(time.Unix(detail.DateStart, 0)).Sub(utils.GetStartOfDay(time.Now())) / constmap.TimeDur1d)
					for _, vv := range extTuan.RefundRules {
						if days >= vv.Day {
							if vv.CanRefund != constmap.Enable {
								return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("订单子项%d天%d前不可退款", v.OrderDetailId, vv.Day))
							}
							amount = int64(math.Floor(float64(applyAmount) * vv.Percent))
							break
						}
					}
				}
			}

			applyRefundAmount += applyAmount
			refundAmount += amount
			refundSubOrders = append(refundSubOrders, models.RefundSuborder{
				OrderId:           order.ID,
				OrderDetailId:     v.OrderDetailId,
				State:             constmap.RefundSubOrderWaitReview,
				RefundQuantity:    v.Quantity,
				ApplyRefundAmount: applyAmount,
				RefundAmount:      amount,
				OtaRefundAmount:   utils.GetOtaPrice(d.OtaPriceRate, amount, false),
			})
		}
	}

	refundOrder := &models.RefundOrder{
		OrderId:           order.ID,
		UserId:            session.UserId,
		State:             constmap.RefundOrderWaitReview,
		ApplyReason:       in.ApplyReason,
		ApplyRefundAmount: applyRefundAmount,
		RefundAmount:      refundAmount,
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&refundOrder).Error; err != nil {
			return err
		}
		refundSubOrders = slice.Map(refundSubOrders, func(index int, item models.RefundSuborder) models.RefundSuborder {
			item.RefundOrderId = refundOrder.ID
			return item
		})
		if err := tx.Create(&refundSubOrders).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		RefundId uint `json:"refund_id"`
	}
	out.RefundId = refundOrder.ID
	return out, nil
}
