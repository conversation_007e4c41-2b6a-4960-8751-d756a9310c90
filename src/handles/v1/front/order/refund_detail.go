package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func RefundDetail(ctx *gin.Context) (any, error) {
	var in struct {
		RefundOrderId uint `form:"refund_order_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	type vitem struct {
		beans.CanRefundDetail
		State           constmap.RefundSubOrderState `json:"state"`
		StateText       string                       `json:"state_text"`
		RejectReason    string                       `json:"reject_reason"`
		RefundQuantity  int                          `json:"refund_quantity"`
		PreRefundAmount int64                        `json:"pre_refund_amount"` //预计退款金额
		RefundAmount    int64                        `json:"refund_amount"`     //实际退款金额
		PenaltyAmount   int64                        `json:"penalty_amount"`    //罚款金额
		Ticket          *beans.RefundTicket          `json:"ticket"`
		Hotel           *beans.RefundHotel           `json:"hotel"`
	}

	var out struct {
		PreRefundAmount int64                     `json:"pre_refund_amount"` //预计退款金额
		RefundAmount    int64                     `json:"refund_amount"`     //实际退款金额
		PenaltyAmount   int64                     `json:"penalty_amount"`    //罚款金额
		OrderId         uint                      `json:"order_id"`
		State           constmap.RefundOrderState `json:"state"`
		StateText       string                    `json:"state_text"`
		Details         []vitem                   `json:"details"`
	}

	var refundOrder models.RefundOrder
	db.Preload("RefundSuborders").
		Preload("RefundSuborders.OrderDetail").
		Where(models.RefundOrder{
			UserId: session.UserId,
		}).Take(&refundOrder, in.RefundOrderId)
	if refundOrder.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "退款订单不存在")
	}

	detailIds := slice.ReduceBy(refundOrder.RefundSuborders, make([]uint, 0), func(index int, item models.RefundSuborder, agg []uint) []uint {
		agg = append(agg, item.OrderDetailId)
		return agg
	})

	var exts []*models.OrderDetailExt
	db.Where("order_detail_id IN ?", detailIds).Find(&exts)

	extMap := slice.ReduceBy(exts, make(map[uint]*models.OrderDetailExt), func(index int, item *models.OrderDetailExt, agg map[uint]*models.OrderDetailExt) map[uint]*models.OrderDetailExt {
		agg[item.OrderDetailId] = item
		return agg
	})

	out.Details = slice.Map(refundOrder.RefundSuborders, func(index int, sub models.RefundSuborder) vitem {
		orderDetail := sub.OrderDetail
		vd := vitem{
			State:           sub.State,
			StateText:       business.RefundSubOrderStateText(sub.State),
			RejectReason:    sub.RejectReason,
			PreRefundAmount: sub.ApplyRefundAmount,
			RefundQuantity:  sub.RefundQuantity,
			CanRefundDetail: beans.CanRefundDetail{
				Quantity:      orderDetail.Quantity,
				OrderDetailId: sub.OrderDetailId,
				OrderSubType:  orderDetail.OrderSubType,
				ProductType:   orderDetail.ProductType,
				ProductId:     orderDetail.ProductId,
				SkuId:         orderDetail.SkuId,
				Pic:           utils.StaticUrl(orderDetail.Pic),
				ProductName:   orderDetail.ProductName,
				SkuName:       orderDetail.SkuName,
				SkuPrice:      orderDetail.SkuPrice,
				DateStart:     orderDetail.DateStart,
				DateEnd:       orderDetail.DateEnd,
			},
		}
		if sub.State == constmap.RefundSubOrderRefunded {
			vd.RefundAmount = sub.RefundAmount
			vd.PenaltyAmount = vd.PreRefundAmount - sub.RefundAmount
		}
		out.PreRefundAmount += vd.PreRefundAmount
		out.RefundAmount += vd.RefundAmount
		out.PenaltyAmount += vd.PenaltyAmount
		if detailExt, ok := extMap[sub.OrderDetailId]; ok {
			switch orderDetail.OrderSubType {
			case constmap.OrderSubTypeTicket:
				ticket, err := orders.BuildRefundTicket(orderDetail.ProductType, detailExt)
				if err != nil {
					my_logger.Errorf("BuildRefundTicket", zap.Error(err))
				}
				vd.Ticket = ticket
			case constmap.OrderSubTypeHotel:
				hotel, err := orders.BuildRefundHotel(orderDetail.ProductType, orderDetail.OtaPriceRate, detailExt)
				if err != nil {
					my_logger.Errorf("BuildRefundHotel", zap.Error(err))
				}
				vd.Hotel = hotel
			}
		}
		return vd
	})

	out.OrderId = refundOrder.OrderId
	out.State = refundOrder.State
	out.StateText = business.RefundOrderStateText(refundOrder.State)
	return out, nil
}
