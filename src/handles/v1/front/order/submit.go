package order

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/order/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/concurrent_slice"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/validators"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func Submit(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		ProductId string `form:"product_id"`
		//ProductType constmap.ProductType `form:"product_type"`
		OrderType constmap.OrderType `form:"order_type"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	session, _ := business.GetFrontLoginUser(ctx)
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "submitOrder", convertor.ToString(session.UserId)), constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()
	if in.OrderType == "" {
		in.OrderType = constmap.OrderTypeTuan
	}
	//if in.ProductType == "" {
	//	in.ProductType = constmap.ProductTypeTuan
	//}

	if in.OrderType == constmap.OrderTypeTuan {
		return submitOrderTuan(ctx)
	} else if in.OrderType == constmap.OrderTypeTicket {
		return submitOrderZwyTicket(ctx)
	} else if in.OrderType == constmap.OrderTypeHotel || in.OrderType == constmap.OrderTypeTravel {
		return submitTravelProduct(ctx)
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
}

func submitTravelProduct(ctx *gin.Context) (any, error) {
	var err error
	var in def.TravelOrderReq
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if !validators.IsMobile(in.ContactsTel) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "联系手机号码格式错误")
	}
	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	var products []beans.TravelProduct
	err = json.Unmarshal([]byte(in.Products), &products)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if len(products) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请至少选择一件产品")
	}

	tasks := parallel_task.NewPool(5)
	defer tasks.Release()
	ch := concurrent_slice.NewChan[*models.OrderDetail](len(products))

	for _, v := range products {
		if err = v.Check(); err != nil {
			return nil, utils.NewError(err)
		}

		tasks.AddTask(func(pv beans.TravelProduct) func() error {
			return func() error {
				detail, err := ota_asm.GenOrderDetail(db, pv.ProductType, session.UserId, pv)
				if err != nil {
					return err
				}
				ch.Append(detail)
				return nil
			}
		}(v))
	}
	if err = tasks.Wait(); err != nil {
		return nil, utils.NewError(err)
	}

	var orderDetails []*models.OrderDetail
	ch.ForEach(func(val *models.OrderDetail) {
		orderDetails = append(orderDetails, val)
	})
	totalAmount := slice.ReduceBy(orderDetails, 0, func(_ int, v *models.OrderDetail, agg int64) int64 {
		return agg + int64(v.Amount)
	})
	if totalAmount == 0 || totalAmount != in.TotalPrice {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "总价与传入价格不一致")
	}
	var date int64
	slice.ForEach(orderDetails, func(_ int, item *models.OrderDetail) {
		if date < item.DateEnd {
			date = item.DateEnd
		}
	})
	order := models.Order{
		UserId:        session.UserId,
		OrderType:     in.OrderType,
		ProductId:     0,
		ContactsName:  in.ContactsName,
		ContactsTel:   in.ContactsTel,
		Title:         orderDetails[0].SkuName,
		PayTimeExpire: convertor.ToPointer(datetime.AddMinute(time.Now(), 30)),
		TotalAmount:   (totalAmount),
		NeedPayAmount: (totalAmount),
		Pic:           orderDetails[0].Pic,
		Date:          time.Unix(date, 0),
	}
	if in.PlanId > 0 {
		var plan models.Plan
		db.Where(models.Plan{UserId: session.UserId}).Take(&plan, in.PlanId)
		if plan.ID > 0 {
			order.PlanId = in.PlanId
			order.Title = plan.Subject
		}
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if err = db.Create(&order).Error; err != nil {
			return err
		}

		slice.ForEach(orderDetails, func(_ int, v *models.OrderDetail) { v.OrderId = order.ID })
		if err = db.Create(&orderDetails).Error; err != nil {
			return err
		}
		exts := slice.FilterMap(orderDetails, func(_ int, v *models.OrderDetail) (*models.OrderDetailExt, bool) {
			var bl bool
			if v.Ext != nil {
				bl = true
				v.Ext.OrderId = v.OrderId
				v.Ext.OrderDetailId = v.ID
			}
			return v.Ext, bl
		})
		if len(exts) > 0 {
			if err = db.Create(&exts).Error; err != nil {
				return err
			}
		}
		var chThd = concurrent_slice.NewChan[models.OrderThirdOrder](len(orderDetails))
		for i := range orderDetails {
			tasks.AddTask(func(v *models.OrderDetail) func() error {
				return func() error {
					ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(v.ProductType))
					if err != nil {
						return err
					}
					orderResp, err := ota.OrderSubmit(&beans_asm.TravelOrderSubmitDto{
						OrderDetail:    v,
						OrderDetailExt: v.Ext,
						ContactsName:   in.ContactsName,
						ContactsTel:    in.ContactsTel,
					})
					if err != nil {
						return err
					}
					chThd.Append(orderResp.ThirdOrder)
					return nil
				}
			}(orderDetails[i]))
		}
		if err = tasks.Wait(); err != nil {
			return err
		}
		var thirdOrders []models.OrderThirdOrder
		chThd.ForEach(func(val models.OrderThirdOrder) {
			thirdOrders = append(thirdOrders, val)
		})
		if len(thirdOrders) > 0 {
			if err = db.Create(&thirdOrders).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}
	var submitResp = &def.SubmitOrderResp{
		NeedPay: order.NeedPayAmount > 0,
		OrderId: order.ID,
	}

	return submitResp, nil
}

func submitOrderTuan(ctx *gin.Context) (*def.SubmitOrderResp, error) {
	var err error
	var in struct {
		ProductId    uint64 `form:"product_id" binding:"required"`
		Date         int64  `form:"date" binding:"required"`
		Skus         string `form:"skus" binding:"required"`
		ContactsName string `form:"name" binding:"required"`
		ContactsTel  string `form:"tel" binding:"required"`
		Peoples      string `form:"peoples" binding:"required"`
	}
	var peoples []*beans.TravelPeople
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	} else if !validators.IsMobile(in.ContactsTel) {
		return nil, utils.NewErrorStr(constmap.ErrorMobile, constmap.ErrorMsgMobile)
	} else if err = json.Unmarshal([]byte(in.Peoples), &peoples); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	type s struct {
		Id       uint `json:"id"`
		Quantity int  `json:"quantity"`
	}
	var skus []s
	if err := json.Unmarshal([]byte(in.Skus), &skus); err != nil {
		return nil, err
	}
	var productSkus []models.TuanSku

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	now := time.Now()
	date := time.Unix(in.Date, 0)
	dateStr := date.Format(constmap.DateFmtLong)

	if date.IsZero() || now.After(date) || date.Format(constmap.DateFmtShort) <= now.Format(constmap.DateFmtShort) {
		return nil, utils.NewErrorStr(constmap.ErrorDateNotSupport, constmap.ErrorMsgDateNotSupport)
	}

	var product models.Tuan
	if err := db.
		Preload("Dates", "start<=? AND end>=?", dateStr, dateStr).
		Preload("Dates.Skus").
		Where("id=? and state=?", in.ProductId, constmap.Enable).Take(&product).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "商品已下架了")
	}

	skuIds := utils.SliceColumn(skus, func(val s) uint {
		return val.Id
	})

	db.Where("tuan_id=? and id in ?", product.ID, maputil.Keys(skuIds)).Find(&productSkus)
	if len(skus) != len(productSkus) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if product.NeedPeople == constmap.Enable {
		if len(peoples) == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请填写出行人信息")
		}
		var needPeopleNum int
		for _, sku := range productSkus {
			for _, v := range skus {
				if v.Id == sku.ID {
					needPeopleNum += v.Quantity * (sku.AdultNum + sku.ChildrenNum)
				}
			}
		}
		if needPeopleNum < len(peoples) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "出行人数信息不足")
		}
	}

	for i, v := range peoples {
		if v.IdType == constmap.IdTypeIdCard && !validators.IsIdCardNo(v.IdNo) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("旅客%d证件号码不正确", i+1))
		}
		if v.Name == "" {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("旅客%d姓名不能为空", i+1))
		}
	}

	//var c int64
	//if db.Model(&models.TuanDate{}).
	//	Where("product_id=? and date=?", product.ID, date).Count(&c); c == 0 {
	//	return nil, utils.NewErrorStr(constmap.ErrorDateNotSupport, constmap.ErrorMsgDateNotSupport)
	//}

	skuPriceMap := make(map[uint]models.TuanDateSku)

	if len(product.Dates) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorDateNotSupport, constmap.ErrorMsgDateNotSupport)
	}

	targetWeek := date.Weekday()
	if targetWeek == time.Sunday {
		targetWeek = 7
	}
	var dayStock int
	for _, v := range product.Dates {
		weeks, err := utils.ToArray[int](v.Weeks, ",")
		if err != nil {
			return nil, utils.NewError(err)
		}
		for _, wk := range weeks {
			if time.Weekday(wk) != targetWeek {
				continue
			}
			dayStock = v.Stock
			for _, sku := range v.Skus {
				skuPriceMap[sku.SkuId] = sku
			}
		}
	}
	var stock models.TuanDateStock
	db.Where("product_id=? AND date=?", product.ID, dateStr).Take(&stock)

	totalQuantity := slice.ReduceBy(skus, 0, func(index int, item s, agg int) int {
		return agg + item.Quantity
	})
	if stock.ID > 0 && stock.TotalStock-stock.SaleStock < totalQuantity || (totalQuantity > dayStock || dayStock == 0) {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "每日库存不足")
	}
	for _, v := range productSkus {
		if _, ok := skuPriceMap[v.ID]; !ok {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("规格[%s]不可购买.", v.Name))
		}
	}

	order := models.Order{
		UserId:        session.UserId,
		OrderType:     constmap.OrderTypeTuan,
		ProductId:     product.ID,
		ContactsName:  in.ContactsName,
		ContactsTel:   in.ContactsTel,
		Date:          date,
		Title:         product.Name,
		Pic:           utils.UnWrapStaticUrl(product.Pic),
		PayTimeExpire: convertor.ToPointer(datetime.AddDay(time.Now(), 3)),
	}
	totalAmount := int64(0)

	var refundRules []beans.TuanRefundRule
	if product.CanRefund == constmap.Enable && !strutil.IsBlank(product.RefundRules) {
		_ = json.Unmarshal([]byte(product.RefundRules), &refundRules)
	}

	details := slice.Map(productSkus, func(index int, item models.TuanSku) *models.OrderDetail {
		vs, _ := slice.FindBy(skus, func(index int, it s) bool {
			return it.Id == item.ID
		})
		skuPrice := skuPriceMap[item.ID]
		detail := &models.OrderDetail{
			OrderSubType: constmap.OrderSubTypeTuan,
			ProductType:  constmap.ProductTypeTuan,
			ProductName:  product.Name,
			ProductId:    product.ID,
			SkuId:        item.ID,
			SkuName:      item.Name,
			SkuPrice:     int64(skuPrice.Price),
			UserId:       order.UserId,
			Quantity:     vs.Quantity,
			Amount:       int64(skuPrice.Price * vs.Quantity),
			Pic:          order.Pic,
			DateStart:    date.Unix(),
			DateEnd:      date.Unix(),
		}
		totalAmount += detail.Amount

		b, _ := json.Marshal(beans.OrderDetailExtTuan{
			AdultNum:        item.AdultNum,
			ChildrenNum:     item.ChildrenNum,
			Price:           skuPrice.Price,
			SettlementPrice: skuPrice.SettlementPrice,
			CanRefund:       product.CanRefund,
			RefundRules:     refundRules,
		})
		detail.Ext = &models.OrderDetailExt{
			Ext: string(b),
		}

		return detail
	})

	order.TotalAmount = totalAmount
	order.NeedPayAmount = totalAmount

	orderPeoples := slice.Map(peoples, func(index int, item *beans.TravelPeople) *models.OrderPeople {
		return &models.OrderPeople{
			Name:         item.Name,
			Phone:        item.Phone,
			IdType:       item.IdType,
			IdNo:         item.IdNo,
			CustomerType: item.CustomerType,
		}
	})

	var parentUtask *models.UserTask
	taskMap := task_biz.LoadRelateTasks(db, []uint{product.ID}, constmap.TaskRelateTuan)
	if task, ok := taskMap[product.ID]; ok {
		parentUtask = &models.UserTask{
			UserId:          session.UserId,
			TaskId:          task.ID,
			State:           constmap.UserTaskProcessing,
			MaxTimes:        task.MaxTimes,
			CondTimes:       0,
			RealProductName: task.RealProductName,
			Version:         1,
		}
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if stock.ID == 0 {
			if err := tx.Create(&models.TuanDateStock{
				ProductId:  product.ID,
				Date:       date,
				TotalStock: dayStock,
				SaleStock:  totalQuantity,
			}).Error; err != nil {
				return err
			}
		} else {
			if tx.Model(&stock).Where("(total_stock-sale_stock)>=?", totalQuantity).Updates(map[string]any{
				"sale_stock": gorm.Expr("sale_stock+?", totalQuantity),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "库存扣减失败")
			}
		}
		if err = tx.Create(&order).Error; err != nil {
			return err
		}
		slice.ForEach(orderPeoples, func(index int, item *models.OrderPeople) { item.OrderId = order.ID })
		if len(orderPeoples) > 0 {
			if err := tx.Create(&orderPeoples).Error; err != nil {
				return err
			}
		}
		for i := range details {
			details[i].OrderId = order.ID
		}
		var exts []*models.OrderDetailExt
		if len(details) > 0 {
			if err := tx.Omit(clause.Associations).Create(&details).Error; err != nil {
				return err
			}
			exts = slice.ReduceBy(details, exts, func(index int, item *models.OrderDetail, agg []*models.OrderDetailExt) []*models.OrderDetailExt {
				if item.Ext != nil {
					item.Ext.OrderId = order.ID
					item.Ext.OrderDetailId = item.ID
					agg = append(agg, item.Ext)
				}
				return agg
			})
		}
		if len(exts) > 0 {
			if err := tx.Create(&exts).Error; err != nil {
				return err
			}
		}
		if parentUtask != nil {
			parentUtask.OrderId = order.ID
			if err := tx.Create(&parentUtask).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	var submitResp = &def.SubmitOrderResp{}
	submitResp.OrderId = order.ID
	submitResp.NeedPay = order.NeedPayAmount > 0

	return submitResp, err
}

func submitOrderZwyTicket(ctx *gin.Context) (*def.SubmitOrderResp, error) {
	var err error
	var in struct {
		ProductId    int64  `form:"product_id" binding:"required"`
		ScenicId     uint   `form:"scenic_id" binding:"required"`
		BookNum      int    `form:"book_num" binding:"required"` //预定数量
		Date         string `form:"date" binding:"required"`
		ContactsName string `form:"name" binding:"required"`
		ContactsTel  string `form:"tel" binding:"required"`
		Peoples      string `form:"peoples" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	var peoples []beans.TravelPeople
	err = json.Unmarshal([]byte(in.Peoples), &peoples)
	if err != nil {
		my_logger.Infof("submitOrderZwyTicket peoples unmarshal err", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if len(peoples) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请至少填写一名旅客信息")
	}
	for i, v := range peoples {
		if v.IdType == constmap.IdTypeIdCard {
			if !validators.IsIdCardNo(v.IdNo) {
				return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d人的证件号码错误", i+1))
			}
		}
	}
	var date time.Time
	if constmap.RegexDateFmtLong.MatchString(in.Date) {
		date, _ = time.ParseInLocation(constmap.DateFmtLong, in.Date, time.Local)
	} else {
		stamp, _ := convertor.ToInt(in.Date)
		date = time.Unix(stamp, 0)
	}

	if date.IsZero() || datetime.BeginOfDay(time.Now()).After(date) {
		return nil, utils.NewErrorStr(constmap.ErrorDateNotSupport, "日期不可选择")
	}
	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)
	pextra := beans.TravelProductExtraTicket{
		Date:  date.Unix(),
		OtaId: convertor.ToString(in.ProductId),
	}
	b, _ := json.Marshal(pextra)
	ota, err := ota_asm.NewOta(constmap.OtaCodeZwy)
	if err != nil {
		return nil, utils.NewError(err)
	}

	var scenic models.Scenic
	if err = db.Take(&scenic, in.ScenicId).Error; err != nil {
		return nil, err
	}

	detail, err := ota.GenOrderDetailTicket(db, session.UserId, beans.TravelProduct{
		ProductType: constmap.ProductTypeZwyTicket,
		ProductId:   in.ScenicId,
		Num:         in.BookNum,
		Peoples:     peoples,
		Extra:       b,
	})
	if err != nil {
		return nil, err
	}
	detail.Pic = utils.UnWrapStaticUrl(scenic.Pic)

	if err != nil {
		return nil, utils.NewError(err)
	}
	order := models.Order{
		UserId:        session.UserId,
		OrderType:     constmap.OrderTypeTicket,
		ProductId:     0,
		ContactsName:  in.ContactsName,
		ContactsTel:   in.ContactsTel,
		Date:          date,
		Title:         detail.SkuName,
		PayTimeExpire: convertor.ToPointer(datetime.AddMinute(time.Now(), 30)),
		TotalAmount:   detail.Amount,
		NeedPayAmount: detail.Amount,
		Pic:           utils.UnWrapStaticUrl(scenic.Pic),
	}
	var details []*models.OrderDetail
	details = append(details, detail)
	orderPeoples := slice.Map(peoples, func(index int, item beans.TravelPeople) models.OrderPeople {
		return models.OrderPeople{
			Name:   item.Name,
			Phone:  item.Phone,
			IdType: item.IdType,
			IdNo:   item.IdNo,
		}
	})

	var submitResp = &def.SubmitOrderResp{}
	submitResp.NeedPay = order.NeedPayAmount > 0

	err = db.Transaction(func(tx *gorm.DB) error {
		if err = tx.Create(&order).Error; err != nil {
			return err
		}

		details = slice.Map(details, func(index int, item *models.OrderDetail) *models.OrderDetail { item.OrderId = order.ID; return item })
		if len(details) > 0 {
			if err = tx.Create(&details).Error; err != nil {
				return err
			}
		}
		if detail.Ext != nil {
			detail.Ext.OrderDetailId = detail.ID
			detail.Ext.OrderId = order.ID
			if err = tx.Create(&detail.Ext).Error; err != nil {
				return err
			}
		}
		orderPeoples = slice.Map(orderPeoples, func(index int, item models.OrderPeople) models.OrderPeople { item.OrderId = order.ID; return item })
		if len(orderPeoples) > 0 {
			if err = tx.Create(orderPeoples).Error; err != nil {
				return err
			}
		}
		orderAddResp, err := ota.OrderSubmit(&beans_asm.TravelOrderSubmitDto{
			OrderDetail:    detail,
			OrderDetailExt: detail.Ext,
			ContactsTel:    in.ContactsTel,
			ContactsName:   in.ContactsName,
		})
		if err != nil {
			return err
		}
		if err = tx.Create(&orderAddResp.ThirdOrder).Error; err != nil {
			return err
		}
		submitResp.OrderId = order.ID
		return nil
	})
	if err != nil {
		if e, ok := err.(my_ota.ApiError); ok {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, e.Msg)
		}
		my_logger.Errorf("submitOrderZwyTicket fail", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "创建订单失败")
	}
	return submitResp, nil
}
