package order

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func PayConfirm(ctx *gin.Context) (interface{}, error) {
	var in struct {
		PaymentId uint `form:"pay_id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var data struct {
		PayStatus int  `json:"pay_status"`
		OrderId   uint `json:"order_id"`
	}
	var err error
	var orderPayment models.OrderPayment

	if err = db.Where("order_payments.id=?", in.PaymentId).First(&orderPayment).Error; err != nil {
		return nil, err
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		return orders.PayConfirm(&orderPayment, db)
	})

	if err != nil {
		return nil, err
	}
	_ = my_queue.Light(constmap.EventOrderPay, gin.H{
		"ids": fmt.Sprintf("%d", orderPayment.OrderId),
	})

	data.PayStatus = constmap.PayStatePayed
	data.OrderId = orderPayment.OrderId

	return data, err
}
