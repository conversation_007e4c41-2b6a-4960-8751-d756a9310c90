package order

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// 可退款子订单
func CanRefunds(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId uint `form:"order_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	order := new(models.Order)
	db.Preload("Details").
		Where(models.Order{
			UserId: session.UserId,
		}).Take(&order, in.OrderId)

	order.Details = beans_asm.OrderDetailLoadExt(db, order.ID, order.Details)

	refundOrder, err := orders.CanRefundDetails(db, order)
	if err != nil {
		return nil, utils.NewError(err)
	}

	var orderPeoples []models.OrderPeople
	db.Where(models.OrderPeople{OrderId: order.ID}).Find(&orderPeoples)

	var detailExts []*models.OrderDetailExt
	db.Where(models.OrderDetailExt{OrderId: refundOrder.OrderId}).Find(&detailExts)
	detailExtMap := slice.ReduceBy(detailExts, make(map[uint]*models.OrderDetailExt), func(index int, item *models.OrderDetailExt, agg map[uint]*models.OrderDetailExt) map[uint]*models.OrderDetailExt {
		agg[item.OrderDetailId] = item
		return agg
	})

	type vitem struct {
		beans.CanRefundDetail
		MinRefundQuantity int                  `json:"min_refund_quantity"`
		Peoples           []beans.TravelPeople `json:"peoples"`
		Tuan              *beans.RefundTuan    `json:"tuan"`
		Ticket            *beans.RefundTicket  `json:"ticket"`
		Hotel             *beans.RefundHotel   `json:"hotel"`
		RefundRules       []string             `json:"refund_rules"`
	}

	var out struct {
		OrderId      uint    `json:"order_id"`
		PayAmount    int64   `json:"pay_amount"`
		TotalAmount  int64   `json:"total_amount"`
		CreatedAt    int64   `json:"created_at"`
		ContactsName string  `json:"contacts_name"`
		ContactsTel  string  `json:"contacts_tel"`
		List         []vitem `json:"list"`
	}

	out.OrderId = refundOrder.OrderId
	out.PayAmount = refundOrder.PayAmount
	out.TotalAmount = refundOrder.TotalAmount
	out.CreatedAt = refundOrder.CreatedAt
	out.ContactsName = refundOrder.ContactsName
	out.ContactsTel = refundOrder.ContactsTel
	out.List = slice.Map(refundOrder.List, func(index int, item beans.CanRefundDetail) vitem {
		v := vitem{
			CanRefundDetail:   item,
			MinRefundQuantity: item.CanRefundQuantity,
			Peoples:           make([]beans.TravelPeople, 0),
			RefundRules:       make([]string, 0),
		}
		if item.OrderSubType == constmap.OrderSubTypeTuan {
			v.MinRefundQuantity = 1
		}
		slice.ForEach(orderPeoples, func(index int, item models.OrderPeople) {
			v.Peoples = append(v.Peoples, beans.TravelPeople{
				Name:         item.Name,
				Phone:        item.Phone,
				IdType:       item.IdType,
				IdNo:         item.IdNo,
				CustomerType: item.CustomerType,
			})
		})

		if ext, ok := detailExtMap[item.OrderDetailId]; ok {
			peoples, _ := beans_asm.UnmarshalPeoplesFrmDetailExt(ext)
			v.Peoples = append(v.Peoples, peoples...)

			switch item.OrderSubType {
			case constmap.OrderSubTypeTuan:
				tuan, err := orders.BuildRefundTuan(item.ProductType, ext)
				if err != nil {
					my_logger.Errorf("BuildRefundTuan", zap.Error(err))
				} else {
					v.Tuan = tuan
					v.RefundRules = orders.BuildTuanRefundRules(tuan.CanRefund, tuan.RefundRules)
				}
			case constmap.OrderSubTypeTicket:
				ticket, err := orders.BuildRefundTicket(item.ProductType, ext)
				if err != nil {
					my_logger.Errorf("BuildRefundTicket", zap.Error(err))
				} else {
					v.Ticket = ticket
					v.RefundRules = orders.BuildTicketRefundRules(ticket.CanRefund, ticket.CancelDay)
				}
			case constmap.OrderSubTypeHotel:
				hotel, err := orders.BuildRefundHotel(item.ProductType, item.OtaPriceRate, ext)
				if err != nil {
					my_logger.Errorf("BuildRefundHotel", zap.Error(err))
				} else {
					v.Hotel = hotel
					v.RefundRules = orders.BuildHotelRefundRules(hotel)
				}
			}
		}
		return v
	})

	return out, nil
}
