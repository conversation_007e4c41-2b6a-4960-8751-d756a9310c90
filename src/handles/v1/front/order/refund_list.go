package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

// 售后订单列表
func RefundList(ctx *gin.Context) (any, error) {
	var in struct {
		RefundIds string `form:"refund_ids"`
		OrderIds  string `form:"order_ids"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	type detail struct {
		beans.CanRefundDetail
		State           constmap.RefundSubOrderState `json:"state"`
		StateText       string                       `json:"state_text"`
		RefundQuantity  int                          `json:"refund_quantity"`
		PreRefundAmount int64                        `json:"pre_refund_amount"` //预计退款金额
		RefundAmount    int64                        `json:"refund_amount"`     //实际退款金额
		Ticket          *beans.RefundTicket          `json:"ticket"`
		Hotel           *beans.RefundHotel           `json:"hotel"`
	}
	type vitem struct {
		PreRefundAmount int64                     `json:"pre_refund_amount"` //预计退款金额
		RefundAmount    int64                     `json:"refund_amount"`     //实际退款金额
		RefundOrderId   uint                      `json:"refund_order_id"`
		OrderId         uint                      `json:"order_id"`
		OrderType       constmap.OrderType        `json:"order_type"`
		Title           string                    `json:"name"`
		RefundCreatedAt int64                     `json:"refund_created_at"`
		State           constmap.RefundOrderState `json:"state"`
		StateText       string                    `json:"state_text"`
		PlanId          uint                      `json:"plan_id"`
		Details         []detail                  `json:"details"`
	}

	var out struct {
		Total int64   `json:"total"`
		List  []vitem `json:"list"`
	}
	out.List = make([]vitem, 0)

	where := strings.Builder{}
	var whereArgs []any
	where.WriteString("1=1")

	if !strutil.IsBlank(in.OrderIds) {
		ids, _ := utils.ToArray[uint](in.OrderIds, ",")
		if len(ids) > 0 {
			where.WriteString(" AND order_id IN ?")
			whereArgs = append(whereArgs, ids)
		}
	}

	if !strutil.IsBlank(in.RefundIds) {
		ids, _ := utils.ToArray[uint](in.RefundIds, ",")
		if len(ids) > 0 {
			where.WriteString(" AND id IN ?")
			whereArgs = append(whereArgs, ids)
		}
	}

	var list []models.RefundOrder
	db.Model(&list).Where(models.RefundOrder{
		UserId: session.UserId,
	}).
		Where(where.String(), whereArgs...).
		Count(&out.Total).
		Joins("Order").
		Preload("RefundSuborders.OrderDetail").
		Order("id DESC").Offset((page - 1) * pageSize).Limit(pageSize).
		Find(&list)
	if len(list) > 0 {
		detailIds := slice.ReduceBy(list, make([]uint, 0), func(index int, item models.RefundOrder, agg []uint) []uint {
			slice.ForEach(item.RefundSuborders, func(index int, v models.RefundSuborder) {
				agg = append(agg, v.OrderDetailId)
			})
			return agg
		})
		var exts []*models.OrderDetailExt
		db.Where("order_detail_id IN ?", detailIds).Find(&exts)
		extMap := slice.ReduceBy(exts, make(map[uint]*models.OrderDetailExt), func(index int, item *models.OrderDetailExt, agg map[uint]*models.OrderDetailExt) map[uint]*models.OrderDetailExt {
			agg[item.OrderDetailId] = item
			return agg
		})
		out.List = slice.Map(list, func(index int, item models.RefundOrder) vitem {
			v := vitem{
				RefundOrderId:   item.ID,
				OrderId:         item.OrderId,
				OrderType:       item.Order.OrderType,
				Title:           item.Order.Title,
				RefundCreatedAt: item.CreatedAt.Unix(),
				State:           item.State,
				StateText:       business.RefundOrderStateText(item.State),
				PlanId:          item.Order.PlanId,
			}
			v.Details = slice.Map(item.RefundSuborders, func(index int, sub models.RefundSuborder) detail {
				orderDetail := sub.OrderDetail
				vd := detail{
					State:           sub.State,
					StateText:       business.RefundSubOrderStateText(sub.State),
					PreRefundAmount: sub.ApplyRefundAmount,
					RefundQuantity:  sub.RefundQuantity,
					CanRefundDetail: beans.CanRefundDetail{
						Quantity:      orderDetail.Quantity,
						OrderDetailId: sub.OrderDetailId,
						OrderSubType:  orderDetail.OrderSubType,
						ProductType:   orderDetail.ProductType,
						ProductId:     orderDetail.ProductId,
						SkuId:         orderDetail.SkuId,
						Pic:           utils.StaticUrl(orderDetail.Pic),
						ProductName:   orderDetail.ProductName,
						SkuName:       orderDetail.SkuName,
						SkuPrice:      orderDetail.SkuPrice,
						DateStart:     orderDetail.DateStart,
						DateEnd:       orderDetail.DateEnd,
					},
				}
				if sub.State == constmap.RefundSubOrderRefunded {
					vd.RefundAmount = sub.RefundAmount
				}
				v.PreRefundAmount += vd.PreRefundAmount
				v.RefundAmount += vd.RefundAmount
				if detailExt, ok := extMap[sub.OrderDetailId]; ok {
					switch orderDetail.OrderSubType {
					case constmap.OrderSubTypeTicket:
						ticket, err := orders.BuildRefundTicket(orderDetail.ProductType, detailExt)
						if err != nil {
							my_logger.Errorf("BuildRefundTicket", zap.Error(err))
						}
						vd.Ticket = ticket
					case constmap.OrderSubTypeHotel:
						hotel, err := orders.BuildRefundHotel(orderDetail.ProductType, orderDetail.OtaPriceRate, detailExt)
						if err != nil {
							my_logger.Errorf("BuildRefundHotel", zap.Error(err))
						}
						vd.Hotel = hotel
					}
				}
				return vd
			})
			return v
		})
	}
	return out, nil
}
