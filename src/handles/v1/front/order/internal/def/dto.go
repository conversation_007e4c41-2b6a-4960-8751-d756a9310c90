package def

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
)

type SubmitOrderResp struct {
	OrderId uint `json:"order_id"`
	NeedPay bool `json:"need_pay"`
}

type IdTypeOption struct {
	Label string          `json:"label"`
	Value constmap.IdType `json:"value"`
}
type PreOrderResp struct {
	Title       string                            `json:"title"`
	TotalPrice  int64                             `json:"total_price"`
	ValidIdType []IdTypeOption                    `json:"valid_id_type"`
	SubOrder    []beans.IFaceHotelOrderPreloadRes `json:"sub_order"`
}
type PreOrderReq struct {
	Products string `form:"products" binding:"required"`
}

type TravelOrderReq struct {
	OrderType    constmap.OrderType `form:"order_type"`
	PlanId       uint               `form:"plan_id"`
	TotalPrice   int64              `form:"total_price" binding:"required"` //应付总价，服务端会对传入价做校验
	Products     string             `form:"products" binding:"required"`    //产品列表
	ContactsName string             `form:"name" binding:"required"`        //联系人
	ContactsTel  string             `form:"tel" binding:"required"`         //联系电话
}
