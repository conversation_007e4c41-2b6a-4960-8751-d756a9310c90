package order

import (
	"encoding/json"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId string `form:"id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	type dHotel struct {
		Id   uint    `json:"id"`
		Name string  `json:"name"`
		Addr string  `json:"addr"`
		Lng  float64 `json:"lng"`
		Lat  float64 `json:"lat"`
		Tel  string  `json:"tel"`
	}
	type dHotelOrder struct {
		dHotel
		Start int64            `json:"start"`
		End   int64            `json:"end"`
		Room  *beans.HotelRoom `json:"room"`
	}
	type dScenic struct {
		Id       uint    `json:"id"`
		Name     string  `json:"name"`
		Lng      float64 `json:"lng"`
		Lat      float64 `json:"lat"`
		Address  string  `json:"address"`
		Tel      string  `json:"tel"`
		CostText string  `json:"cost_text"`
		Pic      string  `json:"pic"`
		Level    int     `json:"level"`
	}
	type d struct {
		OrderSubType constmap.OrderSubType   `json:"order_sub_type"`
		ProductType  constmap.ProductType    `json:"product_type"`
		State        int                     `json:"state"`
		StateText    string                  `json:"state_text"`
		Amount       int64                   `json:"amount"`
		Quantity     int                     `json:"quantity"`
		SkuName      string                  `json:"sku_name"`
		Pic          string                  `json:"pic"`
		ProductId    uint                    `json:"product_id"`
		Peoples      []beans.TravelPeople    `json:"peoples"`
		FeeItems     []beans.SubOrderFeeItem `json:"fee_items"`
		Scenic       *dScenic                `json:"scenic"`
		Hotel        *dHotelOrder            `json:"hotel"`
		DateStart    int64                   `json:"date_start"`
		DateEnd      int64                   `json:"date_end"`
	}

	var data struct {
		Id            uint                 `json:"id"`
		OrderType     constmap.OrderType   `json:"order_type"`
		PlanId        uint                 `json:"plan_id"`
		PayState      int                  `json:"pay_state"`
		CanPay        bool                 `json:"can_pay"`    //是否可支付
		CanCancel     bool                 `json:"can_cancel"` //是否可取消
		CanRefund     bool                 `json:"can_refund"` //是否可退款
		Title         string               `json:"title"`
		CreatedAt     int64                `json:"created_at"`
		Details       []d                  `json:"details"`
		TotalAmount   int64                `json:"total_amount"`
		State         int                  `json:"state"`
		StateText     string               `json:"state_text"`
		PayTime       int64                `json:"pay_time"`
		ContactsName  string               `json:"contacts_name"`
		ContactsTel   string               `json:"contacts_tel"`
		Date          string               `json:"date"`
		Pic           string               `json:"pic"`
		Peoples       []beans.TravelPeople `json:"peoples"`
		PayTimeExpire string               `json:"pay_time_expire"`
		ProductId     uint                 `json:"product_id"`
		HasTask       bool                 `json:"has_task"`
	}
	data.Peoples = make([]beans.TravelPeople, 0)

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var order = new(models.Order)
	if err := db.Preload("Details").
		Preload("Peoples").
		Where(models.Order{UserId: session.UserId}).
		Take(&order, in.OrderId).Error; err != nil {
		return nil, err
	}
	order.Details = beans_asm.OrderDetailLoadExt(db, order.ID, order.Details)

	data.Id = order.ID
	data.PlanId = order.PlanId
	data.Title = order.Title
	data.OrderType = order.OrderType
	data.CreatedAt = order.CreatedAt.Unix()
	data.TotalAmount = order.TotalAmount
	data.State = order.State
	data.PayState = order.PayState
	data.StateText = orders.OrderStateText(order.State)
	data.CanPay = orders.CanPay(order)
	data.CanCancel = orders.CanCancel(order)
	data.PayTime = order.PayTime.Unix()
	data.ProductId = order.ProductId
	if order.PayTimeExpire != nil && !order.PayTimeExpire.IsZero() {
		data.PayTimeExpire = order.PayTimeExpire.Format(constmap.DateFmtLongFull)
	}
	data.ContactsName = order.ContactsName
	data.ContactsTel = order.ContactsTel
	data.Date = order.Date.Format("2006-01-02")
	data.Pic = utils.StaticUrl(order.Pic)
	data.Peoples = slice.Map(order.Peoples, func(_ int, v models.OrderPeople) beans.TravelPeople {
		return beans.TravelPeople{
			Name:   v.Name,
			Phone:  v.Phone,
			IdType: v.IdType,
			IdNo:   v.IdNo,
		}
	})
	data.Details = slice.Map(order.Details, func(index int, item models.OrderDetail) d {
		detail := d{
			OrderSubType: item.OrderSubType,
			ProductType:  item.ProductType,
			Quantity:     item.Quantity,
			SkuName:      item.SkuName,
			Amount:       item.Amount,
			ProductId:    item.ProductId,
			Pic:          utils.StaticUrl(item.Pic),
			DateStart:    item.DateStart,
			DateEnd:      item.DateEnd,
			State:        item.State,
			StateText:    orders.OrderDetailStateText(item.State),
		}

		if item.Ext == nil {
			return detail
		}
		detail.Peoples, _ = beans_asm.UnmarshalPeoplesFrmDetailExt(item.Ext)
		switch item.OrderSubType {
		case constmap.OrderSubTypeTicket:
			detail.Peoples = utils.If(len(detail.Peoples) > 0, detail.Peoples, data.Peoples)
			var scenic models.Scenic
			db.Take(&scenic, item.ProductId)
			detail.Scenic = &dScenic{
				Id:       scenic.ID,
				Name:     scenic.Name,
				Lng:      scenic.Lng,
				Lat:      scenic.Lat,
				Address:  scenic.Address,
				Tel:      scenic.Tel,
				CostText: scenic.CostText,
				Pic:      utils.StaticUrl(scenic.Pic),
				Level:    scenic.Level,
			}
		case constmap.OrderSubTypeHotel:
			var ext beans.OrderDetailExtHotel
			_ = json.Unmarshal([]byte(item.Ext.Ext), &ext)
			var hotel models.Hotel
			db.Take(&hotel, ext.ProductId)
			ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(item.ProductType))
			if err != nil {
				my_logger.Errorf("NewOta", zap.Uint("orderId", order.ID), zap.Uint("orderDetailId", item.ID), zap.Error(err))
				return detail
			}
			slice.ForEach(ext.Room.Policy, func(index int, vv beans.HotelRoomPolicy) {
				ext.Room.Policy[index] = ota.LoadHotelPolicyPrice(vv, item.OtaPriceRate)
			})
			hotelInfo := &dHotelOrder{
				dHotel: dHotel{
					Id:   hotel.ID,
					Name: hotel.Name,
					Addr: hotel.Address,
					Lng:  hotel.Lng,
					Lat:  hotel.Lat,
					Tel:  hotel.Tel,
				},
				Start: ext.Start,
				End:   ext.End,
				Room:  ext.Room,
			}
			detail.FeeItems = ext.FeeItems
			detail.Hotel = hotelInfo
		}
		return detail
	})

	if order.OrderType == constmap.OrderTypeTuan && slice.Contain([]int{constmap.OrderStatePayed, constmap.OrderStateComplete}, order.State) {
		taskMap := task_biz.LoadRelateTasks(db, []uint{order.ProductId}, constmap.TaskRelateTuan)
		data.HasTask = len(taskMap) > 0
	}

	refundOrder, _ := orders.CanRefundDetails(db, order)
	data.CanRefund = refundOrder != nil && len(refundOrder.List) > 0

	return data, nil
}
