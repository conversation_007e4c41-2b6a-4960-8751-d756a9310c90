package order

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 取消订单
func Cancel(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var order = new(models.Order)
	db.Where(models.Order{UserId: session.UserId}).Take(&order, in.Id)

	if order.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单不存在")
	}

	if !orders.CanCancel(order) {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "订单不可取消")
	}

	if err := orders.CloseOrder(db, order, "用户主动取消"); err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Id    uint `json:"id"`
		State int  `json:"state"`
	}
	out.Id = order.ID
	out.State = order.State
	return out, nil
}
