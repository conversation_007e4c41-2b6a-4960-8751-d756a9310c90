package order

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type string `form:"type"`
	}

	_ = ctx.ShouldBind(&in)
	page, pageSize := utils.GetPage(ctx)

	type oitem struct {
		ProductId    uint                   `json:"product_id"`
		ProductType  constmap.ProductType   `json:"product_type"`
		OrderSubType constmap.OrderSubType  `json:"order_sub_type"`
		ProductName  string                 `json:"product_name"`
		SkuId        uint                   `json:"sku_id"`
		SkuName      string                 `json:"sku_name"`
		Pic          string                 `json:"pic"`
		Quantity     int                    `json:"quantity"`
		Amount       int64                  `json:"amount"`
		Address      string                 `json:"address"`
		DateStart    constmap.DateUnixStamp `json:"date_start"`
		DateEnd      constmap.DateUnixStamp `json:"date_end"`
	}
	type item struct {
		OrderId       uint               `json:"order_id"`
		OrderType     constmap.OrderType `json:"order_type"`
		OrderTypeText string             `json:"order_type_text"`
		PlanId        uint               `json:"plan_id"`
		State         int                `json:"state"`
		StateText     string             `json:"state_text"`
		Title         string             `json:"title"`
		Date          int64              `json:"date"`
		CreatedAt     int64              `json:"created_at"`
		TotalAmount   int64              `json:"total_amount"`
		CanPay        bool               `json:"can_pay"`
		CanCancel     bool               `json:"can_cancel"`
		PayTimeExpire int64              `json:"pay_time_expire"`
		Items         []oitem            `json:"items"`
	}

	var data struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}

	data.List = make([]item, 0)

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	whereStr := strings.Builder{}

	whereStr.WriteString("orders.user_id=? ")
	whereArgs := []any{session.UserId}

	switch in.Type {
	case "not": //待支付
		whereStr.WriteString(" and pay_state in ? and orders.state in ?")
		whereArgs = append(whereArgs, []int{constmap.PayStateNotPay, constmap.PayStatePartPay}, []int{constmap.OrderStateNotPay, constmap.OrderStatePayEarnest})
	case "pay": //已支付
		whereStr.WriteString(" and pay_state in ? and orders.state in ?")
		whereArgs = append(whereArgs, []int{constmap.PayStatePayed}, []int{constmap.OrderStatePayed, constmap.OrderStateProcessing, constmap.OrderStateSuccess})
	case "complete": //已完成
		whereStr.WriteString(" and orders.state=?")
		whereArgs = append(whereArgs, constmap.OrderStateComplete)
	}

	db.Model(&models.Order{}).Where(whereStr.String(), whereArgs...).Count(&data.Total)

	var list []*models.Order
	db.Preload("Details").
		Where(whereStr.String(), whereArgs...).
		Order("orders.id desc").
		Limit(pageSize).Offset((page - 1) * pageSize).
		Find(&list)

	var hotelIds []uint
	slice.ForEach(list, func(_ int, order *models.Order) {
		slice.ForEach(order.Details, func(_ int, detail models.OrderDetail) {
			switch detail.OrderSubType {
			case constmap.OrderSubTypeHotel:
				hotelIds = append(hotelIds, detail.ProductId)
			}
		})
	})

	var hotels []models.Hotel
	if len(hotelIds) > 0 {
		db.Find(&hotels, hotelIds)
	}
	hotelMap := slice.ReduceBy(hotels, make(map[uint]models.Hotel), func(_ int, item models.Hotel, agg map[uint]models.Hotel) map[uint]models.Hotel {
		agg[item.ID] = item
		return agg
	})

	data.List = slice.Map(list, func(_ int, order *models.Order) item {
		it := item{
			OrderType:     order.OrderType,
			OrderTypeText: business.OrderTypeText(order.OrderType),
			OrderId:       order.ID,
			State:         order.State,
			PlanId:        order.PlanId,
			StateText:     orders.OrderStateText(order.State),
			Title:         order.Title,
			CreatedAt:     order.CreatedAt.Unix(),
			Date:          order.Date.Unix(),
			TotalAmount:   order.TotalAmount,
			CanPay:        orders.CanPay(order),
			CanCancel:     orders.CanCancel(order),
		}
		if it.CanPay {
			it.PayTimeExpire = order.PayTimeExpire.Unix()
		}
		it.Items = slice.Map(order.Details, func(_ int, item models.OrderDetail) oitem {
			v := oitem{
				ProductType:  item.ProductType,
				OrderSubType: item.OrderSubType,
				ProductId:    item.ProductId,
				ProductName:  item.ProductName,
				SkuId:        item.SkuId,
				SkuName:      item.SkuName,
				Pic:          utils.StaticUrl(item.Pic),
				Quantity:     item.Quantity,
				Amount:       item.Amount,
				DateStart:    constmap.DateUnixStamp{Time: time.Unix(item.DateStart, 0)},
				DateEnd:      constmap.DateUnixStamp{Time: time.Unix(item.DateEnd, 0)},
			}
			if strutil.IsBlank(v.ProductName) {
				v.ProductName = v.SkuName
			}
			switch item.OrderSubType {
			case constmap.OrderSubTypeHotel:
				if hotel, ok := hotelMap[item.ProductId]; ok {
					v.Address = hotel.Address
				}
			}
			return v
		})
		return it
	})

	return data, nil
}
