package order

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Pay(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId uint `form:"order_id" binding:"required"`
		Method  int  `form:"method"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	if in.Method == 0 {
		in.Method = constmap.PayMethodWeixin
	}

	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)

	var order models.Order
	if err := db.Take(&order, in.OrderId).Error; err != nil {
		return nil, err
	} else if order.PayState != constmap.PayStateNotPay {
		return nil, utils.NewErrorStr(constmap.ErrorState, constmap.ErrorMsgState)
	}

	payment := models.OrderPayment{
		OrderId:       order.ID,
		UserId:        order.UserId,
		NeedPayAmount: order.NeedPayAmount,
		PayMethod:     in.Method,
		PaymentType:   constmap.PaymentTypeNormal,
	}

	var err error
	var resp *beans.PayStr

	err = db.Transaction(func(tx *gorm.DB) error {
		resp, err = orders.CreatePayment(db, &payment, &session.User, order.Title)
		return err
	})

	if err != nil {
		return nil, err
	}

	return resp, err
}
