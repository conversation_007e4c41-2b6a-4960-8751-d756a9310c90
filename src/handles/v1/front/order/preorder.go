package order

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/order/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/concurrent_slice"
	"roadtrip-api/src/utils/parallel_task"
)

// 订单预览
func PreOrder(ctx *gin.Context) (any, error) {
	var err error
	var in def.PreOrderReq
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var products []beans.TravelProduct
	err = json.Unmarshal([]byte(in.Products), &products)
	if err != nil {
		return nil, utils.NewError(err)
	}
	if len(products) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请至少选择一件产品")
	}

	var out = new(def.PreOrderResp)
	tasks := parallel_task.NewPool(3)
	defer tasks.Release()
	ch := concurrent_slice.NewChan[beans.IFaceHotelOrderPreloadRes](len(products))

	for _, v := range products {
		if err = v.Check(); err != nil {
			return nil, utils.NewError(err)
		}

		tasks.AddTask(func(v beans.TravelProduct) func() error {
			return func() error {
				ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(v.ProductType))
				if err != nil {
					return err
				}
				extra, err := beans.GetProductExtraHotel(v)
				if err != nil {
					return err
				}
				otaHotelProduct := beans.HotelOrderPreloadDto{
					HotelId:         v.ProductId,
					Num:             v.Num,
					OtaId:           extra.OtaId,
					RoomId:          extra.OtaRoomId,
					PolicyId:        extra.OtaPolicyId,
					Start:           extra.Start,
					End:             extra.End,
					LoadPolicyPrice: true,
					PriceRate:       utils.GetOtaPriceRate(ota.Code()),
				}
				if subOrder, err := ota.HotelOrderPreload(models.New(), &otaHotelProduct); err != nil {
					return err
				} else {
					ch.Append(subOrder)
				}
				return nil
			}
		}(v))
	}
	if err = tasks.Wait(); err != nil {
		return nil, utils.NewError(err)
	}

	ch.ForEach(func(hotel beans.IFaceHotelOrderPreloadRes) {
		out.SubOrder = append(out.SubOrder, hotel)

		switch hotel.GetType() {
		case constmap.OrderSubTypeHotel:
			item := hotel.(*beans.HotelOrderPreloadRes)
			if strutil.IsBlank(out.Title) {
				out.Title = item.FeeName
			}
			out.TotalPrice += item.FeeTotalPrice
		}
	})

	out.ValidIdType = slice.Map(constmap.ValidIdType, func(_ int, v constmap.IdType) def.IdTypeOption {
		return def.IdTypeOption{
			Label: v.ToString(),
			Value: v,
		}
	})

	return out, nil
}
