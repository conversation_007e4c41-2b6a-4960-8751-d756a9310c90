package plan

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Covers(ctx *gin.Context) (any, error) {
	var in def.PlanDetailReq

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if in.AiReqid == "" && in.PlanId < 1 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	type outTimeline struct {
		*beans.JTimeline
	}
	type outSection struct {
		SectionName string         `json:"section_name"` //段标题
		Subject     string         `json:"subject"`      //行程主题
		Timeline    []*outTimeline `json:"timeline"`
	}
	var out struct {
		Sections []outSection `json:"sections"`
	}
	out.Sections = make([]outSection, 0)

	var (
		sceneIds []uint
		hotelIds []uint
	)

	if in.PlanId > 0 {
		var plan models.Plan
		db.Preload("Sections").Where("id=?", in.PlanId).Take(&plan)
		if plan.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
		}
		for _, v := range plan.Sections {
			var content beans.PlanSectionContent
			_ = json.Unmarshal([]byte(v.Content), &content)
			out.Sections = append(out.Sections, outSection{
				SectionName: v.SectionTitle,
				Subject:     v.SectionSubject,
				Timeline: slice.FilterMap(content.Timeline, func(index int, item *beans.JTimeline) (*outTimeline, bool) {
					if item.Type == constmap.JTimelineScene {
						sceneIds = append(sceneIds, item.ItemId)
						return &outTimeline{
							JTimeline: item,
						}, true
					} else if item.Type == constmap.JTimelineHotel {
						hotelIds = append(hotelIds, item.ItemId)
						return &outTimeline{
							JTimeline: item,
						}, true
					} else {
						return nil, false
					}
				}),
			})
		}
	} else if in.AiReqid != "" {
		var journeyData *beans.RenderTravelJourney
		if journeyData == nil {
			var (
				chatMsg = new(ai.ChatCompletionResponse)
				err     error
				ckey    = fmt.Sprintf(constmap.RKAiResp, in.AiReqid)
			)
			my_cache.Get(ckey, &chatMsg)
			a := ai.NewByProvider(chatMsg.Provider)
			if a == nil {
				return nil, utils.NewErrorStr(constmap.ErrorParam, "请求内容已失效")
			}
			plan_biz.RefreshDetailExpCc(in.AiReqid) //刷新过期时间
			journeyData, err = plan_biz.ParseAIJourney(db, a, chatMsg)
			if err != nil {
				return nil, err
			}
		}

		if journeyData == nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "请求内容已失效.")
		}
		slice.ForEach(journeyData.List, func(index int, item *beans.Journey) {
			out.Sections = append(out.Sections, outSection{
				SectionName: item.Title,
				Subject:     item.Subject,
				Timeline: slice.FilterMap(item.Timeline, func(index int, item *beans.JTimeline) (*outTimeline, bool) {
					if item.Type == constmap.JTimelineScene {
						sceneIds = append(sceneIds, item.ItemId)
						return &outTimeline{
							JTimeline: item,
						}, true
					} else if item.Type == constmap.JTimelineHotel {
						hotelIds = append(hotelIds, item.ItemId)
						return &outTimeline{
							JTimeline: item,
						}, true
					} else {
						return nil, false
					}
				}),
			})
		})
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	scenePics := make(map[uint][]string)
	hotelPics := make(map[uint][]string)
	if len(sceneIds) > 0 {
		var scenes []models.ScenicPic
		db.Where("scenic_id in ?", sceneIds).Find(&scenes)

		slice.ForEach(scenes, func(index int, item models.ScenicPic) {
			item.Url = utils.StaticUrl(item.Url)
			if _, ok := scenePics[item.ScenicId]; !ok {
				scenePics[item.ScenicId] = make([]string, 0)
			}
			if item.Main == constmap.Enable {
				scenePics[item.ScenicId] = utils.Unshift(scenePics[item.ScenicId], item.Url)
			} else {
				scenePics[item.ScenicId] = append(scenePics[item.ScenicId], item.Url)
			}
		})
	}

	if len(hotelIds) > 0 {
		var hotels []models.HotelPic
		db.Where("scenic_id in ?", hotelIds).Find(&hotels)
		slice.ForEach(hotels, func(index int, item models.HotelPic) {
			item.Url = utils.StaticUrl(item.Url)
			if _, ok := hotelPics[item.HotelId]; !ok {
				hotelPics[item.HotelId] = make([]string, 0)
			}
			if item.Main == constmap.Enable {
				hotelPics[item.HotelId] = utils.Unshift(hotelPics[item.HotelId], item.Url)
			} else {
				hotelPics[item.HotelId] = append(hotelPics[item.HotelId], item.Url)
			}
		})
	}

	slice.ForEach(out.Sections, func(index int, item outSection) {
		slice.ForEach(item.Timeline, func(index int, item *outTimeline) {
			if item.Type == constmap.JTimelineScene && len(scenePics[item.ItemId]) > 0 {
				item.Pics = scenePics[item.ItemId]
			} else if item.Type == constmap.JTimelineHotel && len(hotelPics[item.ItemId]) > 0 {
				item.Pics = hotelPics[item.ItemId]
			}
		})
	})

	return out, nil
}
