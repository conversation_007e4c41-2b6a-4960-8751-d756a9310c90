package plan

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 预算明细
func CostDetail(ctx *gin.Context) (any, error) {
	var in struct {
		AiReqid string `form:"ai_reqid"`
		PlanId  uint   `form:"plan_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if in.PlanId < 1 && strutil.IsBlank(in.AiReqid) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var out struct {
		CostOk   bool            `json:"cost_ok"`
		PlanCost *beans.PlanCost `json:"plan_cost"`
		PlanInfo struct {
			Subject   string `json:"subject"`
			StartDate int64  `json:"start_date"`
			DayNum    int    `json:"day_num"`
		} `json:"plan_info"`
	}

	planTop, sections, options := plan_asm.BuildPlanSections(db, in.PlanId, in.AiReqid)
	in.AiReqid = planTop.AiReqid

	out.PlanInfo.Subject = planTop.Subject
	out.PlanInfo.StartDate = options.StartDate
	out.PlanInfo.DayNum = len(sections)

	var planCostDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{AiReqid: in.AiReqid}).Take(&planCostDetail)

	out.CostOk = planCostDetail.State == constmap.PlanCostDetailStateDone
	if out.CostOk {
		out.PlanCost = planCostDetail.Cost.Data
	}
	return out, nil
}
