package plan

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
)

// 相关推荐(目的城市相关团游)
func RelateTuan(ctx *gin.Context) (any, error) {
	var in struct {
		AiReqid string `form:"ai_reqid"`
		PlanId  uint   `form:"plan_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.AiReqid == "" && in.PlanId == 0 {
		return nil, errors.New("参数错误")
	}
	var (
		err          error
		db           = utils.GetDB(ctx)
		zoneContents []string
		rsp          = def.RelateTuan{
			List: make([]def.RelateRecItem, 0),
		}
	)

	if in.AiReqid != "" {
		var chatMsg = new(ai.ChatCompletionResponse)
		my_cache.Get(fmt.Sprintf(constmap.RKAiResp, in.AiReqid), chatMsg)
		a := ai.NewByProvider(chatMsg.Provider)
		if a == nil {
			return nil, errors.New("请求内容已失效")
		}
		journeyData, err := plan_biz.ParseAIJourney(db, a, chatMsg)
		if err != nil {
			return nil, err
		}
		for _, v := range journeyData.List {
			zoneContents = append(zoneContents, v.Title)
		}
	} else {
		var sections []models.PlanSection
		db.Where("plan_id=?", in.PlanId).Order("section_sort ASC").Find(&sections)
		for _, v := range sections {
			zoneContents = append(zoneContents, v.SectionTitle)
		}
	}
	if len(zoneContents) == 0 {
		return rsp, nil
	}

	matchContent := strings.Join(zoneContents, "\n")
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []any{
					map[string]any{
						"term": map[string]any{
							"state": constmap.Enable,
						},
					},
				},
				"should": []any{
					map[string]any{
						"match": map[string]any{
							"name": matchContent,
						},
					},
					map[string]any{
						"match": map[string]any{
							"city_name": matchContent,
						},
					},
				},
				"minimum_should_match": 1,
			},
		},
		"size": 20,
	}

	res, err := es.Search[es2.TuanModel](constmap.EsIndexTuan, query)

	var tuanIds []uint
	for _, v := range res.Hits.Hits {
		tuanIds = append(tuanIds, v.Source.ObjId)
	}
	if len(tuanIds) == 0 {
		return rsp, nil
	}
	var tuans []models.Tuan
	err = db.Model(&tuans).Where("tuans.id in ?", tuanIds).Preload("Skus").Joins("Zone").Find(&tuans).Error
	if err != nil {
		return nil, err
	}
	for _, v := range tuans {
		vitem := def.RelateRecItem{
			Id:        v.ID,
			Name:      v.Name,
			Pic:       utils.StaticUrl(v.Pic),
			Days:      v.Days,
			ShortDesc: v.ShortDesc,
			ZoneId:    v.ZoneId,
			ZoneName:  v.Zone.Name,
			Price:     v.Price,
		}

		//if len(v.Skus) > 0 {
		//	vitem.Price = v.Skus[0].Price
		//	for _, skus := range v.Skus {
		//		if skus.Price < vitem.Price {
		//			vitem.Price = skus.Price
		//		}
		//
		//		if skus.IsRequire == constmap.Enable {
		//			vitem.Price = skus.Price
		//			break
		//		}
		//	}
		//}
		rsp.List = append(rsp.List, vitem)
	}

	return rsp, nil
}
