package plan

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/hotel_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	sort2 "sort"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// 行程详情
func Detail(ctx *gin.Context) (any, error) {
	var in def.PlanDetailReq

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.AiReqid == "" && in.PlanId < 1 {
		return nil, errors.New("parameter error")
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	isLogin := session != nil

	type act struct {
		Id    uint   `json:"id"`
		Uuid  string `json:"uuid"`
		Title string `json:"title"`
		Pic   string `json:"pic"`
	}
	var out struct {
		*def.PlanDetailDto
		Distance   float64  `json:"distance"` //单位：米
		Cost       float64  `json:"cost"`
		Cost1      int64    `json:"cost1"`
		Nickname   string   `json:"nickname"`
		Avatar     string   `json:"avatar"`
		Activities []act    `json:"activities"`
		Covers     []string `json:"covers"`
	}
	// 解析详情
	rspDto, err := parseDetail(ctx, in)
	if err != nil {
		return nil, err
	}

	// 未扣费不需要返回更多数据
	if rspDto.IntegralCostState == constmap.Disable {
		out.PlanDetailDto = rspDto
		return out, nil
	}

	var startDate = time.Now()
	if rspDto.PromptOptions.StartDate > 0 {
		if s := time.Unix(rspDto.PromptOptions.StartDate, 0); !s.IsZero() {
			startDate = s
		}
	}

	out.Covers = make([]string, 0)
	var zone *beans.JourneyPoi
	var daysTimelines [][]*beans.JTimeline
	slice.ForEach(rspDto.Sections, func(index int, i1 def.PlanSection) {
		var dayTimelines []*beans.JTimeline
		slice.ForEach(i1.Zones, func(index int, i2 *beans.JourneyPoi) {
			zone = i2
		})
		slice.ForEach(i1.Timeline, func(index int, i2 *beans.JTimeline) {
			if i2.Type == constmap.JTimelineScene {
				out.Covers = append(out.Covers, i2.Pics...)
			}
			dayTimelines = append(dayTimelines, i2)
		})
		daysTimelines = append(daysTimelines, dayTimelines)
	})

	var planCost *beans.PlanCost

	var planCostDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{AiReqid: rspDto.AiReqid}).Take(&planCostDetail)
	if planCostDetail.ID > 0 && planCostDetail.State == constmap.PlanCostDetailStateDone {
		planCost = planCostDetail.Cost.Data
		rspDto.CostOk = true
	} else if rspDto.IntegralCostState != constmap.Disable {
		// 生成预算明细
		_ = my_queue.Weight(constmap.EventPlanUpdated, gin.H{
			"plan_id":  rspDto.PlanId,
			"ai_reqid": rspDto.AiReqid,
		})
	}

	if planCost != nil {
		//nothing
	} else if rspDto.PlanCost != nil {
		planCost = rspDto.PlanCost
	} else {
		planCost = plan_asm.CalcPlanCost(ctx, db, in.PlanDetailReq, startDate, daysTimelines, nil)
		if planCost != nil {
			if aiResp, ok := plan_biz.GetDetailCc(rspDto.AiReqid); ok {
				aiResp.PlanCost = planCost
				_ = plan_biz.SetDetailCc(aiResp)
			}
			if rspDto.PlanId > 0 {
				db.Model(&models.PlanExt{}).Where(models.PlanExt{PlanId: rspDto.PlanId}).Updates(models.PlanExt{
					PlanCost: convertor.ToString(planCost),
				})
			}
		}
	}
	if planCost != nil {
		out.Distance = planCost.Distance
		out.Cost1 = planCost.Total
	}

	out.Cost = utils.CurrencyInt2Float(out.Cost1)

	if zone != nil {
		out.Covers = utils.Unshift(out.Covers, zone.Pic)
	}
	if len(out.Covers) > 3 {
		out.Covers = out.Covers[:3]
	}

	out.PlanDetailDto = rspDto
	var user models.User
	db.Take(&user, out.UserId)
	out.Nickname = user.Nickname
	out.Avatar = utils.StaticUrl(user.Avatar)
	out.Activities = make([]act, 0)
	if out.UserId > 0 {
		// 查询用户参与过的活动
		if isLogin && out.UserId != session.UserId {
			where := &beans.BeWhere{}
			where.Where.WriteString("act.uuid in ?")
			where.Args = append(where.Args, []string{constmap.ActLaxin})
			cuacts := activity_biz.FindValidUserActs(db, session.UserId, where)
			uacts := activity_biz.FindValidUserActs(db, out.UserId, where)
			cuActsMap := slice.ReduceBy(cuacts, make(map[uint]bool), func(index int, item models.UserActivity, agg map[uint]bool) map[uint]bool {
				agg[item.ActivityId] = true
				return agg
			})
			if len(uacts) > 0 {
				var acts []models.Activity
				db.Find(&acts, slice.Map(uacts, func(index int, item models.UserActivity) uint {
					return item.ActivityId
				}))
				out.Activities = slice.FilterMap(acts, func(index int, item models.Activity) (act, bool) {
					//当前用户已经参与过，不展示
					if _, ok := cuActsMap[item.ID]; ok {
						return act{}, false
					}
					v := act{
						Id:    item.ID,
						Uuid:  item.Uuid,
						Title: item.Title,
					}
					if item.Uuid == constmap.ActLaxin {
						v.Pic = "http://rp.yjsoft.com.cn/yiban/static/activity/new/plan-popup-bg.png"
					}
					return v, true
				})
			}
			if out.PlanId > 0 && out.UserId != session.UserId {
				_ = my_queue.Light(constmap.EventTaskAction, gin.H{
					"task_event_req": convertor.ToString(task_asm.DoActivityTaskReq{
						Cond:      constmap.TaskCondSharePlan,
						PlanId:    out.PlanId,
						HelperUid: session.UserId,
					}),
				})
			}
		}
	}

	return out, nil
}

// parseDetail 解析行程详情，根据请求参数选择不同的解析方式
func parseDetail(ctx *gin.Context, in def.PlanDetailReq) (*def.PlanDetailDto, error) {
	var (
		db         = utils.GetDB(ctx)
		session, _ = business.GetFrontLoginUser(ctx)
		rspDto     = new(def.PlanDetailDto)
	)
	isLogin := session != nil
	rspDto.CanEdit = true

	if isLogin {
		c, unlocker := context.WithCancel(ctx)
		defer unlocker()
		my_cache.RedisLock(c, fmt.Sprintf(constmap.RKSpin, "parseDetail", convertor.ToString(session.UserId)), constmap.TimeDur1m)

		if in.PlanId < 1 && !strutil.IsBlank(in.AiReqid) {
			// 有自己的plan_id
			db.Model(&models.Plan{}).
				Select("ID").
				Where(models.Plan{
					AiReqid: in.AiReqid,
					UserId:  session.UserId,
				}).Scan(&in.PlanId)
		}
	}

	var err error
	if in.PlanId > 0 {
		// 通过 PlanId 解析行程详情
		err = parsePlanDetail(in, rspDto, db, session, isLogin)
	} else if in.AiReqid != "" {
		// 通过 AiReqid 解析行程详情
		err = parseAIReqDetail(in, rspDto, db, session, isLogin)
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程内容已失效")
	}

	if err != nil {
		return nil, err
	}

	// 计算场景数量
	countSceneNum(rspDto)

	return rspDto, nil
}

// parsePlanDetail 通过 PlanId 解析行程详情
func parsePlanDetail(in def.PlanDetailReq, rspDto *def.PlanDetailDto, db *gorm.DB, session *models.Session, isLogin bool) error {
	// 加载行程数据
	plan, err := loadPlanData(db, in.PlanId)
	if err != nil {
		return err
	}

	// 解析提示选项
	options, err := parsePromptOptions(plan)
	if err != nil {
		return err
	}

	// 填充基本信息
	fillBasicInfo(rspDto, plan, options)

	// 检查用户权限
	if isLogin {
		checkUserPermissions(db, rspDto, plan, session)
	}

	// 加载区域和时间线数据
	scontMap, zoneMap, sceneMap, hotelMap, _, _ := loadZoneAndTimelineData(db, plan)

	// 构建行程段落
	rspDto.Sections = buildSections(scontMap, zoneMap, sceneMap, hotelMap, plan.Sections)

	// 设置PDF信息
	if rspDto.IsOwn {
		setPdfInfo(rspDto, plan)
	}

	return nil
}

// parseAIReqDetail 通过 AiReqid 解析行程详情
func parseAIReqDetail(in def.PlanDetailReq, rspDto *def.PlanDetailDto, db *gorm.DB, session *models.Session, isLogin bool) error {
	// 从缓存获取AI响应数据
	chatMsg, err := loadAIResponseData(in.AiReqid)
	if err != nil {
		return err
	}

	// 创建AI提供者实例
	a := ai.NewByProvider(chatMsg.Provider)
	if a == nil {
		return utils.NewErrorStr(constmap.ErrorParam, "请求内容已失效")
	}

	// 刷新详情缓存
	plan_biz.RefreshDetailExpCc(in.AiReqid)

	// 解析AI行程数据
	journeyData, err := plan_biz.ParseAIJourney(db, a, chatMsg)
	if err != nil {
		return err
	}

	// 处理积分扣除
	if isLogin && chatMsg.IntegralCostState == constmap.Disable && in.NeedDeduct == constmap.Enable {
		err = handleIntegralDeduction(db, session, chatMsg)
		if err != nil {
			return err
		}
	}

	// 未扣费状态下返回有限信息
	if chatMsg.IntegralCostState == constmap.Disable {
		return handleUnpaidState(rspDto, chatMsg, journeyData, in.AiReqid)
	}

	// 设置积分相关信息
	rspDto.IntegralCost = chatMsg.IntegralCost
	rspDto.IntegralCostState = chatMsg.IntegralCostState

	if journeyData == nil {
		return utils.NewErrorStr(constmap.ErrorSystem, "请求内容已失效.")
	}

	// 查找用户已有的行程
	var plan models.Plan
	if isLogin {
		loadUserPlan(db, &plan, session.UserId, in.AiReqid, chatMsg.Provider, rspDto)
	}

	// 填充行程详情
	fillJourneyDetails(rspDto, chatMsg, journeyData)

	// 构建行程段落
	buildJourneySections(rspDto, journeyData)

	// 设置PDF信息
	if rspDto.IsOwn {
		setPdfInfo(rspDto, &plan)
	}

	return nil
}

// loadAIResponseData 从缓存加载AI响应数据
func loadAIResponseData(aiReqid string) (*ai.ChatCompletionResponse, error) {
	chatMsg := new(ai.ChatCompletionResponse)
	ckey := fmt.Sprintf(constmap.RKAiResp, aiReqid)
	my_cache.Get(ckey, chatMsg)

	if !strutil.IsBlank(string(chatMsg.ContentType)) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请求无效")
	}

	return chatMsg, nil
}

// handleIntegralDeduction 处理积分扣除
func handleIntegralDeduction(db *gorm.DB, session *models.Session, chatMsg *ai.ChatCompletionResponse) error {
	err := db.Transaction(func(tx *gorm.DB) error {
		_, err := plan_asm.CostIntegral(tx, session.UserId, chatMsg.IntegralCost, chatMsg.RequestId)
		return err
	})

	if err == nil {
		chatMsg.IntegralCostState = constmap.Enable
		_ = plan_biz.SetDetailCc(chatMsg)
	}

	return err
}

// handleUnpaidState 处理未扣费状态
func handleUnpaidState(rspDto *def.PlanDetailDto, chatMsg *ai.ChatCompletionResponse, journeyData *beans.RenderTravelJourney, aiReqid string) error {
	rspDto.Days = len(journeyData.List)
	rspDto.Sections = make([]def.PlanSection, 0)
	rspDto.From = chatMsg.From
	rspDto.To = chatMsg.To
	rspDto.PlanLockData = chatMsg.PlanLockData
	rspDto.AiReqid = aiReqid
	return nil
}

// loadUserPlan 加载用户已有的行程
func loadUserPlan(db *gorm.DB, plan *models.Plan, userId uint, aiReqid string, provider string, rspDto *def.PlanDetailDto) {
	db.Where("user_id=? and ai_reqid=? and ai_provider=?", userId, aiReqid, provider).
		Joins("Ext").
		Take(plan)
	if plan.ID > 0 {
		rspDto.PlanId = plan.ID
		rspDto.IsOwn = plan.State == constmap.Enable
	}
	rspDto.UserId = userId
}

// fillJourneyDetails 填充行程详情
func fillJourneyDetails(rspDto *def.PlanDetailDto, chatMsg *ai.ChatCompletionResponse, journeyData *beans.RenderTravelJourney) {
	// 设置提示选项
	if chatMsg.PromptOptions != nil {
		rspDto.PromptOptions = chatMsg.PromptOptions
		rspDto.From = chatMsg.PromptOptions.From
		rspDto.To = chatMsg.PromptOptions.To
		rspDto.Transport = rspDto.PromptOptions.Transport
	}

	// 设置行程信息
	rspDto.PlanCost = chatMsg.PlanCost
	rspDto.Subject = journeyData.Title
	rspDto.Subtitle = journeyData.Subtitle
	rspDto.Notice = journeyData.Notice
	rspDto.Budget = journeyData.Budget
	rspDto.BudgetDetail = journeyData.BudgetDetail
	rspDto.FitFor = journeyData.FitFor
	rspDto.Days = len(journeyData.List)
	rspDto.AiReqid = chatMsg.RequestId
}

// buildJourneySections 构建行程段落
func buildJourneySections(rspDto *def.PlanDetailDto, journeyData *beans.RenderTravelJourney) {
	for _, v := range journeyData.List {
		planSection := def.PlanSection{
			SectionName: v.Title,
			Subject:     v.Subject,
			Zones:       v.WayPoints,
		}

		if len(v.Timeline) > 0 {
			planSection.Timeline = v.Timeline
		} else {
			planSection.Timeline = make([]*beans.JTimeline, 0)
		}

		rspDto.Sections = append(rspDto.Sections, planSection)
	}
}

// checkUserPermissions 检查用户权限
func checkUserPermissions(db *gorm.DB, rspDto *def.PlanDetailDto, plan *models.Plan, session *models.Session) {
	var cnt int64
	if db.Model(&models.Plan{}).Where("user_id=? and ai_reqid=? and ai_provider=? and state=?",
		session.UserId, plan.AiReqid, plan.AiProvider, constmap.Enable).Count(&cnt); cnt > 0 {

		rspDto.IsOwn = true
	}

	db.Model(&models.PlanLike{}).Where(models.PlanLike{PlanId: plan.ID, UserId: session.UserId}).Count(&cnt)
	rspDto.IsLike = cnt > 0
}

// setPdfInfo 设置PDF信息
func setPdfInfo(rspDto *def.PlanDetailDto, plan *models.Plan) {
	rspDto.PdfCover = utils.StaticUrl(plan.Ext.PdfCover)
	rspDto.PdfState = plan.Ext.PdfState
	rspDto.PdfUrl = utils.StaticUrl(plan.Ext.PdfUrl)
}

// countSceneNum 计算场景数量
func countSceneNum(rspDto *def.PlanDetailDto) {
	// 重置场景数量，避免重复计算
	rspDto.SceneNum = 0

	for _, section := range rspDto.Sections {
		for _, timeline := range section.Timeline {
			if timeline.Type == constmap.JTimelineScene {
				rspDto.SceneNum++
			}
		}
	}
}

// loadPlanData 从数据库加载 Plan 数据（包括 Sections 和 Ext）
func loadPlanData(db *gorm.DB, planId uint) (*models.Plan, error) {
	var plan models.Plan
	db.Preload("Sections").Preload("Ext").Where("id = ?", planId).Take(&plan)
	if plan.ID == 0 {
		return nil, errors.New("行程不存在")
	}
	return &plan, nil
}

// parsePromptOptions 解析 PromptOptions 字段
func parsePromptOptions(plan *models.Plan) (*beans.TravelPromptOptions, error) {
	if plan.Ext.PromptOptions.Data != nil {
		return plan.Ext.PromptOptions.Data, nil
	}
	// 返回空的 TravelPromptOptions 结构
	return &beans.TravelPromptOptions{}, nil
}

// fillBasicInfo 将 plan 和 promptOptions 的基础信息填充到 rspDto
func fillBasicInfo(rspDto *def.PlanDetailDto, plan *models.Plan, options *beans.TravelPromptOptions) {
	rspDto.PromptOptions = options
	rspDto.Transport = options.Transport
	rspDto.PlanId = plan.ID
	rspDto.From = plan.From
	rspDto.To = plan.To
	rspDto.Subject = plan.Subject
	rspDto.Subtitle = plan.Subtitle
	rspDto.Notice = plan.Notice
	rspDto.Days = plan.CostDay
	rspDto.FitFor = plan.FitFor
	rspDto.UserId = plan.UserId
	rspDto.AiReqid = plan.AiReqid
	rspDto.Likes = plan.Likes
	rspDto.PlanCost = plan_biz.LoadPlanCost(plan.Ext.PlanCost)
	rspDto.WishId = plan.WishId
	rspDto.CanEdit = plan.WishId == 0
}

// loadZoneAndTimelineData 加载 PassingZones、Timeline 并构建 zoneMap、sceneMap、hotelMap
func loadZoneAndTimelineData(db *gorm.DB, plan *models.Plan) (
	map[uint]beans.PlanSectionContent,
	map[uint]models.Zone,
	map[uint]*models.Scenic,
	map[uint]*models.Hotel,
	[]uint,
	[]uint,
) {
	scontMap := make(map[uint]beans.PlanSectionContent)
	zoneMap := make(map[uint]models.Zone)
	sceneIds := make([]uint, 0)
	hotelIds := make([]uint, 0)

	zoneIds := make([]uint, 0)
	for _, v := range plan.Sections {
		var content beans.PlanSectionContent
		_ = json.Unmarshal([]byte(v.Content), &content)
		scontMap[v.ID] = content
		zoneIds = append(zoneIds, content.PassingZones...)
		slice.ForEach(content.Timeline, func(index int, item *beans.JTimeline) {
			switch item.Type {
			case constmap.JTimelineScene:
				sceneIds = append(sceneIds, item.ItemId)
			case constmap.JTimelineHotel:
				hotelIds = append(hotelIds, item.ItemId)
			}
		})
	}

	sort2.Slice(plan.Sections, func(i, j int) bool {
		return plan.Sections[i].SectionSort < plan.Sections[j].SectionSort
	})

	zoneMap = zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)
	sceneMap := scenics_biz.LoadScenes(db, sceneIds, false)
	hotelMap := hotel_biz.LoadHotels(db, hotelIds, false)

	return scontMap, zoneMap, sceneMap, hotelMap, sceneIds, hotelIds
}

// buildSections 构建最终的 Sections 列表
func buildSections(
	scontMap map[uint]beans.PlanSectionContent,
	zoneMap map[uint]models.Zone,
	sceneMap map[uint]*models.Scenic,
	hotelMap map[uint]*models.Hotel,
	planSections []models.PlanSection,
) []def.PlanSection {
	var sections []def.PlanSection

	for _, v := range planSections {
		var content = scontMap[v.ID]
		var section def.PlanSection
		section.SectionName = v.SectionTitle
		section.Subject = v.SectionSubject
		section.Zones = make([]*beans.JourneyPoi, 0)

		// 处理区域信息
		buildZoneData(&section, content.PassingZones, zoneMap)

		// 处理时间线信息
		if len(content.Timeline) > 0 {
			section.Timeline = content.Timeline
		} else {
			section.Timeline = make([]*beans.JTimeline, 0)
		}

		// 丰富时间线数据
		enrichTimelineData(section.Timeline, sceneMap, hotelMap)

		sections = append(sections, section)
	}

	return sections
}

// buildZoneData 构建区域数据
func buildZoneData(section *def.PlanSection, passingZones []uint, zoneMap map[uint]models.Zone) {
	slice.ForEach(passingZones, func(index int, item uint) {
		if z, ok := zoneMap[item]; ok {
			section.Zones = append(section.Zones, &beans.JourneyPoi{
				Id:   z.ID,
				Pic:  utils.StaticUrl(z.Pic),
				Name: z.Name,
				Type: constmap.PoiTypeZone,
				Lng:  z.Lng,
				Lat:  z.Lat,
			})
		}
	})
}

// enrichTimelineData 丰富时间线数据
func enrichTimelineData(timeline []*beans.JTimeline, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel) {
	slice.ForEach(timeline, func(index int, item *beans.JTimeline) {
		switch item.Type {
		case constmap.JTimelineScene:
			enrichSceneData(item, sceneMap)
		case constmap.JTimelineHotel:
			enrichHotelData(item, hotelMap)
		}
	})
}

// enrichSceneData 丰富景点数据
func enrichSceneData(item *beans.JTimeline, sceneMap map[uint]*models.Scenic) {
	if scene, ok := sceneMap[item.ItemId]; ok && scene.IsFree != constmap.Enable && !strutil.IsBlank(scene.ScenicPrices) {
		var prices beans.ScenicPrices
		_ = json.Unmarshal([]byte(scene.ScenicPrices), &prices)
		item.AvgPrice = utils.CurrencyFloat2Int(prices.Adult.MonthlyPriceAvg)
	}
}

// enrichHotelData 丰富酒店数据
func enrichHotelData(item *beans.JTimeline, hotelMap map[uint]*models.Hotel) {
	if hotel, ok := hotelMap[item.ItemId]; ok {
		item.AvgPrice = hotel.AvgPrice
	}
}
