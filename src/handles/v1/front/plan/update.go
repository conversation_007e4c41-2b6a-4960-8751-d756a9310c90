package plan

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Update(ctx *gin.Context) (any, error) {
	var in struct {
		PlanId    uint   `form:"plan_id" binding:"required"`
		Subject   string `form:"subject"`
		StartDate int64  `form:"start_date"`
		Sections  string `form:"sections" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var plan = new(models.Plan)
	db.Preload("Ext").Where(models.Plan{UserId: session.UserId}).Take(&plan, in.PlanId)

	if plan.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	}

	if plan.WishId > 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程关联心愿单，不可编辑")
	}

	var sections []def.PlanSection
	if err := json.Unmarshal([]byte(in.Sections), &sections); err != nil {
		return nil, utils.NewError(err)
	} else if len(sections) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请保证至少有一天的行程内容")
	}

	plan.Sections = make([]models.PlanSection, 0)

	var sceneIds []uint
	var hotelIds []uint

	for i, v := range sections {
		if v.SectionName == "" {
			return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("请补充第%d天的行程标题", i+1))
		}
		for _, jt := range v.Timeline {
			switch jt.Type {
			case constmap.JTimelineScene:
				sceneIds = append(sceneIds, jt.ItemId)
			case constmap.JTimelineHotel:
				hotelIds = append(hotelIds, jt.ItemId)
			case constmap.JTimelinePlane, constmap.JTimelineTrain, constmap.JTimelineBus:

			}
		}
	}

	var sceneMap = make(map[uint]models.Scenic)
	var hotelMap = make(map[uint]models.Hotel)

	if len(sceneIds) > 0 {
		var scenes []models.Scenic
		db.Find(&scenes, sceneIds)
		sceneMap = slice.ReduceBy(scenes, sceneMap, func(index int, item models.Scenic, agg map[uint]models.Scenic) map[uint]models.Scenic {
			agg[item.ID] = item
			return agg
		})
	}

	if len(hotelIds) > 0 {
		var hotels []models.Hotel
		db.Find(&hotels, hotelIds)
		hotelMap = slice.ReduceBy(hotels, hotelMap, func(index int, item models.Hotel, agg map[uint]models.Hotel) map[uint]models.Hotel {
			agg[item.ID] = item
			return agg
		})
	}

	for i, v := range sections {
		var sectionContent beans.PlanSectionContent
		var passingZones = make([]uint, 0)
		for _, jt := range v.Timeline {
			switch jt.Type {
			case constmap.JTimelineScene:
				if item, ok := sceneMap[jt.ItemId]; ok {
					jt.Poi = &beans.JourneyPoi{
						Type:  constmap.PoiTypeScenic,
						Id:    item.ID,
						Pic:   utils.StaticUrl(item.Pic),
						Name:  item.Name,
						Lng:   item.Lng,
						Lat:   item.Lat,
						Price: item.CostText,
					}
					if len(passingZones) == 0 || len(passingZones) > 0 && passingZones[len(passingZones)-1] != item.ZoneId {
						passingZones = append(passingZones, item.ZoneId)
					}
				}
			case constmap.JTimelineHotel:
				if item, ok := hotelMap[jt.ItemId]; ok {
					jt.Poi = &beans.JourneyPoi{
						Type:  constmap.PoiTypeHotel,
						Id:    item.ID,
						Pic:   utils.StaticUrl(item.Pic),
						Name:  item.Name,
						Lng:   item.Lng,
						Lat:   item.Lat,
						Price: item.CostText,
					}
					if len(passingZones) == 0 || len(passingZones) > 0 && passingZones[len(passingZones)-1] != item.ZoneId {
						passingZones = append(passingZones, item.ZoneId)
					}
				}
			case constmap.JTimelinePlane, constmap.JTimelineTrain, constmap.JTimelineBus:

			}
			sectionContent.PassingZones = passingZones
			sectionContent.Timeline = append(sectionContent.Timeline, jt)
		}
		if len(sectionContent.Timeline) == 0 {
			continue
		}
		b, _ := json.Marshal(sectionContent)
		plan.Sections = append(plan.Sections, models.PlanSection{
			PlanId:         plan.ID,
			SectionSort:    i + 1,
			SectionTitle:   v.SectionName,
			SectionSubject: v.Subject,
			Content:        string(b),
		})
	}

	plan.CostDay = len(sections)
	plan.SceneCnt = len(sceneIds)
	plan.Subject = utils.If(!strutil.IsBlank(in.Subject), in.Subject, plan.Subject)

	options := &beans.TravelPromptOptions{}

	if plan.Ext.PromptOptions.Data != nil {
		*options = *plan.Ext.PromptOptions.Data
	}
	if in.StartDate > 0 {
		options.StartDate = in.StartDate
	}

	plan.Ext.PromptOptions.Data = options

	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Delete(&models.PlanSection{}, models.PlanSection{PlanId: plan.ID}).Error; err != nil {
			return err
		}
		plan.AiProvider = "yjsoft"
		plan.AiReqid = fmt.Sprintf("yj:%s", utils.Md5(utils.UUID()))
		if tx.Model(&plan).Select("*").Omit(clause.Associations, "id").Updates(plan).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "行程内容更新失败")
		}
		if err := tx.Create(&plan.Sections).Error; err != nil {
			return err
		}

		if plan.Ext.ID > 0 {
			if tx.Model(&plan.Ext).Select("*").Omit(clause.Associations, "id", "CreatedAt", "PlanId").
				Updates(plan.Ext).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "[1]更新失败")
			}
		} else {
			plan.Ext.PlanId = plan.ID
			if err := tx.Omit(clause.Associations).Create(&plan.Ext).Error; err != nil {
				return utils.NewError(err)
			}
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewError(err)
	}

	// 生成预算明细
	_ = my_queue.Weight(constmap.EventPlanUpdated, gin.H{
		"plan_id":  plan.ID,
		"ai_reqid": plan.AiReqid,
	})

	var out struct {
		Id         uint   `json:"id"`
		AiProvider string `json:"ai_provider"`
		AiReqid    string `json:"ai_reqid"`
	}
	out.Id = plan.ID
	out.AiProvider = plan.AiProvider
	out.AiReqid = plan.AiReqid
	return out, nil
}
