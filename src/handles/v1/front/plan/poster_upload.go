package plan

import (
	"bytes"
	"encoding/base64"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/my_baidu"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func PosterUpload(ctx *gin.Context) (any, error) {
	var in struct {
		PlanId uint `form:"plan_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var out struct {
		Url string `json:"url"`
	}

	var plan = new(models.Plan)
	db.Where(models.Plan{
		UserId: session.UserId,
		State:  constmap.Enable,
	}).Take(&plan, in.PlanId)

	if plan.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	} else if !strutil.IsBlank(plan.Poster) {
		out.Url = utils.StaticUrl(plan.Poster)
		return out, nil
	}

	poster, _, err := ctx.Request.FormFile("poster")
	if err != nil {
		my_logger.Errorf("上传文件错误", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorParam, "上传文件错误")
	}

	fileData, err := io.ReadAll(poster)
	_ = poster.Close()

	if err != nil {
		my_logger.Errorf("[1]上传文件错误", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorParam, "[1]上传文件错误")
	}
	risk := my_baidu.NewRiskClient()
	if rsp, err := risk.ImageCensor(base64.StdEncoding.EncodeToString(fileData), ""); err != nil || rsp.ConclusionType != my_baidu.ConclusionOk {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "图片违规")
	}

	uri := business.BuildStaticPath(constmap.PathPlanPost, utils.UUID()+".png")
	if err := qiniu_biz.UploadReader(bytes.NewReader(fileData), uri); err != nil {
		my_logger.Errorf("[2]上传文件错误", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "文件上传失败")
	}

	acts := activity_biz.FindValidActs(db, "uuid=?", []any{constmap.ActWeekPk})

	err = db.Transaction(func(tx *gorm.DB) error {
		if tx.Model(&plan).Updates(models.Plan{
			Poster: uri,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "海报保存失败")
		}

		var cnt int64
		for _, act := range acts {
			if tx.Model(&models.ActivityPlan{}).Where(models.ActivityPlan{
				ActivityId: act.ID,
				PlanId:     plan.ID,
			}).Count(&cnt); cnt > 0 {
				continue
			}
			if tx.Model(&models.UserActivity{}).Where(models.UserActivity{
				ActivityId: act.ID,
				UserId:     session.UserId,
			}).Count(&cnt); cnt == 0 {
				if tx.Create(&models.UserActivity{
					ActivityId: act.ID,
					UserId:     session.UserId,
				}).Error != nil {
					return utils.NewErrorStr(constmap.ErrorSystem, "[3]保存失败")
				}
			}
			actPlan := &models.ActivityPlan{
				ActivityId: act.ID,
				PlanId:     plan.ID,
				UserId:     session.UserId,
				Poster:     uri,
			}
			if err := tx.Create(actPlan).Error; err != nil {
				my_logger.Errorf("[1]保存失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, "[1]保存失败")
			}
			actPlanRank := &models.ActivityPlanRank{
				ActivityId:     act.ID,
				ActivityPlanId: actPlan.ID,
				Date:           utils.BeginOfWeek(time.Now()),
			}
			if err := tx.Create(actPlanRank).Error; err != nil {
				my_logger.Errorf("[2]保存失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, "[2]保存失败")
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	out.Url = utils.StaticUrl(uri)
	return out, nil
}
