package plan

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 生成行程
func Submit(ctx *gin.Context) (any, error) {
	var in struct {
		AiReqid string  `form:"ai_reqid"`
		PlanId  uint    `form:"plan_id"`
		Lat     float64 `form:"lat"`
		Lng     float64 `form:"lng"`
		//UseHistory int     `form:"use_history"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	if in.AiReqid == "" && in.PlanId == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	var (
		err        error
		session, _ = business.GetFrontLoginUser(ctx)
		db         = utils.GetDB(ctx)
		rsp        def.SubmitPlan
		plan       = new(models.Plan)
	)
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "plan:submit", fmt.Sprintf("%d", session.UserId)), constmap.TimeDur5s)
	if err != nil {
		my_logger.Infof("Submit Mutex ", zap.String("err", err.Error()))
		return nil, errors.New("操作过快，请稍后再试")
	}
	defer unlocker()

	reenable := func(plan *models.Plan) {
		if plan.State == constmap.Disable { // 重新启用
			var updator models.Plan
			updator.ID = plan.ID
			updator.State = constmap.Enable
			db.Model(&updator).Updates(updator)
		}
	}

	if in.AiReqid != "" {

		db.Preload("Ext").
			Where("user_id=? and ai_reqid=?", session.UserId, in.AiReqid).Take(&plan)
		if plan.ID > 0 {
			rsp.Id = plan.ID
			reenable(plan)
			return rsp, nil
		}

		var chatMsg = new(ai.ChatCompletionResponse)
		my_cache.Get(fmt.Sprintf(constmap.RKAiResp, in.AiReqid), chatMsg)
		plan, err = plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
			UserId: session.UserId,
			State:  constmap.Enable,
		}, chatMsg)
		if err != nil {
			return nil, err
		}
		if err = db.Transaction(func(tx *gorm.DB) error {
			if chatMsg.IntegralCostState == constmap.Disable {
				if _, err = plan_asm.CostIntegral(tx, plan.UserId, chatMsg.IntegralCost, in.AiReqid); err != nil {
					return err
				}
			}
			chatMsg.IntegralCostState = constmap.Enable
			plan.IntegralCostState = constmap.Enable
			return plan_asm.SubmitPlan(tx, plan)
		}); err != nil {
			return nil, err
		}
		// 扣减后重新刷新下缓存里的扣减状态
		plan_biz.RefreshDetailExpCc(chatMsg.RequestId)
	} else if in.PlanId > 0 {
		plan, err = plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
			UserId: session.UserId,
			State:  constmap.Enable,
			PlanId: in.PlanId,
		}, nil)
		if err != nil {
			return nil, err
		}
		if plan.UserId == session.UserId {
			rsp.Id = in.PlanId
			reenable(plan)
			return rsp, nil
		}
		plan.Likes = 0
		plan.Poster = ""
		plan.ID = 0
		plan.UserId = session.UserId
		plan.State = constmap.Enable
		plan.Ext.ID = 0
		plan.Ext.PlanId = 0
		if err = db.Transaction(func(tx *gorm.DB) error {
			return plan_asm.SubmitPlan(tx, plan)
		}); err != nil {
			return nil, err
		}
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	rsp.Id = plan.ID

	return rsp, nil
}
