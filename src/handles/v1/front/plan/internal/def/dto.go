package def

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
)

type PlanDetailReq struct {
	beans.PlanDetailReq
	NeedDeduct int `form:"need_deduct"` //是否需要执行扣费 1是 2否
}

type PlanDetailDto struct {
	beans.TravelPlanTop

	PlanId       uint                  `json:"plan_id"`
	AiReqid      string                `json:"ai_reqid"`
	IsOwn        bool                  `json:"is_own"` //是否已加入行程
	Likes        int                   `json:"likes"`
	IsLike       bool                  `json:"is_like"`
	UserId       uint                  `json:"user_id"`
	Subject      string                `json:"subject"`       //行程主题 上海到南京4天3晚
	Subtitle     string                `json:"subtitle"`      //子标题 自然与文化的完美邂逅
	Notice       string                `json:"notice"`        //注意事项
	FitFor       string                `json:"fit_for"`       //适合季节或月份
	Budget       string                `json:"budget"`        //出行总预算
	BudgetDetail string                `json:"budget_detail"` //预算明细
	PdfState     constmap.PlanPdfState `json:"pdf_state"`
	PdfCover     string                `json:"pdf_cover"`
	PdfUrl       string                `json:"pdf_url"`
	PlanCost     *beans.PlanCost       `json:"plan_cost"`
	Sections     []PlanSection         `json:"sections"` //行程段安排
	CostOk       bool                  `json:"cost_ok"`  //预算明细是否已完成计算
	WishId       uint                  `json:"wish_id"`
	CanEdit      bool                  `json:"can_edit"`
}

type JourneyScenic struct {
	*beans.JourneyPoi
	Address  string `json:"address"`
	OpenTime string `json:"open_time"`
}

type PlanSection struct {
	SectionName string              `json:"section_name"` //段标题
	Subject     string              `json:"subject"`      //行程主题
	Zones       []*beans.JourneyPoi `json:"zones"`
	Timeline    []*beans.JTimeline  `json:"timeline"`
}

type SubmitPlan struct {
	Id uint `json:"id"`
}

// 相关团游推荐
type RelateTuan struct {
	Total int64           `json:"total"`
	List  []RelateRecItem `json:"list"`
}

type RelateRecItem struct {
	Id        uint   `json:"id"`
	Name      string `json:"name"`
	Pic       string `json:"pic"`
	Days      int    `json:"days"`
	ShortDesc string `json:"short_desc"`
	ZoneId    uint   `json:"zone_id"`
	ZoneName  string `json:"zone_name"`
	Price     int    `json:"price"`
}
