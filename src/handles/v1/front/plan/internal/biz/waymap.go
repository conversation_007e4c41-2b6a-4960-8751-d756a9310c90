package biz

import (
	"github.com/duke-git/lancet/v2/slice"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
)

func GetWaymapPoi(rspDto *def.PlanDetailDto) [][]*beans.JourneyPoi {
	// 按天分组提取 waypoints
	var dayTasks = make([][]*beans.JourneyPoi, 0)
	slice.ForEach(rspDto.Sections, func(day int, section def.PlanSection) {
		var waypoints []*beans.JourneyPoi
		slice.ForEach(section.Timeline, func(_ int, timeline *beans.JTimeline) {
			if timeline.Type == constmap.JTimelineScene || timeline.Type == constmap.JTimelineHotel {
				waypoints = append(waypoints, timeline.Poi)
			}
		})
		if len(waypoints) == 0 {
			return
		}

		dayTasks = append(dayTasks, waypoints)
	})
	return dayTasks
}
