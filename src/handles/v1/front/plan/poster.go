package plan

import (
	"bytes"
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/image/draw"
	"golang.org/x/image/font/opentype"
	"image"
	"image/color"
	"image/png"
	"net/http"
	"roadtrip-api/src/assets"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Poster(ctx *gin.Context) (any, error) {
	var in struct {
		PlanId    uint `form:"plan_id" binding:"required"`
		ReturnUrl int  `form:"return_url"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}
	db := utils.GetDB(ctx)

	var plan models.Plan

	if err := db.Preload("Ext").Take(&plan, in.PlanId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在，请先保存行程")
	}

	var promptOptions beans.TravelPromptOptions
	if plan.Ext.PromptOptions.Data != nil {
		promptOptions = *plan.Ext.PromptOptions.Data
	}

	file := constmap.QiniuPrefix + fmt.Sprintf("/tmp/%s/plan_poster_%d", utils.If(config.IsProduction(), "prod", "dev"), plan.ID)
	if config.IsProduction() && qiniu_biz.IsExists(file) {
		if in.ReturnUrl == constmap.Enable {
			return gin.H{"url": utils.StaticUrl(file)}, nil
		}

		ctx.Set(constmap.ContextHttpRaw, true)
		ctx.Redirect(301, utils.StaticUrl(file))
		return nil, nil
	}

	var user models.User
	db.Take(&user, plan.UserId)

	var avatar image.Image
	var bg image.Image
	var trip image.Image
	var err error

	useDefaultAvatar := func() error {
		avatar, _, err = image.Decode(bytes.NewReader(assets.UserDefaultAvatar))
		return err
	}
	if strutil.IsBlank(user.Avatar) {
		if err = useDefaultAvatar(); err != nil {
			return nil, err
		}
	} else if rsp, err := http.Get(utils.StaticUrl(user.Avatar)); err != nil {
		my_logger.Infof("get avatar", zap.String("avatar", utils.StaticUrl(user.Avatar)), zap.Error(err))
		if err = useDefaultAvatar(); err != nil {
			return nil, err
		}
	} else if avatar, _, err = image.Decode(rsp.Body); err != nil {
		my_logger.Infof("decode avatar", zap.Error(err))
		if err = useDefaultAvatar(); err != nil {
			return nil, err
		}
	}

	if bg, _, err = image.Decode(bytes.NewReader(assets.PosterBg)); err != nil {
		return nil, err
	}
	if trip, _, err = image.Decode(bytes.NewReader(assets.PosterTrip)); err != nil {
		return nil, err
	}

	const cwidth = 750
	const cheight = 750
	avatarBound := avatar.Bounds()
	tripBound := trip.Bounds()

	cv := image.NewRGBA(image.Rect(0, 0, cwidth, cheight))

	// 背景色
	utils.DrawRect(cv, cv.Rect, color.Black)

	// 缩放背景图到画布大小
	bgDst := utils.ImageResize(bg, cwidth, 0)
	draw.Draw(cv, bgDst.Bounds(), bgDst, image.Point{}, draw.Over)

	//画头像
	avatarWidth, avatarHeight := 120, 120
	avatarDst := utils.ImageResize(avatar, //以短边缩放
		utils.If(avatarBound.Dx() > avatarBound.Dy(), 0, avatarWidth),
		utils.If(avatarBound.Dx() > avatarBound.Dy(), avatarHeight, 0))
	sp := image.Point{ //平移到指定位置
		X: 20,
		Y: 20,
	}
	avatarBound = image.Rect(0, 0, avatarWidth, avatarHeight).Add(sp)
	mask := utils.CreateRoundedRectMask(float64(avatarWidth), float64(avatarHeight), 60)
	draw.DrawMask(cv, avatarBound, avatarDst, utils.ImageCenterInRect(avatarDst, avatarWidth, avatarHeight), mask, image.Point{}, draw.Over)

	fontLoader, err := utils.NewFontLoader()
	if err != nil {
		return nil, err
	}
	defer fontLoader.Close()

	face28 := "28"
	if err := fontLoader.LoadFace(face28, &opentype.FaceOptions{Size: 28}); err != nil {
		return nil, err
	}
	face34 := "34"
	if err := fontLoader.LoadFace(face34, &opentype.FaceOptions{Size: 34}); err != nil {
		return nil, err
	}
	face24 := "24"
	if err := fontLoader.LoadFace(face24, &opentype.FaceOptions{Size: 24}); err != nil {
		return nil, err
	}

	//昵称
	text := user.Nickname
	w, h := utils.MeasureString(text, fontLoader, face28)
	x := avatarBound.Max.X + 10
	y := avatarBound.Size().Y/2 + avatarBound.Min.Y - h.Round()/2 //按头像居中
	clr, _ := utils.ColorHex("#F3FF00")
	utils.DrawText(cv, x, y, text, fontLoader, face28, clr)

	text = "邀请你一起旅游！"
	x = x + w.Round() + 10
	utils.DrawText(cv, x, y, text, fontLoader, face28, color.White)

	// 出发<=>目的地中间的图片
	x = 360
	y = 153
	tripDst := utils.ImageResize(trip, 176, 48)
	tripBound = tripDst.Bounds().Add(image.Point{x, y})
	draw.Draw(cv, tripBound, tripDst, image.Point{}, draw.Over)

	//出发地
	w, _ = utils.MeasureString(plan.From, fontLoader, face34)
	x = x - 20 - w.Round()
	utils.DrawText(cv, x, y, plan.From, fontLoader, face34, color.White)

	// 目的地
	w, _ = utils.MeasureString(plan.To, fontLoader, face34)
	x = tripBound.Max.X + 20
	utils.DrawText(cv, x, y, plan.To, fontLoader, face34, color.White)

	type col struct {
		Text string
		MidX int //中线位置
	}
	colsTxt := []string{"时间", "住宿", "景点", "出行"}
	cols := make([]col, len(colsTxt))
	colValMap := map[string]string{
		"时间": fmt.Sprintf("%d天", plan.CostDay),
		"住宿": fmt.Sprintf("%d夜", plan.CostDay-1),
		"景点": fmt.Sprintf("%d个", plan.SceneCnt),
		"出行": utils.If(strutil.IsBlank(promptOptions.Transport), "自驾", promptOptions.Transport),
	}
	width := 380
	avgSp := float64(width) // 平均每列宽度
	if len(colsTxt) > 1 {
		avgSp = float64(width) / float64(len(colsTxt)-1)
	}
	x = 40 + (tripBound.Min.X+tripBound.Max.X-width)/2 //内容区最左边
	y = tripBound.Max.Y + 71
	for i, txt := range colsTxt {
		w, _ = utils.MeasureString(txt, fontLoader, face28) //文字宽度
		x1 := x + int(float64(i)*avgSp)
		utils.DrawText(cv, x1-w.Round()/2, y, txt, fontLoader, face28, color.White)
		cols[i] = col{Text: txt, MidX: x1}
	}

	y = tripBound.Max.Y + 25
	for _, v := range cols {
		val := colValMap[v.Text]
		w, _ = utils.MeasureString(val, fontLoader, face24)
		x = v.MidX - w.Round()/2
		utils.DrawText(cv, x, y, val, fontLoader, face24, color.White)
	}

	// 输出到临时文件缓存并响应
	buff := &bytes.Buffer{}
	if err := png.Encode(buff, cv); err != nil {
		return nil, utils.NewError(err)
	}

	if err := qiniu_biz.UploadReader(buff, file); err != nil {
		return nil, err
	}

	if in.ReturnUrl == constmap.Enable {
		return gin.H{"url": utils.StaticUrl(file)}, nil
	}
	ctx.Set(constmap.ContextHttpRaw, true)
	ctx.Redirect(301, utils.StaticUrl(file))

	return nil, nil
}
