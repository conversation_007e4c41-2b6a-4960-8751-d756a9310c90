package plan

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/handles/v1/front/plan/internal/def"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func History(ctx *gin.Context) (any, error) {
	db := utils.GetDB(ctx)

	type vitem struct {
		def.PlanDetailDto
		HistoryId  uint   `json:"history_id"`
		AiReqid    string `json:"ai_reqid"`
		AiProvider string `json:"ai_provider"`
	}
	var out struct {
		List []vitem `json:"list"`
	}
	out.List = make([]vitem, 0)

	session, err := business.GetFrontLoginUser(ctx)

	if err != nil {
		return out, nil
	}

	var list []*models.PlanHistory
	db.Where(models.PlanHistory{UserId: session.UserId}).Order("id desc").Limit(20).Find(&list)

	if len(list) == 0 {
		return out, nil
	}

	reqIds := slice.ReduceBy(list, make([]string, 0), func(index int, item *models.PlanHistory, agg []string) []string {
		return append(agg, item.AiReqid)
	})

	var plans []models.Plan
	db.Select("id,ai_reqid").
		Where("user_id=? AND ai_reqid in ? AND state=?", session.UserId, reqIds, constmap.Enable).Find(&plans)
	planMap := slice.ReduceBy(plans, make(map[string]uint), func(index int, item models.Plan, agg map[string]uint) map[string]uint {
		agg[item.AiReqid] = item.ID
		return agg
	})

	out.List = slice.FilterMap(list, func(index int, item *models.PlanHistory) (vitem, bool) {
		var dto vitem
		if detail, err := parseDetailFromHistory(item); err != nil {
			return dto, false
		} else {
			dto.PlanDetailDto = *detail
		}
		if pid, ok := planMap[item.AiReqid]; ok {
			dto.PlanDetailDto.PlanId = pid
			dto.PlanDetailDto.IsOwn = true
		}
		dto.HistoryId = item.ID
		dto.AiReqid = item.AiReqid
		dto.AiProvider = item.AiProvider
		return dto, true
	})
	slice.SortBy(out.List, func(a, b vitem) bool {
		return a.HistoryId < b.HistoryId
	})
	return out, nil
}

func parseDetailFromHistory(history *models.PlanHistory) (*def.PlanDetailDto, error) {
	detail := new(def.PlanDetailDto)
	journeyHistory := beans.JourneyHistory{}
	if err := json.Unmarshal([]byte(history.Details), &journeyHistory); err == nil {
		options := journeyHistory.PromptOptions
		journey := journeyHistory.JourneyData
		planTop := beans.TravelPlanTop{
			Days: len(journeyHistory.JourneyData.List),
		}
		planTop.SceneNum = slice.ReduceBy(journeyHistory.JourneyData.List, 0, func(index int, item *beans.Journey, agg int) int {
			slice.ForEach(item.Timeline, func(_ int, item *beans.JTimeline) {
				if item.Type == constmap.JTimelineScene {
					agg++
				}
			})
			return agg
		})
		if options != nil {
			planTop.PromptOptions = options
			planTop.From = options.From
			planTop.To = options.To
			planTop.Transport = options.Transport
		}
		detail = &def.PlanDetailDto{
			TravelPlanTop: planTop,
			Subject:       journey.Title,
			Subtitle:      journey.Subtitle,
			Notice:        journey.Notice,
			FitFor:        journey.FitFor,
			Budget:        journey.Budget,
			BudgetDetail:  journey.BudgetDetail,
			Sections: slice.Map(journey.List, func(index int, item *beans.Journey) def.PlanSection {
				return def.PlanSection{
					SectionName: item.Title,
					Subject:     item.Subject,
					Zones:       item.WayPoints,
					Timeline:    item.Timeline,
				}
			}),
		}
		return detail, nil
	} else {
		return nil, err
	}
}
