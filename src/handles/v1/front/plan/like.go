package plan

import (
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func Like(ctx *gin.Context) (any, error) {
	var in struct {
		AiReqid string `form:"ai_reqid"`
		PlanId  uint   `form:"plan_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if strutil.IsBlank(in.AiReqid) && in.PlanId == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	// 未收藏行程先执行收藏
	if in.PlanId < 1 && !strutil.IsBlank(in.AiReqid) {

		var chatMsg = new(ai.ChatCompletionResponse)
		my_cache.Get(fmt.Sprintf(constmap.RKAiResp, in.AiReqid), chatMsg)
		var userId = utils.If(chatMsg.UserId > 0, chatMsg.UserId, session.UserId)

		unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "plan:submit", fmt.Sprintf("%d", userId)), constmap.TimeDur5s)
		if err != nil {
			my_logger.Infof("PlanLike Submit Mutex ", zap.String("err", err.Error()))
			return nil, errors.New("操作过快，请稍后再试")
		}
		defer unlocker()

		//先查一遍是否已经存在plan关联表
		db.Model(&models.Plan{}).Select("ID").Where(models.Plan{AiReqid: in.AiReqid}).Scan(&in.PlanId)

		if in.PlanId < 1 {
			plan, err := plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
				UserId: userId,
				State:  constmap.Disable, //非”收藏“操作无需标记为启用
			}, chatMsg)
			if err != nil {
				return nil, err
			}

			err = db.Transaction(func(tx *gorm.DB) error {
				return plan_asm.SubmitPlan(tx, plan)
			})
			if err != nil {
				return nil, err
			}
			in.PlanId = plan.ID
		}
	}

	var cnt int64
	var plan = new(models.Plan)
	db.Take(&plan, in.PlanId)
	if plan.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	}

	var out struct {
		Op    bool `json:"op"`
		Likes int  `json:"likes"`
	}

	err := db.Transaction(func(tx *gorm.DB) error {

		update := map[string]any{}
		if tx.Model(&models.PlanLike{}).Where(models.PlanLike{PlanId: in.PlanId, UserId: session.UserId}).Count(&cnt); cnt > 0 {
			if err := tx.Unscoped().Delete(&models.PlanLike{}, models.PlanLike{PlanId: in.PlanId, UserId: session.UserId}).Error; err != nil {
				my_logger.Debugf("删除点赞失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			update["likes"] = gorm.Expr("likes-1")
			out.Op = false
		} else {
			if err := tx.Create(&models.PlanLike{PlanId: in.PlanId, UserId: session.UserId}).Error; err != nil {
				my_logger.Debugf("点赞失败", zap.Error(err))
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			update["likes"] = gorm.Expr("likes+1")
			out.Op = true
		}
		if tx.Model(&models.Plan{}).Where("id=?", in.PlanId).Updates(update).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}
		tx.Model(&models.Plan{}).Where("id=?", in.PlanId).Select("likes").Scan(&out.Likes)
		return nil
	})
	if err != nil {
		my_logger.Errorf("PlanLike", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	if out.Op {
		where := &beans.BeWhere{}
		where.Where.WriteString("act.uuid=?")
		where.Args = append(where.Args, constmap.ActWeekPk)
		uacts := activity_biz.FindValidUserActs(db, plan.UserId, where)
		for _, uact := range uacts {
			_ = my_queue.Light(constmap.EventTaskAction, gin.H{
				"task_event_req": convertor.ToString(task_asm.DoActivityTaskReq{
					Cond:       constmap.TaskCondPlanLike,
					UserId:     plan.UserId,
					ActivityId: uact.ActivityId,
					PlanId:     plan.ID,
					HelperUid:  session.UserId,
				}),
			})
		}
	}

	return out, nil
}
