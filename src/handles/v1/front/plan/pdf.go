package plan

import (
	"golang.org/x/exp/slices"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/business/user_biz/user_asm"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func Pdf(ctx *gin.Context) (any, error) {
	// 获取 plan_id 参数
	var in struct {
		PlanID string `form:"plan_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var plan models.Plan

	canGen := []constmap.PlanPdfState{
		constmap.PlanPdfStateFail,
		constmap.PlanPdfStateNone,
	}

	if err := db.Joins("Ext").Where(models.Plan{UserId: session.UserId}).Take(&plan, in.PlanID).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	} else if !slices.Contains(canGen, plan.Ext.PdfState) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程PDF正在生成中")
	}

	if c, err := sys_config_biz.GetConfig(db, constmap.SysConfigGeneratePlanPdfCostAmount); err == nil {
		value, _ := c.(int64)
		if value > 0 {
			err = db.Transaction(func(tx *gorm.DB) error {
				transaction, err := user_asm.CostPackageAmount(tx, session.UserId, int(value), constmap.AccountLogSubPlanPdf)
				if err != nil {
					return err
				}

				ext := plan.Ext
				if tx.Omit(clause.Associations).Model(&ext).
					Where(models.PlanExt{PdfState: constmap.PlanPdfStateNone}).
					Updates(models.PlanExt{
						PdfState:      constmap.PlanPdfStateIng,
						TransactionNo: transaction.TransactionNo,
					}).
					RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
				}

				return nil
			})

			if err != nil {
				return nil, err
			}
		}
	}

	_ = my_queue.PdfEvent(constmap.EventPlanPdf, gin.H{
		"plan_id": plan.ID,
	})

	var out struct {
		TplMsgId string `json:"tpl_msg_id"`
	}

	if cnf, err := sys_config_biz.GetConfig(db, constmap.SysConfigTplMsgKeys); err == nil {
		out.TplMsgId = cnf.(*beans.TplMsgKeys).PlanPdf
	}

	return out, nil
}
