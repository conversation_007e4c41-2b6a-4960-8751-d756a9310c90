package plan

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 提交大模型prmpt
func Prompt(ctx *gin.Context) (any, error) {
	var in struct {
		PromptOptions string `form:"prompt_options" binding:"required"`
		AiReqid       string `form:"ai_reqid"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}
	session, _ := business.GetFrontLoginUser(ctx)
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "aiprompt", fmt.Sprintf("%d", session.UserId)), constmap.TimeDur1m)
	if err != nil {
		my_logger.Infof("Prompt Mutex ", zap.String("err", err.Error()))
		return nil, errors.New("操作过快，请稍后重试")
	}
	defer unlocker()
	db := utils.GetDB(ctx)

	var promptOptions = new(beans.TravelPromptOptions)
	if err = json.Unmarshal([]byte(in.PromptOptions), &promptOptions); err != nil {
		my_logger.Errorf("Unmarshal PromptOptions", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	amount, err := plan_asm.GetCostIntegralAmount(db, false)
	if err != nil {
		return nil, err
	}

	var trans *models.AccountTransaction
	if err = db.Transaction(func(tx *gorm.DB) error {
		if trans, err = plan_asm.CostIntegral(tx, session.UserId, amount, in.AiReqid); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}

	aiModel := ai.NewDefaultAI()
	rsp, err := aiModel.ChatCompletionMessage(plan_asm.BuildPrompt(promptOptions, db))
	if err != nil {
		// 失败后回退
		if trans != nil {
			if transErr := db.Transaction(func(tx *gorm.DB) error {
				reversal, err := account_biz.CreateAccountTransactionReversal(tx, trans.TransactionNo, trans.Amount, "Prompt失败回退")
				if err != nil {
					return err
				}
				if err = account_biz.ExecuteAccountTransactionReversal(tx, reversal.BatchNo); err != nil {
					return err
				}
				return nil
			}); transErr != nil {
				my_logger.Errorf("ExecuteAccountTransactionReversal", zap.Error(transErr))
			}
		}
		return nil, err
	}
	rsp.From = promptOptions.From
	rsp.To = promptOptions.To
	rsp.PromptOptions = promptOptions
	rsp.IntegralCostState = constmap.Enable
	rsp.IntegralCost = amount
	_ = my_cache.SetDefault(fmt.Sprintf(constmap.RKAiResp, rsp.RequestId), rsp)

	var out struct {
		RequestId string `json:"request_id"`
		Provider  string `json:"provider"`
	}
	out.RequestId = rsp.RequestId
	out.Provider = rsp.Provider

	return out, nil
}
