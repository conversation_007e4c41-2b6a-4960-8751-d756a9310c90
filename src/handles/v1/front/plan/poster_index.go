package plan

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"net/url"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

// 行程海报页数据
func PosterIndex(ctx *gin.Context) (any, error) {
	var in struct {
		PlanId uint `form:"plan_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	planTop, sections, _ := plan_asm.BuildPlanSections(db, in.PlanId, "")

	var planCostDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{
		AiReqid: planTop.AiReqid,
	}).Take(&planCostDetail)

	var out struct {
		PlanCnt   int64    `json:"plan_cnt"`   //行程收藏数
		Subject   string   `json:"subject"`    //标题
		Subtitle  string   `json:"subtitle"`   //副标题
		MainTags  []string `json:"main_tags"`  //主标签
		SubTags   []string `json:"sub_tags"`   //副标签
		Scenes    []string `json:"scenes"`     //景点列表
		BgPic     string   `json:"bg_pic"`     //海报背景图片
		DetailUrl string   `json:"detail_url"` //行程详情页链接
		User      struct {
			Nickname string `json:"nickname"`
			Avatar   string `json:"avatar"`
		} `json:"user"`
		Poster string `json:"poster"`
	}

	u := &url.Values{}
	u.Set("plan_id", convertor.ToString(in.PlanId))
	u.Set("showLikeAnimation", "true")

	out.DetailUrl = utils.WxMiniPageUrl("pages/details/details", u)
	out.Subject = planTop.Subject
	out.Subtitle = planTop.Subtitle
	out.Poster = planTop.Poster
	out.MainTags = slice.FilterMap(strings.Split(planCostDetail.MainTags, ","), func(index int, item string) (string, bool) {
		return item, !strutil.IsBlank(item)
	})
	out.SubTags = slice.FilterMap(strings.Split(planCostDetail.SubTags, ","), func(index int, item string) (string, bool) {
		return item, !strutil.IsBlank(item)
	})
	var user = new(models.User)
	db.Take(&user, planTop.UserId)
	out.User.Nickname = user.Nickname
	out.User.Avatar = utils.AvatarUrl(user.Avatar)
	out.Scenes = make([]string, 0)

	var fromZoneId uint
	var targetZoneId uint
	slice.ForEach(sections, func(index int, item beans.PlanSection) {
		for _, v := range item.Content.PassingZones {
			if fromZoneId == 0 {
				fromZoneId = v
			}
			if targetZoneId == 0 || targetZoneId != fromZoneId {
				targetZoneId = v
			}
		}
		slice.ForEach(item.Content.Timeline, func(index int, item *beans.JTimeline) {
			if item.Type == constmap.JTimelineScene {
				out.Scenes = append(out.Scenes, item.Poi.Name)
			}
		})
	})

	if planCostDetail.PrimeScenes.Data != nil {
		primeScenes := *planCostDetail.PrimeScenes.Data
		if len(primeScenes) > 0 {
			out.BgPic = utils.StaticUrl(primeScenes[0].Pic)
		}
	}

	if targetZoneId > 0 && strutil.IsBlank(out.BgPic) {
		var zone models.Zone
		db.Take(&zone, targetZoneId)
		if !strutil.IsBlank(zone.Pic) {
			out.BgPic = utils.StaticUrl(zone.Pic)
		}
	}

	if strutil.IsBlank(out.BgPic) {
		out.BgPic = "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/poster-default.jpg?v=1"
	}

	db.Model(&models.Plan{}).Where(models.Plan{
		UserId: planTop.UserId,
		State:  constmap.Enable,
	}).Count(&out.PlanCnt)

	return out, nil
}
