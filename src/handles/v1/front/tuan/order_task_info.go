package tuan

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func OrderTaskInfo(ctx *gin.Context) (any, error) {
	var in struct {
		TuanId  uint `form:"tuan_id" binding:"required"`
		OrderId uint `form:"order_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var order = new(models.Order)

	q := db.Where(models.Order{
		OrderType: constmap.OrderTypeTuan,
		UserId:    session.UserId,
		ProductId: in.TuanId,
	}).Where("state in ?", []any{constmap.OrderStatePayed, constmap.OrderStateComplete}).Order("id DESC")
	if in.OrderId > 0 {
		q.Where("id = ?", in.OrderId)
	}
	q.Take(&order)
	if order.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorNoOrder, "您还未购买该商品")
	}

	taskMap := task_biz.LoadRelateTasks(db, []uint{order.ProductId}, constmap.TaskRelateTuan)
	if len(taskMap) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "团游未关联任务")
	}

	ptask := taskMap[order.ProductId]

	var tasks []models.Task
	db.Where(models.Task{
		ParentTaskId: ptask.ID,
		State:        constmap.Enable,
	}).Order("id ASC").Find(&tasks)
	sceneIds := make([]uint, 0)
	taskIds := slice.Map(tasks, func(index int, item models.Task) uint {
		sceneIds = append(sceneIds, item.ScenicId)
		return item.ID
	})

	var utasks []*models.UserTask
	db.Where("user_id=? AND task_id in ? AND order_id=?", session.UserId, append(taskIds, ptask.ID), order.ID).Find(&utasks)
	utaskMap := slice.ReduceBy(utasks, make(map[uint]*models.UserTask), func(index int, item *models.UserTask, agg map[uint]*models.UserTask) map[uint]*models.UserTask {
		agg[item.TaskId] = item
		return agg
	})

	var scenes []models.Scenic
	db.Find(&scenes, sceneIds)
	sceneMap := slice.ReduceBy(scenes, make(map[uint]models.Scenic), func(index int, item models.Scenic, agg map[uint]models.Scenic) map[uint]models.Scenic {
		agg[item.ID] = item
		return agg
	})

	putask, ok := utaskMap[ptask.ID]
	if !ok {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "用户任务不存在")
	}

	type scene struct {
		Id      uint    `json:"id"`
		Name    string  `json:"name"`
		Pic     string  `json:"pic"`
		Address string  `json:"address"`
		Lng     float64 `json:"lng"`
		Lat     float64 `json:"lat"`
	}
	type vtask struct {
		Id            uint                   `json:"id"`
		Name          string                 `json:"name"`
		Cover         string                 `json:"cover"`
		RewardName    string                 `json:"reward_name"`
		ShortDesc     string                 `json:"short_desc"`
		Children      []vtask                `json:"children"`
		UserTaskState constmap.UserTaskState `json:"user_task_state"`
		Scene         scene                  `json:"scene"`
	}

	type taskReward struct {
		RewardType      constmap.TaskReward          `json:"reward_type"`
		RewardState     constmap.UserTaskRewardState `json:"reward_state"`
		RealProductName string                       `json:"real_product_name"`
		Num             int                          `json:"num"`        //奖品数量
		Receiver        string                       `json:"receiver"`   //收货人
		RecvPhone       string                       `json:"recv_phone"` //收货人电话
		RecvAddr        string                       `json:"recv_addr"`
		DeliveryName    string                       `json:"delivery_name"` //物流公司
		DeliveryNo      string                       `json:"delivery_no"`   //物流单号
	}

	foo := func(task models.Task) vtask {
		s := sceneMap[task.ScenicId]
		return vtask{
			Id:         task.ID,
			Name:       task.Name,
			Cover:      utils.StaticUrl(task.Cover),
			RewardName: task.RealProductName,
			ShortDesc:  task.ShortDesc,
			Children:   make([]vtask, 0),
			Scene: scene{
				Id:      s.ID,
				Name:    s.Name,
				Pic:     utils.StaticUrl(s.Pic),
				Address: s.Address,
				Lng:     s.Lng,
				Lat:     s.Lat,
			},
		}
	}

	bar := func(v vtask, utask *models.UserTask) vtask {
		if utask == nil {
			v.UserTaskState = constmap.UserTaskProcessing
		} else {
			v.UserTaskState = utask.State
		}
		return v
	}

	var reward = new(models.UserTaskReward)
	db.Where(models.UserTaskReward{
		UserTaskId: putask.ID,
	}).Take(&reward)

	var out struct {
		OrderId          uint        `json:"order_id"`
		Task             vtask       `json:"task"`
		Reward           *taskReward `json:"reward"`
		RewardPickupAddr string      `json:"reward_pickup_addr"` //自提地址
	}
	if reward.ID > 0 {
		out.Reward = &taskReward{
			RewardType:      reward.RewardType,
			RewardState:     reward.RewardState,
			RealProductName: reward.RealProductName,
			Num:             reward.Num,
			Receiver:        reward.Receiver,
			RecvPhone:       reward.RecvPhone,
			RecvAddr:        reward.RecvAddr,
			DeliveryName:    reward.DeliveryName,
			DeliveryNo:      reward.DeliveryNo,
		}
	}
	out.OrderId = order.ID
	out.RewardPickupAddr = "黄浦区-南京东路830号第一百货商业中心a馆8楼"
	out.Task = foo(ptask)
	out.Task = bar(out.Task, putask)
	slice.ForEach(tasks, func(index int, item models.Task) {
		v := foo(item)
		v = bar(v, utaskMap[item.ID])
		out.Task.Children = append(out.Task.Children, v)
	})
	return out, nil
}
