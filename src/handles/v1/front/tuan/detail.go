package tuan

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	var product models.Tuan

	if err := db.Where("tuans.id=?", in.Id).
		Joins("Desc").
		Preload("Pics").
		Preload("Nodes").
		Preload("Skus").
		Take(&product).Error; err != nil {
		return nil, err
	}

	type sku struct {
		Id          uint   `json:"id"`
		Name        string `json:"name"`
		Price       int    `json:"price"`
		IsRequire   bool   `json:"is_require"`
		AdultNum    int    `json:"adult_num"`
		ChildrenNum int    `json:"children_num"`
	}

	var data struct {
		Id            uint                 `json:"id"`
		Name          string               `json:"name"`
		State         int                  `json:"state"`
		MainPic       string               `json:"main_pic"`
		Pics          []string             `json:"pics"`
		Desc          string               `json:"desc"`
		ShortDesc     string               `json:"short_desc"`
		Days          int                  `json:"days"`
		Nodes         []string             `json:"nodes"`
		Schedules     []beans.TuanSchedule `json:"schedules"`
		Skus          []sku                `json:"skus"`
		Price         int                  `json:"price"`
		ProductType   constmap.ProductType `json:"product_type"`
		AgeLimit      string               `json:"age_limit"`
		MemberLimit   string               `json:"member_limit"`
		Base          string               `json:"base"`
		Customization string               `json:"customization"`
		NeedPeople    bool                 `json:"need_people"`
		RefundRules   []string             `json:"refund_rules"`
	}

	data.ProductType = constmap.ProductTypeTuan
	data.Id = product.ID
	data.Name = product.Name
	data.State = product.State
	data.Desc = product.Desc.Content
	data.MainPic = utils.StaticUrl(product.Pic)
	data.ShortDesc = product.ShortDesc
	data.Days = product.Days
	data.Price = product.Price
	data.AgeLimit = product.AgeLimit
	data.MemberLimit = product.MemberLimit
	data.Base = product.Base
	data.Customization = product.Customization
	data.NeedPeople = product.NeedPeople == constmap.Enable
	data.Nodes = slice.Map(product.Nodes, func(index int, item models.TuanTripNode) string {
		return item.Name
	})
	data.Schedules = slice.Map(product.Nodes, func(index int, item models.TuanTripNode) beans.TuanSchedule {
		return beans.TuanSchedule{
			Name:      item.Name,
			ShortDesc: item.ShortDesc,
			Content:   item.Content,
		}
	})
	data.Pics = slice.Map(product.Pics, func(index int, item models.TuanPic) string {
		return utils.StaticUrl(item.Url)
	})
	data.Skus = slice.Map(product.Skus, func(index int, item models.TuanSku) sku {
		return sku{
			Id:          item.ID,
			Price:       item.Price,
			Name:        item.Name,
			IsRequire:   utils.EnableBool(item.IsRequire),
			AdultNum:    item.AdultNum,
			ChildrenNum: item.ChildrenNum,
		}
	})
	rules, _ := beans.UnmarshalTuanRefundRule(product.RefundRules)
	data.RefundRules = orders.BuildTuanRefundRules(product.CanRefund, rules)

	return data, nil
}
