package tuan

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 【废弃】使用skudates
func Dates(ctx *gin.Context) (any, error) {
	var in struct {
		Id    uint  `form:"id" binding:"required"`
		Start int64 `form:"start" binding:"required"`
		Limit int   `form:"limit"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	if in.Limit == 0 {
		in.Limit = 100
	}

	db := utils.GetDB(ctx)

	var list []models.TuanDate
	db.Order("date asc").
		Where("product_id=? and date>=?", in.Id, time.Unix(in.Start, 0)).
		Limit(in.Limit).Find(&list)

	var data struct {
		List []int64 `json:"list"`
	}
	data.List = slice.Map(list, func(index int, item models.TuanDate) int64 {
		return item.Date.Unix()
	})

	return data, nil
}
