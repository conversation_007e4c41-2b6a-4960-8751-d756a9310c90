package tuan

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/validators"
)

func GetTaskReward(ctx *gin.Context) (any, error) {
	var in struct {
		OrderId    uint   `form:"order_id" binding:"required"`
		SelfRecv   int    `form:"self_recv" binding:"required"` //是否自提
		ZoneId     string `form:"zone_id"`                      //收货地址所在城市
		RecvZoneId uint   `form:"recv_zone_id"`                 //收货地址所在城市
		Receiver   string `form:"receiver"`                     //收货人
		RecvPhone  string `form:"recv_phone"`                   //收货人电话
		RecvAddr   string `form:"recv_addr"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	if in.SelfRecv != constmap.Enable {
		if strutil.IsBlank(in.Receiver) || strutil.IsBlank(in.Receiver) || strutil.IsBlank(in.RecvPhone) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
		if !validators.IsMobile(in.RecvPhone) {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "请输入正确的手机号码")
		}
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "tuanTaskReward", convertor.ToString(session.UserId)),
		constmap.TimeDur1m)
	if err != nil {
		return nil, err
	}
	defer unlocker()

	var order = new(models.Order)
	db.Preload("Details").Where(models.Order{
		UserId:    session.UserId,
		OrderType: constmap.OrderTypeTuan,
	}).Where("state in ?", []int{constmap.OrderStatePayed, constmap.OrderStateComplete}).Take(&order, in.OrderId)
	if order.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单不存在")
	}

	taskMap := task_biz.LoadRelateTasks(db, []uint{order.ProductId}, constmap.TaskRelateTuan)
	task, ok := taskMap[order.ProductId]
	if !ok {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "未关联任务")
	}
	var utask = new(models.UserTask)
	db.Where(models.UserTask{
		UserId:  session.UserId,
		TaskId:  task.ID,
		OrderId: order.ID,
	}).Take(utask)

	if utask.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您未参与任务")
	}

	if utask.State != constmap.UserTaskWaitReward {
		if utask.State != constmap.UserTaskDone {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "您还未完成任务")
		}
		return nil, utils.NewErrorStr(constmap.ErrorParam, "您已领取过奖励了")
	}
	var num int

	//已退款的不计入
	refunds := orders.GetRefundsByDetails(db, order.Details)
	slice.ForEach(order.Details, func(index int, item models.OrderDetail) {
		num += item.Quantity
		if rs, ok := refunds[item.ID]; ok {
			slice.ForEach(rs, func(index int, item2 models.RefundSuborder) {
				if item2.State != constmap.RefundSubOrderReject {
					num -= item2.RefundQuantity
				}
			})
		}
	})

	reward := &models.UserTaskReward{
		UserTaskId:      utask.ID,
		UserId:          session.UserId,
		TaskId:          task.ID,
		OrderId:         order.ID,
		RewardType:      task.RewardType,
		RewardState:     utils.If(in.SelfRecv == constmap.Enable, constmap.UserTaskRewardSelf, constmap.UserTaskRewardWait),
		RealProductName: task.RealProductName,
		Num:             num,
		Receiver:        in.Receiver,
		RecvPhone:       in.RecvPhone,
		RecvAddr:        in.RecvAddr,
	}

	var zoneId uint
	if in.RecvZoneId > 0 {
		zoneId = in.RecvZoneId
	} else if !strutil.IsBlank(in.ZoneId) {
		if zid, err := convertor.ToInt(in.ZoneId); err == nil {
			zoneId = uint(zid)
		}
	}
	if zoneId > 0 {
		zs := zone_biz.NewZoneBiz().GetZonesToTop(db, []uint{zoneId}, constmap.ZoneLevelProvince)
		if z, ok := zs[zoneId]; ok {
			reward.RecvAddr = z.Name + " " + reward.RecvAddr
			for z.Parent != nil {
				z = z.Parent
				reward.RecvAddr = z.Name + " " + reward.RecvAddr
			}
		}
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(reward).Error; err != nil {
			return err
		}
		if tx.Model(&utask).Where("version=?", utask.Version).Updates(map[string]any{
			"state":   constmap.UserTaskDone,
			"version": utils.IncrVersion(utask.Version),
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "任务记录更新失败")
		}
		if err := tx.Create(&models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     utask.UserId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogReward,
		}).Error; err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, utils.NewError(err)
	}

	return nil, nil
}
