package tuan

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"
)

func SkuDates(ctx *gin.Context) (any, error) {
	var in struct {
		TuanId uint  `form:"tuan_id" binding:"required"`
		Start  int64 `form:"start" binding:"required"`
		End    int64 `form:"end"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	if in.End == 0 {
		in.End = in.Start + 86400*100
	}
	if in.Start > in.End {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	//最多一年跨度
	if in.End-in.Start > 86400*360 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	start := time.Unix(in.Start, 0)
	end := time.Unix(in.End, 0)
	startStr := start.Format(constmap.DateFmtLong)
	endStr := end.Format(constmap.DateFmtLong)

	var stocks []models.TuanDateStock
	db.Where("product_id=? and date between ? and ?", in.TuanId, startStr, endStr).Find(&stocks)
	leftStockMap := slice.ReduceBy(stocks, make(map[string]int), func(index int, item models.TuanDateStock, agg map[string]int) map[string]int {
		agg[item.Date.Format(constmap.DateFmtLong)] = item.TotalStock - item.SaleStock
		return agg
	})

	var dates []models.TuanDate
	db.Preload("Skus").
		Where("product_id=? AND ((start<=? AND end>=?) OR (start>=? AND start<=?))",
			in.TuanId, startStr, startStr, startStr, endStr).
		Find(&dates)
	type dt struct {
		Data    models.TuanDate
		Start   time.Time
		End     time.Time
		WeekSet *typeset.TypeSet[int]
	}
	dts := slice.Map(dates, func(index int, item models.TuanDate) dt {
		weeks, _ := utils.ToArray[int](item.Weeks, ",")
		return dt{
			Data:    item,
			Start:   item.Start,
			End:     item.End,
			WeekSet: typeset.NewTypeSet(false, weeks...),
		}
	})
	type sku struct {
		SkuId uint `json:"sku_id"`
		Price int  `json:"price"`
	}
	type outItem struct {
		Date      int64 `json:"date"`
		Skus      []sku `json:"skus"`
		LeftStock int   `json:"left_stock"`
	}
	var out struct {
		List []outItem `json:"list"`
	}
	out.List = make([]outItem, 0)
	for start.Unix() <= end.Unix() {
		week := int(start.Weekday())
		day := start.Format(constmap.DateFmtLong)
		if week == 0 {
			week = 7
		}
		for _, v := range dts {
			if !v.WeekSet.Has(week) {
				continue
			}
			if day < v.Start.Format(constmap.DateFmtLong) || day > v.End.Format(constmap.DateFmtLong) {
				continue
			}
			item := outItem{
				Date: start.Unix(),
				Skus: slice.Map(v.Data.Skus, func(index int, item models.TuanDateSku) sku {
					return sku{SkuId: item.SkuId, Price: item.Price}
				}),
			}
			if stock, ok := leftStockMap[day]; ok {
				item.LeftStock = stock
			} else {
				item.LeftStock = v.Data.Stock
			}
			out.List = append(out.List, item)
			break
		}
		start = start.AddDate(0, 0, 1)
	}
	return out, nil
}
