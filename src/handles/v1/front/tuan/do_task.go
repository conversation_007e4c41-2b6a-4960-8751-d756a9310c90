package tuan

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func DoTask(ctx *gin.Context) (any, error) {
	var in struct {
		TuanId  uint    `form:"tuan_id" binding:"required"`
		SceneId uint    `form:"scene_id" binding:"required"`
		Lng     float64 `form:"loc_lng" binding:"required"`
		Lat     float64 `form:"loc_lat" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	tuan := new(models.Tuan)
	db.Take(&tuan, in.TuanId)

	if tuan.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "团游商品不存在")
	}
	ptmap := task_biz.LoadRelateTasks(db, []uint{tuan.ID}, constmap.TaskRelateTuan)
	if len(ptmap) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "任务已下线")
	}
	var parentTask *models.Task
	if p, ok := ptmap[tuan.ID]; ok {
		parentTask = &p
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "任务已下线")
	}

	var out struct {
		OrderIds []uint `json:"order_ids"`
		Success  bool   `json:"success"`
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		rsp, err := task_biz.DoTuanTask(tx, task_biz.DoTuanTaskReq{
			UserId:   session.UserId,
			TaskCond: constmap.TaskCondSceneSign,
			Tuan:     tuan,
			Task:     parentTask,
			ScenicId: in.SceneId,
			Lng:      in.Lng,
			Lat:      in.Lat,
		})
		if err != nil {
			return err
		}
		out.OrderIds = rsp.OrderIds
		return nil
	})
	if err != nil {
		return nil, utils.NewError(err)
	}

	out.Success = len(out.OrderIds) > 0

	return out, nil
}
