package tuan

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
)

func List(ctx *gin.Context) (any, error) {
	var in struct {
		ProvinceId uint   `form:"province_id"`
		Keyword    string `form:"keyword"`
		Cate       string `form:"cate"`
		Days       string `form:"days"`
	}

	_ = ctx.ShouldBind(&in)

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	type item struct {
		Id       uint     `json:"id"`
		Name     string   `json:"name"`
		Pic      string   `json:"pic"`
		ZoneName string   `json:"zone_name"`
		Price    int      `json:"price"`
		Cate     []string `json:"cate"`
		Days     int      `json:"days"`
		Type     string   `json:"type"`
		Link     string   `json:"link"`
	}

	type out struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}
	var data = out{
		List: make([]item, 0),
	}

	must := []map[string]any{
		{"term": map[string]any{"state": constmap.Enable}},
	}
	should := make([]map[string]any, 0)

	if in.ProvinceId > 0 {
		var zones []models.Zone
		db.Where("parent_id=?", in.ProvinceId).Find(&zones)
		zoneIds := slice.Map(zones, func(index int, item models.Zone) uint {
			return item.ID
		})
		if len(zoneIds) > 0 {
			must = append(must, map[string]any{
				"terms": map[string]any{"zone_id": zoneIds},
			})
		}
	}

	if in.Keyword != "" {
		must = append(must, map[string]any{
			"match_phrase": map[string]any{"keycnt": in.Keyword},
		})
	}

	if in.Cate != "" {
		must = append(must, map[string]any{
			"terms": map[string]any{"cate": strutil.SplitAndTrim(in.Cate, ",")},
		})
	}

	if in.Days != "" {
		for _, v := range strutil.SplitAndTrim(in.Days, ",") {
			var rg string
			var day = v
			if idx := strings.IndexByte(v, ':'); idx > -1 {
				rg = v[:idx]
				day = v[idx+1:]
			}
			iDay, err := convertor.ToInt(day)
			if err != nil || iDay == 0 {
				return nil, utils.NewErrorStr(constmap.ErrorParam, "行程天数格式错误")
			}
			switch rg {
			case "gte", "gt":
				should = append(should, map[string]any{
					"range": map[string]any{
						"days": map[string]any{
							rg: iDay,
						},
					},
				})
			default:
				should = append(should, map[string]any{
					"term": map[string]any{"days": iDay},
				})
			}
		}
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must":                 must,
				"should":               should,
				"minimum_should_match": utils.If(len(should) > 0, 1, 0),
			},
		},
		"sort": []map[string]any{
			{"_score": map[string]any{"order": "desc"}},
			{"obj_id": map[string]any{"order": "desc"}},
		},
		"track_total_hits": true,
		"from":             (page - 1) * pageSize,
		"size":             pageSize,
	}
	res, err := es.Search[es2.TuanModel](constmap.EsIndexTuan, query)
	if err != nil {
		my_logger.Errorf("search tuan es", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "搜索团游列表失败")
	}
	data.Total = int64(res.Hits.Total.Value)
	if len(res.Hits.Hits) > 0 {
		ids := make([]uint, 0)
		sortMap := slice.ReduceBy(res.Hits.Hits, make(map[uint]int), func(i int, v es.Hit[es2.TuanModel], agg map[uint]int) map[uint]int {
			ids = append(ids, v.Source.ObjId)
			agg[v.Source.ObjId] = i
			return agg
		})

		var list []models.Tuan
		db.Preload("Skus").
			Joins("Zone").Find(&list, ids)
		data.List = make([]item, len(list))

		// 按搜索结果顺序重排
		slice.SortBy(list, func(a, b models.Tuan) bool {
			return sortMap[a.ID] < sortMap[b.ID]
		})

		for i, v := range list {
			it := item{
				Id:       v.ID,
				Name:     v.Name,
				Pic:      utils.StaticUrl(v.Pic),
				ZoneName: v.Zone.Name,
				Cate:     strutil.SplitAndTrim(v.Cate, ","),
				Days:     v.Days,
				Price:    v.Price,
				Type:     "tuan",
			}
			//if len(v.Skus) > 0 {
			//	it.Price = v.Skus[0].Price
			//	for _, skus := range v.Skus {
			//		if skus.Price < it.Price {
			//			it.Price = skus.Price
			//		}
			//
			//		if skus.IsRequire == constmap.Enable {
			//			it.Price = skus.Price
			//			break
			//		}
			//	}
			//}

			data.List[i] = it
		}
	}

	acts := activity_biz.FindValidActs(db, "uuid=?", []any{constmap.ActLaxin})
	slice.ForEach(acts, func(index int, v models.Activity) {
		data.List = append([]item{
			{
				Id:   v.ID,
				Name: v.Title,
				Pic:  "https://rp.yjsoft.com.cn/yiban/static/activity/new/faxian-item-first.png",
				Cate: make([]string, 0),
				Type: "activity",
				Link: fmt.Sprintf("/pages/activity/activity?code=%s", v.Uuid),
			},
		}, data.List...)
		data.Total++
	})

	return data, nil
}
