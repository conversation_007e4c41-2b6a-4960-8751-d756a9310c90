package tuan

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 团游搜索配置
func Option(ctx *gin.Context) (any, error) {
	type d struct {
		Name  string `json:"name"`
		Value string `json:"value"`
	}
	type out struct {
		Cate []string `json:"cate"`
		Days []d      `json:"days"`
	}

	var rsp = out{
		Cate: make([]string, 0),
		Days: []d{
			{Name: "2天", Value: "2"},
			{Name: "3天", Value: "3"},
			{Name: "4天", Value: "4"},
			{Name: "5天", Value: "5"},
			{Name: "10天以上", Value: "gte:10"},
		},
	}
	db := utils.GetDB(ctx)
	var tuanCates []models.TuanCate
	db.Find(&tuanCates)
	for _, v := range tuanCates {
		rsp.Cate = append(rsp.Cate, v.Name)
	}
	return rsp, nil
}
