package lightbox

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

func Version(ctx *gin.Context) (any, error) {
	var in struct {
		Arch     string `form:"arch" binding:"required"`
		Version  string `form:"version" binding:"required"`
		Os       string `form:"os" binding:"required"`
		PackType string `form:"pack_type"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	var data struct {
		Version     string `json:"version"`
		Url         string `json:"url"`
		Size        int    `json:"size"`
		ReleaseTime int64  `json:"release_time"`
		Note        string `json:"note"`
	}

	v, err := convertor.ToInt(strings.ReplaceAll(in.Version, ".", ""))
	if err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)

	var lbVersion models.LightboxVersion
	whereStr := "arch = ? and os = ? and build_number>?"
	whereArgs := []interface{}{in.Arch, in.Os, v}

	if in.PackType != "" {
		whereStr += " and pack_type =?"
		whereArgs = append(whereArgs, in.PackType)
	}

	if db.Where(whereStr, whereArgs...).Order("id desc").Take(&lbVersion).Error == nil {
		data.Version = lbVersion.Version
		data.Url = lbVersion.Url
		data.Size = lbVersion.Size
		data.ReleaseTime = lbVersion.ReleaseTime.Unix()
		data.Note = lbVersion.Note
	}

	return data, nil
}
