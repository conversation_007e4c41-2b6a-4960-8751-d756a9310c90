package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 消息汇总响应
type MessageIndexResponse struct {
	SystemMessages  *message_biz.MessageTypeSummary `json:"system_messages"`
	TeamMessages    *message_biz.MessageTypeSummary `json:"team_messages"`
	CommentMessages *message_biz.MessageTypeSummary `json:"comment_messages"`
}

// 消息汇总接口（优化版：1条SQL替代9-15条）
func Index(ctx *gin.Context) (any, error) {
	session, err := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录
	if err != nil {
		return MessageIndexResponse{
			SystemMessages: &message_biz.MessageTypeSummary{
				Messages: make([]*message_biz.MessageVO, 0),
			},
			TeamMessages: &message_biz.MessageTypeSummary{
				Messages: make([]*message_biz.MessageVO, 0),
			},
			CommentMessages: &message_biz.MessageTypeSummary{
				Messages: make([]*message_biz.MessageVO, 0),
			},
		}, nil
	}

	db := utils.GetDB(ctx)
	userId := session.UserId

	// 定义需要查询的消息类型和每种类型的消息数量
	messageTypes := []constmap.MessageType{
		constmap.MessageTypeSystem,
		constmap.MessageTypeTeam,
		constmap.MessageTypeComment,
	}
	messageLimit := 3 // 每种类型返回3条最新消息

	// 使用新的批量查询方法，按类型分别缓存
	summaryMap, err := message_biz.GetMessageTypeSummaries(db, userId, messageTypes, messageLimit)
	if err != nil {
		return nil, err
	}

	response := MessageIndexResponse{
		SystemMessages:  summaryMap[constmap.MessageTypeSystem],
		TeamMessages:    summaryMap[constmap.MessageTypeTeam],
		CommentMessages: summaryMap[constmap.MessageTypeComment],
	}

	return response, nil
}
