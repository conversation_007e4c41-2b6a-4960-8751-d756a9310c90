package message

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
	"unicode/utf8"
)

// 快捷回复评论
func ReplyComment(ctx *gin.Context) (any, error) {
	var in struct {
		MessageId uint   `form:"message_id" binding:"required"`
		Content   string `form:"content" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 验证回复内容长度
	if len(in.Content) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "回复内容不能为空")
	}
	if utf8.RuneCountInString(in.Content) > 300 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "回复内容不能超过300个字")
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 防并发处理
	mutexKey := fmt.Sprintf(constmap.RKMutex, "replyComment", fmt.Sprintf("%d:%d", session.UserId, in.MessageId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "操作过快，请稍后重试")
	}
	defer unlocker()

	// 2. 查询消息模板和用户消息状态
	messageDetail, err := message_biz.GetUserMessageDetail(db, in.MessageId, session.UserId)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "消息不存在或无权限访问")
	}

	// 3. 验证消息是否为评论消息且未读
	if messageDetail.Type != constmap.MessageTypeComment {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "不是评论消息")
	}

	if messageDetail.IsRead == constmap.Enable {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "消息已处理")
	}

	// 4. 查询原始评论记录
	var originalComment models.WishComment
	if err := db.Take(&originalComment, messageDetail.WishCommentId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "原始评论不存在")
	}

	// 5. 查询心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, originalComment.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 6. 验证权限：只有心愿单发起人可以回复评论
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无权限回复此评论")
	}

	// 7. 验证心愿单状态：只有进行中或成功的心愿单可以回复评论
	if wish.State != constmap.WishStateProcessing && wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单状态不允许回复评论")
	}

	// 8. 验证原始评论状态：只有审核通过的评论可以回复
	if originalComment.State != constmap.WishCommentStateApproved {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "原始评论状态异常，无法回复")
	}

	// 9. 使用事务处理回复创建和消息标记
	var replyComment *models.WishComment
	err = db.Transaction(func(tx *gorm.DB) error {
		// 创建回复评论
		replyComment = &models.WishComment{
			WishId:          originalComment.WishId,
			UserId:          session.UserId,
			ParentCommentId: originalComment.ID,
			Content:         in.Content,
			State:           constmap.WishCommentStateApproved, // 心愿单发起人的回复直接通过
			IsPrivate:       constmap.Disable,                  // 回复默认公开
		}

		if err := tx.Create(replyComment).Error; err != nil {
			return err
		}

		// 标记消息为已读
		now := time.Now()
		if err := tx.Model(&models.Message{}).
			Where("message_tpl_id = ? AND user_id = ?", in.MessageId, session.UserId).
			Updates(models.Message{
				IsRead:   constmap.Enable,
				ReadTime: &now,
			}).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "回复失败，请稍后重试")
	}

	// 10. 清除相关缓存（事务成功后清理）
	message_biz.ClearUserMessageCacheByTypes(session.UserId, []constmap.MessageType{constmap.MessageTypeComment})

	// 11. 组装返回数据
	var out struct {
		CommentId uint   `json:"comment_id"`
		ReplyId   uint   `json:"reply_id"`
		Content   string `json:"content"`
		Status    string `json:"status"`
	}
	out.CommentId = originalComment.ID
	out.ReplyId = replyComment.ID
	out.Content = in.Content
	out.Status = "replied"

	return out, nil
}
