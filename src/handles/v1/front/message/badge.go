package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/utils"
)

// 消息徽章接口 - 返回未读消息数
func Badge(ctx *gin.Context) (any, error) {
	session, err := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录
	if err != nil {
		return new(message_biz.MessageBadge), nil
	}

	db := utils.GetDB(ctx)
	userId := session.UserId

	// 获取消息徽章（未读数统计）
	badge, err := message_biz.GetMessageBadge(db, userId)
	if err != nil {
		return nil, err
	}

	return badge, nil
}
