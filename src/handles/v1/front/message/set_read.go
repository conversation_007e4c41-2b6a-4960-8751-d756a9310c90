package message

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 批量已读(只标记子类型为普通文本的消息。其他类型由用户进行相关操作时，会自动标记为已读)
func SetRead(ctx *gin.Context) (any, error) {
	var in struct {
		MessageIds string `form:"message_ids" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录

	var msgIds []uint
	if v, err := utils.ToArray[uint](in.MessageIds, ","); err != nil {
		return nil, utils.NewError(err)
	} else {
		msgIds = v
	}
	if len(msgIds) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	userId := session.UserId

	// 批量设置消息已读（仅普通文本消息）
	err := message_biz.BatchSetMessageRead(db, userId, msgIds)
	if err != nil {
		return nil, err
	}

	return gin.H{"success": true}, nil
}
