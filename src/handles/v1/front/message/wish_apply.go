package message

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
	"unicode/utf8"
)

// 同意/拒绝申请
func WishApply(ctx *gin.Context) (any, error) {
	var in struct {
		MessageId    uint   `form:"message_id" binding:"required"`
		Accept       int    `form:"accept" binding:"required"` // 1=同意, 2=拒绝
		RejectReason string `form:"reject_reason"`             // 拒绝原因，拒绝时可选填写
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	// 验证accept参数
	if in.Accept != constmap.Enable && in.Accept != constmap.Disable {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "参数错误")
	}

	// 如果是拒绝且填写了拒绝原因，验证长度
	if in.Accept == constmap.Disable && utf8.RuneCountInString(in.RejectReason) > 200 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "拒绝原因不能超过200个字")
	}

	db := utils.GetDB(ctx)
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		return nil, err
	}

	// 1. 防并发处理
	mutexKey := fmt.Sprintf(constmap.RKMutex, "wishApplyResponse", fmt.Sprintf("%d:%d", session.UserId, in.MessageId))
	unlocker, err := my_cache.Mutex(mutexKey, constmap.TimeDur1m)
	if err != nil {
		return nil, err
	}
	defer unlocker()

	// 2. 查询消息模板和用户消息状态
	messageDetail, err := message_biz.GetUserMessageDetail(db, in.MessageId, session.UserId)
	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "消息不存在或无权限访问")
	}

	// 3. 验证消息是否为组队申请且未处理（未读）
	if messageDetail.Type != constmap.MessageTypeTeam || messageDetail.SubType != constmap.MessageSubTypeTeamApply {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "不是组队申请消息")
	}

	if messageDetail.IsRead == constmap.Enable {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "消息已处理")
	}

	// 4. 查询心愿单成员记录
	var wishMember models.WishMember
	if err := db.Take(&wishMember, messageDetail.WishMemberId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "申请记录不存在")
	}

	// 5. 查询心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, wishMember.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 6. 验证权限：只有心愿单发起人可以处理申请
	if wish.UserId != session.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "只有发起人可以处理申请")
	}

	// 7. 验证心愿单状态是进行中
	if wish.State != constmap.WishStateProcessing {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单已结束，无法处理申请")
	}

	// 8. 验证申请人在成员列表中是申请中状态
	if wishMember.State != constmap.WishMemberStateWaitReview {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "申请状态异常，无法处理")
	}

	// 9. 使用事务处理状态更新和消息发送
	var accepted bool
	err = db.Transaction(func(tx *gorm.DB) error {
		// 更新成员状态和拒绝原因
		updateData := models.WishMember{}

		if in.Accept == constmap.Enable {
			updateData.State = constmap.WishMemberStateApproved // 同意
			updateData.Remark = ""                              // 清空之前可能存在的拒绝原因
			accepted = true
		} else {
			updateData.State = constmap.WishMemberStateRejected // 拒绝
			updateData.Remark = in.RejectReason                 // 保存拒绝原因
			accepted = false
		}

		if err := tx.Model(&wishMember).Updates(updateData).Error; err != nil {
			return err
		}

		// 标记消息为已读
		now := time.Now()
		if err := tx.Model(&models.Message{}).
			Where("message_tpl_id = ? AND user_id = ?", in.MessageId, session.UserId).
			Updates(models.Message{
				IsRead:   constmap.Enable,
				ReadTime: &now,
			}).Error; err != nil {
			return err
		}

		// 发送回复消息给申请人（在事务中执行，确保数据一致性）
		if err := sendApplyResponseMessageInTx(tx, wish.ID, session.User.Nickname, wishMember.UserId, accepted, wish.Title, in.RejectReason); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "处理失败，请稍后重试")
	}

	// 10. 清除相关缓存（事务成功后清理）
	// 收集需要清理缓存的用户ID，避免重复清理
	userIds := []uint{session.UserId}
	if wishMember.UserId != session.UserId {
		userIds = append(userIds, wishMember.UserId)
	}

	// 批量清理缓存
	for _, userId := range userIds {
		message_biz.ClearUserMessageCacheByTypes(userId, []constmap.MessageType{constmap.MessageTypeTeam})
	}

	// 11. 组装返回数据
	var out struct {
		WishId       uint   `json:"wish_id"`
		MemberId     uint   `json:"member_id"`
		Status       string `json:"status"`
		RejectReason string `json:"reject_reason,omitempty"` // 拒绝原因，仅拒绝时返回
	}
	out.WishId = wish.ID
	out.MemberId = wishMember.ID
	if accepted {
		out.Status = "accepted"
	} else {
		out.Status = "rejected"
		out.RejectReason = in.RejectReason
	}

	return out, nil
}

// 在事务中发送申请回复消息给申请人
func sendApplyResponseMessageInTx(tx *gorm.DB, wishId uint, fromUserNickname string, toUserId uint, accepted bool, wishTitle, rejectReason string) error {
	// 构建消息内容
	var title, content string
	if accepted {
		title = "组队申请已同意"
		content = fmt.Sprintf("%s 同意了您的申请，您已成功加入心愿单「%s」", fromUserNickname, wishTitle)
	} else {
		title = "组队申请已拒绝"
		if rejectReason != "" {
			content = fmt.Sprintf("%s 拒绝了您的申请，未能加入心愿单「%s」。拒绝原因：%s", fromUserNickname, wishTitle, rejectReason)
		} else {
			content = fmt.Sprintf("%s 拒绝了您的申请，未能加入心愿单「%s」", fromUserNickname, wishTitle)
		}
	}

	// 创建消息模板
	messageTpl := &models.MessageTpl{
		Type:         constmap.MessageTypeTeam,
		SubType:      constmap.MessageSubTypeText,
		Title:        title,
		Content:      content,
		WishId:       wishId,
		WishMemberId: 0, // 回复消息不关联具体的成员记录
	}

	if err := tx.Create(messageTpl).Error; err != nil {
		return err
	}

	// 创建用户消息记录（未读状态）
	message := &models.Message{
		MessageTplId: messageTpl.ID,
		UserId:       toUserId,
		IsRead:       constmap.Disable, // 未读
	}

	if err := tx.Create(message).Error; err != nil {
		return err
	}

	return nil
}
