package message

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}


// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录
	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 统一查询：所有消息都通过messages表关联
	query := db.Model(&models.MessageTpl{}).
		Select("message_tpls.*, messages.is_read, messages.read_time").
		Joins("INNER JOIN messages ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.type = ? AND messages.user_id = ?", in.Type, session.UserId)

	// 添加已读状态筛选
	if in.IsRead == constmap.Enable { // 已读
		query = query.Where("messages.is_read = 1")
	} else if in.IsRead == constmap.Disable { // 未读
		query = query.Where("messages.is_read = 2")
	}

	// 计算OFFSET
	offset := (page - 1) * pageSize

	err := query.Order("message_tpls.created_at DESC").
		Offset(offset).
		Limit(pageSize + 1). // 多查询一条用于判断hasMore
		Find(&results).Error

	if err != nil {
		return nil, utils.NewError(err)
	}

	// 判断是否有更多数据
	messages := message_biz.ConvertToMessageVOs(results)
	hasMore := len(messages) > pageSize
	if hasMore {
		messages = messages[:pageSize]
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}


