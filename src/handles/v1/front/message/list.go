package message

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 消息列表响应
type MessageListResponse struct {
	HasMore bool                     `json:"has_more"`
	List    []*message_biz.MessageVO `json:"list"`
}

// 消息查询参数
type MessageQueryParams struct {
	UserId      uint
	MessageType constmap.MessageType
	IsRead      int
	Page        int // 页码
	PageSize    int
}

// 消息列表
func List(ctx *gin.Context) (any, error) {
	var in struct {
		Type   constmap.MessageType `form:"type" binding:"required"`
		IsRead int                  `form:"is_read"` // 可选：筛选已读状态 0=全部 1=已读 2=未读
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx) // 路由中间件已验证登录
	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	// 构建查询参数
	params := MessageQueryParams{
		UserId:      session.UserId,
		MessageType: in.Type,
		IsRead:      in.IsRead,
		Page:        page,
		PageSize:    pageSize,
	}

	// 使用优化后的查询方法
	messages, hasMore, err := queryOptimizedMessages(db, params)
	if err != nil {
		return nil, err
	}

	return MessageListResponse{
		HasMore: hasMore,
		List:    messages,
	}, nil
}

// 统一的消息查询方法
func queryOptimizedMessages(db *gorm.DB, params MessageQueryParams) ([]*message_biz.MessageVO, bool, error) {
	var results []struct {
		models.MessageTpl
		IsRead   int        `gorm:"column:is_read"`
		ReadTime *time.Time `gorm:"column:read_time"`
	}

	// 统一查询：所有消息都通过messages表关联
	query := db.Model(&models.MessageTpl{}).
		Select("message_tpls.*, messages.is_read, messages.read_time").
		Joins("INNER JOIN messages ON message_tpls.id = messages.message_tpl_id").
		Where("message_tpls.type = ? AND messages.user_id = ?", params.MessageType, params.UserId)

	// 添加已读状态筛选
	if params.IsRead == constmap.Enable { // 已读
		query = query.Where("messages.is_read = 1")
	} else if params.IsRead == constmap.Disable { // 未读
		query = query.Where("messages.is_read = 2")
	}

	// 添加cursor分页条件
	if params.LastCreatedAt != nil {
		query = query.Where("message_tpls.created_at < ?", *params.LastCreatedAt)
	}

	err := query.Order("message_tpls.created_at DESC").
		Limit(params.PageSize + 1). // 多查询一条用于判断hasMore
		Find(&results).Error

	if err != nil {
		return nil, false, utils.NewError(err)
	}

	// 判断是否有更多数据
	messages := message_biz.ConvertToMessageVOs(results)
	hasMore := len(messages) > params.PageSize
	if hasMore {
		messages = messages[:params.PageSize]
	}

	return messages, hasMore, nil
}
