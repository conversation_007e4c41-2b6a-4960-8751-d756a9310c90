package hotel

import (
	"errors"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
)

type sortType string

const (
	sortByScore sortType = "score" //评分
	sortByStar  sortType = "star"  //星级
)

func Search(ctx *gin.Context) (any, error) {
	var in struct {
		Ids      string   `form:"ids"`
		Keyword  string   `form:"keyword"`
		ZoneIds  string   `form:"zone_ids"`
		Location string   `form:"location"` //lng,lat
		Sort     sortType `form:"sort"`
		Order    string   `form:"order"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewError(err)
	}

	page, pageSize := utils.GetPage(ctx)
	db := utils.GetDB(ctx)

	conds := []any{
		map[string]any{
			"term": map[string]any{
				"type": constmap.PoiTypeHotel,
			},
		},
		map[string]any{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
	}

	sort := []map[string]any{
		{"_score": map[string]any{"order": "desc"}},
	}

	if in.Sort != "" {
		if in.Sort == sortByStar || in.Sort == sortByScore {
			sort = append(sort, map[string]any{
				string(in.Sort): utils.If(in.Order == "asc", "asc", "desc"),
			})
		}
	}

	if !strutil.IsBlank(in.Keyword) {
		conds = append(conds, map[string]any{
			"match": map[string]any{
				"name": in.Keyword,
			},
		})
	}

	if !strutil.IsBlank(in.ZoneIds) {
		zoneIds, err := utils.ToArray[uint](in.ZoneIds, ",")
		if err != nil {
			return nil, utils.NewError(err)
		}
		conds = append(conds, map[string]any{
			"terms": map[string]any{
				"zone_id": zoneIds,
			},
		})
	}

	if !strutil.IsBlank(in.Ids) {
		ids, err := utils.ToArray[uint](in.Ids, ",")
		if err != nil {
			return nil, utils.NewError(err)
		}
		conds = append(conds, map[string]any{
			"terms": map[string]any{
				"obj_id": ids,
			},
		})
	}

	var lng, lat float64
	if !strutil.IsBlank(in.Location) {
		poi, err := utils.ToArray[float64](in.Location, ",")
		if err != nil || len(poi) < 2 {
			return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
		}
		lng, lat = poi[0], poi[1]

	}
	if lng > 0 && lat > 0 {
		sort = append([]map[string]any{
			{
				"_geo_distance": map[string]any{
					"location": map[string]any{
						"lat": lat,
						"lon": lng,
					},
					"order":         "asc",
					"distance_type": "arc",
				},
			},
		}, sort...)
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": conds,
			},
		},
		"sort": sort,
		"from": (page - 1) * pageSize,
		"size": pageSize,
	}
	searchResp, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
	if err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return nil, utils.NewError(errors.New("搜索查询失败"))
	}

	type SearchResultItem struct {
		Id         uint                 `json:"id"`
		Name       string               `json:"name"`
		Star       int                  `json:"star"`
		Score      string               `json:"score"`
		GoodRate   int                  `json:"good_rate"`
		ZoneId     uint                 `json:"zone_id"`
		Lng        float64              `json:"lng"`
		Lat        float64              `json:"lat"`
		CityName   string               `json:"city_name"`
		Address    string               `json:"address"`
		CostText   string               `json:"cost_text"`
		AvgPrice   int64                `json:"avg_price"`
		Pic        string               `json:"pic"`
		Distance   float64              `json:"distance"`
		GuestType  []constmap.GuestType `json:"guest_type"`   //客人类型
		OpenUpTime string               `json:"open_up_time"` //开业时间
		Tel        string               `json:"tel"`
	}

	var out struct {
		List  []SearchResultItem `json:"list"`
		Total int                `json:"total"`
	}

	out.Total = searchResp.Hits.Total.Value
	out.List = make([]SearchResultItem, 0)

	var ids []uint
	for _, v := range searchResp.Hits.Hits {
		ids = append(ids, v.Source.ObjId)
	}

	itemMap := make(map[uint]models.Hotel)
	if len(ids) > 0 {
		var items []models.Hotel
		db.Find(&items, ids)
		itemMap = slice.ReduceBy(items, itemMap, func(index int, item models.Hotel, agg map[uint]models.Hotel) map[uint]models.Hotel {
			agg[item.ID] = item
			return agg
		})
	}

	for _, v := range searchResp.Hits.Hits {
		ritem := SearchResultItem{
			Id:        v.Source.ObjId,
			Name:      v.Source.Name,
			Lat:       v.Source.Location[1],
			Lng:       v.Source.Location[0],
			CityName:  v.Source.CityName,
			ZoneId:    v.Source.ZoneId,
			Star:      v.Source.Star,
			Score:     utils.FormatNumber(v.Score, 1),
			GuestType: make([]constmap.GuestType, 0),
		}
		if vitem, ok := itemMap[v.Source.ObjId]; ok {
			ritem.AvgPrice = vitem.AvgPrice
			ritem.Address = vitem.Address
			ritem.CostText = vitem.CostText
			ritem.Pic = utils.StaticUrl(vitem.Pic)
			ritem.OpenUpTime = vitem.OpenUpTime
			ritem.GoodRate = vitem.GoodRate
			ritem.Tel = vitem.Tel
			ritem.GuestType = slice.Map(strutil.SplitAndTrim(vitem.GuestType, ","), func(_ int, v string) constmap.GuestType {
				i, _ := convertor.ToInt(v)
				return constmap.GuestType(i)
			})
		}
		if lng > 0 && lat > 0 {
			ritem.Distance = utils.CalculateDistance(lng, lat, ritem.Lng, ritem.Lat)
		}
		out.List = append(out.List, ritem)
	}

	zoneIds := slice.Map(out.List, func(index int, item SearchResultItem) uint {
		return item.ZoneId
	})
	zoneMap := zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)

	slice.ForEach(out.List, func(index int, item SearchResultItem) {
		if z, ok := zoneMap[item.ZoneId]; ok {
			item.CityName = z.Name
		}
		out.List[index] = item
	})

	return out, nil
}
