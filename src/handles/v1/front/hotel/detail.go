package hotel

import (
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		Id uint `form:"id" binding:"required"`
	}

	var err error

	if err = ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	var hotel models.Hotel
	if err := db.Preload("Pics").Take(&hotel, in.Id).Error; err != nil {
		return nil, err
	}

	var data struct {
		Address      string               `json:"address"`
		Pics         []string             `json:"pics"`
		Intro        string               `json:"intro"`
		Name         string               `json:"name"`
		Id           uint                 `json:"id"`
		Lng          float64              `json:"lng"`
		Lat          float64              `json:"lat"`
		Tel          string               `json:"tel"`
		BrandName    string               `json:"brand_name"`
		Star         int                  `json:"star"`
		StarDesc     string               `json:"star_desc"`
		GoodRate     int                  `json:"good_rate"`
		Score        string               `json:"score"`
		Pic          string               `json:"pic"`
		GuestType    []constmap.GuestType `json:"guest_type"`
		OpenUpTime   string               `json:"open_up_time"`
		BusinessArea string               `json:"business_area"`
		CostText     string               `json:"cost_text"`
		Tags         []string             `json:"tags"`
		AvgPrice     int64                `json:"avg_price"`
	}

	data.Id = hotel.ID
	data.Address = hotel.Address
	data.Name = hotel.Name
	data.Lng = hotel.Lng
	data.Lat = hotel.Lat
	data.Star = hotel.Star
	data.StarDesc = hotel.StarDesc
	data.GoodRate = hotel.GoodRate
	data.Score = utils.FormatNumber(hotel.Score, 1)
	data.Pic = hotel.Pic
	data.BusinessArea = hotel.BusinessArea
	data.GuestType = slice.Map(strutil.SplitAndTrim(hotel.GuestType, ","), func(_ int, v string) constmap.GuestType {
		i, _ := convertor.ToInt(v)
		return constmap.GuestType(i)
	})
	data.OpenUpTime = hotel.OpenUpTime
	data.BrandName = hotel.BrandName
	data.Tel = hotel.Tel
	data.CostText = hotel.CostText

	data.Pics = slice.Map(hotel.Pics, func(index int, item models.HotelPic) string {
		return utils.StaticUrl(item.Url)
	})
	data.Tags = slice.FilterMap([]string{
		hotel.BrandName,
		hotel.BusinessArea,
		hotel.StarDesc,
	}, func(index int, item string) (string, bool) {
		return item, !strutil.IsBlank(item)
	})
	data.AvgPrice = hotel.AvgPrice

	return data, err
}
