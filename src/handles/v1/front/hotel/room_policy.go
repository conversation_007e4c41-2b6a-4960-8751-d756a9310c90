package hotel

import (
	"fmt"
	"github.com/duke-git/lancet/v2/mathutil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/concurrent_slice"
	"roadtrip-api/src/utils/parallel_task"
)

// 房型价格政策
func RoomPolicy(ctx *gin.Context) (any, error) {
	var err error
	var in struct {
		HotelId uint  `form:"hotel_id" binding:"required"`
		Start   int64 `form:"start" binding:"required"`
		End     int64 `form:"end" binding:"required"`
	}
	if err = ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	type HotelRoomPricePolicy struct {
		OtaId       string               `json:"ota_id"`
		OtaCode     string               `json:"ota_code"`
		OtaCodeName string               `json:"ota_code_name"`
		ProductType constmap.ProductType `json:"product_type"`
		Rooms       []beans.HotelRoom    `json:"rooms"`
	}
	type outS struct {
		HotelId uint                   `json:"hotel_id"`
		Otas    []HotelRoomPricePolicy `json:"otas"`
	}
	out := outS{
		HotelId: in.HotelId,
		Otas:    make([]HotelRoomPricePolicy, 0),
	}
	db := utils.GetDB(ctx)
	var otas []models.HotelOta
	db.Model(&otas).Where(models.HotelOta{HotelId: in.HotelId}).Find(&otas)
	if len(otas) == 0 {
		return out, nil
	}

	tasks := parallel_task.NewPool(3)
	defer tasks.Release()

	var ch = concurrent_slice.NewChan[HotelRoomPricePolicy](len(otas))
	var chPrice = concurrent_slice.NewChan[int64](len(otas) * 1000)

	for _, v := range otas {
		tasks.AddTask(func(v models.HotelOta) func() error {
			return func() error {
				item := HotelRoomPricePolicy{
					OtaId:       v.OtaId,
					OtaCode:     v.OtaCode,
					OtaCodeName: constmap.OtaCodeMap[v.OtaCode],
					Rooms:       make([]beans.HotelRoom, 0),
				}
				ota, err := ota_asm.NewOta(v.OtaCode)
				if err != nil {
					return nil
				}
				item.ProductType = ota.GetHotelProductType()
				rooms, err := ota.QueryHotelRoomPolicy(&beans.QueryHotelRoomPolicyDto{
					OtaId:           v.OtaId,
					Start:           in.Start,
					End:             in.End,
					LoadPolicyPrice: true,
					PriceRate:       utils.GetOtaPriceRate(ota.Code()),
				})
				slice.ForEach(rooms, func(_ int, room beans.HotelRoom) {
					slice.ForEach(room.Policy, func(_ int, vv beans.HotelRoomPolicy) {
						chPrice.Append(vv.SalesPrice)
					})
					item.Rooms = append(item.Rooms, room)
				})
				ch.Append(item)
				return nil
			}
		}(v))
	}
	if err = tasks.Wait(); err != nil {
		my_logger.Errorf("hotel prices error", zap.Error(err))
		var msg = constmap.ErrorMsgSystem
		if e, ok := err.(my_ota.ApiError); ok && e.Code == my_ota.ApiOkCode {
			msg = e.Msg
		}
		return nil, utils.NewErrorStr(constmap.ErrorSystem, msg)
	}
	var otaPrice []int64
	chPrice.ForEach(func(val int64) {
		otaPrice = append(otaPrice, val)
	})
	ch.ForEach(func(val HotelRoomPricePolicy) {
		out.Otas = append(out.Otas, val)
	})

	go func() {
		if len(otaPrice) > 0 {
			db.Model(&models.Hotel{}).Where("id=?", in.HotelId).Updates(models.Hotel{
				CostText: fmt.Sprintf("%.2f", utils.CurrencyInt2Float(int64(mathutil.Average(otaPrice...)))),
			})
		}
	}()
	return out, nil
}
