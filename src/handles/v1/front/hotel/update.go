package hotel

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cart"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func UpdateQuantity(ctx *gin.Context) (any, error) {
	var in struct {
		ProductId uint `form:"product_id" binding:"required"`
		Quantity  int  `form:"quantity" binding:"required"`
	}

	var err error

	if err = ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	cart := my_cart.New(db, session.UserId)
	err = cart.Update(in.ProductId, in.Quantity)

	return nil, err
}
