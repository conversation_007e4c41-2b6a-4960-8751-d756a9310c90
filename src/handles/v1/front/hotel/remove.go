package hotel

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cart"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func Remove(ctx *gin.Context) (any, error) {
	var in struct {
		ProductId uint `form:"product_id" binding:"required"`
	}

	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	session, _ := business.GetFrontLoginUser(ctx)
	db := utils.GetDB(ctx)

	cart := my_cart.New(db, session.UserId)

	err := cart.Remove(in.ProductId)

	return nil, err
}
