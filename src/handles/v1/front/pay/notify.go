package pay

import (
	"fmt"
	models2 "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/models"
	request3 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/notify/request"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func Notify(ctx *gin.Context) (any, error) {
	method := ctx.Param("method")
	db := utils.GetDB(ctx)

	var err error
	var payment models.OrderPayment
	switch method {
	default:
		err = utils.NewErrorStr(constmap.ErrorSystem, "未知的支付方式")
	case "wxpay":
		_, err = my_wechat.HandlePaidNotify(0, ctx.Request, func(message *request3.RequestNotify, transaction *models2.Transaction, fail func(message string)) interface{} {
			if config.IsDebug() {
				my_logger.Infof("wxpay notify data new", zap.Any("body", transaction))
			}

			if message.EventType != "TRANSACTION.SUCCESS" || strutil.IsBlank(transaction.OutTradeNo) {
				return true
			}

			if err = db.Where("out_trade_no=?", transaction.OutTradeNo).First(&payment).Error; err != nil {
				return fmt.Sprintf("get payment err:%s", err.Error())
			}

			return db.Transaction(func(tx *gorm.DB) error {
				return orders.PayConfirm(&payment, db)
			})
		})
	}

	if err != nil {
		ctx.AbortWithStatus(500)
		return nil, err
	}

	if payment.ID > 0 {
		_ = my_queue.Light(constmap.EventOrderPay, gin.H{
			"ids": fmt.Sprintf("%d", payment.OrderId),
		})
	}

	ctx.Set(constmap.ContextHttpRaw, "ok")

	return nil, nil
}
