package chat

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/fileutil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"io"
	"net/http"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/ai/chat_asm"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_sse"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
)

type chatType string

const (
	chatDefault chatType = ""
	chatHotel   chatType = "hotel"  //推荐酒店
	chatRePlan  chatType = "replan" //智能Ai重新生成
)

type chatPromptOption struct {
	Type string `json:"type"`
	Val  any    `json:"val"`
}
type promptParams struct {
	ZoneId    uint     `json:"zone_id"`
	Location  string   `json:"location"` //用户当前位置
	Text      string   `json:"text"`
	Url       string   `json:"url"`
	ContextId string   `json:"context_id"`
	Force     int      `json:"force"`     //确认生成行程
	ChatType  chatType `json:"chat_type"` //对话类型
	SceneId   uint     `json:"scene_id"`  //景点id
	HotelId   uint     `json:"hotel_id"`  //酒店id
	PlanId    uint     `json:"plan_id"`   //行程规划id
}

func matchPromptOption(content string) chatPromptOption {
	var cond string
	mat := regexp.MustCompile("(兴趣偏好|节奏强度|住宿条件|预期天数|出行日期|交通方式|出游方式)").FindAllString(content, -1)
	if len(mat) > 0 {
		cond = mat[len(mat)-1]
	}
	var option chatPromptOption
	if strings.Contains(cond, "兴趣偏好") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"亲子", "人文", "自然", "娱乐"},
		}
	} else if strings.Contains(cond, "节奏强度") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"轻松", "适中", "紧凑"},
		}
	} else if strings.Contains(cond, "住宿条件") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"经济", "舒适", "豪华"},
		}
	} else if strings.Contains(cond, "预期天数") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"2天", "3天", "5天", "一周", "半月"},
		}
	} else if strings.Contains(cond, "出行日期") {
		option = chatPromptOption{
			Type: "calendar",
		}
	} else if strings.Contains(cond, "交通方式") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"租车", "自驾", "公共交通"},
		}
	} else if strings.Contains(cond, "出游方式") {
		option = chatPromptOption{
			Type: "text",
			Val:  []string{"到目的地游玩", "一路玩"},
		}
	}
	return option
}

func PromptStream(ctx *gin.Context) (any, error) {

	var err error
	var upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许所有来源的连接（生产环境建议限制）
		},
	}

	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	ctx.Set(constmap.ContextHttpRaw, true)

	if _, err = business.GetFrontLoginUser(ctx); err != nil {
		b, _ := convertor.ToBytes(business.BuildResponse(nil, utils.NewErrorStr(constmap.ErrorNotLogin, constmap.ErrorMsgNotLogin)))
		_ = conn.WriteMessage(websocket.TextMessage, b)
		return nil, nil
	}

	for {
		messageType, p, err := conn.ReadMessage()
		if err != nil {
			b, _ := convertor.ToBytes(business.BuildResponse(nil, err))
			_ = conn.WriteMessage(websocket.TextMessage, b)
			return nil, nil
		}
		switch messageType {
		default:
			continue
		case websocket.CloseMessage:
			_ = conn.WriteMessage(websocket.CloseMessage, nil)
			return nil, nil

		case websocket.PingMessage:
			_ = conn.WriteMessage(websocket.PongMessage, nil)

		case websocket.TextMessage:

			my_logger.Infof("PromptStream", zap.ByteString("in", p))

			var in promptParams
			if err = json.Unmarshal(p, &in); err != nil {
				b, _ := convertor.ToBytes(business.BuildResponse(nil, err))
				_ = conn.WriteMessage(websocket.TextMessage, b)
			} else if err = promptStream(ctx, ctx, conn, in); err != nil {
				b, _ := convertor.ToBytes(business.BuildResponse(nil, err))
				_ = conn.WriteMessage(websocket.TextMessage, b)
			} else {
				return nil, nil
			}
		}
	}
}

func promptStream(ctx *gin.Context, curCtx context.Context, conn *websocket.Conn, in promptParams) error {

	my_logger.Infof("promptStream", zap.Any("in", in))

	if in.Force != constmap.Enable && (strutil.IsBlank(in.Text) && strutil.IsBlank(in.Url)) {
		return utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var zone models.Zone
	db.Take(&zone, in.ZoneId)

	in.Text = strutil.Substring(in.Text, 0, 200)

	aiModel := ai.NewDify()

	var (
		rsp       *ai.ChatContextStreamingResponse
		err       error
		sysPrompt string
	)

	if in.ChatType == chatHotel {
		var str strings.Builder
		var zoneIds []uint
		var zoneMap map[uint]models.Zone
		var scene models.Scenic
		var hotel models.Hotel
		if in.SceneId > 0 {
			db.Take(&scene, in.SceneId)
			zoneIds = append(zoneIds, scene.ZoneId)
		}
		if in.HotelId > 0 {
			db.Take(&hotel, in.HotelId)
			zoneIds = append(zoneIds, hotel.ZoneId)
		}
		if len(zoneIds) > 0 {
			zoneMap = zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)
		}

		if scene.ID > 0 {
			str.WriteString("用户入住前所在的景点信息:\n")
			str.WriteString(fmt.Sprintf("  - 景点名称:%s\n", scene.Name))
			str.WriteString(fmt.Sprintf("  - 景点地址:%s\n", scene.Address))
			str.WriteString(fmt.Sprintf("  - 景点所在城市:%s\n", zoneMap[scene.ZoneId].Name))
		}

		if hotel.ID > 0 {
			str.WriteString("用户入住前所在的酒店信息:\n")
			str.WriteString(fmt.Sprintf("  - 酒店名称:%s\n", hotel.Name))
			str.WriteString(fmt.Sprintf("  - 酒店地址:%s\n", hotel.Address))
			str.WriteString(fmt.Sprintf("  - 酒店所在城市:%s\n", zoneMap[hotel.ZoneId].Name))
		}
		sysPrompt = str.String()

		rsp, err = aiModel.ChatContextStreamingHotel(curCtx, db, &ai.ChatStreamRequest{
			ConversationId: in.ContextId,
			User:           convertor.ToString(session.UserId),
			Prompt:         in.Text,
			City:           zone.Name,
			Location:       in.Location,
			SysPrompt:      sysPrompt, //酒店信息
		})
	} else {
		if in.PlanId > 0 && in.ChatType == chatRePlan {
			var planExt models.PlanExt
			db.Where(models.PlanExt{PlanId: in.PlanId}).Take(&planExt)
			if planExt.ID > 0 {
				var options = new(beans.TravelPromptOptions)
				if planExt.PromptOptions.Data != nil {
					options = planExt.PromptOptions.Data
				}
				in.Url = ""
				sysPrompt = fmt.Sprintf("想重新生成行程，原来的行程信息如下：\n%s\n\n", plan_asm.BuildPrompt(options, db))
			}
		}
		rsp, err = aiModel.ChatContextStreaming(curCtx, db, &ai.ChatStreamRequest{
			ConversationId: utils.If(in.ContextId == defaultContextId, "", in.ContextId),
			User:           convertor.ToString(session.UserId),
			Prompt:         sysPrompt + utils.If(strutil.IsBlank(in.Url), in.Text, "帮我解析"),
			Url:            in.Url,
			ForceGenerate:  in.Force == constmap.Enable,
			City:           zone.Name,
			Location:       in.Location,
			ChatType:       string(in.ChatType),
		})
	}
	if err != nil {
		my_logger.Errorf("ChatContextStreaming", zap.Error(err))
		if strutil.ContainsAny(err.Error(), []string{"[status 404]", "[status 400]"}) {
			return utils.NewErrorStr(constmap.ErrorChatExpire, constmap.ErrorMsgChatExpire)
		}
		return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	type yjResponse struct {
		*ai.ChatContextCompletionResponse
		beans.PlanLockData
		ContextId string           `json:"context_id"`
		Option    chatPromptOption `json:"option"`
	}

	var taskId string
	var messageId string
	var contextId string
	var wantMore bool
	var buffer bytes.Buffer
	// 向客户端写入一行流回复
	doWrite := func(data any) error {
		bb, err := convertor.ToBytes(data)
		if err != nil {
			return err
		}
		b, err := convertor.ToBytes(business.BuildResponse(json.RawMessage(bb), nil))
		if err != nil {
			return err
		}
		return conn.WriteMessage(websocket.TextMessage, b)
	}
	//处理llm返回的流式应答
	processLine := func(line []byte) error {
		// event: ping
		if len(line) <= 5 || !bytes.Equal(line[0:5], []byte("data:")) {
			return nil
		}
		if err := doWrite(line[5:]); err != nil {
			return err
		}
		line = line[5:]
		pre := &my_sse.Event{}
		if err := json.Unmarshal(line, pre); err != nil {
			return err
		}
		contextId = pre.ConversationId
		messageId = pre.MessageId
		taskId = pre.TaskId
		if pre.Event != "workflow_finished" {
			return nil
		}

		my_logger.Debugf("WorkflowFinished", zap.ByteString("data", pre.Data))

		var data beans.StreamWorkflowFinishData
		_ = json.Unmarshal(pre.Data, &data)

		if data.Status != "succeeded" {
			//nothing
		} else if cnt, ok := data.Outputs["answer"]; !ok {
			//nothing
		} else {
			content := cnt.(string)
			r := &ai.ChatContextCompletionResponse{
				RequestId: fmt.Sprintf("%s:%s", aiModel.GetProvider(), taskId),
				Provider:  aiModel.GetProvider(),
			}
			r.NeedMore = !strutil.ContainsAny(content, []string{"【行程安排】", "【推荐酒店】"})
			yjResp := yjResponse{
				ChatContextCompletionResponse: r,
				ContextId:                     contextId,
			}
			r.Content = content
			if r.NeedMore {
				yjResp.Option = matchPromptOption(content)
			} else if strings.Contains(content, "【行程安排】") {
				_ = fileutil.WriteStringToFile(fmt.Sprintf("./tmp/%s.md", r.Provider), fmt.Sprintf("%s\n%s\n%s\n\n%s\n\n\n\n", r.RequestId, in.Text, in.Url, content), true)
				aiResp := &ai.ChatCompletionResponse{
					RequestId:   r.RequestId,
					Content:     content,
					Provider:    r.Provider,
					ContentType: ai.ContentTypePlan,
					UserId:      session.UserId,
				}

				aiResp.IntegralCostState = constmap.Disable
				if aiResp.IntegralCost, err = plan_asm.GetCostIntegralAmount(db, in.ChatType == chatRePlan); err != nil {
					return err
				}
				options, err := aiModel.ParseOptions(content)
				if err != nil {
					my_logger.Errorf("chat parse options", zap.Error(err))
					return utils.NewErrorStr(constmap.ErrorSystem, "系统错误，请重试一下")
				}
				aiResp.PromptOptions = options
				yjResp.PromptOptions = options

				plan, err := plan_asm.BuildSubmitPlan(db, plan_asm.SavePlanReq{
					UserId: session.UserId,
					State:  constmap.Disable,
				}, aiResp)
				if err != nil {
					return err
				}
				yjResp.SceneNum = plan.SceneCnt
				aiResp.SceneNum = plan.SceneCnt

				_ = plan_biz.SetDetailCc(aiResp)
				yjResp.ContentType = aiResp.ContentType
				yjResp.IntegralCostState = aiResp.IntegralCostState
				yjResp.IntegralCost = aiResp.IntegralCost

			} else if strings.Contains(content, "【推荐酒店】") {
				aiResp := &ai.ChatCompletionResponse{
					RequestId:   r.RequestId,
					Content:     content,
					Provider:    r.Provider,
					ContentType: ai.ContentTypeHotel,
				}
				_ = plan_biz.SetDetailCc(aiResp)
				yjResp.ContentType = aiResp.ContentType
			}

			if in.ChatType == chatDefault {
				//保存会话
				chat_asm.ContextChatSaveChat(r, session.UserId, in.ContextId,
					utils.If(strutil.IsBlank(in.Url), in.Text, fmt.Sprintf("帮我解析%s", in.Url)),
					utils.If(in.ContextId != contextId, contextId, ""))
			}

			b, _ := convertor.ToBytes(yjResp)
			if err := doWrite(&my_sse.Event{
				Event:          "yj_finish",
				ConversationId: contextId,
				MessageId:      messageId,
				TaskId:         taskId,
				Data:           b,
			}); err != nil {
				return err
			}
			if idx := strings.Index(content, "【您可以回复】"); idx > -1 {
				lines := strutil.SplitEx(content[idx+len("【您可以回复】"):], "\n", true)
				lines = slice.FilterMap(lines, func(i int, line string) (string, bool) {
					line = strutil.Trim(line)
					if line == "" {
						return "", false
					}
					return line, true
				})
				if len(lines) > 0 {
					b, _ = convertor.ToBytes(gin.H{
						"suggestions": lines,
					})
					_ = doWrite(&my_sse.Event{
						Event:          "yj_suggestions",
						TaskId:         taskId,
						ConversationId: contextId,
						MessageId:      messageId,
						Data:           b,
					})
				}
			}
		}
		return nil
	}
	for {
		line, prefixed, err := rsp.Reader.ReadLine()
		my_logger.Debugf("SSE Read", zap.ByteString("line", line), zap.Error(err))
		if err != nil {
			if err == io.EOF {
				break
			}
			return err
		}
		if len(line) == 0 {
			continue
		}
		if wantMore {
			buffer.Write(line)
			if !prefixed {
				wantMore = false
				if err := processLine(buffer.Bytes()); err != nil {
					return err
				}
				buffer.Reset()
			}
		} else if prefixed {
			buffer.Write(line)
			wantMore = true
		} else {
			if err := processLine(line); err != nil {
				return err
			}
		}
	}

	return nil
}
