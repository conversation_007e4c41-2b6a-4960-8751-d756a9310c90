package chat

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func Index(ctx *gin.Context) (any, error) {
	var in struct {
		ZoneId uint `form:"zone_id"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	var out struct {
		Prompts *constmap.GenericList[string] `json:"prompts"`
		Logo    string                        `json:"logo"`
		Slogan  string                        `json:"slogan"`
	}

	out.Slogan = "用科技温暖每一次出发"
	out.Logo = "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/chat-logo.png"
	//推荐语
	zoneId := utils.If(in.ZoneId == 0, constmap.DefaultZoneId, in.ZoneId)
	out.Prompts = utils.NewGenericList[string](nil)
	my_cache.Get(fmt.Sprintf(constmap.RKRecPrompts, zoneId), out.Prompts)

	return out, nil
}
