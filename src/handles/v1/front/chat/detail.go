package chat

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/ai/chat_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		ContextId string `form:"context_id" binding:"required"`
		Page      int    `form:"page"`
		PageSize  int    `form:"page_size"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	session, _ := business.GetFrontLoginUser(ctx)
	type vitem struct {
		CreatedAt   int64          `json:"created_at"`
		Role        string         `json:"role"`
		RequestId   string         `json:"request_id"`
		NeedMore    bool           `json:"need_more"`
		Content     string         `json:"content"`
		ContentType ai.ContentType `json:"content_type"`
	}
	var out struct {
		List []vitem `json:"list"`
	}
	rsp, err := chat_asm.ContextChatDetail(session.UserId, in.ContextId)
	if err != nil {
		my_logger.Errorf("ChatDetail", zap.Error(err))
		return nil, utils.NewError(err)
	}
	if in.Page > 0 && in.PageSize > 0 {
		// 倒序截取分页
		slice.Reverse(rsp.List)
		start, end := (in.Page-1)*in.PageSize, in.Page*in.PageSize
		if start > len(rsp.List) {
			start = len(rsp.List)
		} else if start < 0 {
			start = 0
		}
		if end > len(rsp.List) {
			end = len(rsp.List)
		}
		rsp.List = rsp.List[start:end]
		// 正序返回
		slice.Reverse(rsp.List)
	}
	out.List = slice.Map(rsp.List, func(index int, item *chat_asm.ContextChatItem) vitem {
		return vitem{
			CreatedAt:   item.CreatedAt,
			Role:        item.Role,
			RequestId:   item.RequestId,
			NeedMore:    item.NeedMore,
			Content:     item.Content,
			ContentType: item.ContentType,
		}
	})
	return out, nil
}
