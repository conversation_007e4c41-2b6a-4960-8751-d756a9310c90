package chat

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai/chat_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func List(ctx *gin.Context) (any, error) {
	type vitem struct {
		CreatedAt int64  `json:"created_at"`
		ContextId string `json:"context_id"`
		Title     string `json:"title"`
	}
	var out struct {
		List []vitem `json:"list"`
	}
	session, err := business.GetFrontLoginUser(ctx)
	if err != nil {
		out.List = make([]vitem, 0)
		return out, nil
	}
	listRsp, err := chat_asm.ContextChatList(session.UserId)
	if err != nil {
		my_logger.Errorf("ChatList", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	out.List = slice.Map(listRsp, func(index int, item *chat_asm.ContextChatListItem) vitem {
		return vitem{
			CreatedAt: item.CreatedAt,
			ContextId: item.ContextId,
			Title:     utils.If(strutil.IsBlank(item.Title), "新会话", item.Title),
		}
	})
	return out, nil
}
