package chat

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/ai/chat_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

const defaultContextId = "__default_context__"

func NewStreamChat(ctx *gin.Context) (any, error) {
	var in struct {
		ChatType chatType `form:"chat_type"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	var err error
	session, _ := business.GetFrontLoginUser(ctx)
	now := time.Now().Unix()
	contextId := defaultContextId
	systemPrompt := "您好！我是您的AI行程规划助手。请告诉我您想去哪里旅行，我会为您制定个性化的行程计划。"
	if in.ChatType != chatDefault { //临时会话
		contextId = ""
		if in.ChatType == chatHotel {
			systemPrompt = "您好！我是您的AI行程规划助手。如果您要替换酒店，请告诉我您想替换成哪个酒店，比如说我想在这家酒店附近找。"
		}
	}
	var out struct {
		ContextId    string `json:"context_id"`
		SystemPrompt string `json:"system_prompt"`
	}
	out.ContextId = contextId
	out.SystemPrompt = systemPrompt
	//会话列表里只保留一个默认会话
	if in.ChatType == chatDefault {
		if contextList, err := chat_asm.ContextChatList(session.UserId); err != nil {
			my_logger.Errorf("ContextChatList", zap.Error(err))
		} else if _, ok := slice.FindBy(contextList, func(index int, item *chat_asm.ContextChatListItem) bool {
			return item.ContextId == contextId
		}); ok {
			return out, nil
		}
		if err = chat_asm.ContextChatSave(session.UserId, contextId, "", &chat_asm.ContextChatItem{
			CreatedAt: now,
			Role:      "system",
			NeedMore:  true,
			Content:   systemPrompt,
		}); err != nil {
			my_logger.Infof("ContextChatSave", zap.Error(err))
		}
		if err = chat_asm.ContextChatListSave(session.UserId, contextId, "", ""); err != nil {
			my_logger.Infof("ContextChatPrompt", zap.Error(err))
		}
	}

	return out, nil
}
