package account

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/slice"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/gin-gonic/gin"
)

func LogList(ctx *gin.Context) (any, error) {
	var in struct {
		Start int64 `form:"start"`
		End   int64 `form:"end"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)
	page, pageSize := utils.GetPage(ctx)

	acct, err := account_biz.GetAccount(db, session.UserId, constmap.CurrencyIntegral)
	if err != nil {
		return nil, err
	}

	var logs []models.UserAccountLog
	query := db.Model(&models.UserAccountLog{}).
		Where("user_account_id = ?", acct.ID)
	if in.Start > 0 {
		query = query.Where("created_at >= ?", time.Unix(in.Start, 0))
	}
	if in.End > 0 {
		query = query.Where("created_at <= ?", time.Unix(in.End, 0))
	}
	var total int64
	query.Count(&total)
	query = query.Order("created_at desc").Offset((page - 1) * pageSize).Limit(pageSize)
	if err := query.Find(&logs).Error; err != nil {
		return nil, err
	}

	type item struct {
		ID               uint                   `json:"id"`
		CreatedAt        int64                  `json:"created_at"`
		UserAccountId    uint                   `json:"user_account_id"`
		Type             constmap.AccountLog    `json:"type"`
		TypeText         string                 `json:"type_text"`
		SubType          constmap.AccountLogSub `json:"sub_type"`
		SubTypeText      string                 `json:"sub_type_text"`
		Icon             string                 `json:"icon"`
		ChangeAmount     int                    `json:"change_amount"`
		BeforeAmount     int                    `json:"before_amount"`
		AfterAmount      int                    `json:"after_amount"`
		BeforeLockAmount int                    `json:"before_lock_amount"`
		AfterLockAmount  int                    `json:"after_lock_amount"`
		Remark           string                 `json:"remark"`
		IsPlus           int                    `json:"is_plus"`
		PackageId        uint                   `json:"package_id"`
		PackageName      string                 `json:"package_name"`
		PackageExpireAt  int64                  `json:"package_expire_at"` //套餐有效期
		LogExtra         beans.AccountLogExtra  `json:"-"`
	}
	var out struct {
		Total int64  `json:"total"`
		List  []item `json:"list"`
	}
	out.Total = total
	out.List = make([]item, len(logs))
	iconMap := map[constmap.AccountLogSub]string{
		constmap.AccountLogSubBuyPackage:     "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/1.png",
		constmap.AccountLogSubInitUser:       "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/6.png",
		constmap.AccountLogSubBindInviteCode: "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/4.png",
		constmap.AccountLogSubPlanCost:       "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/2.png",
		constmap.AccountLogSubPlanPdf:        "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/5.png",
		constmap.AccountLogSubShareMini:      "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/3.png",
		constmap.AccountLogSubPackageExpire:  "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/7.png",
		constmap.AccountLogSubAdminOperate:   "https://rp.yjsoft.com.cn/yiban/static/home/<USER>/6.png",
	}
	var upkgIds []uint
	for i, log := range logs {
		var extra beans.AccountLogExtra
		if log.Extra != "" {
			_ = json.Unmarshal([]byte(log.Extra), &extra)
		}
		isPlus := constmap.Disable
		switch log.Type {
		case constmap.AccountLogIncrAmount, constmap.AccountLogIncrLockAmount, constmap.AccountLogConvLock2Amount:
			isPlus = constmap.Enable
		}
		out.List[i] = item{
			ID:               log.ID,
			CreatedAt:        log.CreatedAt.Unix(),
			UserAccountId:    log.UserAccountId,
			Type:             log.Type,
			TypeText:         business.AccountLogTypeText(log.Type),
			SubType:          log.SubType,
			SubTypeText:      business.AccountLogSubText(log.SubType),
			Icon:             iconMap[log.SubType],
			ChangeAmount:     log.ChangeAmount,
			BeforeAmount:     log.BeforeAmount,
			AfterAmount:      log.AfterAmount,
			BeforeLockAmount: log.BeforeLockAmount,
			AfterLockAmount:  log.AfterLockAmount,
			Remark:           log.Remark,
			IsPlus:           isPlus,
			LogExtra:         extra,
		}
		if extra.UserPackageId > 0 {
			upkgIds = append(upkgIds, extra.UserPackageId)
		}
	}

	var upkgMap = make(map[uint]*models.UserPackage)
	if len(upkgIds) > 0 {
		var upkgs []*models.UserPackage
		db.Find(&upkgs, upkgIds)
		for _, v := range upkgs {
			upkgMap[v.ID] = v
		}
	}

	slice.ForEach(out.List, func(index int, v item) {
		if upkg, ok := upkgMap[v.LogExtra.UserPackageId]; ok {
			v.PackageId = upkg.PackageId
			v.PackageName = upkg.PackageName
			v.PackageExpireAt = upkg.ExpireAt.Unix()
		}
		if v.SubType == constmap.AccountLogSubAdminOperate {
			v.Remark = utils.If(constmap.AccountLogIncrAmount == v.Type, "系统赠送", "系统扣除")
		}
		out.List[index] = v
	})

	return out, nil
}
