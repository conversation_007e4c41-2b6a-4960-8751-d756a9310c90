package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

// 配置下发
func Index(ctx *gin.Context) (any, error) {
	db := utils.GetDB(ctx)

	var out struct {
		TplMsgId beans.TplMsgWish `json:"tpl_msg_id"` //模板消息id
	}
	if v, err := sys_config_biz.GetConfig(db, constmap.SysConfigTplMsgKeys); err == nil {
		out.TplMsgId = v.(*beans.TplMsgKeys).Wish
	}
	return out, nil
}
