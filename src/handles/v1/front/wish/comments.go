package wish

import (
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
)

// 评论/回复列表
func Comments(ctx *gin.Context) (any, error) {
	var in struct {
		WishId    uint `form:"wish_id" binding:"required"`
		CommentId uint `form:"comment_id"` //查询此评论下的回复
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	db := utils.GetDB(ctx)
	page, pageSize := utils.GetPage(ctx)

	var wish = new(models.Wish)
	if err := db.Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}
	var isOwner = wish.UserId == userId

	var out struct {
		Total int64                `json:"total"`
		List  []*beans.WishComment `json:"list"`
	}

	query := db.Model(&models.WishComment{})

	addCond := func(query *gorm.DB) *gorm.DB {
		if !isOwner {
			query = query.Where("(is_private=? OR user_id=?)", constmap.Disable, userId)
		}
		query = query.Where(models.WishComment{
			WishId: in.WishId,
			State:  constmap.WishCommentStateApproved,
		})
		return query
	}
	query = addCond(query)

	if in.CommentId > 0 {
		query = query.Where(models.WishComment{ParentCommentId: in.CommentId})
	}

	query.Count(&out.Total)

	var comments []*models.WishComment
	query.Order(utils.If(in.CommentId > 0, "id ASC", "id DESC")). //评论倒序，回复正序
									Offset((page - 1) * pageSize).
									Limit(pageSize).
									Find(&comments)

	if len(comments) == 0 {
		out.List = make([]*beans.WishComment, 0)
		return out, nil
	}

	var commentIds []uint
	var userIds = typeset.NewTypeSet[uint](false)
	out.List = slice.Map(comments, func(index int, item *models.WishComment) *beans.WishComment {
		commentIds = append(commentIds, item.ID)
		userIds.Add(item.UserId)
		if item.ReplyUserId > 0 {
			userIds.Add(item.ReplyUserId)
		}
		return &beans.WishComment{
			Id:              item.ID,
			ParentCommentId: item.ParentCommentId,
			UserId:          item.UserId,
			ReplyCommentId:  item.ReplyCommentId,
			ReplyUserId:     item.ReplyUserId,
			Replys:          make([]*beans.WishComment, 0),
			CreatedAt:       item.CreatedAt.Unix(),
		}
	})

	// 查询评论列表时需加载每条评论的回复总数及前3条回复
	if in.CommentId == 0 {
		type vreply struct {
			ParentCommentId uint
			Cnt             int64
		}
		var replys []vreply
		query = db.Model(&models.WishComment{}).
			Select("parent_comment_id, count(id) as cnt").
			Where("parent_comment_id in ?", commentIds).
			Group("parent_comment_id")

		query = addCond(query)
		query.Find(&replys)
		replyNumMap := slice.KeyBy(replys, func(item vreply) uint {
			return item.ParentCommentId
		})
		slice.ForEach(out.List, func(index int, item *beans.WishComment) {
			item.ReplyNum = replyNumMap[item.Id].Cnt
		})

		commentReplys := wish_biz.LoadReplysCc(db, addCond, commentIds)
		slice.ForEach(out.List, func(index int, item *beans.WishComment) {
			if val, ok := commentReplys[item.Id]; ok {
				item.Replys = val
				// 添加待查寻用户
				slice.ForEach(val, func(_ int, v *beans.WishComment) {
					userIds.Add(v.UserId)
					if v.ReplyUserId > 0 {
						userIds.Add(v.ReplyUserId)
					}
				})
			}
		})
	}

	userMap := user_biz.LoadUsers(db, userIds.Values())

	var mapUser func(comment *beans.WishComment)
	mapUser = func(item *beans.WishComment) {
		item.Nickname = userMap[item.UserId].Nickname
		item.Avatar = utils.AvatarUrl(userMap[item.UserId].Avatar)
		if item.ReplyUserId > 0 {
			item.ReplyNickname = userMap[item.ReplyUserId].Nickname
		}
		for _, v := range item.Replys {
			mapUser(v)
		}
	}
	slice.ForEach(out.List, func(index int, item *beans.WishComment) {
		mapUser(item)
	})

	return out, nil
}
