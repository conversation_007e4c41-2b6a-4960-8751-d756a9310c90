package wish

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/validators"
	"sync"
	"time"
)

// 发布心愿
func Submit(ctx *gin.Context) (any, error) {
	type media struct {
		Type constmap.WishMediaType `json:"type"`
		Url  string                 `json:"url"`
	}
	type todo struct {
		Todo   string `json:"todo"`
		IsMust int    `json:"is_must"`
	}
	var in struct {
		From        string                     `form:"from" binding:"required"`
		To          string                     `form:"to" binding:"required"`
		ToPoi       string                     `form:"to_poi" binding:"required"`
		ToZoneId    uint                       `form:"to_zone_id" binding:"required"`
		Title       string                     `form:"title" binding:"required"`
		BudgetType  constmap.WishBudgetType    `form:"budget_type" binding:"required"`
		Budget      string                     `form:"budget" binding:"required"`
		TotalPeople int                        `form:"total_people" binding:"required"`
		DepartDate  string                     `form:"depart_date"`
		ReturnDate  string                     `form:"return_date"`
		Deadline    int64                      `form:"deadline" binding:"required"`
		OpenScope   int                        `form:"open_scope" binding:"required"`
		MemberDesc  string                     `form:"member_desc"`
		WishDesc    string                     `form:"wish_desc"  binding:"required"`
		Tags        utils.Marshaller[[]string] `form:"tags"`
		Todos       utils.Marshaller[[]todo]   `form:"todos"`
		Medias      utils.Marshaller[[]media]  `form:"medias"`
		Phone       string                     `form:"phone" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, err
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	if unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "wishSubmit", convertor.ToString(session.UserId)), constmap.TimeDur1m); err != nil {
		return nil, err
	} else {
		defer unlocker()
	}

	if in.TotalPeople < 2 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "总人数最少2人")
	}

	if len([]rune(in.WishDesc)) > 300 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿描述最长300个字")
	}

	if len([]rune(in.MemberDesc)) > 300 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "成员描述最长300个字")
	}

	if !validators.IsMobile(in.Phone) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "手机号码格式错误")
	}

	var deadline = utils.GetEndOfDay(time.Unix(in.Deadline, 0))
	if deadline.IsZero() || deadline.Before(time.Now()) {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "截止时间不能小于当前时间")
	}

	var cnt int64
	if db.Model(&models.Zone{}).Where("id=?", in.ToZoneId).Count(&cnt); cnt == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "目的地不存在")
	}

	wish := &models.Wish{
		UserId:      session.UserId,
		Title:       in.Title,
		From:        in.From,
		To:          in.To,
		ToZoneId:    in.ToZoneId,
		ToPoi:       in.ToPoi,
		BudgetType:  in.BudgetType,
		Budget:      in.Budget,
		TotalPeople: in.TotalPeople,
		DepartDate:  in.DepartDate,
		ReturnDate:  in.ReturnDate,
		Deadline:    deadline,
		OpenScope:   utils.If(constmap.Enable == in.OpenScope, constmap.Enable, constmap.Disable),
		State:       constmap.WishStateWaitReview,
		MemberDesc:  in.MemberDesc,
		WishDesc:    in.WishDesc,
	}

	var tags []*models.WishTag
	tags = slice.FilterMap(*in.Tags.Data, func(index int, item string) (*models.WishTag, bool) {
		if strutil.IsBlank(item) {
			return nil, false
		} else {
			return &models.WishTag{
				Tag:       strutil.Trim(item),
				WishCount: 1,
			}, true
		}
	})

	var todos []*models.WishTodo
	if in.Todos.Data != nil {
		var tds = *in.Todos.Data
		if len(tds) > 0 {
			todos = slice.FilterMap(tds, func(index int, item todo) (*models.WishTodo, bool) {
				if strutil.IsBlank(item.Todo) {
					return nil, false
				} else {
					return &models.WishTodo{
						Todo:   item.Todo,
						IsMust: utils.If(constmap.Enable == item.IsMust, constmap.Enable, constmap.Disable),
					}, true
				}
			})
		}
	}

	member := &models.WishMember{
		UserId:  session.UserId,
		Phone:   in.Phone,
		IsOwner: constmap.Enable,
		State:   constmap.WishMemberStateApproved,
	}

	var medias []*models.WishMedia
	if in.Medias.Data != nil {
		tasks := parallel_task.NewPool(5)
		defer tasks.Release()

		var ms = *in.Medias.Data
		var mx sync.Mutex
		if len(ms) > 0 {
			slice.ForEach(ms, func(index int, item media) {
				if item.Type != constmap.WishMediaPicture && item.Type != constmap.WishMediaVideo {
					return
				}
				tasks.AddTask(func() error {
					var u = utils.UnWrapStaticUrl(item.Url)
					var fu = utils.StaticUrl(u)
					size, err := utils.RemoteFileSize(fu)
					if err != nil {
						my_logger.Errorf("RemoteFileSize", zap.String("url", fu), zap.Error(err))
						return fmt.Errorf("文件读取失败")
					}
					//文件最大20m
					if size > 20*1024*1024 {
						return fmt.Errorf("视频不能大于20M")
					} else {
						mx.Lock()
						medias = append(medias, &models.WishMedia{
							MediaType: item.Type,
							ResUrl:    u,
							Size:      size,
						})
						mx.Unlock()
					}
					return nil
				})
			})
			if err := tasks.Wait(); err != nil {
				return nil, err
			}
		}
	}
	var wishId = wish.ID
	err := db.Transaction(func(tx *gorm.DB) error {
		if err := tx.Omit(clause.Associations).Create(&wish).Error; err != nil {
			return err
		}
		wishId = wish.ID
		if err := tx.Create(&models.WishExt{
			WishId: wishId,
		}).Error; err != nil {
			return err
		}

		var updateWish models.Wish

		if len(tags) > 0 {
			if err := tx.Clauses(clause.OnConflict{
				DoUpdates: clause.Set{
					{Column: clause.Column{Name: "wish_count"}, Value: gorm.Expr("wish_count+1")},
				},
			}).Create(&tags).Error; err != nil {
				return err
			}
			tagNames := slice.Map(tags, func(index int, item *models.WishTag) string {
				return item.Tag
			})
			var tagIds []uint
			tx.Model(&models.WishTag{}).Where("tag in ?", tagNames).Pluck("id", &tagIds)
			updateWish.TagIds.Data = &tagIds
		}

		if len(todos) > 0 {
			todos = slice.Map(todos, func(index int, item *models.WishTodo) *models.WishTodo {
				item.WishId = wishId
				return item
			})
			if err := tx.Create(&todos).Error; err != nil {
				return err
			}
		}

		member.WishId = wishId
		if err := tx.Create(&member).Error; err != nil {
			return err
		}

		if len(medias) > 0 {
			medias = slice.Map(medias, func(index int, item *models.WishMedia) *models.WishMedia {
				item.WishId = wishId
				return item
			})
			if err := tx.Create(&medias).Error; err != nil {
				return err
			}
			if m, ok := slice.FindBy(medias, func(index int, item *models.WishMedia) bool {
				return item.MediaType == constmap.WishMediaPicture
			}); ok {
				updateWish.Cover = utils.UnWrapStaticUrl(m.ResUrl)
			}
		}

		if tx.Model(&wish).Omit(clause.Associations).Updates(updateWish).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "更新Wish失败")
		}
		return nil
	})
	if err != nil {
		my_logger.Errorf("Submit Wish trans", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "保存失败")
	}

	_ = my_queue.Weight(constmap.EventWishSubmit, gin.H{
		"id": wishId,
	})

	var out struct {
		WishId uint `json:"wish_id"`
	}
	out.WishId = wishId

	return out, nil
}
