package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 删除评论
func DelComment(ctx *gin.Context) (any, error) {
	var in struct {
		CommentId uint `form:"comment_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var comment = new(models.WishComment)
	if db.Take(&comment, in.CommentId).Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "评论不存在")
	}
	if comment.State != constmap.WishCommentStateApproved {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前评论状态不可操作")
	}
	var wish = new(models.Wish)
	db.Take(&wish, comment.WishId)
	if wish.State != constmap.WishStateProcessing && wish.State != constmap.WishStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单状态异常")
	}
	if session.UserId != wish.UserId && session.UserId != comment.UserId {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无权操作")
	}
	if err := db.Delete(&comment).Error; err != nil {
		return nil, utils.NewError(err)
	}
	var out struct {
		Id        uint  `json:"id"`
		DeletedAt int64 `json:"deleted_at"`
	}
	out.Id = comment.ID
	out.DeletedAt = comment.DeletedAt.Time.Unix()
	return out, nil
}
