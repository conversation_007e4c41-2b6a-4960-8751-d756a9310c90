package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/banner_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func SquareIndex(ctx *gin.Context) (any, error) {

	db := utils.GetDB(ctx)

	var out struct {
		Banners    []beans.Banner        `json:"banners"`
		HotZones   []beans.WishHotZone   `json:"hot_zones"`
		HotTags    []beans.WishHotTag    `json:"hot_tags"`
		HotDayTags []beans.WishHotDayTag `json:"hot_day_tags"`
	}

	banners := banner_biz.LoadBanners(db, []constmap.BannerPosition{constmap.BannerPositionWishSquare})
	out.Banners = banners[constmap.BannerPositionWishSquare]
	out.HotZones, _ = wish_biz.GetHotZonesCc(db)
	out.HotTags, _ = wish_biz.GetHotTagsCc()
	out.HotDayTags, _ = wish_biz.GetHotDayTagsCc()
	return out, nil
}
