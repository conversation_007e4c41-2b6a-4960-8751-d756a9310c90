package wish

import (
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// checkUserCanApplyMember 检查用户是否可以申请成为同行人
func checkUserCanApplyMember(db *gorm.DB, wish *models.Wish, userId uint) bool {
	// 检查心愿单状态：只有进行中的心愿单才能申请
	if wish.State != constmap.WishStateProcessing {
		return false
	}

	// 检查心愿单截止时间：截止时间后不能申请
	if time.Now().After(wish.Deadline) {
		return false
	}

	// 检查用户是否为创建者：创建者不能申请自己的心愿单
	if wish.UserId == userId {
		return false
	}

	// 检查用户是否已申请过
	var member models.WishMember
	err := db.Where(models.WishMember{WishId: wish.ID, UserId: userId}).Take(&member).Error
	return err != nil // 如果查询失败说明没有申请记录，可以申请
}

func Detail(ctx *gin.Context) (any, error) {
	var in struct {
		WishId uint `form:"wish_id" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	var wish = new(models.Wish)

	var userId uint
	if session, err := business.GetFrontLoginUser(ctx); err == nil {
		userId = session.UserId
	}

	// 查询心愿单主数据及关联数据（不包含Ext）
	if err := db.Preload("Todos").Preload("Members").Preload("Medias").Take(&wish, in.WishId).Error; err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "心愿单不存在")
	}

	// 检查访问权限：如果是私有心愿单，只允许创建者查看
	if wish.OpenScope == constmap.Disable {
		// 获取当前登录用户
		session, err := business.GetFrontLoginUser(ctx)
		if err != nil {
			return nil, utils.NewErrorStr(constmap.ErrorNotLogin, "请先登录")
		}

		// 检查是否为创建者
		if session.UserId != wish.UserId {
			return nil, utils.NewErrorStr(constmap.ErrorParam, "无权限查看此心愿单")
		}
	}

	// 收集所有需要查询的用户ID（创建者 + 成员）
	userIds := []uint{wish.UserId}
	for _, member := range wish.Members {
		if member.UserId > 0 {
			userIds = append(userIds, member.UserId)
		}
	}

	// 批量查询用户信息
	userMap := user_biz.LoadUsers(db, userIds)

	// 解析标签信息 - 确保输出空数组而不是null
	tags := make([]string, 0)
	tagMap := wish_biz.LoadWishTags(db, *wish.TagIds.Data)
	if len(tagMap) > 0 {
		tags = slice.Map(maputil.Values(tagMap), func(index int, item *models.WishTag) string {
			return item.Tag
		})
	}

	// 组装输出数据
	type todoItem struct {
		Id     uint   `json:"id"`
		Todo   string `json:"todo"`
		IsMust int    `json:"is_must"`
	}

	type memberItem struct {
		Id       uint                     `json:"id"`
		UserId   uint                     `json:"user_id"`
		Nickname string                   `json:"nickname"`
		Avatar   string                   `json:"avatar"`
		IsOwner  int                      `json:"is_owner"`
		Budget   string                   `json:"budget"`
		Remark   string                   `json:"remark"`
		State    constmap.WishMemberState `json:"state"`
	}

	type mediaItem struct {
		Id        uint                   `json:"id"`
		MediaType constmap.WishMediaType `json:"media_type"`
		Size      int64                  `json:"size"`
		ResUrl    string                 `json:"res_url"`
	}

	var out struct {
		WishId         uint                    `json:"wish_id"`
		UserId         uint                    `json:"user_id"`
		Nickname       string                  `json:"nickname"`
		Avatar         string                  `json:"avatar"`
		Title          string                  `json:"title"`
		Cover          string                  `json:"cover"`
		From           string                  `json:"from"`
		To             string                  `json:"to"`
		ToZoneId       uint                    `json:"to_zone_id"`
		ToPoi          string                  `json:"to_poi"`
		BudgetType     constmap.WishBudgetType `json:"budget_type"`
		Budget         string                  `json:"budget"`
		TotalPeople    int                     `json:"total_people"`
		DepartDate     string                  `json:"depart_date"`
		ReturnDate     string                  `json:"return_date"`
		DateStr        string                  `json:"date_str"`
		Deadline       int64                   `json:"deadline"`
		OpenScope      int                     `json:"open_scope"`
		State          constmap.WishState      `json:"state"`
		StateText      string                  `json:"state_text"`
		Likes          int64                   `json:"likes"`
		IsLike         bool                    `json:"is_like"`          //是否已点赞
		CanApplyMember bool                    `json:"can_apply_member"` //是否可申请同行人
		MemberDesc     string                  `json:"member_desc"`
		WishDesc       string                  `json:"wish_desc"`
		Tags           []string                `json:"tags"`
		CreatedAt      int64                   `json:"created_at"`
		Todos          []todoItem              `json:"todos"`
		Members        []memberItem            `json:"members"`
		Medias         []mediaItem             `json:"medias"`
		PlanPrompt     string                  `json:"plan_prompt"`
	}

	// 填充基本信息
	out.WishId = wish.ID
	out.UserId = wish.UserId
	out.Title = wish.Title
	out.Cover = utils.StaticUrl(wish.Cover)
	out.From = wish.From
	out.To = wish.To
	out.ToZoneId = wish.ToZoneId
	out.ToPoi = wish.ToPoi
	out.BudgetType = wish.BudgetType
	out.Budget = wish.Budget
	out.TotalPeople = wish.TotalPeople
	out.DepartDate = wish.DepartDate
	out.ReturnDate = wish.ReturnDate
	out.DateStr = wish_biz.GetDepartReturnStr(wish.DepartDate, wish.ReturnDate)
	out.Deadline = wish.Deadline.Unix()
	out.OpenScope = wish.OpenScope
	out.State = wish.State
	out.StateText = business.WishStateFrontText(wish.State)

	// 使用新的通用点赞系统获取点赞数
	likeMap := like_biz.LoadLikes(db, constmap.LikeWish, []uint{wish.ID})
	likeDoMap := like_biz.LoadLikesDo(db, constmap.LikeWish, []uint{wish.ID}, userId)
	out.Likes = likeMap[wish.ID]

	// 查询当前用户状态（点赞状态和申请状态）
	out.IsLike = false         // 默认未点赞
	out.CanApplyMember = false // 默认不可申请

	session, sessionErr := business.GetFrontLoginUser(ctx)
	if sessionErr == nil {
		// 用户已登录，查询相关状态
		out.IsLike = likeDoMap[wish.ID]
		out.CanApplyMember = checkUserCanApplyMember(db, wish, session.UserId)
	}

	out.MemberDesc = wish.MemberDesc
	out.WishDesc = wish.WishDesc
	out.Tags = tags
	out.CreatedAt = wish.CreatedAt.Unix()
	// 生成AI提示语
	out.PlanPrompt = wish_biz.BuildWishPrompt(wish, wish.Todos)

	// 填充用户信息
	user, userExists := userMap[wish.UserId]
	if userExists {
		out.Nickname = user.Nickname
		out.Avatar = utils.AvatarUrl(user.Avatar)
	}

	out.Todos = make([]todoItem, 0)
	if len(wish.Todos) > 0 {
		out.Todos = slice.Map(wish.Todos, func(index int, item models.WishTodo) todoItem {
			return todoItem{
				Id:     item.ID,
				Todo:   item.Todo,
				IsMust: item.IsMust,
			}
		})
	}

	// 填充Members - 确保输出空数组而不是null
	out.Members = make([]memberItem, 0)
	if len(wish.Members) > 0 {
		out.Members = slice.Map(wish.Members, func(index int, item models.WishMember) memberItem {
			memberUser, memberUserExists := userMap[item.UserId]

			return memberItem{
				Id:       item.ID,
				UserId:   item.UserId,
				Nickname: utils.If(memberUserExists, memberUser.Nickname, ""),
				Avatar:   utils.If(memberUserExists, utils.AvatarUrl(memberUser.Avatar), ""),
				IsOwner:  item.IsOwner,
				Budget:   item.Budget,
				Remark:   item.Remark,
				State:    item.State,
			}
		})
	}

	// 填充Medias - 确保输出空数组而不是null
	out.Medias = make([]mediaItem, 0)
	if len(wish.Medias) > 0 {
		out.Medias = slice.Map(wish.Medias, func(index int, item models.WishMedia) mediaItem {
			return mediaItem{
				Id:        item.ID,
				MediaType: item.MediaType,
				Size:      item.Size,
				ResUrl:    utils.StaticUrl(item.ResUrl),
			}
		})
	}

	return out, nil
}
