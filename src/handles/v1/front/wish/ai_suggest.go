package wish

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func AISuggest(ctx *gin.Context) (any, error) {
	type suggestType string
	const (
		SuggestTypeTodos   suggestType = "todos"   //计划做的事
		SuggestTypeSubject suggestType = "subject" //主题
		SuggestTypeDesc    suggestType = "desc"    //描述
	)
	var in struct {
		Type   suggestType `form:"type" binding:"required"`
		Prompt string      `form:"prompt" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)

	var out struct {
		Suggests []string `json:"suggests"`
	}
	var aiError error

	switch in.Type {
	default:
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	case SuggestTypeTodos:
		if res, err := my_dify.WishTodos(ctx, db, in.Prompt); err != nil {
			aiError = err
		} else {
			out.Suggests = res.WantToDo
		}
	case SuggestTypeSubject:
		if res, err := my_dify.WishSubject(ctx, db, in.Prompt); err != nil {
			aiError = err
		} else {
			out.Suggests = []string{res.Subject}
		}
	case SuggestTypeDesc:
		if res, err := my_dify.WishDesc(ctx, db, in.Prompt); err != nil {
			aiError = err
		} else {
			out.Suggests = []string{res.Desc}
		}
	}
	if aiError != nil {
		my_logger.Errorf("[AISuggest]dify.%s", zap.Error(aiError))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	return out, nil
}
