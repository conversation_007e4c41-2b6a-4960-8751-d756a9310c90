package wish

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 设为私密
func CommentSetPrivate(ctx *gin.Context) (any, error) {
	var in struct {
		CommentId uint `form:"comment_id" binding:"required"`
		IsPrivate int  `form:"is_private" binding:"required"`
	}
	if err := ctx.ShouldBind(&in); err != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}

	db := utils.GetDB(ctx)
	session, _ := business.GetFrontLoginUser(ctx)

	var comment = new(models.WishComment)
	if db.Take(&comment, in.CommentId).Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "评论不存在")
	}
	if comment.State != constmap.WishCommentStateApproved {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "当前评论状态不可操作")
	}
	var cnt int64
	if db.Model(&models.Wish{}).Where("id=? AND state in ? and user_id=?",
		comment.WishId,
		[]constmap.WishState{constmap.WishStateProcessing, constmap.WishStateSuccess},
		session.UserId,
	).Count(&cnt); cnt == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "无权操作")
	}

	if err := db.Model(&comment).Updates(models.WishComment{
		IsPrivate: utils.If(in.IsPrivate == constmap.Enable, constmap.Enable, constmap.Disable),
	}).Error; err != nil {
		return nil, utils.NewError(err)
	}

	var out struct {
		Id        uint `json:"id"`
		IsPrivate int  `json:"is_private"`
	}
	out.Id = comment.ID
	out.IsPrivate = comment.IsPrivate
	return out, nil
}
