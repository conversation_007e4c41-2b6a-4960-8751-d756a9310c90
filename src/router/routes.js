import Layout from "../layout/index.vue";
import login from '../views/login/index.vue'

export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: login,
    hidden: true,
  },
  {
    path: '/',
    component: Layout,
    name: 'container',
    redirect: {name: 'Orders'},
    children: [{
      path: 'home',
      name: 'Home',
      component: () => import('../views/home/<USER>'),
      meta: {
        icon: 'HomeFilled',
        title: '控制台',
        fixed: true,
      }
    }]
  },
  {
    path: '/wifi',
    name: 'wifi',
    component: Layout,
    meta: {
      title: '机上WIFI',
      icon: 'Goods'
    },
    children: [
      {
        path: 'advertisers',
        name: 'Advertiser',
        component: () => import('../views/advertiser/AdvertiserIndex.vue'),
        meta: {
          title: '品牌方管理'
        }
      },
      {
        path: 'cards',
        name: 'WifiCards',
        component: () => import('../views/wifi/FlowCardIndex.vue'),
        meta: {
          title: '品牌方流量卡采购'
        }
      },
      {
        path: 'codes',
        name: 'WifiCodes',
        component: () => import('../views/wifi/Codes.vue'),
        meta: {
          title: '上网码'
        }
      },
    ]
  },
  {
    path: '/product',
    name: 'product',
    component: Layout,
    meta: {
      title: '商品管理',
      icon: 'Goods'
    },
    children: [{
      path: 'list',
      name: 'Products',
      component: () => import('../views/tuan/TuanIndex.vue'),
      meta: {
        title: '团游列表'
      }
    }
    ]
  },
  {
    path: '/scene',
    name: 'scene',
    component: Layout,
    meta: {
      title: '景点管理',
      icon: 'Sunrise'
    },
    children: [
      {
        path: 'list',
        name: 'Scenes',
        component: () => import('../views/scene/SceneIndex.vue'),
        meta: {
          icon: 'Sunrise',
          title: '景点列表'
        }
      },
      {
        path: 'scenesDetail',
        name: 'ScenesDetail',
        component: () => import('../views/scene/SceneDetail.vue'),
        hidden: true,
        meta: {
          title: '景点详情'
        }
      }
    ]
  },
  {
    path: '/order',
    name: 'order',
    component: Layout,
    meta: {
      title: '订单管理',
      icon: 'Histogram',
    },
    children: [
      {
        path: 'list',
        name: 'Orders',
        component: () => import('../views/order/OrderIndex.vue'),
        meta: {
          title: '订单列表'
        },
      },
      {
        path: 'refunds',
        name: 'Refunds',
        component: () => import('../views/order/Refunds.vue'),
        meta: {
          title: '退款管理'
        },
      },
      {
        path: 'detail',
        name: 'OrderDetail',
        component: () => import('../views/order/OrderDetail.vue'),
        hidden: true,
        meta: {
          title: '订单详情'
        },
      },
    ]
  },
  {
    path: '/activity',
    name: 'activity',
    component: Layout,
    meta: {
      title: '活动管理',
      icon: 'Coordinate',
    },
    children: [
      {
        path: 'list',
        name: 'ActivityList',
        component: () => import('../views/activity/ActivityIndex.vue'),
        meta: {
          title: '活动列表'
        },
      },
    ]
  },
  {
    path: '/package',
    name: 'package',
    component: Layout,
    meta: {
      title: '套餐管理',
      icon: 'Box'
    },
    children: [
      {
        path: 'list',
        name: 'PackageList',
        component: () => import('../views/package/PackageIndex.vue'),
        meta: {
          title: '套餐管理'
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'user',
    component: Layout,
    meta: {
      title: '用户管理',
      icon: 'User',
    },
    children: [
      {
        path: 'list',
        name: 'UserList',
        component: () => import('../views/user/UserIndex.vue'),
        meta: {
          icon: 'User',
          title: '用户列表'
        },
      },
      {
        path: 'detail',
        name: 'UserDetail',
        component: () => import('../views/user/UserDetail.vue'),
        hidden: true,
        meta: {
          title: '用户详情'
        },
      },
    ]
  },
  {
    path: '/workflow',
    name: 'workflow',
    component: Layout,
    meta: {
      title: '工作流管理',
      icon: 'Connection'
    },
    children: [
      {
        path: 'list',
        name: 'WorkflowList',
        component: () => import('../views/workflow/WorkflowIndex.vue'),
        meta: {
          title: '工作流管理'
        }
      }
    ]
  },
  {
    path: '/task',
    name: 'task',
    component: Layout,
    meta: {
      title: '任务管理',
      icon: 'Grape',
    },
    children: [
      {
        path: 'list',
        name: 'Tasks',
        component: () => import('../views/task/TaskIndex.vue'),
        meta: {
          title: '任务列表'
        },
      },
      {
        path: 'detail',
        name: 'TaskDetail',
        component: () => import('../views/task/TaskDetail.vue'),
        hidden: true,
        meta: {
          title: '任务详情'
        },
      },
    ]
  },
  {
    path: '/wish',
    name: 'wish',
    component: Layout,
    meta: {
      title: '旅行心愿管理',
      icon: 'Star'
    },
    children: [
      {
        path: 'list',
        name: 'WishIndex',
        component: () => import('../views/wish/WishIndex.vue'),
        meta: {
          title: '心愿列表'
        }
      },
      {
        path: 'detail',
        name: 'WishDetail',
        component: () => import('../views/wish/WishDetail.vue'),
        hidden: true,
        meta: {
          title: '心愿详情'
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'SettingIndex',
    component: Layout,
    meta: {
      title: '设置',
      icon: 'Setting',
    },
    children: [
      {
        path: 'lightbox-versions',
        name: 'LightBoxVersions',
        component: () => import('../views/setting/LightBoxVersion.vue'),
        meta: {
          title: '万花筒版本管理',
          icon: 'Setting'
        },
      },
      {
        path: 'index',
        name: 'SettingIndex',
        component: () => import('../views/setting/SettingIndex.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        },
      },
    ]
  },

  {
    path: '/404',
    name: '404',
    hidden: true,
    component: () => import('../views/ErrorMessage/404.vue')
  }
]

export const dynamicRoutes = []

const routes = [...constantRoutes.map(item => {
  if (!item.meta) {
    item.meta = {}
  }
  if (item.children) {
    item.children.forEach(child => {
      child.meta.constant = true
    })
  }
  item.meta.constant = true
  return item
}), ...dynamicRoutes]

export default routes
