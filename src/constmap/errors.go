package constmap

import "fmt"

const (
	RequestOk     = iota
	ErrorNotLogin = 900000
	ErrorSystem   = ErrorNotLogin + iota
	ErrorParam
	ErrorLoginFail
	ErrorTwoPass
	ErrorUpload
	ErrorIsGot
	ErrorState
	ErrorOldPwd
	ErrorStockNotEnough
	ErrorRoute
	ErrorMobile
	ErrorDateNotSupport
	ErrorNotPermission
	ErrorPay
	ErrorApplyRefund
	ErrorVersion
	ErrorChatExpire
	ErrorPackType
	ErrorNoOrder
	ErrorAccountAmountNotEnough //余额不足
)

const (
	ErrorMsgSystem         = "系统错误"
	ErrorMsgParam          = "参数错误"
	ErrorMsgNotLogin       = "您还没有登录"
	ErrorMsgLoginFail      = "账户名或者密码错误"
	ErrorMsgUpload         = "上传文件失败"
	ErrorMsgTwoPass        = "两次输入的密码不同"
	ErrorMsgIsGot          = "该上网码已被领取"
	ErrorMsgState          = "状态不正确"
	ErrorMsgOldPwd         = "原密码错误"
	ErrorMsgStockNotEnough = "库存不足"
	ErrorMsgMobile         = "不是正确的手机号码"
	ErrorMsgRoute          = "找不到该路线"
	ErrorMsgDateNotSupport = "当前日期不支持报名"
	ErrorMsgNotPermission  = "权限不足"
	ErrorMsgPay            = "支付失败"
	ErrorMsgApplyRefund    = "退款申请失败"
	ErrorMsgVersion        = "版本格式不正确"
	ErrorMsgAirlineRepeat  = "航空公司重复了"
	ErrorMsgConcurrency    = "操作过快，请稍后"
	ErrorMsgChatExpire     = "会话已过期，请开启新的会话"
	ErrorMsgPackType       = "打包类型错误"
)

type AppError struct {
	Msg  string
	Code int
}

func (a AppError) Error() string {
	return fmt.Sprintf("[%d] %s", a.Code, a.Msg)
}
