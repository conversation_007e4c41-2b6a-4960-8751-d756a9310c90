package constmap

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/condition"
	"github.com/duke-git/lancet/v2/convertor"
	"time"
)

type GenericList[T any] []T

func (z *GenericList[T]) MarshalBinary() (data []byte, err error) {
	return json.Marshal(z)
}

func (z *GenericList[T]) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, z)
}

func (o TravelInterest) ToString() string {
	switch o {
	case TravelInterestNature:
		return "自然"
	case TravelInterestHumanity:
		return "人文"
	case TravelInterestChildren:
		return "亲子"
	case TravelInterestEntertainment:
		return "娱乐"
	}
	return ""
}

func (o TravelStrength) ToString() string {
	switch o {
	case TravelStrengthRelaxed:
		return "轻松"
	case TravelStrengthModerate:
		return "适中"
	case TravelStrengthCompact:
		return "紧凑"
	}
	return ""
}

func (o TravelAccommodation) ToString() string {
	switch o {
	case TravelAccommodationEconomic:
		return "经济"
	case TravelAccommodationModerate:
		return "舒适"
	case TravelAccommodationLuxury:
		return "豪华"
	}
	return ""
}

func (o TravelHowPlay) ToString() string {
	switch o {
	case TravelHowPlayDestination:
		return "到目的地周边玩"
	case TravelHowPlayRouting:
		return "一路玩过去"
	}
	return string(o)
}

func (o IdType) ToString() string {
	m := map[IdType]string{
		IdTypeIdCard:           "身份证",
		IdTypeStudent:          "学生证",
		IdTypeSoldier:          "军官证",
		IdTypePassport:         "护照",
		IdTypeResidenceBooklet: "户口本",
		IdTypeGAPassport:       "港澳通行证",
		IdTypeTaiwanResident:   "台胞证",
		IdTypeTaiwanPassport:   "台湾通行证",
		IdTypeGreenCard:        "外国人在中国永久居留证",
	}
	s, ok := m[o]
	return condition.TernaryOperator(ok, s, UnknownStr)
}
func (o DateTimeMinute) MarshalJSON() ([]byte, error) {
	if o.Time.IsZero() {
		return []byte(`""`), nil
	}
	return []byte(fmt.Sprintf(`"%s"`, o.Time.Format(DateFmtLongMinute))), nil
}

func (o *DateTimeMinute) UnmarshalJSON(b []byte) error {
	if len(b) == 0 || b[0] == '"' && b[1] == '"' {
		return nil
	}
	//去除左右的双引号再解码
	t, err := time.Parse(DateFmtLongMinute, string(b[1:len(b)-1]))
	if err != nil {
		return err
	}
	o.Time = t
	return nil
}
func (o DateTime) MarshalJSON() ([]byte, error) {
	if o.Time.IsZero() {
		return []byte(`""`), nil
	}
	return []byte(fmt.Sprintf(`"%s"`, o.Time.Format(DateFmtLongFull))), nil
}

func (o *DateTime) UnmarshalJSON(b []byte) error {
	if len(b) == 0 || b[0] == '"' && b[1] == '"' {
		return nil
	}
	//去除左右的双引号再解码
	t, err := time.Parse(DateFmtLongFull, string(b[1:len(b)-1]))
	if err != nil {
		return err
	}
	o.Time = t
	return nil
}

func (o Date) MarshalJSON() ([]byte, error) {
	if o.Time.IsZero() {
		return []byte(`""`), nil
	}
	return []byte(fmt.Sprintf(`"%s"`, o.Time.Format(DateFmtLong))), nil
}

func (o *Date) UnmarshalJSON(b []byte) error {
	if len(b) == 0 || b[0] == '"' && b[1] == '"' {
		return nil
	}
	//去除左右的双引号再解码
	t, err := time.Parse(DateFmtLong, string(b[1:len(b)-1]))
	if err != nil {
		return err
	}
	o.Time = t
	return nil
}

func (o DateUnixStamp) MarshalJSON() ([]byte, error) {
	if o.Time.IsZero() {
		return []byte(`0`), nil
	}
	return []byte(convertor.ToString(o.Unix())), nil
}

func (o *DateUnixStamp) UnmarshalJSON(b []byte) error {
	if len(b) == 0 || b[0] == '"' && b[1] == '"' {
		return nil
	}
	t, err := convertor.ToInt(string(b))
	if err != nil {
		return err
	}
	o.Time = time.Unix(t, 0)
	return nil
}

func (o HotelCancelPolicyType) ToString() string {
	s, ok := map[HotelCancelPolicyType]string{
		HotelCancelPolicyFree:       "无费用",
		HotelCancelPolicyFixedMoney: "固定费用",
		HotelCancelPolicyPercent:    "固定比例",
		HotelCancelPolicyFirstNight: "首晚房费",
		HotelCancelPolicyAll:        "全部房费",
	}[o]
	return condition.TernaryOperator(ok, s, UnknownStr)
}
