package models

import "time"

type FlowCardRewardCode struct {
	Model

	Code       string `gorm:"size:50;uniqueIndex"`
	State      int    `gorm:"default:1"`
	FlowCardId uint   `gorm:"index"`

	FlowCard FlowCard
}

type Advertiser struct {
	Model

	Name  string `gorm:"size:50"`
	State int    `gorm:"default:1;type:tinyint"`
}

type Airline struct {
	Model

	Code        string `gorm:"size:10"`
	Name        string `gorm:"size:60"`
	ProductName string
	Logo        string
	Desc        string
	Scope       string
}

type FlowCardAirline struct {
	Model

	FlowCardId     uint   `gorm:"index"`
	Code           string `gorm:"size:8"`
	TotalStock     int
	RemainingStock int
	LockedStock    int

	Air Airline `gorm:"foreignKey:code;references:code"`
}

type FlowCardCode struct {
	Model

	Code     string    `gorm:"uniqueIndex"`
	GotState int       `gorm:"default:1;type:tinyint"`
	Airline  string    `gorm:"size:8"`
	State    int       `gorm:"default:1;type:tinyint"`
	StartAt  time.Time `gorm:"type:date"`
	EndAt    time.Time `gorm:"type:date"`
	UsedAt   time.Time
}

type FlowCardRelCode struct {
	Model

	UserId         uint
	FlowCardId     uint
	AdvertiserId   uint
	Airline        string `gorm:"size:8"`
	Ip             string `gorm:"size:20"`
	RewardId       uint
	FlowCardCodeId uint
	GetType        int `gorm:"default:1;type:tinyint"`

	Air        Airline      `gorm:"foreignKey:Airline;references:Code"`
	Code       FlowCardCode `gorm:"foreignKey:FlowCardCodeId"`
	Advertiser Advertiser
	User       User
}

type FlowCard struct {
	Model

	Code         string
	AdvertiserId uint
	StartTime    time.Time
	EndTime      time.Time
	TemplateId   uint
	State        int `gorm:"default:1;type:tinyint"`
	Type         int `gorm:"default:1;type:tinyint"`
	GrantType    int `gorm:"default:1;type:tinyint"`
	Title        string

	Advertiser Advertiser
	Airlines   []FlowCardAirline
}
