package models

import (
	"fmt"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"roadtrip-api/src/config"
	"sync"
	"time"
)

var db *gorm.DB
var once sync.Once

func initDb() {
	var err error
	logLevel := logger.Warn
	if config.IsDebug() {
		logLevel = logger.Info
	}

	dsn := fmt.Sprintf(`%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local&sql_mode="STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION"`,
		config.Config.Db.Username,
		config.Config.Db.Password,
		config.Config.Db.Host,
		config.Config.Db.Port,
		config.Config.Db.Name,
		config.Config.Db.Charset)

	db, err = gorm.Open(mysql.New(mysql.Config{
		DSN:                       dsn,
		DefaultStringSize:         256,
		SkipInitializeWithVersion: false,
	}), &gorm.Config{
		SkipDefaultTransaction:                   true,
		IgnoreRelationshipsWhenMigrating:         true,
		DisableForeignKeyConstraintWhenMigrating: true,
		PrepareStmt:                              true,
		Logger: logger.New(
			log.New(os.Stdout, "\r\n", log.LstdFlags),
			logger.Config{
				SlowThreshold:             time.Second * 5,
				IgnoreRecordNotFoundError: true,
				LogLevel:                  logLevel,
			},
		),
	})
	if err != nil {
		panic(err)
	}

	sqldb, _ := db.DB()
	sqldb.SetMaxIdleConns(2)
	sqldb.SetMaxOpenConns(120)
	sqldb.SetConnMaxLifetime(10 * time.Minute)

	if err = migrate(); err != nil {
		panic(err)
	}
}

func New() *gorm.DB {
	once.Do(initDb)

	return db.Session(&gorm.Session{
		NewDB: true,
	})
}

type Model struct {
	gorm.Model
}
