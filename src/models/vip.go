package models

import (
	"roadtrip-api/src/constmap"
	"time"
)

// vip配置
type VipConf struct {
	Model

	Name        string `gorm:"comment:会员名称"`
	Desc        string
	Icon        string
	MarketPrice uint `gorm:"comment:市场价"`
	SalePrice   uint `gorm:"comment:现价"`
	Years       int  `gorm:"type:tinyint;default:1;comment:效期"`
	State       int  `gorm:"type:tinyint;not null;default:2"`
}

// vip权益
type VipBenefit struct {
	Model

	VipConfId   uint `gorm:"index"`
	Code        constmap.VipBenefitCode
	Name        string
	Desc        string
	Icon        string
	BenefitType constmap.VipBenefitType `gorm:"type:tinyint"`
	Times       int                     `gorm:"comment:权益次数"`
	Discount    float64                 `gorm:"comment:权益优惠折扣"`
	Extra       string                  `gorm:"type:text"`
}

// 会员信息
type UserVip struct {
	Model

	UserId    uint `gorm:"index:idx_user"`
	Start     time.Time
	End       time.Time `gorm:"index:idx_end"`
	State     int       `gorm:"index:idx_end;index:idx_user;type:tinyint;default:2"`
	Price     uint      `gorm:"comment:开通价格"`
	RealName  string
	FirstName string
	LastName  string
	IdCard    string `gorm:"comment:身份证号码"`
	Extra     string `gorm:"type:text"`
}

// 会员权益
type UserVipBenefit struct {
	Model

	UserVipId   uint `gorm:"index"`
	UserId      uint
	ConfId      uint
	BenefitId   uint
	BenefitType constmap.VipBenefitType `gorm:"type:tinyint"`
	Times       int
	RemainTimes int `gorm:"comment:剩余次数"`
}

// 用户套餐汇总
type UserPackageSummary struct {
	Model

	UserId      uint `gorm:"unique"`
	PackageId   uint
	PackageName string    //套餐名称
	ExpireAt    time.Time //过期时间
	Version     uint64    `gorm:"default:0"`
}

// 用户套餐
type UserPackage struct {
	Model

	UserId       uint `gorm:"index:upkg"`
	PaymentId    uint
	PackageId    uint `gorm:"index:upkg"`
	PackageName  string
	PayPrice     int64
	TotalAmount  int                       `gorm:"default:0"` //积分余额
	Amount       int                       `gorm:"default:0"` //剩余积分
	UsedAmount   int                       `gorm:"default:0"` //已使用积分
	ExpireAmount int                       `gorm:"default:0"` //过期积分
	State        constmap.UserPackageState `gorm:"index:uexp"`
	ExpireAt     time.Time                 `gorm:"index:uexp"` //过期时间
	Remark       string
	Version      uint64 `gorm:"default:0"`
}

type UserPackageExt struct {
	Model

	UserPackageId uint   `gorm:"unique"`
	Extra         string `gorm:"type:text"`
}

// 套餐
type Package struct {
	Model

	Name         string
	Desc         string
	Price        int64                   //价格：分
	RealPrice    int64                   // 实际价格：分
	Amount       int                     //给予金额
	ExpNum       int                     //到期数字
	ExpUnit      constmap.PackageExpUnit `gorm:"type:tinyint"` //到期数字单位
	NewUsable    int                     `gorm:"type:tinyint"` //首充可用
	NewRealPrice int64                   // 首充实际价格：分
	State        int                     `gorm:"type:tinyint"`
	Sort         int                     `gorm:"default:1"`
	Tags         string
}
