package models

import (
	"roadtrip-api/src/constmap"
	"time"
)

type Order struct {
	Model

	UserId        uint
	State         int `gorm:"default:1;type:tinyint"`
	PayState      int `gorm:"default:1;type:tinyint"`
	PayTime       time.Time
	OrderType     constmap.OrderType `gorm:"index;size:32;default:'tuan';comment:订单类型"`
	Pic           string
	PlanId        uint
	ProductId     uint
	ContactsName  string
	ContactsTel   string
	NeedPayAmount int64
	RefundAmount  int64 //已退款金额 RefundAmount<=NeedPayAmount
	TotalAmount   int64
	Title         string
	Date          time.Time
	PayTimeExpire *time.Time

	Payments    []OrderPayment
	Details     []OrderDetail
	User        User
	ThirdOrders []OrderThirdOrder
	Vouchers    []OrderVoucher
	Peoples     []OrderPeople
}

type OrderDetail struct {
	Model

	OrderId      uint `gorm:"index"`
	State        int  `gorm:"type:tinyint;default:30;comment:子订单状态"`
	UserId       uint
	SkuId        uint
	ScenicId     uint
	SubOrderNo   string                `gorm:"index;size:64;comment:子订单号"`
	OrderSubType constmap.OrderSubType `gorm:"index;type:tinyint;default:1;comment:订单子类型"`
	ProductType  constmap.ProductType  `gorm:"index:idx_prod;size:32;default:'tuan';comment:产品类型"`
	ProductId    uint                  `gorm:"index:idx_prod;comment:产品id"`
	Pic          string
	ProductName  string
	SkuName      string
	SkuPrice     int64 //义伴单价
	DateStart    int64 `gorm:"not null;default:0"`
	DateEnd      int64 `gorm:"not null;default:0"`
	Quantity     int
	Amount       int64           // 总价: 单价*数量
	OtaAmount    int64           // 渠道总价
	OtaPriceRate int64           // 渠道加价比例
	Remark       string          `gorm:"size:512;comment:备注"`
	Ext          *OrderDetailExt `gorm:"-"`
}
type OrderDetailExt struct {
	Model

	OrderId       uint   `gorm:"index"`
	OrderDetailId uint   `gorm:"unique"`
	Peoples       string `gorm:"type:text;comment:人员登记"`
	Rooms         string `gorm:"type:text;comment:房间序号"`
	Ext           string `gorm:"type:text;comment:扩展信息"`
}

type OrderPayment struct {
	Model

	OrderId         uint `gorm:"index"`
	UserId          uint
	State           int `gorm:"default:1;type:tinyint"`
	NeedPayAmount   int64
	ActualPayAmount int64
	PayTime         *time.Time
	OutTradeNo      string `gorm:"index"`
	ThirdPartyNo    string
	PayMethod       int
	Remarks         string
	RefundAmount    int64 `gorm:"default:0"`
	BuyerId         string
	PaymentType     constmap.PaymentType
	Extra           string `gorm:"type:text"`

	Order Order
}

// 微信退款(三方订单退款均终态后生成)
type RefundPayment struct {
	Model

	PayId         uint
	OrderId       uint
	RefundOrderId uint
	RefundAmount  int64
	OutRefundNo   string `gorm:"size:50"` //站内退款单号
	ThirdPartyNo  string // 微信退款单号
	State         int    `gorm:"default:1"`
	RefundReason  string
	Resp          string `gorm:"type:text"`

	Payment     OrderPayment `gorm:"foreignKey:PayId"`
	RefundOrder RefundOrder
}

// 退款订单
type RefundOrder struct {
	Model

	OrderId           uint `gorm:"index"`
	UserId            uint `gorm:"index"`
	RefundPaymentId   uint // 发起微信退款时生成
	State             constmap.RefundOrderState
	ApplyReason       string
	ApplyRefundAmount int64 // 申请退款金额
	RefundAmount      int64 // 退款金额(三方退款均终态后重写)

	Order           Order
	RefundSuborders []RefundSuborder
}

// 退款子订单
type RefundSuborder struct {
	Model

	OrderId            uint `gorm:"index"`
	OrderDetailId      uint `gorm:"index"`
	RefundOrderId      uint `gorm:"index"`
	RefundSubPaymentId uint //子订单退款审核通过后生成
	State              constmap.RefundSubOrderState
	RefundQuantity     int    // 退款数量
	RejectReason       string // 拒绝原因
	ApplyRefundAmount  int64  // 申请退款金额
	RefundAmount       int64  // 退款金额(三方退款成功后重新计算)
	OtaRefundAmount    int64  // 渠道应退款金额
	OtaPenaltyAmount   int64  // 渠道退费罚金(三方退款成功后重写)

	OrderDetail OrderDetail
}

// 子订单退款(子订单退款审核通过后生成)
type RefundSubPayment struct {
	Model

	RefundSuborderId uint `gorm:"index"`
	State            constmap.RefundSubPaymentState
	RefundNo         string `gorm:"size:50"` //站内退款单号
	OutRefundNo      string // 三方退款单号
	RefundAmount     int64  // 退款金额
	OtaRefundAmount  int64  // 渠道应退款金额
	OtaPenaltyAmount int64  // 渠道退费罚金
	FailReason       string //失败原因
	Resp             string `gorm:"type:text"`

	RefundSuborder RefundSuborder
}

// 旅客信息
type OrderPeople struct {
	Model
	OrderId      uint
	Name         string          `gorm:"size:64;not null;comment:姓名"`
	Phone        string          `gorm:"size:32;not null;comment:手机号"`
	IdType       constmap.IdType `gorm:"not null;comment:证件类型"`
	IdNo         string          `gorm:"size:64;not null;comment:证件号"`
	CustomerType int             `gorm:"type:tinyint"` //1成人 2儿童
}

// 三方订单关联
type OrderThirdOrder struct {
	Model
	OrderId        uint                    `gorm:"index"`
	OrderDetailId  uint                    `gorm:"index"`
	ThirdOrderId   string                  `gorm:"uniqueIndex:uniq_third"`
	ThirdOrderType constmap.ThirdOrderType `gorm:"uniqueIndex:uniq_third"`
	ThirdPayState  constmap.ThirdPayState  `gorm:"index;tinyint;default:10;comment:三方订单支付状态"`
	Remark         string                  `gorm:"size:512;comment:备注"`
	OrderDetail    OrderDetail
}

// 订单核销凭证
type OrderVoucher struct {
	Model
	OrderId       uint `gorm:"index"`
	OrderDetailId uint
	State         constmap.VoucherState `gorm:"type:tinyint"`
	VoucherNo     string                `gorm:"size:64;comment:核销码"`
	Pdf           string                `gorm:"size:512;comment:pdf地址"`
	Qr            string                `gorm:"size:512;comment:二维码地址"`
	Url           string                `gorm:"size:512;comment:普通链接地址"`
	Ext           string                `gorm:"type:text;comment:扩展信息"`
}
