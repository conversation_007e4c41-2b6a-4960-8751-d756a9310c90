package models

import (
	"encoding/json"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

type Zone struct {
	Model

	Name     string
	Level    int    `gorm:"default:2"`
	AdCode   string `gorm:"index"`
	ParentId uint
	State    int `gorm:"default:1"`
	Lng      float64
	Lat      float64
	Pic      string `gorm:"column:pic;type:varchar(1000) not null;default:'';comment:背景图片"`
	Pinyin   string

	Parent *Zone `gorm:"foreignKey:ParentId"`
}

func (z *Zone) MarshalBinary() (data []byte, err error) {
	return json.Marshal(z)
}

func (z *Zone) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, z)
}

type Session struct {
	Model

	UserId uint
	Token  string

	User User
}

type User struct {
	Model

	Nickname       string
	Avatar         string
	InviteCode     string `gorm:"index"` //邀请码
	BindInviteCode string //邀请人邀请码
	BindUserId     uint   //邀请人ID
}

type UserOauth struct {
	Model

	UserId uint   `gorm:"index"`
	OpenId string `gorm:"size:50;uniqueIndex"`
	Type   int    `gorm:"type:tinyint;default:1"`

	User User
}

// 常用出行人
type UserPeople struct {
	Model

	UserId    uint            `gorm:"index"`
	Name      string          `gorm:"size:64;not null;comment:姓名"`
	Phone     string          `gorm:"size:32;not null;comment:手机号"`
	IdType    constmap.IdType `gorm:"not null;comment:证件类型"`
	IdNo      string          `gorm:"size:64;not null;comment:证件号"`
	IsDefault int             `gorm:"default:2"`
}

type Banner struct {
	Model

	Title    string
	Start    time.Time
	End      time.Time
	Link     string
	Pic      string
	State    int                     `gorm:"type:tinyint;default:2"`
	Position constmap.BannerPosition `gorm:"default:1"`
	Sort     int
}

// 城市民俗文化、美食、特产
type ZoneSpeciality struct {
	Model

	ZoneId uint                                       `gorm:"unique"`
	Data   utils.Marshaller[beans.ZoneSpecialityData] `gorm:"type:text"`
}

type Like struct {
	Model

	Type  constmap.LikeType `gorm:"type:tinyint;uniqueIndex:like"`
	ResId uint              `gorm:"uniqueIndex:like"`
	Likes int64             `gorm:"default:0"`
}

type LikeDo struct {
	Model

	Type   constmap.LikeType `gorm:"type:tinyint;uniqueIndex:likedo"`
	ResId  uint              `gorm:"uniqueIndex:likedo"`
	UserId uint              `gorm:"uniqueIndex:likedo"`
}

// 消息模板
type MessageTpl struct {
	Model

	Type    constmap.MessageType    `gorm:"index:typ;type:tinyint"`
	SubType constmap.MessageSubType `gorm:"type:tinyint"`
	Title   string
	Content string `gorm:"type:text"`

	//业务字段
	WishId        uint
	WishCommentId uint //心愿评论ID:评论消息时使用
	WishMemberId  uint //心愿成员ID:邀请组队时使用
}

// 消息
type Message struct {
	Model

	MessageTplId uint       `gorm:"uniqueIndex:umsg"`
	UserId       uint       `gorm:"uniqueIndex:umsg"`
	IsRead       int        `gorm:"type:tinyint;default:2"`
	ReadTime     *time.Time `gorm:"comment:读取时间"`
}
