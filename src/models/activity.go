package models

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

// 活动
type Activity struct {
	Model

	Title   string
	Cover   string
	Uuid    string `gorm:"uniqueIndex"` //活动标识
	State   int
	Start   time.Time `gorm:"type:datetime"`
	End     time.Time `gorm:"type:datetime"`
	MpTplId string    //微信模板ID
	Link    string    //跳转地址

	Ext ActivityExt
}

type ActivityExt struct {
	Model

	ActivityId uint   `gorm:"index"`
	Rules      string `gorm:"type:text"` //规则说明
}

// 用户活动关联
type UserActivity struct {
	Model

	UserId     uint                                    `gorm:"uniqueIndex:uact"`
	ActivityId uint                                    `gorm:"uniqueIndex:uact"`
	Ext        utils.Marshaller[beans.UserActivityExt] `gorm:"type:text"`
}

type ActivityPlan struct {
	Model

	ActivityId uint `gorm:"uniqueIndex:actRank"`
	PlanId     uint `gorm:"uniqueIndex:actRank"`
	UserId     uint `gorm:"index"`
	Poster     string
}

type ActivityPlanRank struct {
	Model

	ActivityId     uint      `gorm:"index:rk"`
	ActivityPlanId uint      `gorm:"uniqueIndex:actRank"`
	Date           time.Time `gorm:"type:date;uniqueIndex:actRank;index:rk"`
	Score          int64     `gorm:"index:rk;default:0"`
}

// 排行榜结算后入榜榜单
type ActivityPlanRankSettle struct {
	Model

	ActivityId     uint      `gorm:"index:rk"`
	ActivityPlanId uint      `gorm:"uniqueIndex:actRank"`
	Date           time.Time `gorm:"type:date;uniqueIndex:actRank;index:rk"`
	Score          int64     `gorm:"index:rk;default:0"`
}

type ActivityPlanLike struct {
	Model

	ActivityPlanRankId uint `gorm:"uniqueIndex:likeIt"`
	UserId             uint `gorm:"uniqueIndex:likeIt"`
}

// 任务
type Task struct {
	Model

	Name            string
	State           int
	ParentTaskId    uint                  `gorm:"index"`
	MaxTimes        int                   //可奖励次数
	IntervalType    constmap.TaskInterval //任务周期
	CondType        constmap.TaskCond     //达成条件动作
	CondAmount      int                   `gorm:"default:1"` //(积分等)达成条件数额
	RankSettleNum   int                   //排行榜任务前N个排名有奖励
	RewardAmount    int                   //每次奖励数额
	RewardId        string                //奖励项的值
	RewardType      constmap.TaskReward   `gorm:"type:tinyint"`           //奖励类型
	RewardDuplicate int                   `gorm:"type:tinyint;default 2"` //是否可重叠奖励。否时奖励未领取时不再生成新奖励
	RealProductName string                //实物奖品名称
	ShortDesc       string                `gorm:"size:512"` //简短介绍
	ScenicId        uint                  //景点id
	Cover           string                //封面图片

	Ext TaskExt
}

type TaskExt struct {
	Model

	TaskId   uint   `gorm:"index"`
	Tutorial string `gorm:"type:text"` //攻略教程
}

// 任务关联
type TaskRelates struct {
	Model

	TaskId   uint                    `gorm:"index:trelate"`
	Type     constmap.TaskRelateType `gorm:"index:trelate;type:tinyint"`
	RelateId uint                    `gorm:"index:trelate"`
}

// 用户任务关联
type UserTask struct {
	Model

	UserId          uint `gorm:"index:utask"`
	TaskId          uint `gorm:"index:utask"`
	ParentTaskId    uint
	Date            time.Time              `gorm:"type:date;index:utask"` //周任务指向周一
	OrderId         uint                   //任务订单关联
	State           constmap.UserTaskState //用户任务状态
	MaxTimes        int                    //可奖励次数
	CondTimes       int                    //条件达成次数
	CondAccAmount   int                    //条件累计数额
	RewardTimes     int                    //奖励领取次数
	RewardAccAmount int                    //奖励累计领取数额
	RewardCode      string                 //领取奖励的绑定(机上wifi码)
	RealProductName string                 //实物奖品名称
	Version         uint64

	Logs []UserTaskLog
}

// 用户任务日志
type UserTaskLog struct {
	Model

	UserTaskId uint                     `gorm:"index"`
	UserId     uint                     `gorm:"index:utask"`
	TaskId     uint                     `gorm:"index:utask"`
	Type       constmap.UserTaskLogType `gorm:"type:tinyint"`
	HelperUid  uint                     //助力人uid
	PlanId     uint                     //行程id
	ActivityId uint                     //分享的活动ID
	ScenicId   uint                     ///景点id
	AiReqid    string
	Extra      string `gorm:"type:text"`
}

type UserTaskReward struct {
	Model

	UserTaskId      uint                         `gorm:"unique"`
	UserId          uint                         `gorm:"index:ureward"`
	TaskId          uint                         `gorm:"index:ureward"`
	OrderId         uint                         `gorm:"index"`
	RewardType      constmap.TaskReward          `gorm:"type:tinyint"`
	RewardState     constmap.UserTaskRewardState `gorm:"type:tinyint"`
	RealProductName string
	Num             int    //奖品数量
	Receiver        string //收货人
	RecvPhone       string //收货人电话
	RecvAddr        string `gorm:"size:500"`
	DeliveryName    string //物流公司
	DeliveryNo      string //物流单号
}
