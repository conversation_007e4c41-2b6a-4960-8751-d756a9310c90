package es

import "roadtrip-api/src/constmap"

type PoiModel struct {
	Type     int       `json:"type"`
	ObjId    uint      `json:"obj_id"`
	Name     string    `json:"name"`
	Location []float64 `json:"location"`
	Ota      []string  `json:"ota"`
	OtaId    []string  `json:"ota_id"`
	Keywords string    `json:"keywords"`
}

type Zone struct {
	PoiModel

	Level int `json:"level"`
}

type Scenic struct {
	PoiModel

	ZoneId   uint     `json:"zone_id"`
	CityName string   `json:"city_name"`
	State    int      `json:"state"`
	Level    int      `json:"level"`
	Llm      int      `json:"llm"`
	IsFree   bool     `json:"is_free"`
	Tags     []string `json:"tags"`
	Cate     []string `json:"cate"`
}

type Hotel struct {
	PoiModel

	ZoneId    uint               `json:"zone_id"`
	CityName  string             `json:"city_name"`
	State     int                `json:"state"`
	Star      int                `json:"star"`
	Score     float64            `json:"score"`
	BrandName string             `json:"brand_name"`
	AvgPrice  int64              `json:"avg_price"`
	GuestType constmap.GuestType `json:"guest_type"`
}
