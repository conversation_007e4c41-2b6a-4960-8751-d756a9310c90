package es

import (
	"roadtrip-api/src/constmap"
)

type Wish struct {
	ObjId     uint               `json:"obj_id"`
	ToZoneId  uint               `json:"to_zone_id"`
	Title     string             `json:"title"`
	Tags      []string           `json:"tags"`
	DayTags   []string           `json:"day_tags"` //周末、春节etc
	Likes     int64              `json:"likes"`    //点赞数
	State     constmap.WishState `json:"state"`
	OpenScope int                `json:"open_scope"`
	CreatedAt constmap.DateTime  `json:"created_at"`
}
