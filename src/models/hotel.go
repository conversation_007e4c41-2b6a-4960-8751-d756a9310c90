package models

import "time"

type Hotel struct {
	Model

	ZoneId       uint
	Name         string
	Lng          float64
	Lat          float64
	District     string //行政区
	BusinessArea string //商圈
	Address      string
	Tel          string
	CostText     string
	AvgPrice     int64
	Pic          string
	State        int     `gorm:"default:1"`
	Star         int     //星级
	StarDesc     string  //星级描述
	GoodRate     int     //好评率
	Score        float64 //评分
	BrandId      uint
	BrandName    string //品牌名称
	GuestType    string //客人类型
	OpenUpTime   string //开业时间

	Pics []HotelPic
	Zone Zone
}

type HotelExt struct {
	Model

	HotelId   uint   `gorm:"unique"`
	RecReason string `gorm:"type:text"` //推荐理由
}

type HotelPic struct {
	Model

	HotelId uint `gorm:"index"`
	Url     string
	Title   string `json:"text"`
	Main    int    `gorm:"default:2;comment:是否是主图"`
}

type HotelTag struct {
	Model

	HotelId uint
	Value   string
}

type HotelOta struct {
	Model

	HotelId uint `gorm:"index"`
	OtaId   string
	OtaCode string
}

type HotelType struct {
	Model

	HotelId  uint
	TypeCode string
}

type HotelBrand struct {
	Model

	BrandName string
}

type HotelOtaCeekee struct {
	Model

	ZoneId     uint
	CkZoneId   int
	HotelId    string `gorm:"uniqueIndex;size:50;not null"`
	HotelName  string `gorm:"comment:名称"`
	MatchState int    `gorm:"type:tinyint;default:2;comment:匹配状态"`
	Reason     string `gorm:"size:1000;not null;default:'';comment:未匹配原因"`
	Raw        string `gorm:"type:text;comment:原始数据"`
}

type HotelPrice struct {
	Model

	HotelId    uint      `gorm:"uniqueIndex:hotel_date"`
	Date       time.Time `gorm:"type:date;uniqueIndex:hotel_date"`
	AvgPrice   int64     //均价(分)
	IsFestival int       `gorm:"type:tinyint"` //是否是节假日
}

// 酒店知识库分段
type HotelSegment struct {
	Model

	HotelId   uint `gorm:"unique"`
	SegmentId string
}
