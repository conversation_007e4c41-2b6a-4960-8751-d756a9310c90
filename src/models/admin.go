package models

import "roadtrip-api/src/constmap"

type AdminSession struct {
	Model

	UserId   uint
	Token    string
	Username string

	User AdminUser
}

type AdminUser struct {
	Model

	Username string
	Nickname string
	Mobile   string
	Password string
	State    int `gorm:"default:1;type:tinyint"`
}

type UploadTmp struct {
	Model

	Url string `gorm:"size:1000"`
}

// 系统配置
type SysConfig struct {
	Model

	Key       string                      `gorm:"uniqueIndex"`
	Value     string                      `gorm:"type:text"`
	ValueType constmap.SysConfigValueType `gorm:"type:tinyint;default:1"`
	Desc      string                      `gorm:"comment:配置描述"`
}
