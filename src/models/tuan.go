package models

import (
	"time"
)

type Tuan struct {
	Model

	Name          string
	State         int    `gorm:"default:2"`
	Pic           string `gorm:"size:1000"`
	Days          int    //行程天数
	ShortDesc     string `gorm:"size:2000"`
	ZoneId        uint
	Cate          string `gorm:"not null;default '';comment:团游类型(多个)"`
	Price         int    //起始价
	AgeLimit      string //年龄限制
	MemberLimit   string //人数限制
	Base          string //集散/解散地
	Customization string //定制化
	NeedPeople    int    `gorm:"default:1"` //是否需要填写出行人
	CanRefund     int    `gorm:"type:tinyint;comment:是否支持退款"`
	RefundRules   string `gorm:"size:2000"`

	Pics  []TuanPic `gorm:"foreignKey:ProductId"`
	Desc  TuanDesc  `gorm:"foreignKey:ProductId"`
	Nodes []TuanTripNode
	Skus  []TuanSku
	Dates []TuanDate `gorm:"foreignKey:ProductId"`
	Zone  Zone
}

// 行程安排
type TuanTripNode struct {
	Model

	TuanId    uint
	Name      string
	ShortDesc string `gorm:"size:2000"`
	Content   string `gorm:"type:text"`
}

// 团游类型
type TuanCate struct {
	Model

	Name string
}

type TuanDesc struct {
	Model

	ProductId uint
	Content   string `gorm:"type:text"`
}

type TuanPic struct {
	Model

	ProductId uint
	Url       string
}

type TuanDateStock struct {
	Model

	ProductId  uint      `gorm:"uniqueIndex:date_stock"`
	Date       time.Time `gorm:"type:date;uniqueIndex:date_stock"`
	TotalStock int
	SaleStock  int //己售库存
}

type TuanDate struct {
	Model

	ProductId uint      `gorm:"index"`
	Date      time.Time `gorm:"type:date"` //【废弃】
	Start     time.Time `gorm:"type:date"`
	End       time.Time `gorm:"type:date"`
	Weeks     string    //星期值，1-7，多个逗号分隔
	Stock     int       //每日库存

	Skus []TuanDateSku
}

type TuanDateSku struct {
	Model

	ProductId       uint `gorm:"index"`
	TuanDateId      uint
	SkuId           uint `gorm:"index"`
	Price           int
	SettlementPrice int
}

type TuanSku struct {
	Model

	TuanId          uint
	Price           int
	SettlementPrice int
	Name            string
	IsRequire       int `gorm:"default:2;type:tinyint;comment:是否是必选"`
	AdultNum        int `gorm:"type:tinyint;comment:成人数量"`
	ChildrenNum     int `gorm:"type:tinyint;comment:儿童数量"`
}
