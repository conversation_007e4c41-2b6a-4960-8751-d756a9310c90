package models

type Scenic struct {
	Model

	ZoneId         uint
	Name           string
	Lng            float64
	Lat            float64
	Address        string
	Tel            string
	CostText       string
	Pic            string
	Level          int    `gorm:"type:tinyint"`
	Llm            int    `gorm:"type:tinyint;default:2;comment:是否参与模型推荐"`
	State          int    `gorm:"type:tinyint;default:1"`
	Tags           string `gorm:"size:500;not null;default:'';comment:标签列表"`
	Cate           string `gorm:"size:500;not null;default:'';comment:分类列表"`
	Altitude       string `gorm:"size:100;not null;default:'';comment:海拔"`
	OpenTime       string `gorm:"size:1000;not null;default:'';comment:开放时间"` //4月1日-10月31日 08:30-17:00 16:00停止检票;11月1日-次年3月31日 08:30-16:30 15:30停止检票
	SuggestHour    string `gorm:"size:100;not null;default:'';comment:建议游玩时间"`
	IsFree         int    `gorm:"type:tinyint"` //是否免费
	IsBooking      int    `gorm:"type:tinyint"` //需要预约
	ScenicPrices   string `gorm:"size:2048"`
	PlayCosts      string //游玩时长
	BestPlayMonths string //最佳游玩月份 1,2,...,12
	Desc           string `gorm:"type:text;comment:详细描述"`

	Pics []ScenicPic
	Zone Zone
	Ext  ScenicExt
}

type ScenicExt struct {
	Model

	ScenicId     uint
	PlayTutorial string `gorm:"type:text"` //游玩攻略
	WarningNotes string `gorm:"type:text"` //注意事项
}

type ScenicPic struct {
	Model

	ScenicId uint
	Url      string
	Title    string `json:"text"`
	Main     int    `gorm:"default:2;comment:是否是主图"`
}

type ScenicTag struct {
	Model

	Value string `gorm:"index"`
}

// 景点类型
type ScenicCate struct {
	Model

	Value string `gorm:"index"`
}

type ScenicOta struct {
	Model

	ScenicId uint   `gorm:"index"`
	OtaId    string //otaid
	OtaCode  string //ota代号
}

type ScenicType struct {
	Model

	ScenicId uint
	TypeCode string
}

// 自我游景点列表
type ScenicOtaZwy struct {
	Model

	ViewId     string `gorm:"uniqueIndex;size:50;not null;comment:景点编号"`
	ViewName   string `gorm:"comment:景点名称"`
	MatchState int    `gorm:"type:tinyint;default:2;comment:匹配状态"`
	Reason     string `gorm:"size:1000;not null;default:'';comment:未匹配原因"`
	Raw        string `gorm:"type:text;comment:景点信息"`
}

func (o ScenicOtaZwy) TableName() string {
	return "wifi.scenic_ota_zwy"
}
