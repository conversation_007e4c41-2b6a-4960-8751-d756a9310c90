package models

import (
	"roadtrip-api/src/constmap"
	"time"
)

// 资金账户
type UserAccount struct {
	Model

	UserId     uint                  `gorm:"uniqueIndex:uacc"`
	Currency   constmap.CurrencyType `gorm:"uniqueIndex:uacc"` //币种
	Amount     int                   `gorm:"default:0"`        //余额
	LockAmount int                   `gorm:"default:0"`        //冻结额
	Version    uint64                //乐观锁版本号
}

// 账户流水明细
type UserAccountLog struct {
	Model

	CreatedAt        time.Time `gorm:"index"`
	UserAccountId    uint      `gorm:"index"`
	Type             constmap.AccountLog
	SubType          constmap.AccountLogSub
	ChangeAmount     int //变动金额
	BeforeAmount     int
	AfterAmount      int
	BeforeLockAmount int
	AfterLockAmount  int
	Remark           string
	Extra            string `gorm:"type:text"`
}

// 账户交易流水
// 记录平台每一笔充值、消费、退款等交易
// 支持第三方支付号（如微信支付交易号）
type AccountTransaction struct {
	Model

	UserId            uint `gorm:"index"` // 用户ID
	UserAccountId     uint `gorm:"index"` // 账户ID
	Currency          constmap.CurrencyType
	TransactionNo     string `gorm:"unique"`    // 平台交易号
	Amount            int    `gorm:"default:0"` // 交易金额
	ReversedAmount    int    `gorm:"default:0"` // 已回退金额
	AccountLogType    constmap.AccountLog
	AccountLogSubType constmap.AccountLogSub
	State             constmap.TransactionState
	Remark            string // 备注
	Version           uint64 //乐观锁版本号
}

// 账户交易回退记录
// 支持一笔交易分批次回退
// 每次回退单独记录
type AccountTransactionReversal struct {
	Model

	AccountTransactionId uint                      `gorm:"index"`     // 关联交易流水主键ID
	AccountTransactionNo string                    `gorm:"index"`     // 关联交易流水号
	BatchNo              string                    `gorm:"unique"`    // 回退批次号
	Amount               int                       `gorm:"default:0"` // 本次回退金额
	State                constmap.TransactionState // 状态（成功、失败、处理中）
	Remark               string                    // 备注
	Version              uint64                    //乐观锁版本号
}
