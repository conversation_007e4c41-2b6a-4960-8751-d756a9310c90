package models

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

type Plan struct {
	Model

	From              string
	To                string
	BgPic             string `gorm:"comment:主图"`
	ContactName       string
	ContactTel        string
	Likes             int `gorm:"type:int unsigned not null;default:0"`
	Peoples           int
	UserId            uint   `gorm:"index"`
	CostDay           int    `gorm:"column:cost_day;type:int unsigned not null;default:0;comment:行程天数"`  //行程天数
	FitFor            string `gorm:"column:fit_for;type:varchar(32) not null;default:'';comment:适合季节月份"` //适合季节月份
	FitMonths         string //1,2,...,12
	SceneCnt          int    `gorm:"column:scene_cnt;type:int unsigned not null;default:0;comment:景点数"`                      //景点数
	AiReqid           string `gorm:"column:ai_reqid;index:idx_plan_ai;type:varchar(128) not null;default:'';comment:AI请求ID"` //AI请求ID
	AiProvider        string `gorm:"column:ai_provider;type:varchar(16) not null;default:'doubao';comment:AI模型类型"`           //AI模型类型
	Subject           string `gorm:"column:subject;type:varchar(255) not null;default:'';comment:标题"`                        //标题
	Subtitle          string `gorm:"column:subtitle;type:varchar(255) not null;default:'';comment:子标题"`                      //子标题
	Notice            string `gorm:"column:notice;type:varchar(2048) not null;default:'';comment:注意事项"`
	State             int    `gorm:"type:tinyint;default:1;comment:状态 1正常 2删除"`
	IntegralCostState int    `gorm:"type:tinyint;default:1"` //是否已扣费
	IntegralCost      int64  //扣费金额
	Poster            string //行程海报
	WishId            uint   `gorm:"index"` //心愿单id

	Sections []PlanSection `gorm:"foreignKey:PlanId"`
	Ext      PlanExt
}

type PlanLike struct {
	Model

	PlanId uint `gorm:"uniqueIndex:plike"`
	UserId uint `gorm:"uniqueIndex:plike"`
}

type PlanExt struct {
	Model

	PlanId        uint                                        `gorm:"unique"`
	PlanCost      string                                      `gorm:"type:text"` //行程费用
	PromptOptions utils.Marshaller[beans.TravelPromptOptions] `gorm:"type:text"`
	PdfState      constmap.PlanPdfState                       `gorm:"type:tinyint;default:0"`
	PdfUrl        string
	PdfCover      string
	PdfReason     string
	TransactionNo string `gorm:"comment:交易流水号"`
}

// 预算明细
type PlanCostDetail struct {
	Model

	AiReqid     string                                      `gorm:"unique"`
	State       constmap.PlanCostDetailState                `gorm:"type:tinyint"`
	Cost        utils.Marshaller[beans.PlanCost]            `gorm:"type:text"`
	MainTags    string                                      //主标签
	SubTags     string                                      //副标签
	PrimeScenes utils.Marshaller[[]beans.PlanPdfPrimeScene] `gorm:"type:text"` //代表景点
	Cover       string                                      //封面图
}

type PlanHistory struct {
	Model

	UserId     uint   `gorm:"index"`
	AiReqid    string `gorm:"index"` //AI请求ID
	AiProvider string //AI模型类型
	Details    string `gorm:"type:longtext"`
}

// 行程分段详情
type PlanSection struct {
	Model

	PlanId         uint   `gorm:"index"`
	SectionSort    int    `gorm:"column:section_sort;type:int unsigned not null;default:1;comment:正序排序"`     //排序值，正序
	SectionTitle   string `gorm:"column:section_title;type:varchar(255) not null;default:'';comment:标题"`     //段落标题
	SectionSubject string `gorm:"column:section_subject;type:varchar(255) not null;default:'';comment:行程主题"` //行程主题
	SectionDesc    string `gorm:"column:section_desc;type:varchar(2048) not null;default:'';comment:描述"`     //段落描述【废弃】
	Content        string `gorm:"column:content;type:text;comment:行程内容"`                                     //beans.PlanSectionContent
}

func (o PlanSection) TableName() string {
	return "wifi.plan_section"
}
