@mixin center($direction: row) {
  display: flex;
  flex-direction: $direction;
  align-items: center;
  justify-content: center;
}

@mixin h3 {
  font-size: $h3-v2;
  font-weight: 500;
}

@mixin h1 {
  font-size: $h1-v2;
  font-weight: bold;
}

@mixin ellipse($line: 1) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $line >1 {
    display: flex;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    flex-direction: column;
    justify-content: center;
    line-clamp: $line;
  }

  @else {
    white-space: nowrap;
  }
}