/* 
  Reset style sheet
*/

html,
body {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body,
#app {
  height: 100%;
  width: 100%;
}

/* flex */
.flx-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flx-justify-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flx-align-center {
  display: flex;
  align-items: center;
}

.flx-row {
  display: flex;
  flex-flow: row;
  align-items: center;
}

.flx-row-right {
  display: flex;
  flex-flow: row-reverse;
  align-items: center;
}

.col-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
}

.ml10 {
  margin-left: 10px;
}

.p10 {
  padding: 10px;
}

.p20 {
  padding: 20px;
}

.m10 {
  margin: 10px;
}

.m20 {
  margin: 20px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.w100 {
  width: 100%;
}

.ml20 {
  margin-left: 20px;
}

.mR10 {
  margin-right: 10px;
}
.flx1{
  flex:1;
}
.one-cut-txt {
  /*强制文字在一行文本框内*/
  white-space: nowrap;
  /*溢出部分文字隐藏*/
  overflow: hidden;
  /*溢出部分省略号处理*/
  text-overflow: ellipsis;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: block;
  height: 0px;
  clear: both;
  overflow: hidden;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.2s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.2s;
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.2s;
}

/* Breadcrumb */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.2s ease;
}

.breadcrumb-enter-from,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(10px);
}

.breadcrumb-leave-active {
  position: absolute;
  z-index: -1;
}

/* scroll bar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: white;
}

::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0px white;
  -webkit-box-shadow: inset 0 0 0px white;
  background-color: #dddee0;
  border-radius: 20px;
}

.content-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  font-size: 23px;
  font-weight: bold;
  color: rgb(88, 88, 88);
  line-height: 100px;
  min-height: 400px;
}
