package config

type MailConf struct {
	Host     string
	Port     int
	User     string
	Password string
	Name     string //可选，发件人名称
}

type AppConfig struct {
	App struct {
		Env          string
		Port         int
		Debug        bool
		PayDebug     bool   `toml:"pay_debug"`
		UploadTmpDir string `toml:"upload_tmp_dir"`
		UploadDir    string `toml:"upload_dir"`
		TmpDir       string `toml:"tmp_dir"`
		StaticHost   string `toml:"static_host"`
		FrontHost    string `toml:"front_host"`
		FontDir      string `toml:"font_dir"`
		DefaultFont  string `toml:"default_font"`
		EmojiFont    string `toml:"emoji_font"`
		Log          struct {
			LogStdout bool   `toml:"log_stdout"` //日志输出到终端
			LogPath   string `toml:"log_path"`   //日志输出到文件路径
		}
	}
	Db struct {
		Host     string
		Port     int
		Username string
		Password string
		Name     string
		Charset  string
	}
	Redis struct {
		Host string
		Db   int
	}
	Wechat struct {
		AppId  string `toml:"appid"`
		Secret string
	}
	WechatOfficial struct {
		AppId  string `toml:"appid"`
		Secret string
	} `toml:"wechat_official"`
	Amap struct {
		Key    string
		Secret string
	}
	WechatPay struct {
		Mchid       string
		SerialNo    string `toml:"serial_no"`
		CertPath    string `toml:"cert_path"`
		KeyPath     string `toml:"key_path"`
		MchApiV2Key string `toml:"mch_api_v2_key"`
		MchApiV3Key string `toml:"mch_api_v3_key"`
		NotifyUrl   string `toml:"notify_url"`
	} `toml:"wechat_pay"`
	Doubao struct {
		ApiKey     string `toml:"api_key"`
		Model      string
		Prompt     string
		Output     string
		CtxPrompt  string `toml:"ctx_prompt"`
		UsysPrompt string `toml:"usys_prompt"`
	}
	Es struct {
		Host          string
		Username      string
		Password      string
		StopwordsPath string `toml:"stopwords_path"`
	}
	Moonshot struct {
		ApiKey string `toml:"api_key"`
	}
	Ip struct {
		Dat string
	}
	Ota struct {
		Zwy struct { //自我游
			Host    string
			AgentId int64  `toml:"agent_id"` //分销商ID（由自我游商户提供）
			ApiKey  string `toml:"api_key"`  //由自我游商户提供
		}
		Ceekee struct { //思客
			Host      string
			Version   string
			AccessKey string `toml:"access_key"`
			PartnerId string `toml:"partner_id"`
			MemberId  string `toml:"member_id"`
			PriceRate int64  `toml:"price_rate"`
		}
		Tongcheng struct { //同程
			Host    string
			ApiKey  string `toml:"api_key"`
			AgentId string `toml:"agent_id"` //分销商ID（由同程商户提供）
		}
	}
	Mail struct {
		Conf *MailConf
		To   []string
	}
	QiyeWeixin struct {
		Robot struct {
			Webhook string
		}
	} `toml:"qiye_weixin"`
	JinaAi struct {
		ApiKey string `toml:"api_key"`
	} `toml:"jina_ai"`
	Dify struct {
		Uri   string
		YjKey string `toml:"yj_key"`
	}
	Qiniu struct {
		AccessKey string `toml:"access_key"`
		SecretKey string `toml:"secret_key"`
		Bucket    string
	}
	BaiduRisk struct {
		ApiKey    string `toml:"api_key"`
		ApiSecret string `toml:"api_secret"`
	} `toml:"baidu_risk"`
}
