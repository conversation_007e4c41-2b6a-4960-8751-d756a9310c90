package config

import (
	"github.com/BurntSushi/toml"
	"os"
)

var Config AppConfig

func InitConfig(file string) {
	var err error
	if _, err = os.Stat(file); err != nil {
		panic(err)
	}

	b, _ := os.ReadFile(file)
	_, err = toml.Decode(string(b), &Config)
	if err != nil {
		panic(err)
	}
}

func IsDebug() bool {
	return Config.App.Debug
}

func IsProduction() bool {
	return Config.App.Env == "" || Config.App.Env == "prod"
}
