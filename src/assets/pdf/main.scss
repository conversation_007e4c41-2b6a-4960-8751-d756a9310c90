* {
  padding: 0;
  margin: 0;
  /* 中文通用字体组合 */
  font-family: "Microsoft Yahei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Zen Hei", sans-serif;
}

body {
  color: #333;
  max-width: 790px;
  position: relative;
  margin: 0 auto;
}

.iconfont, body {
  font-size: 15px;
}

.iconfont {
  margin-right: 4px;
}

ul, ul li {
  list-style: none;
}

ul li {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

img {
  width: 100%;
  object-fit: contain;
}

.bg-title {
  position: relative;
  display: inline-block;
  font-weight: bold;

  &:after {
    content: " ";
    width: 100%;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 35%;
    background: #FACA14;
    z-index: -1;
    border-top-right-radius: 20px;
    border-top-left-radius: 20px;
  }
}

.content {
  padding: 41px 25px;
}

.panel {
  margin-bottom: 41px;

  .panel-title {
    color: #1890FF;
    font-weight: bold;
    margin-bottom: 25px;
    display: flex;
    gap: 4px;
    align-items: center;
    font-size: 20px;

    > div {
      display: flex;

      span {
        display: block;
        width: 7px;
        background: #52C41A;
        height: 28px;

        &:first-child {
          background: #1890FF;
        }
      }
    }
  }
}

.notice {
  .iconfont {
    color: #fda132;
  }
}

@import "summary";
@import "prime-zone";
@import "sections";
@import "zones";
@import "footer";
@import "budget";
