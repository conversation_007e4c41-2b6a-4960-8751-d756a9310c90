@charset "UTF-8";
* {
  padding: 0;
  margin: 0;
  /* 中文通用字体组合 */
  font-family: "Microsoft Yahei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", "WenQuanYi Zen Hei", sans-serif;
}

body {
  color: #333;
  max-width: 790px;
  position: relative;
  margin: 0 auto;
}

.iconfont, body {
  font-size: 15px;
}

.iconfont {
  margin-right: 4px;
}

ul, ul li {
  list-style: none;
}

ul li {
  margin-bottom: 8px;
}
ul li:last-child {
  margin-bottom: 0;
}

img {
  width: 100%;
  object-fit: contain;
}

.bg-title {
  position: relative;
  display: inline-block;
  font-weight: bold;
}
.bg-title:after {
  content: " ";
  width: 100%;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 35%;
  background: #FACA14;
  z-index: -1;
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
}

.content {
  padding: 41px 25px;
}

.panel {
  margin-bottom: 41px;
}
.panel .panel-title {
  color: #1890FF;
  font-weight: bold;
  margin-bottom: 25px;
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 20px;
}
.panel .panel-title > div {
  display: flex;
}
.panel .panel-title > div span {
  display: block;
  width: 7px;
  background: #52C41A;
  height: 28px;
}
.panel .panel-title > div span:first-child {
  background: #1890FF;
}

.notice .iconfont {
  color: #fda132;
}

.top {
  position: relative;
  height: 444px;
}
.top img {
  z-index: -1;
  width: 100%;
  max-height: 444px;
  object-fit: fill;
}
.top .summary {
  background: rgba(8, 105, 155, 0.2);
  backdrop-filter: blur(10px);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 41px;
  font-size: 20px;
}
.top .summary > div {
  display: flex;
}
.top .summary .subject {
  font-size: 41px;
}
.top .summary .line {
  align-items: center;
  gap: 8px;
}
.top .summary .line > div {
  display: flex;
  align-items: center;
}

.map {
  margin-top: 15px;
}

.prime-zone .descriptions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.prime-zone .descriptions .t {
  margin-bottom: 8px;
  font-weight: bold;
}
.prime-zone .prime-scene {
  margin-top: 16px;
}
.prime-zone .prime-scene > div:first-child {
  margin-bottom: 8px;
  font-weight: bold;
}
.prime-zone .prime-scene .scene {
  margin-bottom: 16px;
  display: flex;
  gap: 16px;
  align-items: stretch;
}
.prime-zone .prime-scene .scene:last-child {
  margin-bottom: 0;
}
.prime-zone .prime-scene .scene .img {
  width: 329px;
  max-height: 185px;
}
.prime-zone .prime-scene .scene .img img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}
.prime-zone .prime-scene .scene .right {
  font-size: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.prime-zone .prime-scene .scene .right .scene-name {
  font-weight: bold;
  margin-bottom: 8px;
}
.prime-zone .prime-scene .scene .right .tips {
  display: flex;
  flex-direction: column;
}
.prime-zone .prime-scene .scene .right .tips > div {
  display: flex;
}
.prime-zone .prime-scene .scene .right .tips > div .t {
  font-weight: bold;
}

.sections {
  display: flex;
  flex-direction: column;
  gap: 13.2px;
}
.sections .section-title {
  font-weight: bold;
  font-size: 25px;
  margin-bottom: 25px;
}
.sections .hotel {
  display: flex;
  gap: 17px;
}
.sections .hotel .hotel-pic {
  width: 329px;
  height: 185px;
  overflow: hidden;
}
.sections .hotel .hotel-pic img {
  width: 100%;
  object-fit: fill;
}
.sections .hotel .right {
  width: 371px;
}
.sections .hotel .right .title, .sections .hotel .right .iconfont {
  color: #1890FF;
}
.sections .hotel .right .title {
  margin-bottom: 8px;
}
.sections .hotel .right .title span:last-child {
  font-size: 20px;
  font-weight: bold;
}
.sections .hotel .right .desc {
  margin-bottom: 16px;
}
.sections .hotel .right .last {
  display: flex;
  gap: 8px;
}
.sections .timeline-item .pics {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  margin-bottom: 17.6px;
}
.sections .timeline-item .pics div {
  width: 244px;
  max-height: 104px;
  overflow: hidden;
}
.sections .timeline-item .pics div img {
  width: 100%;
  object-fit: fill;
}
.sections .timeline-item .title {
  display: flex;
  color: #1890FF;
  margin-top: 16px;
  align-items: center;
}
.sections .timeline-item .desc {
  margin: 8px 0;
}
.sections .timeline-item .line {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}
.sections .timeline-item .line .iconfont {
  color: #1890FF;
}
.sections .timeline-item .tags {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
}
.sections .timeline-item .tags span {
  border-radius: 38px;
  padding: 5px 16px;
}
.sections .timeline-item .tags span:nth-child(3n+1) {
  background: #E7F4FF;
  color: #1890FF;
}
.sections .timeline-item .tags span:nth-child(3n+2) {
  background: #FFF2C1;
  color: #694209;
}
.sections .timeline-item .tags span:nth-child(3n+3) {
  background: #F5DFFF;
  color: #690969;
}
.sections .timeline-item .warning, .sections .timeline-item .best-capture > div:last-child {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 16px 0;
}
.sections .timeline-item .warning div .iconfont {
  color: #FACA14;
  font-weight: bold;
}
.sections .timeline-item .warning div:first-child {
  display: flex;
  align-items: center;
}
.sections .timeline-item .best-capture > div:last-child {
  flex-wrap: wrap;
}
.sections .timeline-item .best-capture .capture-item .pic {
  height: 155px;
  width: 362px;
  overflow: hidden;
}
.sections .timeline-item .best-capture .capture-item .poi-name {
  margin-top: 8px;
  font-weight: bold;
}
.sections .timeline-item .best-capture .capture-item .best-capture-time {
  font-size: 10px;
}
.sections .timeline-item .building-group-list {
  margin-top: 16px;
}
.sections .timeline-item .building-group-list ul {
  display: flex;
  justify-content: flex-start;
  gap: 41px;
  margin-top: 8px;
}
.sections .timeline-item .building-group-list ul li {
  list-style: disc;
  margin-left: 18px;
}

.zones-card {
  margin-top: 41px;
}
.zones-card .special {
  margin-top: 25px;
}
.zones-card .list {
  display: flex;
  gap: 24px;
  margin-top: 8px;
  flex-wrap: wrap;
}
.zones-card .list > div {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 8px;
}
.zones-card .list > div .iconfont {
  color: #1890FF;
  margin-right: 4px;
}
.zones-card .list > div .pic {
  width: 230px;
  height: 130px;
  overflow: hidden;
}
.zones-card .list > div .pic img {
  width: 100%;
  object-fit: fill;
}

.footer {
  position: relative;
  page-break-inside: avoid;
}
.footer .f-c {
  margin-bottom: 83px;
  padding: 37px 41px;
  display: flex;
  justify-content: space-between;
  font-size: 20px;
  background: linear-gradient(180deg, #A5EAF9 0%, #EEF9E9 100%), linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 250, 228, 0.68) 100%);
}
.footer .f-c .left {
  width: 232px;
}
.footer .f-c .right {
  display: flex;
  font-size: 20px;
  align-items: stretch;
}
.footer .f-c .right .qrcode {
  width: 90px;
}
.footer .ren {
  width: 154px;
  position: absolute;
  left: 284px;
  bottom: -41px;
}

.budget-card .panel-content {
  display: flex;
  justify-content: space-between;
  gap: 80px;
}
.budget-card .panel-content .left {
  flex: 1;
}
.budget-card .panel-content .left .summary {
  margin-bottom: 170px;
}
.budget-card .panel-content .left .summary .info {
  display: flex;
  justify-content: space-between;
}
.budget-card .panel-content .left .summary .info > div:last-child {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.budget-card .panel-content .left .summary .info > div .day, .budget-card .panel-content .left .summary .info > div .price {
  font-size: 20px;
  font-weight: bold;
}
.budget-card .panel-content .left .summary .info > div .price {
  color: #1890FF;
}
.budget-card .panel-content .right {
  flex: 1;
}
.budget-card .panel-content .right .bg-title {
  margin-bottom: 8px;
}
.budget-card .panel-content .list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.budget-card .panel-content .list .item {
  display: flex;
}
.budget-card .panel-content .list .item .iconfont {
  color: white;
  background: #1890FF;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}
.budget-card .panel-content .list .item .item-data {
  flex: 1;
}
.budget-card .panel-content .list .item .item-data .title {
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}
.budget-card .panel-content .list .item .item-data .title .price {
  color: #1890FF;
  font-weight: normal;
}
.budget-card .panel-content .list .item .item-data .sub-item div {
  display: flex;
  align-items: center;
  justify-content: center;
  justify-content: space-between;
}
.budget-card .panel-content .list .item .item-data .desc {
  font-size: 12px;
  color: #999999;
}

/*# sourceMappingURL=main.css.map */
