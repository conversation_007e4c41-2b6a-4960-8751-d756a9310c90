.footer {
  position: relative;
  page-break-inside: avoid;

  .f-c {
    margin-bottom: 83px;
    padding: 37px 41px;
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    background: linear-gradient(180deg, #A5EAF9 0%, #EEF9E9 100%), linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 250, 228, 0.68) 100%);

    .left {
      width: 232px;
    }

    .right {
      display: flex;
      font-size: 20px;
      align-items: stretch;

      .qrcode {
        width: 90px;
      }

    }
  }

  .ren {
    width: 154px;
    //height: calc(600px * 0.44);
    position: absolute;
    left: 284px;
    bottom: -41px;
  }
}
