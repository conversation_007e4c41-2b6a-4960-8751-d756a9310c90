@mixin center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.budget-card {
  .panel-content {
    display: flex;
    justify-content: space-between;
    gap: 80px;

    .left {
      flex: 1;

      .summary {
        margin-bottom: 170px;

        .info {
          display: flex;
          justify-content: space-between;

          > div {
            &:last-child {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
            }

            .day, .price {
              font-size: 20px;
              font-weight: bold;
            }

            .price {
              color: #1890FF;
            }
          }
        }
      }
    }

    .right {
      flex: 1;

      .bg-title {
        margin-bottom: 8px;
      }
    }

    .list {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .item {
        display: flex;

        .iconfont {
          color: white;
          background: #1890FF;
          width: 30px;
          height: 30px;
          border-radius: 50%;
          @include center();
          font-size: 20px;
        }

        .item-data {
          flex: 1;

          .title {
            font-weight: bold;
            display: flex;
            justify-content: space-between;

            .price {
              color: #1890FF;
              font-weight: normal;
            }
          }

          .sub-item {
            div {
              @include center();
              justify-content: space-between;
            }
          }

          .desc {
            font-size: 12px;
            color: #999999;
          }
        }

      }
    }
  }
}
