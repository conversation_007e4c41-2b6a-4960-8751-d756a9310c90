<!DOCTYPE html>
<html lang="zh_CN">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <link href="http://h5.funfuntrip.cn/pdf/iconfont/iconfont.css?v=1" rel="stylesheet" type="text/css"/>
    <style type="text/css">{{.CssStyle}}</style>
</head>
<body>
<div class="main">
    {{template "summary" .}}
    <div class="content">
        <div class="panel notice">
            <div class="panel-title">
                <div>
                    <span></span>
                    <span></span>
                </div>
                出行注意事项
            </div>
            <div class="panel-content">
                <ul>
                    {{ range .Attentions }}
                        <li>{{.}}</li>
                    {{ end }}
                </ul>
            </div>
        </div>

        <div class="panel prime-zone">
            <div class="panel-title">
                <div>
                    <span></span>
                    <span></span>
                </div>
                目的地介绍
            </div>
            <div class="panel-content">
                <div class="descriptions">
                    <div>
                        <div class="t"><i class="iconfont icon-weizhi"></i>地理位置</div>
                        <div>{{ .PrimeZone.ZoneDesc }}</div>
                    </div>
                    <div>
                        <div class="t"><i class="iconfont icon-nongyun"></i>气候特点</div>
                        <div>{{ .PrimeZone.Climate }}</div>
                    </div>
                    <div>
                        <div class="t"><i class="iconfont icon-wenhua"></i>文化背景</div>
                        <div>{{ .PrimeZone.CultureBackground }}</div>
                    </div>
                </div>
                {{template "prime_scene" .}}
            </div>
        </div>

        {{ template "sections" .Sections }}

        {{template "zones" .}}
        {{template "budget" .}}
    </div>
    {{ template "footer" . }}
</div>

<script>
  function resize() {
    let scale = window.innerWidth / 1920;
    document.body.style.transform = `scale(${scale})`;
    document.body.style.transformOrigin = '0 0';
    document.body.style.width = `${100 / scale}%`;
    document.body.style.height = `${100 / scale}%`;
  }

  //
  // window.onload = resize;
  // window.onresize = resize;
</script>
</body>
</html>
