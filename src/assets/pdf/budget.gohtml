{{define "budget"}}
    {{if .CostDetail}}
        <div class="panel budget-card">
            <div class="panel-title">
                <div>
                    <span></span>
                    <span></span>
                </div>
                旅行AI预算预估
            </div>
            <div class="panel-content">
                <div class="left">
                    <div class="summary">
                        <div class="bg-title">总费用</div>
                        <div class="info">
                            <div>
                                <div class="price">{{format_money .CostDetail.Total}}</div>
                                预估总预算/人
                            </div>
                            <div>
                                <div class="day">{{.DayNum}}天</div>
                                <div>总天数</div>
                            </div>
                        </div>

                    </div>
                    <img src="{{.ChartCostImage}}">
                </div>
                <div class="right">
                    <div class="bg-title">费用明细</div>
                    <div class="list">
                        <div class="item">
                            <i class="iconfont icon-zhusu"></i>
                            <div class="item-data">
                                <div class="title">住宿费用<span class="price">{{format_money .CostDetail.Hotel}}</span>
                                </div>
                                <div class="desc">标准间x{{sub .DayNum 1}}晚</div>
                            </div>
                        </div>
                        <div class="item">
                            <i class="iconfont icon-qiche"></i>
                            <div class="item-data">
                                <div class="title">交通费用<span
                                            class="price">{{format_money (add .CostDetail.Highway .CostDetail.Fuel)}}</span>
                                </div>
                                <div class="sub-item">
                                    <div>
                                        油费<span class="price">{{format_money .CostDetail.Fuel}}</span>
                                    </div>
                                    <div class="desc">汽油7.9L/100KM（约{{divide .CostDetail.Distance 100}}KM）</div>
                                </div>
                                <div class="sub-item">
                                    <div>
                                        高速费用<span class="price">{{format_money .CostDetail.Highway}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="item">
                            <i class="iconfont icon-zhusu"></i>
                            <div class="item-data">
                                <div class="title">餐饮费用<span class="price">{{format_money .CostDetail.Food}}</span>
                                </div>
                                <div class="desc">{{.DayNum}}天x3餐x1人</div>
                            </div>
                        </div>
                        {{if .CostDetail.Scenes}}
                            <div class="item">
                                <i class="iconfont icon-ancient-gate-fill"></i>
                                <div class="item-data">
                                    <div class="title">景点门票<span
                                                class="price">{{format_money .CostDetail.Ticket}}</span>
                                    </div>
                                    {{range .CostDetail.Scenes}}
                                        <div class="sub-item">
                                            <div>
                                                {{.Name}}

                                                {{if .IsFree}}
                                                    <span>免费</span>
                                                {{else}}
                                                    {{if gt .Fee 0}}
                                                        <span class="price">{{format_money .Fee}}
                                                </span>
                                                    {{else}}
                                                        <span>-</span>
                                                    {{end}}
                                                {{end}}

                                            </div>
                                        </div>
                                    {{end}}

                                </div>
                            </div>
                        {{end}}
                        {{if .CostDetail.Specialities}}
                            <div class="item">
                                <i class="iconfont icon-gengduo1"></i>
                                <div class="item-data">
                                    <div class="title">其他<span
                                                class="price">{{format_money .CostTotalSpecialities}}</span>
                                    </div>
                                    {{range .CostDetail.Specialities}}
                                        <div class="sub-item">
                                            <div>
                                                {{.Name}}
                                                {{if gt .Fee 0}}
                                                    <span class="price">{{format_money .Fee}}
                                                </span>
                                                {{else}}
                                                    <span>-</span>
                                                {{end}}
                                            </div>
                                        </div>
                                    {{end}}

                                </div>
                            </div>
                        {{end}}

                    </div>
                </div>
            </div>
        </div>
    {{end}}
{{end}}
