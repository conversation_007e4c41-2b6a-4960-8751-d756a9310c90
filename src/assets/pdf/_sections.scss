.sections {
  display: flex;
  flex-direction: column;
  gap: calc(30px * 0.44);

  .section-title {
    font-weight: bold;
    font-size: 25px;
    margin-bottom: 25px;
  }

  .hotel {
    display: flex;
    gap: 17px;

    .hotel-pic {
      width: 329px;
      height: 185px;
      overflow: hidden;

      img {
        width: 100%;
        object-fit: fill;
      }
    }

    .right {
      width: 371px;

      .title, .iconfont {
        color: #1890FF;
      }

      .title {
        margin-bottom: 8px;

        span {
          &:last-child {
            font-size: 20px;
            font-weight: bold;
          }
        }
      }

      .desc {
        margin-bottom: 16px;
      }

      .last {
        display: flex;
        gap: 8px;
      }
    }
  }

  .timeline-item {
    .pics {
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      margin-bottom: calc(40px * 0.44);

      div {
        width: 244px;
        max-height: 104px;
        overflow: hidden;

        img {
          width: 100%;
          object-fit: fill;
        }
      }
    }

    .title {
      display: flex;
      color: #1890FF;
      margin-top: 16px;
      align-items: center;
    }

    .desc {
      margin: 8px 0;
    }

    .line {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;

      .iconfont {
        color: #1890FF;
      }
    }

    .tags {
      display: flex;
      gap: 16px;
      margin-bottom: 8px;

      span {
        border-radius: 38px;
        padding: 5px 16px;

        &:nth-child(3n+1) {
          background: #E7F4FF;
          color: #1890FF;
        }

        &:nth-child(3n+2) {
          background: #FFF2C1;
          color: #694209;
        }

        &:nth-child(3n+3) {
          background: #F5DFFF;
          color: #690969;
        }
      }
    }

    .warning, .best-capture > div:last-child {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin: 16px 0;
    }

    .warning {
      div {
        .iconfont {
          color: #FACA14;
          font-weight: bold;
        }

        &:first-child {
          display: flex;
          align-items: center;
        }
      }
    }

    .best-capture {
      > div {
        &:last-child {
          flex-wrap: wrap;
        }
      }

      .capture-item {
        .pic {
          height: 155px;
          width: 362px;
          overflow: hidden;
        }

        .poi-name {
          margin-top: 8px;
          font-weight: bold;
        }

        .best-capture-time {
          font-size: 10px;
        }
      }
    }

    .building-group-list {
      margin-top: 16px;

      ul {
        display: flex;
        justify-content: flex-start;
        gap: 41px;
        margin-top: 8px;

        li {
          list-style: disc;
          margin-left: 18px;
        }

      }
    }
  }
}
