<template>
  <el-select v-model="value" clearable>
    <el-option v-for="item in list" :key="item.id"
               :label="`${item.name}`" :value="item.id"/>
  </el-select>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {advertisers} from "../api/modules/advertiser";

const props = defineProps({
  modelValue: [Number, String],
  state: Number,
})
const emit = defineEmits(['update:modelValue'])
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const list = ref([])

onMounted(() => {
  advertisers({page_size: 200}).then(res => {
    const {data} = res
    list.value = data.list
  })
})

</script>
