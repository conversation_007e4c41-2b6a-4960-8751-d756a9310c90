<template>
  <div :class="{ 'is-dragging': isDragging }" class="file-upload">
    <!-- 上传区域 -->
    <div
        ref="uploadRef"
        :class="{ 'simple-mode': simple, 'draggable': draggable }"
        class="upload-area"
        @click="handleClick"
        @dragover.stop.prevent="handleDragOver"
        @dragleave.stop.prevent="handleDragLeave"
        @drop.stop.prevent="handleDrop"
    >
      <!-- 自定义内容插槽 -->
      <slot name="content">
        <el-button v-if="simple" type="primary">{{ title }}</el-button>
        <div v-else>
          <div class="upload-icon">
            <i class="el-icon-upload"></i>
          </div>
          <div class="upload-text">
            点击或拖拽文件到此处上传
          </div>
        </div>
      </slot>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
        ref="fileInput"
        :accept="accept"
        class="hidden-input"
        type="file"
        @change="handleFileChange"
    />
  </div>
</template>

<script setup>
import {ref, onMounted, onUnmounted} from 'vue';
import {upload} from '../api/modules/index'
import {MaxImageSize} from "../utils/constmap"; // 直接引入外部封装好的上传函数

const props = defineProps({
  accept: String,
  size: {
    type: [String, Number],
    default: MaxImageSize // 默认大小限制为1MB
  },
  beforeUpload: Function,
  draggable: {
    type: Boolean,
    default: true
  },
  simple: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '上传文件'
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits([
  'success'
]);

// 组件引用
const uploadRef = ref(null);
const fileInput = ref(null);

// 状态
const isDragging = ref(false);

// 处理点击上传区域
const handleClick = () => {
  fileInput.value.click();
};

// 处理文件选择
const handleFileChange = (e) => {
  const file = e.target.files[0];
  if (!file) return;

  processFile(file);
  fileInput.value.value = ''; // 重置输入，允许重复选择同一文件
};

// 处理拖拽相关事件
const handleDragOver = () => {
  if (props.draggable) {
    isDragging.value = true;
  }
};

const handleDragLeave = () => {
  isDragging.value = false;
};

const handleDrop = (e) => {
  if (!props.draggable) return;

  isDragging.value = false;
  const file = e.dataTransfer.files[0];
  if (!file) return;

  processFile(file);
};

// 处理文件上传
const processFile = async (file) => {
  const postData = new FormData();
  postData.append('file', file);
  // 添加 props.data 中的额外参数
  Object.keys(props.data).forEach(key => {
    postData.append(key, props.data[key]);
  });

  // 检查文件大小
  if (file.size > props.size) {
    console.error(`文件大小超过限制 (${props.size / (1024 * 1024)}MB)`);
    return;
  }

  // 执行 beforeUpload 钩子
  if (props.beforeUpload) {
    const beforeResult = props.beforeUpload(file);
    if (beforeResult === false) {
      return;
    }
  }

  try {
    // 调用外部封装好的上传函数
    const {data} = await upload(postData);

    // 合并原始文件信息和上传结果
    const mergedFile = Object.assign({}, {
      file
    }, data);

    // 触发 success 事件，返回合并后的文件对象
    emit('success', mergedFile);
  } catch (error) {
    console.error('文件上传失败:', error);
  }
};

// 组件挂载时设置拖拽事件
onMounted(() => {
  if (props.draggable) {
    // 防止全局拖拽行为
    document.addEventListener('dragover', preventDefault, false);
    document.addEventListener('drop', preventDefault, false);
  }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  if (props.draggable) {
    document.removeEventListener('dragover', preventDefault);
    document.removeEventListener('drop', preventDefault);
  }
});

// 阻止默认拖拽行为
const preventDefault = (e) => {
  e.preventDefault();
  e.stopPropagation();
};
</script>

<style lang="scss" scoped>
$color-primary: #409EFF;
$color-text: #606266;
$color-text-secondary: #909399;
$color-border: #dcdfe6;
$color-background: #f5f7fa;
$border-radius: 4px;
$transition-time: 0.3s;

.file-upload {
  display: inline-block;
  position: relative;

  .upload-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 8px;
  }

  .upload-area {
    border: 1px dashed $color-border;
    border-radius: $border-radius;
    padding: 20px;
    text-align: center;
    transition: all $transition-time;
    cursor: pointer;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &:hover {
      border-color: $color-primary;
    }

    &.draggable {
      border-style: dashed;
    }

    &.is-dragging {
      background-color: $color-background;
      border-color: $color-primary;
    }

    &.simple-mode {
      border: none;
      padding: 0;
      text-align: left;
      min-height: auto;
    }

    .upload-icon {
      font-size: 28px;
      color: #8c939d;
      margin-bottom: 10px;
    }

    .upload-text {
      font-size: 14px;
      color: $color-text;
    }
  }

  .hidden-input {
    display: none;
  }
}
</style>
