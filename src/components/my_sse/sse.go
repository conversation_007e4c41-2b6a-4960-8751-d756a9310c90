package my_sse

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"time"
)

type SSE struct {
	ctx        *gin.Context
	pingTicker *time.Ticker
}

type Event struct {
	Event          string          `json:"event"`
	ConversationId string          `json:"conversation_id"`
	MessageId      string          `json:"message_id"`
	TaskId         string          `json:"task_id"`
	WorkflowRunId  string          `json:"workflow_run_id"`
	Data           json.RawMessage `json:"data"`
}

func NewSSE(ctx *gin.Context) *SSE {
	v := &SSE{
		ctx: ctx,
	}
	// 设置 SSE 响应头
	ctx.Writer.Header().Set("Content-Type", "text/event-stream; charset=utf-8")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")
	ctx.Writer.Header().Set("Access-Control-Allow-Origin", "*") // 允许跨域
	v.pingTicker = time.NewTicker(5 * time.Second)
	go func() {
		for {
			select {
			case <-ctx.Done():
				return
			case <-v.pingTicker.C:
				v.SendPing()
			}
		}
	}()
	return v
}

func (o *SSE) Close() {
	o.pingTicker.Stop()
}

func (o *SSE) Send(e *Event) error {
	b, err := json.Marshal(e)
	if err != nil {
		return err
	}
	err = o.SendBytes(b)
	return err
}

func (o *SSE) SendPing() {
	_, _ = o.ctx.Writer.Write([]byte("event: ping\n\n"))
	o.ctx.Writer.Flush()
}

func (o *SSE) SendBytes(b []byte) error {
	if len(b) == 0 {
		return nil
	}
	if len(b) <= 5 || !bytes.Equal(b[0:5], []byte("data:")) {
		if _, err := o.ctx.Writer.Write([]byte("data:")); err != nil {
			return err
		}
	}
	_, err := o.ctx.Writer.Write(b)
	if err != nil {
		return err
	}
	_, err = o.ctx.Writer.Write([]byte("\n\n"))
	if err != nil {
		return err
	}
	o.ctx.Writer.Flush()
	return err
}
