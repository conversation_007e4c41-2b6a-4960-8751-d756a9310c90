<template>
  <div ref="chartRef" :style="echartsStyle" />
</template>

<script setup >

import 'echarts-liquidfill'
import * as echarts from 'echarts'
import { computed, nextTick, onMounted, onUnmounted, ref, markRaw, watch } from 'vue'

const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  width: [Number, String],
  height: [Number, String],
  theme: [Object, String],
  renderer: {
    type: String,
    default: 'canvas'
  }
})

const echartsStyle = computed(() => {
  return props.width || props.height
    ? { height: props.height + 'px', width: props.width + 'px' }
    : { height: '100%', width: '100%' }
})

const chartRef = ref(null)
const chartInstance = ref(null)

function draw () {
  if (chartInstance.value) {
    chartInstance.value.setOption(props.option, { notMerge: true })
  }
}

function resize () {
  if (chartInstance.value) {
    chartInstance.value.resize({ animation: { duration: 300 } })
  }
}

watch(props, () => {
  draw()
})

function init () {
  if (!chartRef.value) {
    return
  }

  chartInstance.value = echarts.getInstanceByDom(chartRef.value)
  if (!chartInstance.value) {
    chartInstance.value = markRaw(
      echarts.init(chartRef.value, props.theme, {
        renderer: props.renderer
      })
    )

    draw()
  }
}

onMounted(() => {
  window.addEventListener('resize', resize)

  nextTick(() => init())
})

onUnmounted(() => {
  window.removeEventListener('resize', resize)

  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
})

</script>
