<template>
  <el-select
      v-model="value"
      multiple
  >
    <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id"/>
  </el-select>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {roles} from "../api/modules/staff";

const props = defineProps(['modelValue'])
const emit = defineEmits(['update:modelValue'])
const options = ref([])
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const selectProps = {
  multiple: true,
}

onMounted(() => {
  roles({page_size: 100}).then(res => {
    const {data} = res
    options.value = data.list
  })
})

</script>
