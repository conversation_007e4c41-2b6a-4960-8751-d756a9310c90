<script setup>
import {computed} from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  modelValue: Array,
  clearable: <PERSON><PERSON>an,
  isNext: <PERSON><PERSON><PERSON>,
})
const emit = defineEmits(['update:modelValue'])

const shortcuts = [
  {
    text: '最近七天',
    value() {
      const now = dayjs()

      if (props.isNext) {
        return [now.toDate(), now.add(7, 'day').toDate()]
      }

      return [now.add(-7, "day").toDate(), now.toDate()]
    }
  },
  {
    text: '最近半年',
    value() {
      const now = dayjs()

      if (props.isNext) {
        return [now.toDate(), now.add(6, 'month').toDate()]
      }

      return [now.add(-6, "month").toDate(), now.toDate()]
    }
  },
  {
    text: '最近一年',
    value() {
      const now = dayjs()

      if (props.isNext) {
        return [now.toDate(), now.add(1, 'year').toDate()]
      }

      return [now.add(-1, "year").toDate(), now.toDate()]
    }
  }
]

const value2 = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

</script>

<template>
  <el-date-picker
      v-model="value2"
      :clearable="props.clearable"
      :shortcuts="shortcuts"
      type="daterange"/>
</template>

<style lang="scss" scoped>

</style>
