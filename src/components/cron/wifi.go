package cron

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"time"
)

func wifi() {
	_, _ = c.AddFunc("*/1 * * * *", endFlowcard)
	_, _ = c.AddFunc("*/1 * * * *", endFlowCardCode)
}

func endFlowcard() {
	db := models.New()
	now := time.Now()
	var nextId uint

	for {
		var list []models.FlowCard
		db.Where("id>? and state=? and end_time<?", nextId, constmap.Enable, now).
			Order("id asc").
			Limit(100).
			Find(&list)

		if len(list) == 0 {
			break
		}

		for _, card := range list {
			db.Model(&card).Updates(models.FlowCard{State: constmap.Disable})
			nextId = card.ID
		}
	}
}

func endFlowCardCode() {
	db := models.New()
	now := time.Now()
	var nextId uint

	for {
		var list []models.FlowCardCode
		db.Where("id>? and got_state=? and end_at<?", nextId, constmap.GotStateDefault, now).
			Order("id asc").
			Limit(100).
			Find(&list)

		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID

		for _, code := range list {
			db.Model(&code).Updates(models.FlowCardCode{
				State:    constmap.Disable,
				GotState: constmap.GotStateExpired,
			})
		}
	}
}
