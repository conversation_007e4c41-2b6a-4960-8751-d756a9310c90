package cron

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 关闭超时的心愿单
func closeWishByDeadline() {
	db := models.New()
	now := time.Now()
	var nextId uint
	var totalClosed int

	my_logger.Infof("开始执行心愿单超时关闭任务")

	for {
		var wishes []models.Wish
		// 查询截止时间已过且状态为可关闭的心愿单
		db.Where("id > ? AND deadline < ? AND state IN (?)",
			nextId,
			now,
			[]constmap.WishState{constmap.WishStateWaitReview, constmap.WishStateProcessing},
		).Limit(100).Order("id ASC").Find(&wishes)

		if len(wishes) == 0 {
			break
		}

		// 批量更新状态为关闭
		var wishIds []uint
		for _, wish := range wishes {
			nextId = wish.ID
			wishIds = append(wishIds, wish.ID)
		}

		// 批量更新状态
		result := db.Model(&models.Wish{}).Where("id IN (?)", wishIds).Updates(models.Wish{
			State:        constmap.WishStateClosed,
			RejectReason: "超时关闭",
		})

		if result.Error != nil {
			my_logger.Errorf("批量关闭心愿单失败", zap.Uints("wishIds", wishIds), zap.Error(result.Error))
			continue
		}

		closedCount := int(result.RowsAffected)
		totalClosed += closedCount

		my_logger.Infof("批量关闭心愿单成功", zap.Int("count", closedCount), zap.Uints("wishIds", wishIds))

		_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
			"ids": utils.Join(wishIds, ","),
		})
		// 为每个关闭的心愿单发送消息队列事件
		for _, wishId := range wishIds {
			if err := my_queue.Light(constmap.EventWishClose, gin.H{
				"id":     wishId,
				"reason": "超时关闭",
			}); err != nil {
				my_logger.Errorf("发送心愿单关闭事件失败", zap.Uint("wishId", wishId), zap.Error(err))
				// 消息队列发送失败不影响主流程，继续处理
			}
		}
	}

	my_logger.Infof("心愿单超时关闭任务完成", zap.Int("totalClosed", totalClosed))
}
