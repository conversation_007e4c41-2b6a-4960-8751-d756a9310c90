package cron

import (
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

// 迭代"处理中"状态的子订单
func orderDetailProcessing() {
	db := models.New()
	var nextId uint
	for {
		var list []models.OrderDetail
		db.Model(&list).
			Select("id,order_id").
			Where("id>?", nextId).
			Where(models.OrderDetail{State: constmap.OrderDetailStateProcessing}).
			Order("id asc").Limit(100).Find(&list)
		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID
		var third []models.OrderThirdOrder
		db.Model(&third).
			Where("order_detail_id in ?", slice.Map(list, func(_ int, item models.OrderDetail) uint {
				return item.ID
			})).
			Find(&third)
		slice.ForEach(third, func(index int, thd models.OrderThirdOrder) {
			switch thd.ThirdOrderType {
			case constmap.ThirdOrderTypeZwy:
				if thd.ThirdPayState == constmap.ThirdPayStateNeedConfirm ||
					thd.ThirdPayState == constmap.ThirdPayStateWaitPrint {
					if err := orders.ThirdOrderConfirm(db, thd); err != nil {
						my_logger.Errorf("ThirdOrderConfirm",
							zap.String("type", string(thd.ThirdOrderType)),
							zap.Uint("orderId", thd.OrderId), zap.Error(err))
					}
				} else if thd.ThirdPayState == constmap.ThirdPayStateWaitPay {
					if err := orders.ThirdOrderPay(db, thd.OrderId); err != nil {
						my_logger.Errorf("ThirdOrderPay",
							zap.String("type", string(thd.ThirdOrderType)),
							zap.Uint("orderId", thd.OrderId), zap.Error(err))
					}
				}
			case constmap.ThirdOrderTypeCeekee:
				if thd.ThirdPayState == constmap.ThirdPayStateWaitPay {
					if err := orders.ThirdOrderPay(db, thd.OrderId); err != nil {
						my_logger.Errorf("ThirdOrderPay",
							zap.String("type", string(thd.ThirdOrderType)),
							zap.Uint("orderId", thd.OrderId), zap.Error(err))
					}
				} else if thd.ThirdPayState == constmap.ThirdPayStateNeedConfirm {
					if err := orders.ThirdOrderConfirm(db, thd); err != nil {
						my_logger.Errorf("ThirdOrderConfirm",
							zap.String("type", string(thd.ThirdOrderType)),
							zap.Uint("orderId", thd.OrderId), zap.Error(err))
					}
				}
			}
		})
	}
}
