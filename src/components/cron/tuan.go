package cron

import (
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/models"
	"time"
)

func tuan() {
	_, _ = c.AddFunc("*/1 * * * *", closeOrder)
}

func closeOrder() {
	db := models.New()

	before := time.Now()
	var nextId uint
	for {
		var list []models.Order
		db.Where("id>? and state in ? and pay_time_expire<?",
			nextId,
			orders.CanCloseStates(),
			before,
		).Limit(100).Order("id asc").Find(&list)

		if len(list) == 0 {
			break
		}

		for _, order := range list {
			nextId = order.ID
			err := orders.CloseOrder(db, &order, "支付超时自动关闭")
			if err != nil {
				my_logger.Errorf("close order error", zap.Uint("orderId", order.ID), zap.Error(err))
			}
		}
	}
}
