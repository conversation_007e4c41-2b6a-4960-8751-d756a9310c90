package cron

import (
	"context"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/hotel_biz/hotel_asm"
	"roadtrip-api/src/components/cron/cron_biz"
	"roadtrip-api/src/components/cron/cron_biz/cron_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/models"
)

func spiderPlan() {
	db := models.New()
	if err := cron_biz.SpiderPlan(db, false, 10, nil); err != nil {
		my_logger.Errorf("CronSpiderPlan", zap.Error(err))
	} else {
		my_logger.Infof("CronSpiderPlan finish")
	}
}

func spiderRecPrompts() {
	db := models.New()
	if err := cron_biz.SpiderRecPrompt(db, false, nil); err != nil {
		my_logger.Errorf("CronSpiderPrompts", zap.Error(err))
	} else {
		my_logger.Infof("CronSpiderPrompts finish")
	}
}

// 处理酒店均价
func hotelPriceSync() {
	db := models.New()
	if err := cron_asm.HotelPriceSync(db, nil, true); err != nil {
		my_logger.Errorf("CronHotelPriceSync", zap.Error(err))
	} else {
		my_logger.Infof("CronHotelPriceSync finish")
	}
}

// 同步酒店知识库
func hotelKnowledgeSync() {
	my_logger.Infof("hotelKnowledgeSync cron")
	hotel_asm.StartLoopUpdate(context.Background())
	my_logger.Infof("hotelKnowledgeSync cron finish")
}
