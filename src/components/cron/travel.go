package cron

import (
	"go.uber.org/zap"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/models"
	"time"
)

// 每天定时清理一个月前的行程历史
func clearPlanHistory() {
	db := models.New()
	tm := time.Now().AddDate(0, -1, 0)
	for {
		var list []models.PlanHistory
		if ret := db.Unscoped().Where("created_at<?", tm).Limit(10).Delete(&list); ret.Error != nil {
			my_logger.Errorf("clear plan history error", zap.Error(ret.Error))
			return
		} else if ret.RowsAffected == 0 {
			break
		}
	}
}
