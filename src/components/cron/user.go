package cron

import (
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/user_biz/user_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"time"
)

// 会员过期
func vipExpire() {
	db := models.New()
	var nextId uint
	var now = time.Now()
	for {
		var list []models.UserVip
		db.Model(&list).Where("id>? AND state=? AND end<?", nextId, constmap.Enable, now).Order("id ASC").Limit(100).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			if !user_biz.IsUserVipValid(&v) {
				db.Model(&v).Updates(models.UserVip{
					State: constmap.Disable,
				})
			}
		}
	}
}

// 套餐过期积分清零
func userPackageExpire() {
	db := models.New()
	var nextId uint
	var now = time.Now().Format(constmap.DateFmtLongFull)
	for {
		var list []*models.UserPackage
		db.Where("id>? AND state=? AND expire_at<?", nextId, constmap.UserPackageStateUsing, now).Order("id ASC").Limit(100).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			if err := user_asm.ClearPackageAmount(db, v); err != nil {
				my_logger.Errorf("ClearPackageAmount", zap.Uint("upkgId", v.ID), zap.Uint("userId", v.UserId), zap.Error(err))
			} else {
				my_logger.Infof("ClearPackageAmount success", zap.Uint("upkgId", v.ID), zap.Uint("userId", v.UserId), zap.Int("amount", v.Amount))
			}
		}
	}
}
