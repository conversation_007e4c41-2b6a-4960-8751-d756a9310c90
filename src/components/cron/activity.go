package cron

import (
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/task_biz/task_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 周榜结算
func weekPKSettle() {
	db := models.New()

	my_logger.Infof("开始周榜结算")

	// 已开始、已结束并且保证结束前最后一周可结算
	var activity = new(models.Activity)
	db.Where(models.Activity{
		Uuid:  constmap.ActWeekPk,
		State: constmap.Enable,
	}).Where("`start`<now()").
		Where("date_add(`end`, interval 7 day)>?", utils.BeginOfWeek(time.Now()).Format(constmap.DateFmtLong)).
		Take(activity)
	if activity.ID == 0 {
		my_logger.Infof("无周榜活动")
		return
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		return task_asm.DoWeekPkSettle(tx, activity, utils.BeginOfWeek(time.Now()))
	})
	if err != nil {
		my_logger.Errorf("周榜结算失败", zap.Error(err))
	} else {
		my_logger.Infof("周榜结算成功")
	}
}
