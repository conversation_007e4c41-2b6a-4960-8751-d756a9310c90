package cron

import (
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 发起退款申请
func applyRefund() {
	db := models.New()
	var nextId uint
	for {
		var list []models.RefundPayment
		db.Model(&list).Where("id>? AND state=?", nextId, constmap.RefundStateApproved).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			logs := []zap.Field{
				zap.Uint("refundPaymentId", v.ID),
				zap.Uint("orderId", v.OrderId),
			}
			if err := orders.ApplyRefund(db, v); err != nil {
				my_logger.Errorf("ApplyRefund error", utils.Unshift(logs, zap.Error(err))...)
			} else {
				my_logger.Infof("ApplyRefund success", logs...)
			}
		}
	}
}

// 发起三方退款
func applySuborderRefund() {
	db := models.New()
	var nextId uint
	for {
		var list []*models.RefundSubPayment
		db.Model(&list).
			Preload("RefundSuborder.OrderDetail").
			Where("id>? AND state=?", nextId, constmap.RefundSubPaymentWaitRefund).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID
		for _, v := range list {
			logs := []zap.Field{
				zap.Uint("refundSubPaymentId", v.ID),
				zap.Uint("refundOrderId", v.RefundSuborder.RefundOrderId),
				zap.Uint("orderId", v.RefundSuborder.OrderId),
				zap.Uint("orderDetailId", v.RefundSuborder.OrderDetailId),
			}
			if err := orders.ApplySubOrderRefund(db, v); err != nil {
				my_logger.Errorf("applySuborderRefund error", utils.Unshift(logs, zap.Error(err))...)
			} else {
				my_logger.Infof("applySuborderRefund success", logs...)
			}
		}
	}
}

// 三方退款结果核对
func suborderRefundCheck() {
	db := models.New()
	var nextId uint
	for {
		var list []*models.RefundSubPayment
		db.Model(&list).
			Preload("RefundSuborder.OrderDetail").
			Where("id>? AND state=?", nextId, constmap.RefundSubPaymentRefunding).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID
		for _, v := range list {
			logs := []zap.Field{
				zap.Uint("refundSubPaymentId", v.ID),
				zap.Uint("refundOrderId", v.RefundSuborder.RefundOrderId),
				zap.Uint("orderId", v.RefundSuborder.OrderId),
				zap.Uint("orderDetailId", v.RefundSuborder.OrderDetailId),
			}
			if err := orders.SubOrderRefundCheck(db, v); err != nil {
				my_logger.Errorf("suborderRefundCheck error", utils.Unshift(logs, zap.Error(err))...)
			} else {
				my_logger.Infof("suborderRefundCheck success", logs...)
			}
		}
	}
}

// 追踪退款结果
func refundCheck() {
	db := models.New()
	var nextId uint
	for {
		var list []models.RefundPayment
		db.Model(&list).Where("id>? AND state in ?", nextId, []int{constmap.RefundStateThirdRefund, constmap.RefundStateIng}).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			logs := []zap.Field{
				zap.Uint("refundPaymentId", v.ID),
				zap.Uint("orderId", v.OrderId),
			}
			if err := orders.RefundCheck(db, v); err != nil {
				my_logger.Errorf("RefundCheck error", utils.Unshift(logs, zap.Error(err))...)
			} else {
				my_logger.Infof("RefundCheck success", logs...)
			}
		}
	}
}

// 自动标记已到期订单为已核销
func autoVerifyOrder() {
	db := models.New()
	var nextId uint
	var now = time.Now()
	var states = orders.CanVerifyStates()
	for {
		var list []models.Order
		db.Model(&list).Where("id>? AND state in ? AND date<?", nextId, states, now).Order("id ASC").Limit(100).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			err := orders.VerifyOrder(db, v)
			if err != nil {
				my_logger.Errorf("auto verify order error", zap.Uint("orderId", v.ID), zap.Error(err))
			}
		}
	}
}

// 导出团游任务订单
func exportTaskOrders() {
	db := models.New()
	var nextId uint
	for {
		var list []models.TaskRelates
		db.Model(&list).Where("id>? AND type=?", nextId, constmap.TaskRelateTuan).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			if err := orders.ExportTaskOrders(db, v.RelateId, v.TaskId); err != nil {
				my_logger.Errorf("exportTaskOrders", zap.Error(err))
			}
		}
	}

}
