package cron

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"path/filepath"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/chromedp_biz"
	"roadtrip-api/src/components/business/plan_biz/plan_asm"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"

	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func doChromeDp(planId uint) {
	db := models.New()

	var plan models.Plan
	if err := db.Preload("Sections").
		Joins("Ext").Take(&plan, planId).Error; err != nil || plan.Ext.PdfState != constmap.PlanPdfStateIng {
		my_logger.Errorf("doChromeDp plan not found", zap.Uint("planId", planId), zap.Int("planState", int(plan.Ext.PdfState)), zap.Error(err))
		return
	}

	my_logger.Infof("start doChromeDp", zap.Uint("plan_id", planId), zap.String("subject", plan.Subject))
	defer func() {
		if r := recover(); r != nil {
			my_logger.Errorf("doChromeDp panic recovered",
				zap.Uint("plan_id", planId),
				zap.String("subject", plan.Subject),
				zap.Any("panic", r))

			// 发生panic时，将PDF状态设置为失败
			ext := plan.Ext
			_ = db.Transaction(func(tx *gorm.DB) error {
				update := models.PlanExt{
					PdfState:  constmap.PlanPdfStateFail,
					PdfReason: fmt.Sprintf("系统异常: %v", r),
				}
				if tx.Model(&ext).
					Updates(update).
					RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
				}

				if err := rollbackMoney(tx, &plan); err != nil {
					return err
				}

				return nil
			})
		}
		my_logger.Infof("end doChromeDp", zap.Uint("plan_id", planId), zap.String("subject", plan.Subject))
	}()

	ext := plan.Ext

	var tempHtmlFile string
	buffer, err := plan_asm.RenderPlanHtml(db, &plan)
	if err == nil {
		if t, err := os.CreateTemp("", "plan_html_*.html"); err == nil {
			tempHtmlFile = t.Name()
		}
		defer os.Remove(tempHtmlFile)
		_ = os.WriteFile(tempHtmlFile, buffer.Bytes(), 0644)
	}

	if err != nil {
		my_logger.Errorf("doChromeDp render html", zap.Error(err))
		_ = db.Transaction(func(tx *gorm.DB) error {
			update := models.PlanExt{
				PdfState:  constmap.PlanPdfStateFail,
				PdfReason: err.Error(),
			}
			if tx.Model(&ext).
				Updates(update).
				RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}

			if err = rollbackMoney(tx, &plan); err != nil {
				return err
			}

			return nil
		})

		return
	}

	if config.Config.App.Debug {
		_ = os.WriteFile("/tmp/1.html", buffer.Bytes(), 0644)
	}

	pdfFileUrl := filepath.Join(constmap.QiniuPrefix, "pdf", utils.UUID()+".pdf")
	pdfCoverFile := strings.TrimSuffix(pdfFileUrl, filepath.Ext(pdfFileUrl)) + ".jpg"

	tasks := chromedp.Tasks{
		chromedp.WaitVisible(".footer img", chromedp.ByQuery),
		chromedp.ScrollIntoView("body", chromedp.ByQuery),
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 生成 PDF
			buf, _, err := page.PrintToPDF().WithPreferCSSPageSize(true).
				WithPrintBackground(true).Do(ctx)
			if err != nil {
				return errors.Wrap(err, "PrintToPDF")
			}

			//os.WriteFile("/tmp/1.pdf", buf, 0644)

			if err = qiniu_biz.UploadReader(bytes.NewReader(buf), pdfFileUrl); err != nil {
				return errors.Wrap(err, "qiniu.UploadReader")
			}

			return nil
		}),
		chromedp.ActionFunc(func(ctx context.Context) error {
			var screenshot []byte
			if err = chromedp.FullScreenshot(&screenshot, 90).Do(ctx); err != nil {
				return err
			}
			//os.WriteFile("/tmp/1.jpg", screenshot, 0644)

			if err = qiniu_biz.UploadReader(bytes.NewReader(screenshot), pdfCoverFile); err != nil {
				return err
			}

			return nil
		}),
	}

	my_logger.Infof("开始执行ChromeDP任务", zap.Uint("plan_id", planId))
	err = chromedp_biz.ChromeRun(fmt.Sprintf("file://%s", tempHtmlFile), tasks, true, "", 0)
	if err != nil {
		my_logger.Errorf("doChromeDp chrome run", zap.Error(err))

		_ = db.Transaction(func(tx *gorm.DB) error {
			if tx.Model(&ext).Omit(clause.Associations).Updates(models.PlanExt{
				PdfState:  constmap.PlanPdfStateFail,
				PdfReason: err.Error(),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
			}
			return rollbackMoney(tx, &plan)
		})

		return
	}

	update := models.PlanExt{
		PdfState: constmap.PlanPdfStateDone,
		PdfUrl:   pdfFileUrl,
		PdfCover: pdfCoverFile,
	}

	db.Model(&ext).Omit(clause.Associations).Updates(update)

	_ = my_queue.Light(constmap.EventSubMsg, gin.H{
		"type":    "plan_pdf_done",
		"user_id": plan.UserId,
		"plan_id": plan.ID,
	})
}

func rollbackMoney(tx *gorm.DB, plan *models.Plan) error {
	trans, _ := account_biz.GetAccountTransaction(tx, plan.Ext.TransactionNo)

	reversal, err := account_biz.CreateAccountTransactionReversal(tx, plan.Ext.TransactionNo, trans.Amount, "")
	if err != nil {
		return err
	}
	if err = account_biz.ExecuteAccountTransactionReversal(tx, reversal.BatchNo); err != nil {
		return err
	}

	return nil
}
