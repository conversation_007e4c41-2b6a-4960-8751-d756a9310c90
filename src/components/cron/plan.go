package cron

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"time"
)

// 生成费用明细
func planCost() {
	ctime := time.Now().Add(-5 * time.Minute).Format(constmap.DateFmtLongFull)
	db := models.New()
	var nextId uint
	for {
		var list []models.PlanCostDetail
		db.Model(&list).Where("id>? AND state=? AND created_at<?",
			nextId, constmap.PlanCostDetailStateRunning, ctime).Order("id ASC").Limit(10).Find(&list)
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			nextId = v.ID
			// 生成预算明细
			_ = my_queue.Weight(constmap.EventPlanUpdated, gin.H{
				"ai_reqid": v.AiReqid,
				"force":    true,
			})
		}
	}
}
