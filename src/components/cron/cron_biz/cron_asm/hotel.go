package cron_asm

import (
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-redis/redis"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/hotel_biz/hotel_asm"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 抓取每个月均价
func HotelPriceSync(db *gorm.DB, zoneIds []uint, doContinue bool) error {
	if len(zoneIds) == 0 {
		zoneIds, _ = utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ",")
	}

	var zones []*models.Zone
	db.Find(&zones, zoneIds)

	ckey := fmt.Sprintf(constmap.RKSpider, "hotelAvgPrice", 0)

	rds := my_cache.RedisClient()

	for _, zone := range zones {
		my_logger.Infof("start zone", zap.String("zoneName", zone.Name))
		var nextId uint
		if doContinue {
			rds.HGet(ckey, convertor.ToString(zone.ID)).Scan(&nextId)
		}
		for {
			var scenes []*models.Scenic
			db.Where(models.Scenic{
				ZoneId: zone.ID,
				Llm:    constmap.Enable,
				State:  constmap.Enable,
			}).
				Where("id>?", nextId).
				Order("id asc").Limit(100).Find(&scenes)
			if len(scenes) == 0 {
				break
			}
			for _, scene := range scenes {
				nextId = scene.ID
				my_logger.Infof("search hotel", zap.String("zoneName", zone.Name), zap.String("sceneName", scene.Name))

				_ = scenics_biz.QueryHotelsByPoi(db, constmap.SearchRadius, scene, func(hotelId uint) (bool, error) {
					hotel_asm.Add2Update([]uint{hotelId})

					return false, nil
				})
			}
		}
	}

	return nil
}

// 同步酒店知识库
func HotelKnowledgeSync(ctx context.Context, db *gorm.DB, zoneIds []uint, sceneIds []uint, doContinue bool) error {
	if len(zoneIds) == 0 && len(sceneIds) == 0 {
		zoneIds, _ = utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ",")
	} else if len(zoneIds) == 0 && len(sceneIds) > 0 {
		db.Model(&models.Scenic{}).Where("id in ?", sceneIds).Pluck("zone_id", &zoneIds)
	}

	var zones []*models.Zone
	db.Find(&zones, zoneIds)

	ckey := fmt.Sprintf(constmap.RKSpider, "hotelKnowledgeSync", 0)

	rds := my_cache.RedisClient()

	hotelIds := make([]uint, 0)
	chunkSize := 20 //数组达到阈值触发并发排队

	for _, zone := range zones {
		my_logger.Infof("start zone", zap.String("zoneName", zone.Name))
		var nextId uint
		if doContinue {
			rds.HGet(ckey, convertor.ToString(zone.ID)).Scan(&nextId)
		}
		for {
			var scenes []*models.Scenic
			q := db.Where(models.Scenic{
				ZoneId: zone.ID,
				Llm:    constmap.Enable,
				State:  constmap.Enable,
			}).
				Where("id>?", nextId).
				Order("id asc").Limit(100)
			if len(sceneIds) > 0 {
				q = q.Where("id in ?", sceneIds)
			}
			q.Find(&scenes)
			if len(scenes) == 0 {
				break
			}
			for _, scene := range scenes {
				nextId = scene.ID
				my_logger.Infof("search hotel", zap.String("zoneName", zone.Name), zap.String("sceneName", scene.Name))

				if err := scenics_biz.QueryHotelsByPoi(db, constmap.SearchRadius, scene, func(hotelId uint) (bool, error) {
					hotelIds = append(hotelIds, hotelId)
					if len(hotelIds) >= chunkSize {

						for _, ids := range slice.Chunk(hotelIds, 1) {
							//一组id走一个批次
							hotel_asm.Add2Update(ids)
						}
						hotelIds = hotelIds[:0]
						return false, nil
					}
					return false, nil
				}); err != nil {
					return errors.Wrap(err, "HotelKnowledgeSync")
				}

				if doContinue {
					_, _ = rds.Pipelined(func(pipeliner redis.Pipeliner) error {
						pipeliner.HSet(ckey, convertor.ToString(zone.ID), nextId)
						pipeliner.Expire(ckey, constmap.TimeDur15d)
						return nil
					})
				}
			}
		}
	}
	if len(hotelIds) > 0 {
		hotel_asm.Add2Update(hotelIds)
	}
	return nil
}
