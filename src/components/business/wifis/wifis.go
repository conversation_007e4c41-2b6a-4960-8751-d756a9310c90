package wifis

import (
	"bytes"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"roadtrip-api/src/assets"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"gorm.io/gorm"
)

var typeMaps = map[int]string{
	constmap.FlowCardTypeUnique:  "唯一码",
	constmap.FlowCardTypeDefault: "通用码",
}

var gotStateMap = map[int]string{
	constmap.GotStateDefault: "未领取",
	constmap.GotStated:       "已领取",
	constmap.GotStateExpired: "已过期",
}

func FlowcardTypeText(typ int) string {
	if t, ok := typeMaps[typ]; ok {
		return t
	}
	return constmap.UnknownStr
}

func FlowCardCodeGetStateText(state int) string {
	if t, ok := gotStateMap[state]; ok {
		return t
	}
	return constmap.UnknownStr
}

func FlowcardStateText(state int) string {
	var ma = map[int]string{
		constmap.Enable:  "可用",
		constmap.Disable: "不可用",
	}

	if t, ok := ma[state]; ok {
		return t
	}

	return constmap.UnknownStr
}

// GenCardImage 生成卡片
func GenCardImage(card *models.FlowCard, rewardCode *models.FlowCardRewardCode, includeCover bool) (bytes.Buffer, error) {
	genQrCodeUrl := func(code string) string {
		return config.Config.App.FrontHost + "/#/card?code=" + code
	}

	codeStr := utils.IfBy(rewardCode != nil, func() string {
		return rewardCode.Code
	}, func() string {
		return card.Code
	})
	qrcode, _ := business.EncodeQrCode(genQrCodeUrl(codeStr), 180, 180)

	var target *image.RGBA
	if includeCover {
		var bg image.Image
		var err error

		if bg, err = jpeg.Decode(bytes.NewReader(assets.CardQrBg)); err != nil {
			return bytes.Buffer{}, err
		}
		target = image.NewRGBA(bg.Bounds())
		if img, ok := bg.(*image.RGBA); ok {
			draw.Draw(target, bg.Bounds(), img, image.Point{}, draw.Src)
		} else {
			draw.Draw(target, bg.Bounds(), bg, image.Point{}, draw.Src)
		}
		xOffset := 768
		yOffset := 266
		destRect := image.Rectangle{
			Min: image.Point{X: xOffset, Y: yOffset},
			Max: image.Point{X: xOffset + qrcode.Bounds().Dx(), Y: yOffset + qrcode.Bounds().Dy()},
		}
		draw.Draw(target, destRect, qrcode, image.Point{}, draw.Over)
	} else {
		target = image.NewRGBA(image.Rect(0, 0, 180, 180))
		draw.Draw(target, target.Bounds(), qrcode, image.Point{}, draw.Over)
	}

	var buf bytes.Buffer
	if err := png.Encode(&buf, target); err != nil {
		return bytes.Buffer{}, err
	}
	return buf, nil
}

func Get(db *gorm.DB, user *models.User, card *beans_asm.GetCard, airline, ip string) (*models.FlowCardRelCode, error) {
	now := time.Now()

	if card.Card.State != constmap.Enable {
		return nil, utils.NewErrorStr(constmap.ErrorState, constmap.ErrorMsgState)
	} else if card.GotState == constmap.GotStated {
		return nil, utils.NewErrorStr(constmap.ErrorIsGot, constmap.ErrorMsgIsGot)
	} else if now.Before(card.Card.StartTime) || now.After(card.Card.EndTime) {
		return nil, utils.NewErrorStr(constmap.ErrorState, constmap.ErrorMsgState)
	}

	var rel models.FlowCardRelCode

	err := db.Transaction(func(tx *gorm.DB) error {
		num := 1
		if tx.Model(&models.FlowCardAirline{}).
			Where("flow_card_id=? and code=? and remaining_stock>=?", card.Card.ID, airline, num).
			Updates(map[string]any{
				"remaining_stock": gorm.Expr("remaining_stock-?", num),
			}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorStockNotEnough, constmap.ErrorMsgStockNotEnough)
		}

		var rewardId uint

		if card.IsReward {
			rewardId = card.Reward.ID

			if tx.Model(&card.Reward).
				Where("state=?", constmap.GotStateDefault).
				Updates(models.FlowCardRewardCode{State: constmap.GotStated}).
				RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorIsGot, constmap.ErrorMsgIsGot)
			}
		}

		code, isOk := getUnused(tx, airline)
		if !isOk {
			return utils.NewErrorStr(constmap.ErrorStockNotEnough, constmap.ErrorMsgStockNotEnough)
		}

		if tx.Model(&code).
			Where("state=? and got_state=?", constmap.Enable, constmap.GotStateDefault).
			Updates(models.FlowCardCode{GotState: constmap.GotStated}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorStockNotEnough, constmap.ErrorMsgStockNotEnough)
		}

		rel = models.FlowCardRelCode{
			UserId:         user.ID,
			FlowCardId:     card.Card.ID,
			AdvertiserId:   card.Card.AdvertiserId,
			Airline:        airline,
			Ip:             ip,
			RewardId:       rewardId,
			GetType:        constmap.GetMethodType,
			FlowCardCodeId: code.ID,
		}
		err := tx.Create(&rel).Error

		return err
	})

	return &rel, err
}

func getUnused(db *gorm.DB, airline string) (*models.FlowCardCode, bool) {
	var code models.FlowCardCode
	now := time.Now()
	if err := db.Where("state=? and got_state=? and airline=? and end_at>?",
		constmap.Enable,
		constmap.GotStateDefault,
		airline,
		now).
		Order("id asc").Take(&code).Error; err != nil {
		return nil, false
	}

	return &code, true
}

func GetCardByCode(db *gorm.DB, code string, user *models.User) (*beans_asm.GetCard, error) {
	var myCard beans_asm.GetCard

	if err := db.Where("flow_card_reward_codes.code=?", code).Joins("FlowCard.Advertiser").Take(&myCard.Reward).Error; err != nil {
		if err = db.Where("flow_cards.code=?", code).Joins("Advertiser").Take(&myCard.Card).Error; err != nil {
			return nil, err
		}
	} else {
		myCard.Card = myCard.Reward.FlowCard
	}

	myCard.IsReward = myCard.Reward.ID > 0
	myCard.GotState = constmap.GotStateDefault

	if myCard.IsReward {
		var rel models.FlowCardRelCode

		if err := db.Where("reward_id=?", myCard.Reward.ID).Take(&rel).Error; err == nil {
			myCard.GotState = constmap.GotStated
			myCard.GetByMy = rel.UserId == user.ID
			if myCard.GetByMy {
				myCard.RelId = rel.ID
			}
		}
	}
	if user != nil && user.ID > 0 && !myCard.IsReward {
		var rel models.FlowCardRelCode

		if db.Where("flow_card_id=? and user_id=?", myCard.Card.ID, user.ID).Take(&rel).Error == nil {
			myCard.GotState = constmap.GotStated
			myCard.GetByMy = true
			if myCard.GetByMy {
				myCard.RelId = rel.ID
			}
		}

	}

	return &myCard, nil
}
