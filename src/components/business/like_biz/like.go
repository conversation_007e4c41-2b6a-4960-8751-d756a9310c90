package like_biz

import (
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

// 获取对应类型的点赞数
func LoadLikes(db *gorm.DB, typ constmap.LikeType, ids []uint) map[uint]int64 {
	if len(ids) == 0 {
		return make(map[uint]int64)
	}

	var likes []*models.Like
	db.Where("type = ? AND res_id IN ?", typ, ids).Find(&likes)

	result := make(map[uint]int64)
	for _, like := range likes {
		result[like.ResId] = like.Likes
	}

	// 为没有点赞记录的对象创建默认记录
	for _, id := range ids {
		if _, exists := result[id]; !exists {
			result[id] = 0
		}
	}

	return result
}

// 检查用户是否对指定对象点赞
func LoadLikesDo(db *gorm.DB, typ constmap.LikeType, objectIds []uint, userId uint) map[uint]bool {
	if len(objectIds) == 0 || userId == 0 {
		return make(map[uint]bool)
	}

	var likeDos []*models.LikeDo
	db.Where("type = ? AND res_id IN ? AND user_id = ?", typ, objectIds, userId).Find(&likeDos)

	result := make(map[uint]bool)
	// 先将所有对象设置为未点赞
	for _, id := range objectIds {
		result[id] = false
	}

	// 设置已点赞的对象
	for _, likeDo := range likeDos {
		result[likeDo.ResId] = true
	}

	return result
}

// 执行点赞或取消点赞操作
func DoLike(tx *gorm.DB, typ constmap.LikeType, objectId, userId uint) (isLike bool, likes int64, err error) {
	// 检查用户是否已点赞
	var likeDo models.LikeDo

	if tx.Where(models.LikeDo{
		Type:   typ,
		ResId:  objectId,
		UserId: userId,
	}).Take(&likeDo).Error == nil {
		// 已点赞，执行取消点赞
		if err := tx.Unscoped().Delete(&likeDo).Error; err != nil {
			return false, 0, err
		}
		isLike = false

		// 减少点赞数
		if tx.Model(&models.Like{}).Where(models.Like{Type: typ, ResId: objectId}).
			Updates(map[string]any{"likes": gorm.Expr("GREATEST(likes - 1, 0)")}).RowsAffected == 0 {
			return false, 0, utils.NewErrorStr(constmap.ErrorSystem, "[1]点赞失败")
		}
	} else {
		// 未点赞，执行点赞
		likeDo = models.LikeDo{
			Type:   typ,
			ResId:  objectId,
			UserId: userId,
		}
		if err := tx.Create(&likeDo).Error; err != nil {
			return false, 0, err
		}
		isLike = true

		// 增加点赞数
		var finalLike models.Like
		if tx.Where(models.Like{Type: typ, ResId: objectId}).Take(&finalLike).Error == nil {
			if tx.Model(&finalLike).
				Updates(map[string]any{"likes": gorm.Expr("likes + 1")}).RowsAffected == 0 {
				return false, 0, utils.NewErrorStr(constmap.ErrorSystem, "[2]点赞失败")
			}
		} else {
			if err := tx.Create(&models.Like{
				Type:  typ,
				ResId: objectId,
				Likes: 1,
			}).Error; err != nil {
				return false, 0, err
			}
		}
	}

	// 获取最新点赞数
	tx.Model(&models.Like{}).Select("likes").Where(models.Like{
		Type:  typ,
		ResId: objectId,
	}).Scan(&likes)

	return isLike, likes, err
}
