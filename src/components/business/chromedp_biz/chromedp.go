package chromedp_biz

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/chromedp/chromedp"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"math/rand"
	"net/url"
	"roadtrip-api/src/components/my_logger"
	"strings"
	"sync"
	"time"
)

var s sync.Mutex

// ChromeRun 启动Chrome浏览器并执行一系列任务。
// 参数:
//
//	url: 浏览器将要访问的网址。
//	tasks: 一个chromedp.Tasks切片，包含要执行的任务。
//	headless: 是否以无头模式运行浏览器。
//	windowSize: 窗口大小，格式为"宽度*高度"。
//	scale: 页面缩放比例。
//
// 返回值:
//
//	如果执行过程中发生错误，则返回错误。
func ChromeRun(url string, tasks chromedp.Tasks, headless bool, windowSize string, scale float64) error {
	s.Lock()
	defer s.Unlock()

	windowWidth := 1920
	windowHeight := 1080
	if scale < 1 {
		scale = 1
	}

	if strutil.IsNotBlank(windowSize) {
		sizes := strings.Split(windowSize, "*")
		if len(sizes) == 2 {
			if w, err := convertor.ToInt(sizes[0]); err != nil {
				return err
			} else {
				windowWidth = int(w)
			}
			if h, err := convertor.ToInt(sizes[1]); err != nil {
				return err
			} else {
				windowHeight = int(h)
			}
		}
	}

	// 自定义 User-Agent
	userAgent := "Mozilla/5.0 (X11; Linux x86_64; rv:138.0) Gecko/20100101 Firefox/138.0"

	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("user-agent", userAgent),
		chromedp.Flag("headless", headless),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.WindowSize(windowWidth, windowHeight),
		chromedp.NoDefaultBrowserCheck,
	)
	allocCtx, _ := chromedp.NewExecAllocator(context.Background(), opts...)
	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	ctx, cancel = context.WithTimeout(ctx, 1*time.Minute)
	defer cancel()

	// 在tasks开头添加taskEmulateViewport
	tasks = append(chromedp.Tasks{
		chromedp.EmulateViewport(int64(windowWidth), int64(windowHeight), chromedp.EmulateScale(scale)),
		chromedp.Navigate(url),
	}, tasks...)

	tasks = append(tasks, chromedp.Sleep(time.Duration(2+rand.Intn(3))*time.Second))

	return errors.Wrap(chromedp.Run(ctx, tasks), url)
}

// bing必应搜图
func BingPic(key string) (string, error) {
	var src string
	var mAttr string

	my_logger.Debugf("BingPic", zap.String("key", key))

	u := fmt.Sprintf("https://cn.bing.com/images/search?q=%s&qft=+filterui:imagesize-large+filterui:aspect-wide+filterui:photo-photo&form=IRFLTR&first=1", url.QueryEscape(key))

	tasks := chromedp.Tasks{
		chromedp.WaitVisible(".dgControl ul.dgControl_list:first-child li:first-child .imgpt a.iusc img", chromedp.ByQuery),

		// 尝试多个选择器
		chromedp.ActionFunc(func(ctx context.Context) error {
			selector := ".dgControl ul.dgControl_list:first-child li:first-child .imgpt a.iusc"
			if err := chromedp.AttributeValue(selector, "m", &mAttr, nil).Do(ctx); err != nil {
				return errors.Wrap(err, "获取属性失败")
			} else {
				return nil
			}
		}),
	}

	if err := ChromeRun(u, tasks, true, "", 0); err != nil {
		return "", err
	}

	// 解析JSON获取murl
	if mAttr != "" {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(mAttr), &data); err == nil {
			if murl, ok := data["murl"].(string); ok {
				src = murl
			} else {
				return "", errors.New("murl not found in JSON")
			}
		} else {
			return "", errors.Wrap(err, "JSON解析失败")
		}
	} else {
		return "", errors.New("mAttr is empty")
	}

	return src, nil
}
