package wish_asm

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/gin-gonic/gin"
	"os/exec"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/holiday_biz"
	"roadtrip-api/src/components/business/like_biz"
	"roadtrip-api/src/components/business/message_biz"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/wish_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_baidu"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/utils/typeset"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// 心愿单风险检测
func RiskCheck(db *gorm.DB, wishId uint) error {
	var wish = new(models.Wish)
	if err := db.Joins("Ext").Preload("Todos").Preload("Medias").Take(&wish, wishId).Error; err != nil {
		return err
	}

	if wish.State != constmap.WishStateWaitReview {
		return nil
	}

	if strutil.IsBlank(wish.Cover) {
		// 视频抽取封面
		if media, ok := slice.FindBy(wish.Medias, func(index int, item models.WishMedia) bool {
			return true
		}); ok {
			if media.MediaType == constmap.WishMediaPicture {
				wish.Cover = utils.UnWrapStaticUrl(media.ResUrl)
			} else if media.MediaType == constmap.WishMediaVideo {
				// 从视频抽取封面
				videoUrl := utils.StaticUrl(media.ResUrl)

				// 创建临时文件存储封面
				tempFile, err := utils.CreateTemp("", "video_cover_*.jpg")
				if err != nil {
					my_logger.Errorf("创建临时文件失败", zap.Error(err))
				} else {
					defer tempFile.Close()

					// 抽取视频封面
					if err := extractVideoCover(videoUrl, tempFile.Name()); err != nil {
						my_logger.Errorf("抽取视频封面失败", zap.String("videoUrl", videoUrl), zap.Error(err))
					} else {
						// 生成七牛云存储路径
						coverPath := business.BuildStaticPath(constmap.PathWishMedia, fmt.Sprintf("cover/%s.jpg", utils.UUID()))

						// 上传到七牛云
						if err := qiniu_biz.UploadFile(tempFile.Name(), coverPath); err != nil {
							my_logger.Errorf("上传封面到七牛云失败", zap.String("coverPath", coverPath), zap.Error(err))
						} else {
							_ = qiniu_biz.SetObjectExpire(coverPath, 90)
							// 更新wish.Cover字段
							db.Model(&wish).Omit(clause.Associations).Updates(models.Wish{
								Cover: utils.UnWrapStaticUrl(coverPath),
							})
						}
					}
				}
			}
		}
	}

	// ========================= 内容审核 ========================

	tasks := parallel_task.NewPool(10)
	defer tasks.Release()

	riskCli := my_baidu.NewRiskClient()
	checkFails := make([]string, 0)
	var mx sync.Mutex

	//审核文本
	tasks.AddTask(func() error {
		var txts = new(strings.Builder)
		writeln := func(ss ...string) {
			slice.ForEach(ss, func(index int, item string) {
				txts.WriteString(item)
			})
			txts.WriteByte('\n')
		}
		writeln("标题:", wish.Title)
		writeln("出发地:", wish.From)
		writeln("目的地:", wish.To)
		writeln("预算:", wish.Budget)
		writeln("成员描述:", wish.MemberDesc)
		writeln("心愿描述:", wish.WishDesc)
		tagMap := wish_biz.LoadWishTags(db, *wish.TagIds.Data)
		if len(tagMap) > 0 {
			tagList := slice.Map(maputil.Values(tagMap), func(index int, item *models.WishTag) string { return item.Tag })
			writeln("标签:", strings.Join(tagList, ","))
		}

		todos := slice.Map(wish.Todos, func(index int, item models.WishTodo) string {
			return item.Todo
		})
		if len(todos) > 0 {
			writeln("心愿事项:", strings.Join(todos, "\n"))
		}

		if rsp, err := riskCli.TextCensor(txts.String()); err != nil {
			return err
		} else if rsp.ConclusionType != my_baidu.ConclusionOk {
			b, _ := json.Marshal(rsp)
			mx.Lock()
			checkFails = append(checkFails, fmt.Sprintf("文本审核失败:\n%s", string(b)))
			mx.Unlock()
		}

		return nil
	})

	//审核图片视频资源
	type videoCheckResponse struct {
		LogID                    int64                              `json:"log_id"`                   // 调用唯一ID
		ErrorCode                int64                              `json:"error_code"`               // 服务调用错误码，失败才返回，成功不返回
		ErrorMsg                 string                             `json:"error_msg"`                // 服务调用提示信息，失败才返回，成功不返回
		Conclusion               string                             `json:"conclusion"`               // 审核结果描述，可取值：合规、不合规、疑似
		ConclusionType           my_baidu.ConclusionType            `json:"conclusionType"`           // 审核结果，可取值：1 合规，2 不合规，3 疑似， 4 审核失败
		IsHitMd5                 bool                               `json:"isHitMd5"`                 // 是否命中视频黑库MD5提示
		Msg                      string                             `json:"msg"`                      // 命中MD5提示
		ConclusionTypeGroupInfos []my_baidu.ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos"` // 审核结论汇总
	}
	slice.ForEach(wish.Medias, func(index int, item models.WishMedia) {
		tasks.AddTask(func() error {
			fullUrl := utils.StaticUrl(item.ResUrl)
			if item.MediaType == constmap.WishMediaPicture {
				if rsp, err := riskCli.ImageCensor("", fullUrl); err != nil {
					return err
				} else if rsp.ConclusionType != my_baidu.ConclusionOk {
					b, _ := json.Marshal(rsp)
					mx.Lock()
					checkFails = append(checkFails, fmt.Sprintf("文件[%d]图片审核失败:\n%s", index, string(b)))
					mx.Unlock()
				}
			} else if item.MediaType == constmap.WishMediaVideo {
				if rsp, err := riskCli.VideoCensor(fullUrl); err != nil {
					return err
				} else if rsp.ConclusionType != my_baidu.ConclusionOk {
					b, _ := json.Marshal(videoCheckResponse{
						LogID:                    rsp.LogID,
						ErrorCode:                rsp.ErrorCode,
						ErrorMsg:                 rsp.ErrorMsg,
						Conclusion:               rsp.Conclusion,
						ConclusionType:           rsp.ConclusionType,
						IsHitMd5:                 rsp.IsHitMd5,
						Msg:                      rsp.Msg,
						ConclusionTypeGroupInfos: rsp.ConclusionTypeGroupInfos,
					})
					mx.Lock()
					checkFails = append(checkFails, fmt.Sprintf("文件[%d]视频审核失败:\n%s", index, string(b)))
					mx.Unlock()
				}
			}
			return nil
		})
	})

	if err := tasks.Wait(); err != nil {
		return utils.NewError(err) //需要重新运行队列消息的返回AppError
	}

	if len(checkFails) == 0 {
		_ = SetRiskCheckOk(db, wish, wish.Medias)
	} else {
		_ = db.Transaction(func(tx *gorm.DB) error {
			tx.Model(&wish).Omit(clause.Associations).Updates(models.Wish{
				State:        constmap.WishStateRejected,
				RejectReason: "内容违规",
			})
			tx.Model(&wish.Ext).Updates(models.WishExt{
				RiskCheck: strings.Join(checkFails, "\n\n"),
			})
			return nil
		})
	}

	return nil
}

func SetRiskCheckOk(db *gorm.DB, wish *models.Wish, medias []models.WishMedia) error {
	// 更新状态和封面
	updateData := models.Wish{
		State: constmap.WishStateProcessing,
	}
	db.Model(&wish).Omit(clause.Associations).Updates(updateData)
	// 审核通过后七牛文件设置为永久
	res := make([]string, 0)
	if !strutil.IsBlank(wish.Cover) {
		res = append(res, wish.Cover)
	}
	slice.ForEach(medias, func(index int, item models.WishMedia) {
		res = append(res, item.ResUrl)
	})
	slice.ForEach(res, func(index int, item string) {
		_ = qiniu_biz.SetObjectExpire(item, 0)
	})
	_ = my_queue.Light(constmap.EventWishSyncEs, gin.H{
		"ids": convertor.ToString(wish.ID),
	})
	return nil
}

func SyncAll(db *gorm.DB) error {
	var list []uint
	var nextId uint
	for {
		db.Model(&models.Wish{}).Where("id>?", nextId).Order("id ASC").Limit(100).Pluck("id", &list)
		if len(list) == 0 {
			return nil
		}
		nextId = list[len(list)-1]
		if err := syncEs(db, list); err != nil {
			return err
		}
	}
}

func Sync2EsByIds(db *gorm.DB, ids []uint) error {
	return syncEs(db, ids)
}

func syncEs(db *gorm.DB, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}
	var list []*models.Wish
	db.Take(&list, ids)

	var tagIds = typeset.NewTypeSet[uint](false)
	slice.ForEach(list, func(index int, item *models.Wish) {
		tagIds.Add(*item.TagIds.Data...)
	})

	var tagMap = wish_biz.LoadWishTags(db, tagIds.Values())
	likeMap := like_biz.LoadLikes(db, constmap.LikeWish, ids)

	type bulkDoc struct {
		es2.Wish
		Id string
	}

	items := slice.Map(list, func(index int, item *models.Wish) bulkDoc {
		ret := bulkDoc{
			Id: utils.EsWishId(item.ID),
			Wish: es2.Wish{
				ObjId:     item.ID,
				ToZoneId:  item.ToZoneId,
				Title:     item.Title,
				Tags:      make([]string, 0),
				DayTags:   GetHolidayTags(item.DepartDate, item.ReturnDate),
				Likes:     likeMap[item.ID],
				State:     item.State,
				OpenScope: item.OpenScope,
				CreatedAt: constmap.DateTime{item.CreatedAt},
			},
		}
		for _, tagId := range *item.TagIds.Data {
			if tag, ok := tagMap[tagId]; ok {
				ret.Wish.Tags = append(ret.Wish.Tags, tag.Tag)
			}
		}
		return ret
	})

	var bulkBody strings.Builder
	for _, vk := range slice.Chunk(items, 10) {
		for _, v := range vk {
			bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexWish, v.Id))
			b, _ := json.Marshal(v.Wish)
			bulkBody.Write(append(b, '\n'))
		}
	}
	if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
		my_logger.Errorf("es error", zap.Error(err))
		return err
	}
	return nil
}

func GetHolidayTags(startStr, endStr string) []string {
	if strutil.IsBlank(startStr) {
		return []string{}
	}
	var start time.Time
	var end time.Time
	times := wish_biz.ParseDepartReturn(startStr, endStr)
	switch len(times) {
	default:
		return []string{}
	case 1:
		start = times[0]
		end = utils.GetStartOfDay(datetime.EndOfMonth(start)) //指向月末那天
	case 2:
		start = times[0]
		end = times[1]
	}

	holiday, _ := holiday_biz.GetHoliday(start, end)
	holidaySet := make(map[string]beans.Holiday)
	tagSet := typeset.NewTypeSet[string](false)
	//寒假 1月中~2月中 暑假 7.7~8.31
	slice.ForEach(holiday, func(index int, item beans.Holiday) {
		if item.WorkDay == constmap.Disable {
			holidaySet[item.Date.Format(constmap.DateFmtLong)] = item
		}
	})
	for start.Unix() <= end.Unix() {
		_, month, day := start.Date()
		week := start.Weekday()
		if week == time.Saturday || week == time.Sunday {
			tagSet.Add("周末")
		}
		if month == time.January && day >= 15 || month == time.February && day <= 15 {
			tagSet.Add("寒假")
		} else if month == time.July && day >= 7 || month == time.August && day <= 31 {
			tagSet.Add("暑假")
		}
		if item, ok := holidaySet[start.Format(constmap.DateFmtLong)]; ok {
			tagSet.Add(item.Festival)
		}
		start = start.AddDate(0, 0, 1)
	}

	return tagSet.Values()
}

// 获取视频时长（秒）
func getVideoDuration(videoUrl string) (float64, error) {
	cmd := exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=duration", "-of", "csv=p=0", videoUrl)
	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("获取视频时长失败: %w", err)
	}

	durationStr := strings.TrimSpace(string(output))
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		return 0, fmt.Errorf("解析视频时长失败: %w", err)
	}

	return duration, nil
}

// 抽取视频封面（在视频10%位置）
func extractVideoCover(videoUrl string, outputPath string) error {
	// 获取视频时长
	duration, err := getVideoDuration(videoUrl)
	if err != nil {
		return err
	}

	// 计算10%位置的时间点
	seekTime := duration * 0.1

	// 使用ffmpeg抽取封面
	cmd := exec.Command("ffmpeg", "-i", videoUrl, "-ss", fmt.Sprintf("%.2f", seekTime), "-vframes", "1", "-y", outputPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("抽取视频封面失败: %w", err)
	}

	return nil
}

// 心愿单评论审核处理
func ReviewWishComment(db *gorm.DB, commentId uint) error {
	// 查询评论信息
	var comment models.WishComment
	if err := db.Take(&comment, commentId).Error; err != nil {
		my_logger.Errorf("查询心愿单评论失败", zap.Uint("commentId", commentId), zap.Error(err))
		return err
	}

	// 检查评论状态，只审核待审核状态的评论
	if comment.State != constmap.WishCommentStateWaitReview {
		my_logger.Infof("心愿单评论状态不需要审核", zap.Uint("commentId", commentId), zap.Int("state", int(comment.State)))
		return nil
	}

	// 使用百度文本审核
	riskCli := my_baidu.NewRiskClient()
	rsp, err := riskCli.TextCensor(comment.Content)
	if err != nil {
		my_logger.Errorf("百度文本审核失败", zap.Uint("commentId", commentId), zap.String("content", comment.Content), zap.Error(err))
		return err
	}

	my_logger.Infof("百度文本审核结果",
		zap.Uint("commentId", commentId),
		zap.String("conclusion", rsp.Conclusion),
		zap.Int("conclusionType", int(rsp.ConclusionType)))

	// 根据审核结果更新评论状态
	var newState constmap.WishCommentState
	if rsp.ConclusionType == my_baidu.ConclusionOk {
		// 审核通过
		newState = constmap.WishCommentStateApproved
		my_logger.Infof("心愿单评论审核通过", zap.Uint("commentId", commentId))
	} else {
		// 审核不通过
		newState = constmap.WishCommentStateRejected
		my_logger.Errorf("心愿单评论审核不通过",
			zap.Uint("commentId", commentId),
			zap.String("conclusion", rsp.Conclusion),
			zap.String("content", comment.Content))
	}

	// 更新评论状态
	if err := db.Model(&comment).
		Updates(models.WishComment{State: newState}).Error; err != nil {
		my_logger.Errorf("更新心愿单评论状态失败", zap.Uint("commentId", commentId), zap.Int("newState", int(newState)), zap.Error(err))
		return err
	}

	// 如果审核通过，发送消息通知
	if newState == constmap.WishCommentStateApproved {
		if err := sendCommentApprovedMessage(db, commentId, &comment); err != nil {
			// 消息发送失败不影响审核结果，只记录错误
			my_logger.Errorf("发送评论审核通过消息失败", zap.Uint("commentId", commentId), zap.Error(err))
		} else {
			my_logger.Infof("发送评论审核通过消息成功", zap.Uint("commentId", commentId))
		}
	}

	my_logger.Infof("心愿单评论审核完成", zap.Uint("commentId", commentId), zap.Int("finalState", int(newState)))
	return nil
}

// 发送评论审核通过消息（私有函数）
func sendCommentApprovedMessage(db *gorm.DB, commentId uint, comment *models.WishComment) error {
	// 查询关联的心愿单信息
	var wish models.Wish
	if err := db.Take(&wish, comment.WishId).Error; err != nil {
		return err
	}

	// 查询评论者信息
	commentUserMap := user_biz.LoadUsers(db, []uint{comment.UserId})
	commentUser, exists := commentUserMap[comment.UserId]
	if !exists {
		return fmt.Errorf("评论者信息不存在")
	}

	// 确定消息接收人
	var toUserId uint
	var title, content string

	if comment.ReplyUserId > 0 {
		// 有被回复人，给被回复人发消息
		toUserId = comment.ReplyUserId
		title = "您的评论收到新回复"
		content = fmt.Sprintf("%s 回复了您在心愿单「%s」中的评论：%s", commentUser.Nickname, wish.Title, comment.Content)
	} else {
		// 没有被回复人，给心愿单发起人发消息
		toUserId = wish.UserId
		title = "心愿单收到新评论"
		content = fmt.Sprintf("%s 评论了您的心愿单「%s」：%s", commentUser.Nickname, wish.Title, comment.Content)
	}

	// 如果接收人是评论者本人，不发送消息
	if toUserId == comment.UserId {
		return nil
	}

	// 使用事务创建消息模板和用户消息记录
	err := db.Transaction(func(tx *gorm.DB) error {
		// 创建消息模板
		messageTpl := &models.MessageTpl{
			Type:          constmap.MessageTypeComment,
			SubType:       constmap.MessageSubTypeText,
			Title:         title,
			Content:       content,
			WishId:        wish.ID,
			WishCommentId: commentId,
			WishMemberId:  0,
		}

		if err := tx.Create(messageTpl).Error; err != nil {
			return err
		}

		// 创建用户消息记录（未读状态）
		message := &models.Message{
			MessageTplId: messageTpl.ID,
			UserId:       toUserId,
			IsRead:       constmap.Disable, // 未读
		}

		if err := tx.Create(message).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return err
	}

	// 清除相关缓存
	message_biz.ClearUserMessageCacheByTypes(toUserId, []constmap.MessageType{constmap.MessageTypeComment})

	return nil
}
