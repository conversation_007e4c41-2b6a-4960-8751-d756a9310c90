package orders

import (
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
	"time"
)

func CheckRefundSubOrderIsFinal(suborder *models.RefundSuborder) bool {
	finals := []constmap.RefundSubOrderState{
		constmap.RefundSubOrderRefunded,
		constmap.RefundSubOrderAbnormal,
		constmap.RefundSubOrderReject,
	}
	return slice.Contain(finals, suborder.State)
}

// RefundSubordersFinalDo 检查所有子退款单是否是终态了
func RefundSubordersFinalDo(tx *gorm.DB, refundOrder *models.RefundOrder) error {
	var subOrders []models.RefundSuborder
	tx.Where("refund_order_id=?", refundOrder.ID).Find(&subOrders)

	finalSubOrders := slice.Filter(subOrders, func(index int, item models.RefundSuborder) bool {
		return CheckRefundSubOrderIsFinal(&item)
	})
	if len(finalSubOrders) != len(subOrders) {
		return nil
	}

	var refundAmount int64
	for _, subOrder := range subOrders {
		if subOrder.State == constmap.RefundSubOrderRefunded {
			refundAmount += subOrder.RefundAmount
		}
	}

	// 退款金额大于0
	if refundAmount <= 0 {
		if tx.Model(refundOrder).Omit(clause.Associations).Where(models.RefundOrder{
			State: refundOrder.State,
		}).Updates(models.RefundOrder{
			State: constmap.RefundOrderFinish,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}

		return DoSuborderAllRefund(tx, refundOrder)
	}

	var payment models.OrderPayment
	tx.Where("order_id=? and state=?", refundOrder.OrderId, constmap.PayStatePayed).Take(&payment)
	if payment.ID == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "订单支付单不存在")
	}

	refundPayment := models.RefundPayment{
		State:         constmap.RefundStateApproved,
		RefundAmount:  refundAmount,
		RefundOrderId: refundOrder.ID,
		OrderId:       refundOrder.OrderId,
		PayId:         payment.ID,
		OutRefundNo:   utils.GenOrderNo(),
	}

	return tx.Create(&refundPayment).Error
}

// 退款单最终处理
func RefundOrderFinalDo(tx *gorm.DB, refundPayment *models.RefundPayment) error {
	refundOrder := new(models.RefundOrder)
	tx.Take(&refundOrder, refundPayment.RefundOrderId)
	if tx.Model(&refundOrder).Omit(clause.Associations).Updates(models.RefundOrder{
		State: constmap.RefundOrderFinish,
	}).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "主退款单更新失败")
	}

	if tx.Model(&models.Order{}).Where("id=?", refundPayment.OrderId).Updates(map[string]any{
		"refund_amount": gorm.Expr("refund_amount+?", refundPayment.RefundAmount),
	}).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}

	return DoSuborderAllRefund(tx, refundOrder)
}

// 重置异常退款单
func ResetOrderRefundPayment(db *gorm.DB, orderId uint) error {
	var refund models.RefundPayment
	db.Model(&refund).Last(&refund, models.RefundPayment{OrderId: orderId, State: constmap.RefundStateAbnormal})
	if refund.ID == 0 {
		return fmt.Errorf("订单[%d]未找到关联的异常退款单", orderId)
	}
	if db.Model(&refund).
		Select("State", "Resp").
		Omit(clause.Associations).
		Updates(models.RefundPayment{
			State: constmap.RefundStateApproved,
			Resp:  "",
		}).RowsAffected == 0 {
		return fmt.Errorf("订单[%d]异常退款单重置失败", orderId)
	}
	return nil
}

// 申请子订单退款
func ApplySubOrderRefund(db *gorm.DB, refundSubPayment *models.RefundSubPayment) error {
	if refundSubPayment.State != constmap.RefundSubPaymentWaitRefund {
		return utils.NewError(errors.New("退款单状态不正确"))
	}
	if refundSubPayment.RefundSuborder.OrderDetail.ID == 0 {
		return utils.NewError(errors.New("退款单关联数据异常"))
	}
	ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(refundSubPayment.RefundSuborder.OrderDetail.ProductType))
	if err != nil {
		return utils.NewError(err)
	}
	thirdOrder := new(models.OrderThirdOrder)
	db.Where(models.OrderThirdOrder{
		OrderDetailId: refundSubPayment.RefundSuborder.OrderDetailId,
	}).Take(&thirdOrder)

	detailExt := new(models.OrderDetailExt)
	db.Where(models.OrderDetailExt{
		OrderDetailId: refundSubPayment.RefundSuborder.OrderDetailId,
	})

	if refundErr := ota.ApplyRefund(db, thirdOrder, &refundSubPayment.RefundSuborder.OrderDetail, detailExt, refundSubPayment, &refundSubPayment.RefundSuborder); refundErr != nil {

		var mailBody strings.Builder
		mailBody.WriteString(fmt.Sprintf("订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.OrderId)))
		mailBody.WriteString(fmt.Sprintf("退款订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.RefundOrderId)))
		mailBody.WriteString(fmt.Sprintf("退款子订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.ID)))
		mailBody.WriteString(fmt.Sprintf("失败原因: <b>%s</b><br/>", refundErr.Error()))

		_ = my_queue.Light(constmap.EventMail, gin.H{
			"subject": utils.MailSubjectEnv("第三方订单退款失败"),
			"body":    mailBody.String(),
		})

		err := db.Transaction(func(tx *gorm.DB) error {
			if tx.Model(&refundSubPayment).Omit(clause.Associations).Where(models.RefundSubPayment{
				State: refundSubPayment.State,
			}).Updates(models.RefundSubPayment{
				State: constmap.RefundSubPaymentAbnormal,
				Resp:  refundErr.Error(),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款支付单更新失败")
			}

			if tx.Model(&refundSubPayment.RefundSuborder).Omit(clause.Associations).Where(models.RefundSuborder{
				State: refundSubPayment.RefundSuborder.State,
			}).Updates(models.RefundSuborder{
				State: constmap.RefundSubOrderAbnormal,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款子订单更新失败")
			}

			if thirdOrder.ID > 0 {
				if tx.Model(&thirdOrder).Omit(clause.Associations).Where(models.OrderThirdOrder{
					ThirdPayState: thirdOrder.ThirdPayState,
				}).Updates(models.OrderThirdOrder{
					ThirdPayState: constmap.ThirdPayStateRefundFail,
					Remark:        refundErr.Error(),
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, "三方订单更新失败")
				}
			}

			var refundOrder = new(models.RefundOrder)
			tx.Take(&refundOrder, refundSubPayment.RefundSuborder.RefundOrderId)
			if err := RefundSubordersFinalDo(tx, refundOrder); err != nil {
				return err
			}

			return nil
		})
		return err
	} else {
		err := db.Transaction(func(tx *gorm.DB) error {
			if tx.Model(&refundSubPayment).Omit(clause.Associations).Where(models.RefundSubPayment{
				State: refundSubPayment.State,
			}).Updates(models.RefundSubPayment{
				OutRefundNo: refundSubPayment.OutRefundNo,
				State:       constmap.RefundSubPaymentRefunding,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款支付单更新失败")
			}

			if tx.Model(&refundSubPayment.RefundSuborder).Omit(clause.Associations).Where(models.RefundSuborder{
				State: refundSubPayment.RefundSuborder.State,
			}).Updates(models.RefundSuborder{
				State: constmap.RefundSubOrderRefunding,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款子订单更新失败")
			}

			if thirdOrder.ID > 0 {
				if tx.Model(&thirdOrder).Omit(clause.Associations).Where(models.OrderThirdOrder{
					ThirdPayState: thirdOrder.ThirdPayState,
				}).Updates(models.OrderThirdOrder{
					ThirdPayState: constmap.ThirdPayStateRefunding,
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, "三方订单更新失败")
				}
			}

			return nil
		})
		return err
	}
}

func SubOrderRefundCheck(db *gorm.DB, refundSubPayment *models.RefundSubPayment) error {
	if refundSubPayment.State != constmap.RefundSubPaymentRefunding {
		return utils.NewError(errors.New("退款单状态不正确"))
	}
	if refundSubPayment.RefundSuborder.OrderDetail.ID == 0 {
		return utils.NewError(errors.New("退款单关联数据异常"))
	}
	ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(refundSubPayment.RefundSuborder.OrderDetail.ProductType))
	if err != nil {
		return utils.NewError(err)
	}
	thirdOrder := new(models.OrderThirdOrder)
	db.Where(models.OrderThirdOrder{
		OrderDetailId: refundSubPayment.RefundSuborder.OrderDetailId,
	}).Take(&thirdOrder)

	detailExt := new(models.OrderDetailExt)
	db.Where(models.OrderDetailExt{
		OrderDetailId: refundSubPayment.RefundSuborder.OrderDetailId,
	}).Take(&detailExt)
	if refundRsp, refundErr := ota.QueryRefund(db, thirdOrder, &refundSubPayment.RefundSuborder.OrderDetail, detailExt, refundSubPayment, &refundSubPayment.RefundSuborder); refundErr != nil {

		var mailBody strings.Builder
		mailBody.WriteString(fmt.Sprintf("订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.OrderId)))
		mailBody.WriteString(fmt.Sprintf("退款订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.RefundOrderId)))
		mailBody.WriteString(fmt.Sprintf("退款子订单号: <b>%s</b><br/>", convertor.ToString(refundSubPayment.RefundSuborder.ID)))
		mailBody.WriteString(fmt.Sprintf("失败原因: <b>%s</b><br/>", refundErr.Error()))

		_ = my_queue.Light(constmap.EventMail, gin.H{
			"subject": utils.MailSubjectEnv("第三方订单退款失败"),
			"body":    mailBody.String(),
		})

		err := db.Transaction(func(tx *gorm.DB) error {
			if tx.Model(&refundSubPayment).Omit(clause.Associations).Where(models.RefundSubPayment{
				State: refundSubPayment.State,
			}).Updates(models.RefundSubPayment{
				State: constmap.RefundSubPaymentAbnormal,
				Resp:  refundErr.Error(),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款支付单更新失败")
			}

			if tx.Model(&refundSubPayment.RefundSuborder).Omit(clause.Associations).Where(models.RefundSuborder{
				State: refundSubPayment.RefundSuborder.State,
			}).Updates(models.RefundSuborder{
				State: constmap.RefundSubOrderAbnormal,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款子订单更新失败")
			}

			if thirdOrder.ID > 0 {
				if tx.Model(&thirdOrder).Omit(clause.Associations).Where(models.OrderThirdOrder{
					ThirdPayState: thirdOrder.ThirdPayState,
				}).Updates(models.OrderThirdOrder{
					ThirdPayState: constmap.ThirdPayStateRefundFail,
					Remark:        refundErr.Error(),
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, "三方订单更新失败")
				}
			}

			var refundOrder = new(models.RefundOrder)
			tx.Take(&refundOrder, refundSubPayment.RefundSuborder.RefundOrderId)
			if err := RefundSubordersFinalDo(tx, refundOrder); err != nil {
				return err
			}

			return nil
		})
		return err
	} else if refundRsp.RefundOk {
		err := db.Transaction(func(tx *gorm.DB) error {

			if tx.Model(&refundSubPayment).Omit(clause.Associations).Where(models.RefundSubPayment{
				State: refundSubPayment.State,
			}).Updates(models.RefundSubPayment{
				State:            constmap.RefundSubPaymentRefunded,
				RefundAmount:     refundRsp.YbRefundAmount,
				OtaRefundAmount:  refundRsp.RefundAmount,
				OtaPenaltyAmount: refundRsp.PenaltyAmount,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款支付单更新失败")
			}

			if tx.Model(&refundSubPayment.RefundSuborder).Omit(clause.Associations).Where(models.RefundSuborder{
				State: refundSubPayment.RefundSuborder.State,
			}).Updates(models.RefundSuborder{
				State:            constmap.RefundSubOrderRefunded,
				RefundAmount:     refundRsp.YbRefundAmount,
				OtaRefundAmount:  refundRsp.RefundAmount,
				OtaPenaltyAmount: refundRsp.PenaltyAmount,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "退款子订单更新失败")
			}

			if thirdOrder.ID > 0 {
				if tx.Model(&thirdOrder).Omit(clause.Associations).Where(models.OrderThirdOrder{
					ThirdPayState: thirdOrder.ThirdPayState,
				}).Updates(models.OrderThirdOrder{
					ThirdPayState: constmap.ThirdPayStateRefund,
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, "三方订单更新失败")
				}
			}

			var refundOrder = new(models.RefundOrder)
			tx.Take(&refundOrder, refundSubPayment.RefundSuborder.RefundOrderId)
			if err := RefundSubordersFinalDo(tx, refundOrder); err != nil {
				return err
			}

			return nil
		})
		return err
	}
	return nil
}

// 发起站内退款
func ApplyRefund(db *gorm.DB, refund models.RefundPayment) error {
	if refund.Payment.ID == 0 {
		db.Model(&refund.Payment).Take(&refund.Payment, refund.PayId)
	}
	res, refundErr := my_wechat.DoRefund(refund.OutRefundNo, refund.RefundReason, refund.RefundAmount, &refund.Payment)
	if refundErr != nil {
		err := db.Transaction(func(tx *gorm.DB) error {
			tx.Model(&refund).
				Omit(clause.Associations).Updates(models.RefundPayment{
				State: constmap.RefundStateAbnormal,
				Resp:  refundErr.Error(),
			})
			if err := RefundOrderFinalDo(tx, &refund); err != nil {
				return err
			}
			return nil
		})

		return err
	}
	db.Model(&refund).
		Select("State", "ThirdPartyNo", "Resp").
		Omit(clause.Associations).Updates(models.RefundPayment{
		State:        constmap.RefundStateIng,
		ThirdPartyNo: res.RefundID,
		Resp:         "",
	})
	return nil
}

// 站內退款检查
func RefundCheck(db *gorm.DB, refund models.RefundPayment) error {
	err := db.Transaction(func(tx *gorm.DB) error {
		if refund.State == constmap.RefundStateIng {
			// 查询站内退款状态
			var payment models.OrderPayment
			tx.Take(&payment, refund.PayId)
			res, err := my_wechat.QueryRefund(payment.PayMethod, refund.OutRefundNo)
			if err != nil {
				my_logger.Errorf("[wechat]query refund fail", zap.String("outRefundNo", refund.OutRefundNo), zap.Error(err))
				return nil
			}
			switch res.Status {
			case "ABNORMAL":
				tx.Model(&refund).
					Omit(clause.Associations).Updates(models.RefundPayment{
					State: constmap.RefundStateAbnormal,
					Resp:  fmt.Sprintf("退款异常:[%s]%s", res.Code, res.Message),
				})
			case "CLOSED":
				tx.Model(&refund).
					Omit(clause.Associations).Updates(models.RefundPayment{
					State: constmap.RefundStateAbnormal,
					Resp:  "退款关闭",
				})
			case "SUCCESS":
				tx.Model(&refund).
					Select("State", "Resp").
					Omit(clause.Associations).
					Updates(models.RefundPayment{
						State: constmap.RefundStateComplete,
						Resp:  "",
					})
			}
			if err := RefundOrderFinalDo(tx, &refund); err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func CanRefundDetails(db *gorm.DB, order *models.Order) (*beans.CanRefundOrder, error) {
	if order.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单不存在")
	}
	if order.State != constmap.OrderStatePayed && order.State != constmap.OrderStateSuccess {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "订单状态不可退款")
	}
	var orderSubRefunds []models.RefundSuborder
	db.Where(models.RefundSuborder{
		OrderId: order.ID,
	}).Find(&orderSubRefunds)

	var refundSubOrderMap = slice.ReduceBy(orderSubRefunds, make(map[uint][]models.RefundSuborder), func(index int, item models.RefundSuborder, agg map[uint][]models.RefundSuborder) map[uint][]models.RefundSuborder {
		agg[item.OrderDetailId] = append(agg[item.OrderDetailId], item)
		return agg
	})

	refundSubOrderStates := typeset.NewTypeSet(false,
		constmap.RefundSubOrderWaitReview,
		constmap.RefundSubOrderWaitRefund,
		constmap.RefundSubOrderRefunding,
		constmap.RefundSubOrderRefunded,
		constmap.RefundSubOrderAbnormal)
	var out = &beans.CanRefundOrder{
		OrderId:      order.ID,
		TotalAmount:  order.TotalAmount,
		CreatedAt:    order.CreatedAt.Unix(),
		PayAmount:    order.NeedPayAmount,
		ContactsName: order.ContactsName,
		ContactsTel:  order.ContactsTel,
	}

	now := time.Now()
	out.List = slice.FilterMap(order.Details, func(index int, item models.OrderDetail) (beans.CanRefundDetail, bool) {
		if item.State != constmap.OrderDetailStateSuccess {
			return beans.CanRefundDetail{}, false
		}
		var quantity = item.Quantity
		if refunds, ok := refundSubOrderMap[item.ID]; ok {
			slice.ForEach(refunds, func(index int, item2 models.RefundSuborder) {
				if refundSubOrderStates.Has(item2.State) {
					quantity -= item2.RefundQuantity
				}
			})
		}
		if quantity <= 0 {
			return beans.CanRefundDetail{}, false
		}

		if item.Ext == nil {
			return beans.CanRefundDetail{}, false
		}
		ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(item.ProductType))
		if err != nil {
			my_logger.Errorf("NewOta", zap.Error(err))
			return beans.CanRefundDetail{}, false
		}
		day := int(utils.GetStartOfDay(time.Unix(item.DateStart, 0)).Sub(utils.GetStartOfDay(now)) / constmap.TimeDur1d)
		skuRefundPrice := item.SkuPrice

		switch item.OrderSubType {
		case constmap.OrderSubTypeTuan:
			ext, err := ota.UnmarshalOrderExtTuan(item.Ext.Ext)
			if err != nil {
				my_logger.Errorf("UnmarshalOrderExtTuan", zap.Error(err))
				return beans.CanRefundDetail{}, false
			}
			if ext.CanRefund == constmap.Disable {
				return beans.CanRefundDetail{}, false
			}

			for _, v := range ext.RefundRules {
				if v.Day <= day {
					if v.CanRefund == constmap.Disable {
						return beans.CanRefundDetail{}, false
					}
					skuRefundPrice = int64(float64(item.SkuPrice) * v.Percent)
				}
			}

		case constmap.OrderSubTypeTicket:
			ext, err := ota.UnmarshalOrderExtTicket(item.Ext.Ext)
			if err != nil {
				my_logger.Errorf("UnmarshalOrderExtTicket", zap.Error(err))
				return beans.CanRefundDetail{}, false
			}
			if ext.CanRefund == constmap.Disable {
				return beans.CanRefundDetail{}, false
			}
			if day < ext.CancelDay {
				return beans.CanRefundDetail{}, false
			}
		case constmap.OrderSubTypeHotel:
			ext, err := ota.UnmarshalOrderExtHotel(item.Ext.Ext)
			if err != nil {
				my_logger.Errorf("UnmarshalOrderExtHotel", zap.Error(err))
				return beans.CanRefundDetail{}, false
			}
			if !ext.Room.Policy[0].CanCancel {
				return beans.CanRefundDetail{}, false
			}
		}

		v := beans.CanRefundDetail{
			Quantity:          item.Quantity,
			CanRefundQuantity: quantity,
			OrderDetailId:     item.ID,
			OrderSubType:      item.OrderSubType,
			ProductType:       item.ProductType,
			ProductId:         item.ProductId,
			SkuId:             item.SkuId,
			Pic:               utils.StaticUrl(item.Pic),
			ProductName:       item.ProductName,
			SkuName:           item.SkuName,
			SkuPrice:          item.SkuPrice,
			SkuRefundPrice:    skuRefundPrice,
			DateStart:         item.DateStart,
			DateEnd:           item.DateEnd,
			OtaPriceRate:      item.OtaPriceRate,
		}
		return v, true
	})
	return out, nil
}

func BuildRefundTuan(productType constmap.ProductType, orderDetailExt *models.OrderDetailExt) (*beans.RefundTuan, error) {
	if ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(productType)); err != nil {
		return nil, err
	} else if extTuan, err := ota.UnmarshalOrderExtTuan(orderDetailExt.Ext); err != nil {
		return nil, err
	} else {
		return &beans.RefundTuan{
			CanRefund:   extTuan.CanRefund,
			RefundRules: extTuan.RefundRules,
		}, nil
	}
}

func BuildTuanRefundRules(canRefund int, rules []beans.TuanRefundRule) []string {
	var refundRules []string
	if canRefund != constmap.Enable {
		refundRules = append(refundRules, "不支持退款")
	} else {
		if len(rules) == 0 {
			refundRules = append(refundRules, "支持退款")
		}
		var str strings.Builder
		slice.ForEach(rules, func(index int, item beans.TuanRefundRule) {
			str.Reset()
			if item.Day > 0 {
				str.WriteString(fmt.Sprintf("团期日(不含团期日当天)%d天前", item.Day))
			} else if item.Day == 0 {
				str.WriteString("团期当日")
			} else {
				str.WriteString(fmt.Sprintf("团期日(不含团期日当天)%d天后", item.Day))
			}
			if item.CanRefund != constmap.Enable {
				str.WriteString("不支持申请退款")
			} else {
				str.WriteString(fmt.Sprintf("申请退款，损失为订单%.0f%%的费用", (1-item.Percent)*100))
			}
			refundRules = append(refundRules, str.String())
		})
	}
	return refundRules
}

func BuildRefundTicket(productType constmap.ProductType, orderDetailExt *models.OrderDetailExt) (*beans.RefundTicket, error) {
	if ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(productType)); err != nil {
		return nil, err
	} else if extTicket, err := ota.UnmarshalOrderExtTicket(orderDetailExt.Ext); err != nil {
		return nil, err
	} else {
		return &beans.RefundTicket{
			Date:      extTicket.Date,
			CanRefund: extTicket.CanRefund,
			CancelDay: extTicket.CancelDay,
		}, nil
	}
}

func BuildTicketRefundRules(canRefund int, cancelDay int) []string {
	if canRefund != constmap.Enable {
		return []string{"不支持退款"}
	}
	if cancelDay > 0 {
		return []string{fmt.Sprintf("游玩日(不含当天)前%d天可退款", cancelDay)}
	} else {
		return []string{"游玩日当天前可退款"}
	}
}

func BuildRefundHotel(productType constmap.ProductType, priceRate int64, orderDetailExt *models.OrderDetailExt) (*beans.RefundHotel, error) {
	if ota, err := ota_asm.NewOta(beans.ProductType2OtaCode(productType)); err != nil {
		return nil, err
	} else if extHotel, err := ota.UnmarshalOrderExtHotel(orderDetailExt.Ext); err != nil {
		return nil, err
	} else {
		policy := extHotel.Room.Policy[0]
		return &beans.RefundHotel{
			Name:    extHotel.Room.Name,
			Pic:     extHotel.Room.Pic,
			BedDesc: extHotel.Room.BedDesc,
			Attrs:   extHotel.Room.Attrs,
			Policy: beans.RefundHotelPolicy{
				CanCancel:  policy.CanCancel,
				CancelRule: policy.CancelRule,
				CancelDesc: policy.CancelDesc,
				CancelPolices: slice.Map(policy.CancelPolicy, func(index int, item beans.HotelRoomCancelPolicy) beans.HotelRoomCancelPolicy {
					item.Text = item.GetText(priceRate)
					return item
				}),
			},
		}, nil
	}
}

func BuildHotelRefundRules(refund *beans.RefundHotel) []string {
	if refund == nil {
		return []string{}
	}
	if !refund.Policy.CanCancel {
		return []string{"不支持退款"}
	}
	return []string{
		refund.Policy.CancelRule,
		refund.Policy.CancelDesc,
	}
}

func GetRefundsByDetails(db *gorm.DB, details []models.OrderDetail) map[uint][]models.RefundSuborder {
	ret := make(map[uint][]models.RefundSuborder)
	if len(details) == 0 {
		return ret
	}
	oids := slice.Map(details, func(index int, item models.OrderDetail) uint {
		return item.ID
	})

	var refunds []models.RefundSuborder
	db.Where("order_detail_id IN ?", oids).Find(&refunds)
	for _, refund := range refunds {
		ret[refund.OrderDetailId] = append(ret[refund.OrderDetailId], refund)
	}
	return ret
}
