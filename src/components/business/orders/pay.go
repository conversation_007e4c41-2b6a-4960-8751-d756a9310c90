package orders

import (
	"context"
	"encoding/json"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/business/user_biz/user_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/request"
	response3 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/response"
	"github.com/duke-git/lancet/v2/condition"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func PayComplete(tx *gorm.DB, payment *models.OrderPayment, order *models.Order,
	thirdPartyNo, buyerId string, successTime time.Time,
) error {
	//互斥等待
	ctx, cancel := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "paycomplete", convertor.ToString(payment.ID)), constmap.TimeDur5s)
	defer cancel()

	//加锁后重查
	tx.Take(&payment, payment.ID)

	if payment.State != constmap.PayStateNotPay {
		return nil
	}

	var err error
	if payment.PaymentType == constmap.PaymentTypeNormal && order.PayState == constmap.PayStatePayed {
		return nil
	} else if payment.PaymentType == constmap.PaymentTypeVip && payment.State == constmap.PayStatePayed {
		return nil
	}

	if err = tx.Model(&payment).Omit(clause.Associations).Updates(models.OrderPayment{
		State:           constmap.PayStatePayed,
		PayTime:         convertor.ToPointer(successTime),
		ThirdPartyNo:    thirdPartyNo,
		BuyerId:         buyerId,
		ActualPayAmount: payment.NeedPayAmount,
	}).Error; err != nil {
		return err
	}

	switch payment.PaymentType {
	case constmap.PaymentTypeVip:
		return payCompleteVip(tx, payment)
	case constmap.PaymentTypePackage:
		return payCompletePkg(tx, payment)
	}

	orderPayState := constmap.PayStatePayed

	var payments []models.OrderPayment
	tx.Where("state=? and order_id=?", constmap.PayStatePayed, order.ID).Find(&payments)
	totalPayAmount := slice.ReduceBy(payments, 0, func(index int, item models.OrderPayment, agg int64) int64 {
		return agg + item.ActualPayAmount
	})
	if config.Config.App.PayDebug && totalPayAmount < order.NeedPayAmount {
		//orderPayState = constmap.PayStatePartPay
	}
	if orderPayState == constmap.PayStatePayed {
		if err = tx.Model(&models.OrderDetail{}).
			Where(models.OrderDetail{OrderId: order.ID, State: constmap.OrderDetailStateWaitPay}).
			Updates(&models.OrderDetail{State: constmap.OrderDetailStateProcessing}).Error; err != nil {
			return err
		}
	}

	updateOrders := models.Order{
		PayState: orderPayState,
		State:    condition.TernaryOperator(orderPayState == constmap.PayStatePayed, constmap.OrderStatePayed, constmap.OrderStatePayEarnest),
		PayTime:  *payment.PayTime,
	}
	if err = tx.Model(&order).Omit(clause.Associations).Updates(updateOrders).Error; err != nil {
		return err
	}
	return nil
}

func payCompleteVip(tx *gorm.DB, payment *models.OrderPayment) error {
	var err error
	var paymentExtra beans_asm.PaymentExtra
	_ = json.Unmarshal([]byte(payment.Extra), &paymentExtra)
	var vipConf models.VipConf
	var userVip models.UserVip
	tx.Take(&vipConf, paymentExtra.VipConfId)
	tx.Take(&userVip, models.UserVip{UserId: payment.UserId, State: constmap.Enable})
	if user_biz.IsUserVipValid(&userVip) {
		//续费
		if tx.Model(&userVip).Updates(models.UserVip{
			End:       utils.GetEndOfDay(datetime.AddYear(userVip.End, int64(vipConf.Years))),
			RealName:  paymentExtra.RealName,
			FirstName: paymentExtra.FirstName,
			LastName:  paymentExtra.LastName,
			IdCard:    paymentExtra.IdCard,
		}).RowsAffected == 0 {
			err = utils.NewErrorStr(constmap.ErrorSystem, "会员续费更新失败")
		}
		if err != nil {
			return err
		}
		var ub []models.UserVipBenefit
		tx.Find(&ub, models.UserVipBenefit{UserVipId: userVip.ID})
		for _, v := range ub {
			if v.BenefitType == constmap.VipBenefitTypeTimes {
				if tx.Model(&v).Updates(map[string]any{
					"remain_times": gorm.Expr("remain_times+?", v.Times),
				}).RowsAffected == 0 {
					err = utils.NewErrorStr(constmap.ErrorSystem, "会员权益更新失败")
				}
			}
		}
	} else {
		//开通
		userVip = models.UserVip{
			UserId:    payment.UserId,
			Start:     time.Now(),
			End:       utils.GetEndOfDay(datetime.AddYear(time.Now(), int64(vipConf.Years))),
			State:     constmap.Enable,
			Price:     vipConf.SalePrice,
			RealName:  paymentExtra.RealName,
			FirstName: paymentExtra.FirstName,
			LastName:  paymentExtra.LastName,
			IdCard:    paymentExtra.IdCard,
		}
		userVipExtra := beans.UserVipExtra{
			VipConfId: paymentExtra.VipConfId,
		}
		b, _ := json.Marshal(userVipExtra)
		userVip.Extra = string(b)
		err = tx.Create(&userVip).Error
		if err != nil {
			return err
		}
		var benefits []models.VipBenefit
		tx.Find(&benefits, models.VipBenefit{VipConfId: vipConf.ID})
		userBenefits := slice.Map(benefits, func(_ int, v models.VipBenefit) models.UserVipBenefit {
			return models.UserVipBenefit{
				UserVipId:   userVip.ID,
				UserId:      payment.UserId,
				ConfId:      vipConf.ID,
				BenefitId:   v.ID,
				BenefitType: v.BenefitType,
				Times:       v.Times,
				RemainTimes: v.Times,
			}
		})
		if len(userBenefits) > 0 {
			err = tx.Create(&userBenefits).Error
		}
	}
	if err != nil {
		return err
	}
	paymentExtra.VipId = userVip.ID
	b, _ := json.Marshal(paymentExtra)
	payment.Extra = string(b)
	tx.Model(&payment).Updates(models.OrderPayment{
		Extra: payment.Extra,
	})
	return nil
}

func payCompletePkg(tx *gorm.DB, payment *models.OrderPayment) error {
	var err error
	var paymentExtra beans_asm.PaymentExtra
	_ = json.Unmarshal([]byte(payment.Extra), &paymentExtra)

	var pkg = paymentExtra.Package

	if err = user_asm.AddUserPackage(tx, payment, paymentExtra.PackageIsNew, pkg); err != nil {
		return err
	}

	return nil
}

func PayConfirm(orderPayment *models.OrderPayment, tx *gorm.DB) error {
	successTime := time.Now()
	var thirdPartyNo, buyerId string
	var err error

	switch orderPayment.PayMethod {
	case constmap.PayMethodWeixin, constmap.PayMethodMpWeixin:
		var resp *response3.ResponseOrder
		if resp, err = my_wechat.QueryPayByOutTradeNumber(orderPayment.PayMethod, orderPayment.OutTradeNo); err != nil {
			return err
		} else if resp.TradeState != "SUCCESS" {
			return utils.NewErrorStr(constmap.ErrorPay, constmap.ErrorMsgPay)
		} else {
			thirdPartyNo = resp.TransactionID
			if t, err := datetime.FormatStrToTime(resp.SuccessTime, constmap.TimeLayoutWeixinPay); err == nil {
				successTime = t
			}

			openId := resp.Payer.OpenID
			buyerId = convertor.ToString(openId)
		}
	}

	var order models.Order
	if orderPayment.OrderId > 0 {
		tx.Joins("User").Take(&order, orderPayment.OrderId)
	}

	err = PayComplete(tx, orderPayment, &order, thirdPartyNo, buyerId, successTime)

	return err
}

func CreatePayment(tx *gorm.DB, payment *models.OrderPayment, user *models.User, title string) (*beans.PayStr, error) {
	payment.OutTradeNo = utils.Md5(utils.UUID())

	if config.Config.App.PayDebug && typeset.NewTypeSet(false, constmap.PayMethodWeixin, constmap.PayMethodMpWeixin).Has(payment.PayMethod) {
		payment.NeedPayAmount = 1
	}

	if err := tx.Create(&payment).Error; err != nil {
		return nil, err
	}

	var data = beans.PayStr{
		PayId: payment.ID,
	}

	//微信支付
	if typeset.NewTypeSet(false, constmap.PayMethodWeixin, constmap.PayMethodMpWeixin).Has(payment.PayMethod) {
		var oauth models.UserOauth
		var typ = constmap.OauthTypeWeixinH5
		if payment.PayMethod == constmap.PayMethodMpWeixin {
			typ = constmap.OauthTypeMpWeixin
		}
		tx.Where("user_id=? and type=?", user.ID, typ).Order("id desc").First(&oauth)

		req := request.RequestJSAPIPrepay{
			Description: title,
			OutTradeNo:  payment.OutTradeNo,
			Amount: &request.JSAPIAmount{
				Total:    int(payment.NeedPayAmount),
				Currency: "CNY",
			},
			Payer: &request.JSAPIPayer{OpenID: oauth.OpenId},
		}

		resp, err := my_wechat.CreatePay(payment.PayMethod, &req)
		if err != nil {
			return nil, err
		}

		if err = tx.Model(&payment).Omit(clause.Associations).
			Updates(models.OrderPayment{ThirdPartyNo: resp.PrepayID}).Error; err != nil {
			return nil, err
		}

		data.PayStr = convertor.ToString(resp.PayStr)
	}

	return &data, nil
}

func OrderPayCallback(db *gorm.DB, orderId uint) error {
	return ThirdOrderPay(db, orderId)
}
