package orders

import (
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils/typeset"
)

func VoucherStateChange(db *gorm.DB, orderDetail models.OrderDetail, state constmap.VoucherState) error {
	var vouchers []models.OrderVoucher
	db.Model(&vouchers).Find(&vouchers, models.OrderVoucher{OrderId: orderDetail.OrderId, OrderDetailId: orderDetail.ID})
	if len(vouchers) > 0 && state == constmap.VoucherStateUsed {
		// 部分核销成功或作废，标记子订单完成
		stateSet := typeset.NewTypeSet(false, constmap.VoucherStateUsed, constmap.VoucherStateCanceling, constmap.VoucherStateCancel)
		if slice.Every(vouchers, func(_ int, v models.OrderVoucher) bool { return stateSet.Has(v.State) }) {
			db.Model(&models.OrderDetail{}).Omit(clause.Associations).Where("id=?", orderDetail.ID).Updates(models.OrderDetail{State: constmap.OrderDetailStateComplete})
			return OrderDetailStateChange(db, orderDetail.OrderId, constmap.OrderDetailStateComplete)
		}
	}
	return nil
}
