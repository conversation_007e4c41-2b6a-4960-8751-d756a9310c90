package orders

import (
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func ExportTaskOrders(db *gorm.DB, tuanId, ptaskId uint) error {
	var tasks []models.Task
	db.Where(models.Task{
		ParentTaskId: ptaskId,
		State:        constmap.Enable,
	}).Find(&tasks)
	//taskMap := slice.ReduceBy(tasks, make(map[uint]models.Task), func(index int, item models.Task, agg map[uint]models.Task) map[uint]models.Task {
	//	agg[item.ID] = item
	//	return agg
	//})
	var sceneids = slice.Map(tasks, func(index int, item models.Task) uint {
		return item.ScenicId
	})
	var scenes []models.Scenic
	db.Find(&scenes, sceneids)
	sceneMap := slice.ReduceBy(scenes, make(map[uint]models.Scenic), func(index int, item models.Scenic, agg map[uint]models.Scenic) map[uint]models.Scenic {
		agg[item.ID] = item
		return agg
	})
	f := excelize.NewFile()
	defer f.Close()
	sheet := "订单导出"
	index, _ := f.NewSheet(sheet)
	f.SetActiveSheet(index)
	_ = f.DeleteSheet("Sheet1")

	rowIndex := 1
	head := []string{"手机号码", "姓名", "订单号", "下单时间"}
	slice.ForEach(scenes, func(index int, item models.Scenic) {
		head = append(head, item.Name, item.Name+"打卡时间")
	})
	utils.SetCellValue(f, sheet, rowIndex, 1, head)

	var nextId uint
	var total int
	for {
		list := []models.Order{}
		if err := db.Where("product_id = ? and id > ?", tuanId, nextId).Order("id asc").Limit(100).Find(&list).Error; err != nil {
			return err
		}
		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID
		for _, v := range list {
			total++
			rowIndex++
			colMap := map[string]any{
				"手机号码": v.ContactsTel,
				"姓名":   v.ContactsName,
				"订单号":  v.ID,
				"下单时间": v.CreatedAt.Format(constmap.DateFmtLongFull),
			}

			var utasks []models.UserTask
			db.Where(models.UserTask{
				UserId:  v.UserId,
				OrderId: v.ID,
			}).Find(&utasks)
			utaskIds := slice.ReduceBy(utasks, make([]uint, 0), func(index int, item models.UserTask, agg []uint) []uint {
				agg = append(agg, item.ID)
				return agg
			})
			var utaskLogMap = make(map[uint]models.UserTaskLog)
			if len(utaskIds) > 0 {
				var utaskLogs []models.UserTaskLog
				db.Where("user_task_id in ? and type=?", utaskIds, constmap.UserTaskLogDo).Find(&utaskLogs)
				utaskLogMap = slice.ReduceBy(utaskLogs, make(map[uint]models.UserTaskLog), func(index int, item models.UserTaskLog, agg map[uint]models.UserTaskLog) map[uint]models.UserTaskLog {
					agg[item.TaskId] = item
					return agg
				})
			}
			slice.ForEach(tasks, func(index int, item models.Task) {
				scene := sceneMap[item.ScenicId]
				if log, ok := utaskLogMap[item.ID]; ok {
					colMap[scene.Name] = "已打卡"
					colMap[scene.Name+"打卡时间"] = log.CreatedAt.Format(constmap.DateFmtLongFull)
				} else {
					colMap[scene.Name] = "未打卡"
				}
			})

			if colMap != nil {
				row := make([]any, 0)
				slice.ForEach(head, func(_ int, item string) {
					row = append(row, colMap[item])
				})
				utils.SetCellValue(f, sheet, rowIndex, 1, row)
			}
		}
	}

	if total == 0 {
		return nil
	}

	filename := fmt.Sprintf("%s-%s", sheet, time.Now().Format(constmap.DateFmtShortFull))
	filenameFull := filename + ".xlsx"
	tmpfs, err := utils.CreateTemp("", filename)
	if err != nil {
		return err
	}
	defer tmpfs.Close()

	if _, err := f.WriteTo(tmpfs); err != nil {
		return err
	}

	if err := utils.Mail(nil, utils.MailSubjectEnv(sheet), "", nil, map[string]string{
		filenameFull: tmpfs.Name(),
	}); err != nil {
		return err
	}
	return nil
}
