package orders

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func DoSuborderAllRefund(tx *gorm.DB, banner *models.RefundOrder) error {
	var refundSubOrders []models.RefundSuborder

	finalRefundStates := []constmap.RefundSubOrderState{
		constmap.RefundSubOrderRefunded,
		constmap.RefundSubOrderReject,
	}

	tx.Where("refund_order_id=? and refund_suborders.state in ?",
		banner.ID, finalRefundStates).Joins("OrderDetail").Find(&refundSubOrders)

	for _, refundSubOrder := range refundSubOrders {

		quantity := refundSubOrder.OrderDetail.Quantity

		var allRefunds models.RefundSuborder
		tx.Model(&models.RefundSuborder{}).
			Where("order_detail_id=? and state=?",
				refundSubOrder.OrderDetailId, constmap.RefundSubOrderRefunded).
			Select("sum(refund_quantity) as refund_quantity").
			Take(&allRefunds)

		if allRefunds.RefundQuantity != quantity {
			continue
		}

		////已经全部退款了的子订单，需要标记为退款完成
		//_, hasRefund := slice.FindBy(allRefunds, func(_ int, v models.RefundSuborder) bool {
		//	return v.State == constmap.RefundSubOrderRefunded
		//})
		//if !hasRefund {
		//	continue
		//}

		orderDetail := refundSubOrder.OrderDetail

		if tx.Model(&orderDetail).Omit(clause.Associations).Where("state=?", orderDetail.State).
			Updates(models.OrderDetail{State: constmap.OrderDetailStateRefund}).
			RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorApplyRefund, constmap.ErrorMsgApplyRefund)
		}

	}

	//当该主订单下，所有子订单都退款完成，则更新主订单状态为退款完成
	var count int64 = 0
	tx.Model(&models.OrderDetail{}).Where("order_id=? and state !=?", banner.OrderId, constmap.OrderDetailStateRefund).
		Count(&count)
	if count == 0 {
		if tx.Model(&models.Order{}).Where("id=?", banner.OrderId).Updates(models.Order{
			State: constmap.OrderStateRefund,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
		}
	}

	return nil
}
