package orders

import (
	"context"
	"fmt"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func SpinLockThirdOrder(ctx context.Context, thirdOrderTableId uint) {
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "SpinLockThirdOrder", convertor.ToString(thirdOrderTableId)), constmap.TimeDur30s)
}

// 第三方OTA订单发起支付
func ThirdOrderPay(db *gorm.DB, orderId uint) error {
	var err error
	var order models.Order
	if orderId == 0 {
		return nil
	}

	ctx, canceler := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "ThirdOrderPay", convertor.ToString(orderId)), constmap.TimeDur1m)
	defer canceler()

	db.Select("id,state").Take(&order, orderId)
	if order.State != constmap.OrderStatePayed {
		return nil
	}
	var thirdOrders []*models.OrderThirdOrder
	db.Model(&thirdOrders).Where(models.OrderThirdOrder{OrderId: order.ID}).Find(&thirdOrders)
	type failOrder struct {
		err        error
		thirdOrder *models.OrderThirdOrder
	}
	var failed []failOrder
	for _, thirdOrder := range thirdOrders {
		//加锁后重查
		SpinLockThirdOrder(ctx, thirdOrder.ID)
		db.Model(&thirdOrder).Take(&thirdOrder, thirdOrder.ID)

		err = db.Transaction(func(tx *gorm.DB) error {
			if thirdOrder.ThirdPayState == constmap.ThirdPayStateWaitPay {
				if ota, err := ota_asm.NewOta(ota_asm.ThirdOrderType2OtaCode(thirdOrder.ThirdOrderType)); err != nil {
					return nil
				} else if res, err := ota.OrderPay(&beans_asm.TravelOrderPayDto{ThirdOrder: thirdOrder}); err != nil {
					failed = append(failed, failOrder{
						err:        err,
						thirdOrder: thirdOrder,
					})
					my_logger.Errorf(fmt.Sprintf("[%s]third order pay fail", ota.Code()),
						zap.Uint("orderId", order.ID), zap.Error(err))
					tx.Model(&thirdOrder).Omit(clause.Associations).
						Updates(models.OrderThirdOrder{ThirdPayState: constmap.ThirdPayStateFail, Remark: err.Error()})
					tx.Model(&models.OrderDetail{}).Omit(clause.Associations).
						Where("id=?", thirdOrder.OrderDetailId).
						Updates(&models.OrderDetail{State: constmap.OrderDetailStateFail, Remark: err.Error()})
				} else {
					tx.Model(&thirdOrder).Omit(clause.Associations).
						Updates(models.OrderThirdOrder{ThirdPayState: res.ThirdOrderState})
				}
			}
			return nil
		})
	}
	if len(failed) > 0 {
		mailBody := strings.Builder{}
		mailBody.WriteString(fmt.Sprintf("订单号: <b>%d<b/><br/>", order.ID))
		slice.ForEach(failed, func(index int, item failOrder) {
			mailBody.WriteString(fmt.Sprintf("第三方订单号: [<b>%s</b>]<b>%s<b/><br/>",
				item.thirdOrder.ThirdOrderType, item.thirdOrder.ThirdOrderId))
			mailBody.WriteString(fmt.Sprintf("错误内容: <b>%s<b/><br/>", item.err.Error()))
		})

		_ = my_queue.Light(constmap.EventMail, gin.H{
			"subject": utils.MailSubjectEnv("第三方订单支付失败"),
			"body":    mailBody.String(),
		})
	}
	return err
}

// 第三方订单查询是否已确认已支付
func ThirdOrderConfirm(db *gorm.DB, thdOrder models.OrderThirdOrder) error {

	ctx, canceller := context.WithCancel(context.Background())
	defer canceller()
	//加锁后重查
	SpinLockThirdOrder(ctx, thdOrder.ID)
	db.Model(&thdOrder).Take(&thdOrder, thdOrder.ID)

	var err error
	var targetState constmap.ThirdPayState
	var targetError error

	switch thdOrder.ThirdOrderType {

	default:
		return nil

	case constmap.ThirdOrderTypeZwy:
		oid, err := convertor.ToInt(thdOrder.ThirdOrderId)
		if err != nil {
			return err
		}
		res, err := my_ota.NewZwy().OrderDetail(my_ota.ZwyOrderDetailReq{
			OrderId: oid,
		})
		if err != nil {
			return err
		}
		if res.OrderState == my_ota.ZwyOrderState0WaitConfirm {
			return nil
		}

		if res.OrderState == my_ota.ZwyOrderState1Confirmed {
			targetState = constmap.ThirdPayStateWaitPay
		} else if res.OrderState == my_ota.ZwyOrderState2Payed {
			if res.OrderState2 == my_ota.ZwySubOrderState3Printed {
				targetState = constmap.ThirdPayStatePayed
			} else if res.OrderState2 == my_ota.ZwySubOrderState1Noticed || res.OrderState2 == my_ota.ZwySubOrderStateNone {
				return nil
			} else {
				targetError = fmt.Errorf("出票状态检测失败[%d-%s]", res.OrderState, res.OrderState2)
			}
		} else {
			targetError = fmt.Errorf("脚本确认失败,remote order statue[%d]", res.OrderState)
		}

	case constmap.ThirdOrderTypeCeekee:
		oid, err := convertor.ToInt(thdOrder.ThirdOrderId)
		if err != nil {
			return err
		}
		res, err := my_ota.NewCeeKee().OrderDetail(my_ota.CeekeeOrderDetailReq{
			OrderSerialNo: oid,
		})
		if err != nil {
			return err
		}
		if res.HotelOrderStatus == my_ota.CeekeeEnumOrderState32WaitPay ||
			res.HotelOrderStatus == my_ota.CeekeeEnumOrderState22WaitConfirm {
			return nil
		}

		if res.HotelOrderStatus == my_ota.CeekeeEnumOrderState23CanUse {
			targetState = constmap.ThirdPayStatePayed
		} else {
			targetError = fmt.Errorf("脚本确认失败,remote order status[%d]", res.HotelOrderStatus)
		}

	}
	err = db.Transaction(func(tx *gorm.DB) error {
		if targetError != nil {
			tx.Model(&models.OrderDetail{}).
				Omit(clause.Associations).
				Where("id=?", thdOrder.OrderDetailId).
				Updates(models.OrderDetail{State: constmap.OrderDetailStateFail, Remark: "确认失败"})
			tx.Model(&thdOrder).Omit(clause.Associations).
				Updates(&models.OrderThirdOrder{ThirdPayState: constmap.ThirdPayStateConfirmFail, Remark: targetError.Error()})

			var mailBody strings.Builder
			mailBody.WriteString(fmt.Sprintf("订单号: <b>%s</b><br/>", convertor.ToString(thdOrder.OrderId)))
			mailBody.WriteString(fmt.Sprintf("三方订单号: [<b>%s</b>]<b>%s</b><br/>", thdOrder.ThirdOrderType, convertor.ToString(thdOrder.ThirdOrderId)))
			mailBody.WriteString(fmt.Sprintf("失败原因: <b>%s</b><br/>", targetError.Error()))

			_ = my_queue.Light(constmap.EventMail, gin.H{
				"subject": utils.MailSubjectEnv("第三方订单确认失败"),
				"body":    mailBody.String(),
			})
		} else {
			tx.Model(&thdOrder).Omit(clause.Associations).Updates(&models.OrderThirdOrder{ThirdPayState: targetState})
			err = thirdOrderStateChange(tx, thdOrder.OrderId, thdOrder.OrderDetailId, targetState)
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

// 三方订单状态流转
func thirdOrderStateChange(tx *gorm.DB, orderId uint, orderDetailId uint, thirdOrderState constmap.ThirdPayState) error {

	if !typeset.NewTypeSet(false, constmap.ThirdPayStatePayed).Has(thirdOrderState) {
		return nil
	}

	switch thirdOrderState {

	case constmap.ThirdPayStatePayed:
		//三方订单已确认已支付=》子订单处理完成
		var orderDetail models.OrderDetail
		orderDetail.ID = orderDetailId
		tx.Model(&orderDetail).Omit(clause.Associations).
			Where(models.OrderDetail{State: constmap.OrderDetailStateProcessing, OrderId: orderId}).
			Updates(&models.OrderDetail{State: constmap.OrderDetailStateSuccess})
		return OrderDetailStateChange(tx, orderId, constmap.OrderDetailStateSuccess)

	}

	return nil
}
