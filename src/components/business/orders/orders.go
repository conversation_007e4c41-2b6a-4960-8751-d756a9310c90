package orders

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"time"
)

var stateTextMaps = map[int]string{
	constmap.OrderStateNotPay:     "未支付",
	constmap.OrderStatePayEarnest: "已付预付款",
	constmap.OrderStatePayed:      "已付款",
	constmap.OrderStateCancel:     "已取消",
	constmap.OrderStateRefund:     "已退款",
	constmap.OrderStateComplete:   "已完成",
	constmap.OrderStateProcessing: "处理中",
	constmap.OrderStateSuccess:    "处理成功",
}

var detailStateTextMap = map[int]string{
	constmap.OrderDetailStateWaitPay:    "待支付",
	constmap.OrderDetailStateProcessing: "待确认",
	constmap.OrderDetailStateFail:       "确认失败",
	constmap.OrderDetailStateSuccess:    "待使用",
	constmap.OrderDetailStateComplete:   "已使用",
	constmap.OrderDetailStateRefund:     "已退款",
}

var payStateTextMaps = map[int]string{
	constmap.PayStatePayed:   "已支付",
	constmap.PayStateNotPay:  "未支付",
	constmap.PayStatePartPay: "部分付款",
}
var payMethodTextMaps = map[int]string{
	constmap.PayMethodWeixin:   "公众号支付",
	constmap.PayMethodMpWeixin: "微信支付",
	constmap.PayMethodTransfer: "对公转账",
}

func OrderDetailStateText(state int) string {
	return utils.If(detailStateTextMap[state] != "", detailStateTextMap[state], constmap.UnknownStr)
}
func PayMethodText(state int) string {
	if t, ok := payMethodTextMaps[state]; ok {
		return t
	}

	return constmap.UnknownStr
}

func OrderPayStateText(state int) string {
	if t, ok := payStateTextMaps[state]; ok {
		return t
	}

	return constmap.UnknownStr
}

func OrderStateText(state int) string {
	if t, ok := stateTextMaps[state]; ok {
		return t
	}

	return constmap.UnknownStr
}
func CanCancel(order *models.Order) bool {
	return order.State == constmap.OrderStateNotPay && (order.PayTimeExpire != nil && order.PayTimeExpire.After(time.Now()))
}
func CanRefund(payment *models.OrderPayment, order *models.Order) bool {
	if order.State == constmap.OrderStateCancel {
		return false
	}

	return payment.State == constmap.PayStatePayed && payment.RefundAmount < payment.NeedPayAmount
}

// 可关闭订单状态
func CanCloseStates() []int {
	return []int{constmap.OrderStateNotPay}
}

// 可核销状态
func CanVerifyStates() []int {
	return []int{constmap.OrderStatePayed, constmap.OrderStateSuccess}
}

func CanPay(order *models.Order) bool {
	if slices.Contains([]int{constmap.OrderStateNotPay, constmap.OrderStatePayEarnest}, order.State) && (order.PayTimeExpire != nil && order.PayTimeExpire.After(time.Now())) {
		return true
	}

	return false
}

func CloseOrder(db *gorm.DB, order *models.Order, msg string) error {
	var err error
	var thirdOrders []models.OrderThirdOrder
	var orderDetails []models.OrderDetail
	db.Model(&thirdOrders).Where("order_id=?", order.ID).Find(&thirdOrders)
	db.Model(&orderDetails).Where("order_id=?", order.ID).Find(&orderDetails)
	return db.Transaction(func(tx *gorm.DB) error {
		if tx.Model(&order).Omit(clause.Associations).Updates(models.Order{State: constmap.OrderStateCancel}).RowsAffected == 0 {
			return fmt.Errorf("no record")
		}
		for _, v := range thirdOrders {
			if v.ThirdOrderType == constmap.ThirdOrderTypeZwy {
				var cancelNum int
				for _, vd := range orderDetails {
					if vd.ID == v.OrderDetailId {
						cancelNum += vd.Quantity
					}
				}
				if cancelNum > 0 {
					orderId, _ := convertor.ToInt(v.ThirdOrderId)
					res, err := my_ota.NewZwy().OrderCancel(my_ota.ZwyOrderCancelReq{
						OrderId:    orderId,
						CancelMemo: msg,
						CancelNum:  cancelNum,
					})
					if err == nil {
						my_logger.Infof("[zwy]cancel order", zap.Uint("id", order.ID), zap.Any("res", res))
					} else {
						my_logger.Errorf("[zwy]cancel order fail", zap.Uint("id", order.ID), zap.Error(err))
					}
				}
			}
		}
		return err
	})
}

// 标记已到期订单为已核销
func VerifyOrder(db *gorm.DB, order models.Order) error {
	var err error
	if !typeset.NewTypeSet(false, CanVerifyStates()...).Has(order.State) {
		return nil
	}
	var details []models.OrderDetail
	db.Model(&details).Find(&details, models.OrderDetail{OrderId: order.ID})
	err = db.Transaction(func(tx *gorm.DB) error {
		detailIds := slice.ReduceBy(details, make([]uint, 0), func(_ int, v models.OrderDetail, agg []uint) []uint {
			if v.State == constmap.OrderDetailStateSuccess {
				agg = append(agg, v.ID)
			}
			return agg
		})
		if len(detailIds) > 0 {
			if db.Model(&details).Where("id in ?", detailIds).
				Omit(clause.Associations).Updates(models.OrderDetail{State: constmap.OrderDetailStateComplete}).RowsAffected == 0 {
				return fmt.Errorf("update details state fail:%v", detailIds)
			}
		}
		if db.Model(&order).Where("id=?", order.ID).
			Omit(clause.Associations).Updates(models.Order{State: constmap.OrderStateComplete}).RowsAffected == 0 {
			return fmt.Errorf("update order fail:%d", order.ID)
		}
		return nil
	})
	return err
}
