package orders

import (
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils/typeset"
)

// 子订单状态流转
func OrderDetailStateChange(tx *gorm.DB, orderId uint, orderDetailState int) error {

	if !typeset.NewTypeSet(false, constmap.OrderDetailStateSuccess, constmap.OrderDetailStateComplete).
		Has(orderDetailState) {
		return nil
	}

	var details []models.OrderDetail
	tx.Model(&details).Select("id,state").Where("order_id=?", orderId).Find(&details)

	switch orderDetailState {

	case constmap.OrderDetailStateSuccess:
		// 全部子订单处理成功，标记主订单处理成功
		if _, ok := slice.FindBy(details, func(_ int, v models.OrderDetail) bool {
			return v.State != constmap.OrderDetailStateSuccess
		}); ok {
			return nil
		}
		tx.Model(&models.Order{}).Omit(clause.Associations).
			Where("id=? and state in ?", orderId, []int{constmap.OrderStatePayed, constmap.OrderStateProcessing}).
			Updates(models.Order{State: constmap.OrderStateSuccess})

	case constmap.OrderDetailStateComplete:
		// 全部子订单完成或退款，标记主订单完成
		if _, ok := slice.FindBy(details, func(_ int, v models.OrderDetail) bool {
			return v.State != constmap.OrderDetailStateComplete && v.State != constmap.OrderDetailStateRefund
		}); ok {
			return nil
		}
		tx.Model(&models.Order{}).
			Where("id=?", orderId).
			Omit(clause.Associations).
			Updates(models.Order{State: constmap.OrderStateComplete})

	}

	return nil
}
