package plan_biz

import (
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/utils/typeset"
	"strings"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

func ParseAIJourney(db *gorm.DB, a ai.IAi, chatRes *ai.ChatCompletionResponse) (*beans.RenderTravelJourney, error) {
	var err error
	var data = &beans.RenderTravelJourney{
		List: make([]*beans.Journey, 0),
	}
	err = a.Parse(chatRes.Content, nil, data)
	if err != nil {
		return nil, err
	}

	data.Notice = strutil.Trim(data.Notice)
	for i, journey := range data.List {
		journey.Content = strutil.Trim(journey.Content)
		journey.Title = strutil.Trim(journey.Title)

		fillWayPoint(db, journey, 0, 0)
		fillScenicSpots(journey, db)
		if i < len(data.List)-1 {
			fillHotels(chatRes, journey, db)
		}
	}

	slice.ForEach(data.List, func(i1 int, it1 *beans.Journey) {
		slice.ForEach(it1.Timeline, func(i2 int, it2 *beans.JTimeline) {
			if it2.Type == constmap.JTimelineScene && it2.ItemId > 0 {
				data.SceneNum++
			}
		})
	})

	// 每一天从上一天酒店为起点
	//var thotel *beans.JTimeline
	//slice.ForEach(data.List, func(index int, item *beans.Journey) {
	//	if thotel != nil {
	//		item.Timeline = append([]*beans.JTimeline{thotel}, item.Timeline...)
	//	}
	//	slice.ForEach(item.Timeline, func(index int, timeline *beans.JTimeline) {
	//		if timeline.Type == constmap.JTimelineHotel {
	//			thotel = new(beans.JTimeline)
	//			_ = utils.Copy(timeline, thotel)
	//			thotel.Time = "早晨"
	//		}
	//	})
	//})

	return data, nil
}

func normalizeZone(zt string) string {
	names := utils.SplitByRunes([]rune(zt), []rune(",，;；、 "))
	zones := zone_biz.NewZoneBiz().NormalizeZoneNames(slice.Map(names, func(index int, item string) models.Zone {
		return models.Zone{Name: item}
	}))
	return strings.Join(slice.Map(zones, func(index int, item models.Zone) string {
		return item.Name
	}), "、")
}

func fillWayPoint(db *gorm.DB, journey *beans.Journey, lng, lat float64) {
	journey.WayPoints = make([]*beans.JourneyPoi, 0)

	var err error
	var res *es.SearchResponse[es2.Zone]
	if res, err = waypoint(utils.If(journey.Zones == "", journey.Title, normalizeZone(journey.Zones))); err != nil {
		return
	} else if res.Hits.Total.Value == 0 {
		if res, err = waypoint(journey.Content); err != nil || res.Hits.Total.Value == 0 {
			return
		}
	}

	zoneMap := zone_biz.NewZoneBiz().GetZonesCc(db)
	for _, hit := range res.Hits.Hits {
		id := hit.Source.ObjId

		var zone models.Zone
		var ok bool
		if zone, ok = zoneMap[id]; !ok {
			my_logger.Errorf("zone not found", zap.Uint("id", id))
			continue
		}
		if zone.Level == constmap.ZoneLevelDistrict {
			if zone, ok = zoneMap[zone.ParentId]; !ok {
				my_logger.Errorf("zone not found", zap.Uint("id", id))
				continue
			}
		}

		journey.WayPoints = append(journey.WayPoints, &beans.JourneyPoi{
			Pic:  utils.StaticUrl(zone.Pic),
			Name: normalizeZone(zone.Name),
			Id:   zone.ID,
			Lat:  zone.Lat,
			Lng:  zone.Lng,
			Type: constmap.PoiTypeZone,
		})
	}

	if lng > 0 && lat > 0 {
		sort(journey.WayPoints, lng, lat)
	} else {
		sortByPosition(journey)
	}

}

// 城市按文字命中顺序排序
func sortByPosition(journey *beans.Journey) {
	slice.SortBy(journey.WayPoints, func(a, b *beans.JourneyPoi) bool {
		var posA = -1
		var posB = -1
		if posA = strings.Index(journey.Zones, a.Name); posA > -1 {
		} else if posA = strings.Index(journey.Title, a.Name); posA > -1 {
		} else {
			posA = len(journey.Title) + strings.Index(journey.Content, a.Name)
		}
		if posB = strings.Index(journey.Zones, b.Name); posB > -1 {
		} else if posB = strings.Index(journey.Title, b.Name); posB == -1 {
		} else {
			posB = len(journey.Title) + strings.Index(journey.Content, b.Name)
		}
		return posA < posB
	})
}

func waypoint(text string) (*es.SearchResponse[es2.Zone], error) {
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []any{
					map[string]any{
						"terms": map[string]any{
							"level": []int{constmap.ZoneLevelCity, constmap.ZoneLevelDistrict},
						},
					},
					map[string]any{
						"term": map[string]any{
							"type": constmap.PoiTypeZone,
						},
					},
					map[string]any{
						"match": map[string]any{
							"name": text,
						},
					},
				},
			},
		},
	}
	return es.Search[es2.Zone](constmap.EsIndexPoi, query)
}

func sort(list []*beans.JourneyPoi, lng, lat float64) {
	slice.SortBy(list, func(a, b *beans.JourneyPoi) bool {
		d1 := utils.CalculateDistance(lng, lat, a.Lng, a.Lat)
		d2 := utils.CalculateDistance(lng, lat, b.Lng, b.Lat)

		return d1 < d2
	})
}

// 计算指定经纬度周围3公里范围内所有酒店的平均价格
// lng: 经度
// lat: 纬度
// 返回值: 平均价格(int64)
func getHotelAvgPriceByLocation(lng, lat float64) (int64, error) {
	// 构建查询条件
	must := []any{
		map[string]any{
			"term": map[string]any{
				"type": constmap.PoiTypeHotel,
			},
		},
		map[string]any{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		map[string]any{
			"geo_distance": map[string]any{
				"distance": fmt.Sprintf("%.1fkm", constmap.SearchRadius),
				"location": map[string]any{
					"lat": lat,
					"lon": lng,
				},
			},
		},
	}

	// 构建聚合查询
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": must,
			},
		},
		"size": 0, // 不返回文档，只返回聚合结果
		"aggs": map[string]any{
			"avg_price": map[string]any{
				"avg": map[string]any{
					"field": "avg_price",
				},
			},
		},
	}

	// ES查询结果结构体
	var result struct {
		Aggregations struct {
			AvgPrice struct {
				Value float64 `json:"value"`
			} `json:"avg_price"`
		} `json:"aggregations"`
	}
	// 执行查询并获取聚合结果
	if err := es.SearchWithAggregations(constmap.EsIndexPoi, query, &result); err != nil {
		my_logger.Errorf("ES查询失败query", zap.Any("query", query))
		return 0, fmt.Errorf("ES查询失败: %w", err)
	}

	// 获取平均值
	if result.Aggregations.AvgPrice.Value <= 0 {
		return 0, nil // 没有有效价格或平均价格为0
	}

	return int64(result.Aggregations.AvgPrice.Value), nil
}
func fillHotels(chatRes *ai.ChatCompletionResponse, journey *beans.Journey, db *gorm.DB) {
	//journey.Hotels = make([]*beans.JourneyPoi, 0)
	//re := regexp.MustCompile("酒店|晚上|住宿|退房|休息")
	//if !re.MatchString(journey.Content) {return}
	if len(journey.WayPoints) == 0 {
		return
	}

	var lng, lat float64

	if len(journey.Timeline) > 0 && len(journey.ScenicSpots) > 0 {
		var lastId uint
		for _, v := range journey.Timeline {
			if v.Type == constmap.JTimelineScene && v.ItemId > 0 {
				lastId = v.ItemId
			}
		}
		if lastId > 0 {
			for _, v := range journey.ScenicSpots {
				if lastId == v.Id {
					lng = v.Lng
					lat = v.Lat
					break
				}
			}
		}
	}
	if lng == 0 {
		if len(journey.ScenicSpots) > 0 {
			last := journey.ScenicSpots[len(journey.ScenicSpots)-1]
			lng = last.Lng
			lat = last.Lat
		} else {
			last := journey.WayPoints[len(journey.WayPoints)-1]
			lng = last.Lng
			lat = last.Lat
		}
	}

	avgPrice, err := getHotelAvgPriceByLocation(lng, lat)
	if err != nil {
		my_logger.Errorf("getHotelAvgPriceByLocation error", zap.Error(err))
	}

	must := []any{
		map[string]any{
			"term": map[string]any{
				"type": constmap.PoiTypeHotel,
			},
		},
		map[string]any{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
	}

	buildGeoCond := func(radius float64) {
		if radius <= 0 {
			radius = constmap.SearchRadius
		}
		must = append(must, map[string]any{
			"geo_distance": map[string]any{
				"distance": fmt.Sprintf("%.1fkm", radius),
				"location": map[string]any{
					"lat": lat,
					"lon": lng,
				},
			},
		})
	}
	buildStarCond := func() {
		if chatRes.PromptOptions != nil && chatRes.PromptOptions.Accommodation != "" {
			starMap := map[constmap.TravelAccommodation][]int{
				constmap.TravelAccommodationEconomic: {0, 1, 2},
				constmap.TravelAccommodationModerate: {3},
				constmap.TravelAccommodationLuxury:   {4, 5},
			}
			if v, ok := starMap[chatRes.Accommodation]; ok {
				must = append(must, map[string]any{
					"terms": map[string]any{
						"star": v,
					},
				})
			}
		}
	}
	if journey.HotelConds != nil {
		cond := journey.HotelConds
		if !strutil.IsBlank(cond.Name) {
			must = append(must, map[string]any{
				"match": map[string]any{
					"name": cond.Name,
				},
			})
		}

		if !strutil.IsBlank(cond.BrandName) {
			must = append(must, map[string]any{
				"match": map[string]any{
					"brand_name": cond.BrandName,
				},
			})
		}

		buildGeoCond(cond.Radius)

		if cond.Star > 0 {
			must = append(must, map[string]any{
				"range": map[string]any{
					"star": map[string]any{
						"gte": cond.Star,
					},
				},
			})
		} else if !strutil.IsBlank(cond.StarRange) {
			ranges := strings.Split(cond.PriceRange, "-")
			if len(ranges) == 2 {
				var start float64
				var end float64
				start, _ = convertor.ToFloat(ranges[0])
				end, _ = convertor.ToFloat(ranges[1])
				if start == 0 && end > 0 {
					// -300
					must = append(must, map[string]any{
						"range": map[string]any{
							"star": map[string]any{
								"lte": utils.CurrencyFloat2Int(end),
							},
						},
					})
				} else if start > 0 && end == 0 {
					// 300-
					must = append(must, map[string]any{
						"range": map[string]any{
							"star": map[string]any{
								"gte": utils.CurrencyFloat2Int(start),
							},
						},
					})
				} else if start > 0 && end > 0 {
					must = append(must, map[string]any{
						"range": map[string]any{
							"star": map[string]any{
								"gte": utils.CurrencyFloat2Int(start),
								"lte": utils.CurrencyFloat2Int(end),
							},
						},
					})
				}
			}
		} else {
			buildStarCond()
		}

		if !strutil.IsBlank(cond.PriceRange) {
			ranges := strings.Split(cond.PriceRange, "-")
			if len(ranges) == 2 {
				var start float64
				var end float64
				start, _ = convertor.ToFloat(ranges[0])
				end, _ = convertor.ToFloat(ranges[1])
				if start == 0 && end > 0 {
					// -300
					must = append(must, map[string]any{
						"range": map[string]any{
							"avg_price": map[string]any{
								"lte": utils.CurrencyFloat2Int(end),
							},
						},
					})
				} else if start > 0 && end == 0 {
					// 300-
					must = append(must, map[string]any{
						"range": map[string]any{
							"avg_price": map[string]any{
								"gte": utils.CurrencyFloat2Int(start),
							},
						},
					})
				} else if start > 0 && end > 0 {
					must = append(must, map[string]any{
						"range": map[string]any{
							"avg_price": map[string]any{
								"gte": utils.CurrencyFloat2Int(start),
								"lte": utils.CurrencyFloat2Int(end),
							},
						},
					})
				}
			}
		}
	} else {
		buildStarCond()
		buildGeoCond(0)
	}

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": must,
			},
		},
		"sort": []map[string]any{
			{"_score": map[string]any{"order": "desc"}},
			{
				"_script": map[string]any{
					"type": "number",
					"script": map[string]any{
						"source": "Math.abs(doc['avg_price'].value - params.avgPrice)",
						"params": map[string]any{
							"avgPrice": avgPrice,
						},
					},
					"order": "asc", // Sort by closest to average price
				},
			},
			{"score": map[string]any{"order": "desc"}},
		},
		"size": 1,
	}

	res, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
	if err != nil || res.Hits.Total.Value == 0 {
		return
	}

	var ids []uint
	for _, hit := range res.Hits.Hits {
		ids = append(ids, hit.Source.ObjId)
	}
	if len(ids) == 0 {
		return
	}

	var hotel models.Hotel
	db.Where("id in ?", ids).Take(&hotel)
	//if len(list) == 0 {
	//	return
	//}
	//for _, hotel := range list {
	//	journey.Hotels = append(journey.Hotels, &beans.JourneyPoi{
	//		Name: hotel.Name,
	//		Id:   hotel.ID,
	//		Lat:  hotel.Lat,
	//		Lng:  hotel.Lng,
	//		Pic:  utils.StaticUrl(hotel.Pic),
	//		Type: constmap.PoiTypeHotel,
	//	})
	//}
	//hotel := list[0]
	timeline := beans.JTimeline{
		//Pics:   []string{utils.StaticUrl(hotel.Pic)},
		Title:    hotel.Name,
		Type:     constmap.JTimelineHotel,
		Time:     "住宿",
		ItemId:   hotel.ID,
		AvgPrice: hotel.AvgPrice,
		Poi: &beans.JourneyPoi{
			Name: hotel.Name,
			Id:   hotel.ID,
			Lat:  hotel.Lat,
			Lng:  hotel.Lng,
			//Pic:  utils.StaticUrl(hotel.Pic),
			Type: constmap.PoiTypeHotel,
		},
		Tags: make([]string, 0),
	}
	timeline.Tags = slice.FilterMap([]string{
		hotel.BrandName,
		hotel.BusinessArea,
		hotel.StarDesc,
	}, func(index int, item string) (string, bool) {
		return item, !strutil.IsBlank(item)
	})
	if !strutil.IsBlank(hotel.Pic) {
		timeline.Poi.Pic = utils.StaticUrl(hotel.Pic)
		timeline.Pics = append(timeline.Pics, timeline.Poi.Pic)
	}

	journey.Timeline = append(journey.Timeline, &timeline)
}

func fillScenicSpots(journey *beans.Journey, db *gorm.DB) {
	defer func() {
		for _, v := range journey.Timeline {
			if v.Type == constmap.JTimelineScene && v.ItemId == 0 {
				v.Type = constmap.JTimelineText
			}
		}
	}()
	if len(journey.WayPoints) == 0 {
		return
	}
	journey.ScenicSpots = make([]*beans.JourneyPoi, 0)

	tasks := parallel_task.NewPool(len(journey.Timeline))
	defer tasks.Release()

	_, hasScene := slice.FindBy(journey.Timeline, func(index int, item *beans.JTimeline) bool {
		return item.Type == constmap.JTimelineScene
	})

	for _, timeline := range journey.Timeline {
		if hasScene && timeline.Type == constmap.JTimelineScene || !hasScene { //有明确给出景点就用景点类型的条目,否则全部都走一遍ES匹配
			f := func(jTimeline *beans.JTimeline) func() error {
				return func() error {
					matchSceneTimeLine(journey, jTimeline, db)
					return nil
				}
			}
			tasks.AddTask(f(timeline))
		}
	}

	_ = tasks.Wait()

	timeTexts := typeset.NewTypeSet(false, "上午", "下午", "晚上")
	timelines := make([]*beans.JTimeline, 0)
	for _, timeline := range journey.Timeline {
		if timeline.Type == constmap.JTimelineScene && timeline.ItemId > 0 {
			journey.ScenicSpots = append(journey.ScenicSpots, timeline.Poi)
		}
		if timeline.Type == constmap.JTimelineText && strutil.IsBlank(timeline.Desc) && timeTexts.Has(timeline.Title) {
			continue
		}
		timelines = append(timelines, timeline)
	}
	journey.Timeline = timelines
}
