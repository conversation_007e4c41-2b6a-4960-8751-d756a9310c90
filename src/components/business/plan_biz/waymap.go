package plan_biz

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/concurrent_slice"
	"roadtrip-api/src/utils/parallel_task"
	"strings"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
)

func Waymap(ctx context.Context, in beans.PlanDetailReq, dayTasks [][]*beans.JourneyPoi) (*beans.WaymapOut, error) {

	// 不使用用户当前位置，使用首个景点作为起点
	in.Lng = 0
	in.Lat = 0

	type waymapPoint struct {
		beans.WaymapPoint
		KeyPoint bool //关键点
	}

	type waymapResp struct {
		beans.WaymapResp
		Polyline []waymapPoint `json:"polyline"`
	}

	var out = new(beans.WaymapOut)
	out.Routes = make([]beans.WaymapResp, 0)

	if len(dayTasks) == 0 {
		return out, nil
	}

	outRoutes := make([]waymapResp, 0)

	ckey := fmt.Sprintf(constmap.RKWayMap, in.AiReqid, in.PlanId)

	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, ckey, ""), constmap.TimeDur1m)
	defer my_cache.Remove(fmt.Sprintf(constmap.RKSpin, ckey, ""))

	if !in.DisableCache {
		if str, _ := my_cache.RedisClient().Get(ckey).Result(); str != "" {
			_ = json.Unmarshal([]byte(str), &out)
			return out, nil
		}
	}

	// 并发任务池
	tasks := parallel_task.NewPool(utils.If(config.IsProduction(), 5, 3))
	defer tasks.Release()
	ch := concurrent_slice.NewChan[waymapResp](len(dayTasks))

	var prevPoint string // 上一天的终点，用于连接
	for day, waypoints := range dayTasks {

		if len(waypoints) == 0 {
			continue
		}

		// 如果是第一天且没有初始位置，则使用用户当前位置
		if prevPoint == "" && day == 0 {
			if in.Lng > 0 && in.Lat > 0 {
				prevPoint = utils.JoinPoi(in.Lng, in.Lat)
			} else {
				prevPoint = utils.JoinPoi(waypoints[0].Lng, waypoints[0].Lat)
				waypoints = waypoints[1:]
			}
		}

		if len(waypoints) == 0 {
			continue
		}

		last := waypoints[len(waypoints)-1]
		to := utils.JoinPoi(last.Lng, last.Lat)
		if to == prevPoint {
			continue
		}

		var chunk []*beans.JourneyPoi
		if len(waypoints) > 1 {
			chunk = waypoints[:len(waypoints)-1]
		}

		// 提交任务
		tasks.AddTask(func(day int, from string, to string, points []string) func() error {
			return func() error {
				if from == "" || from == to {
					return nil
				}
				rsp, err := amap.Driving(from, to, points, "polyline,cost")
				if err != nil {
					return err
				}
				if rsp.Count == 0 {
					return utils.NewError(fmt.Errorf("无有效的线路"))
				}
				paths := rsp.Route.Paths[0]
				var polyline []waymapPoint

				var (
					lng       float64
					lat       float64
					distance  float64
					cost      float64
					keyPoints = make([]waymapPoint, 0)
				)
				for _, v := range append(points, from, to) {
					tmp := strings.Split(v, ",")
					lng, _ = convertor.ToFloat(tmp[0])
					lat, _ = convertor.ToFloat(tmp[1])
					keyPoints = append(keyPoints, waymapPoint{
						WaymapPoint: beans.WaymapPoint{lng, lat},
					})
				}
				for _, vv := range paths.Steps {
					slice.ForEach(strings.Split(vv.Polyline, ";"), func(index int, item string) {
						tmp := strings.Split(item, ",")
						lng, _ = convertor.ToFloat(tmp[0])
						lat, _ = convertor.ToFloat(tmp[1])
						_, ok := slice.FindBy(keyPoints, func(index int, w waymapPoint) bool {
							return utils.CalculateDistance(w.WaymapPoint[0], w.WaymapPoint[1], lng, lat) <= 0.3
						})
						polyline = append(polyline, waymapPoint{
							WaymapPoint: beans.WaymapPoint{lng, lat},
							KeyPoint:    ok,
						})
					})
					cost += vv.Cost.Tolls
				}
				distance += paths.Distance

				ch.Append(waymapResp{
					WaymapResp: beans.WaymapResp{
						Day:      day,
						Distance: distance,
						Cost:     cost,
					},
					Polyline: polyline,
				})
				return nil
			}
		}(day, prevPoint, to,
			slice.Map(chunk, func(i int, item *beans.JourneyPoi) string {
				return utils.JoinPoi(item.Lng, item.Lat)
			}),
		))

		prevPoint = to // 更新上一天的终点，供下一天使用
	}

	if err := tasks.Wait(); err != nil {
		my_logger.Errorf("行程地图生成失败", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "行程地图生成失败")
	}
	ch.ForEach(func(val waymapResp) {
		outRoutes = append(outRoutes, val)
	})
	slice.SortBy(outRoutes, func(a, b waymapResp) bool {
		return a.Day <= b.Day
	})

	sample := utils.If(in.PolylineSample > 0, in.PolylineSample, 0.05)

	sampleFunc := func(sl []waymapPoint, step int) []waymapPoint {
		if len(sl) <= step {
			return sl
		}
		ret := make([]waymapPoint, 0, 1+len(sl)/step)
		for i := 0; i < len(sl); i += step {
			ret = append(ret, sl[i])
			// 保留关键坐标
			for j := i; j < i+step; j++ {
				if j < len(sl)-1 {
					if sl[j].KeyPoint {
						ret = append(ret, sl[j])
					}
				}
			}
		}
		return ret
	}

	slice.ForEach(outRoutes, func(i int, item waymapResp) {
		if len(item.Polyline) == 0 {
			return
		}
		// 采样
		first, last := item.Polyline[0], item.Polyline[len(item.Polyline)-1]
		item.Polyline = sampleFunc(item.Polyline, int(math.Ceil(1/sample)))
		item.Polyline = utils.Unshift(item.Polyline, first) //至少包含起始、最末的点
		item.Polyline = append(item.Polyline, last)
		item.WaymapResp.Polyline = slice.Map(item.Polyline, func(index int, item waymapPoint) beans.WaymapPoint {
			return item.WaymapPoint
		})
		out.Routes = append(out.Routes, item.WaymapResp)
	})

	if !in.DisableCache {
		_ = my_cache.Set(ckey, convertor.ToString(out), constmap.TimeDur10m)
	}
	return out, nil
}
