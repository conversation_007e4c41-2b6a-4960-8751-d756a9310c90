package plan_biz

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
)

func matchSceneTimeLine(journey *beans.Journey, timeline *beans.JTimeline, db *gorm.DB) {
	// 去除内容里的省市文字
	zoneNameMap := convertor.ToMap(journey.WayPoints, func(t *beans.JourneyPoi) (string, string) {
		return normalizeZone(t.Name), ""
	})

	var content string
	if timeline.Type == constmap.JTimelineScene {
		content = timeline.Title
	} else {
		content = strutil.ReplaceWithMap(timeline.Title, zoneNameMap) + strutil.ReplaceWithMap(timeline.Desc, zoneNameMap)
	}

	must := []any{
		map[string]any{
			"term": map[string]any{
				"type": constmap.PoiTypeScenic,
			},
		},
		map[string]any{
			"term": map[string]any{
				"state": constmap.Enable,
			},
		},
		map[string]any{
			"term": map[string]any{
				"llm": constmap.Enable,
			},
		},
	}

	zoneIds := slice.Map(journey.WayPoints, func(index int, item *beans.JourneyPoi) uint {
		return item.Id
	})

	must = append(must, map[string]any{
		"terms": map[string]any{
			"zone_id": zoneIds,
		},
	}, map[string]any{
		"match": map[string]any{
			"name": map[string]any{
				"query": content,
			},
		},
	})

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": must,
			},
		},
		"sort": []map[string]any{
			{"_score": map[string]any{"order": "desc"}},
		},
		"size": 1,
	}

	res, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)

	if err != nil || res.Hits.Total.Value == 0 {
		return
	}
	var ids []uint
	for _, hit := range res.Hits.Hits {
		ids = append(ids, hit.Source.ObjId)
	}
	if len(ids) == 0 {
		return
	}
	var list []models.Scenic
	db.Where("id in ?", ids).Find(&list)

	for _, s := range list {
		poi := &beans.JourneyPoi{
			Name:  s.Name,
			Id:    s.ID,
			Lat:   s.Lat,
			Lng:   s.Lng,
			Price: s.CostText,
		}

		timeline.ItemId = s.ID

		timeline.Cate = slice.FilterMap(strings.Split(s.Cate, ","), func(index int, item string) (string, bool) {
			return item, !strutil.IsBlank(item)
		})
		timeline.Tags = slice.FilterMap(strings.Split(s.Tags, ","), func(index int, item string) (string, bool) {
			return item, !strutil.IsBlank(item)
		})
		if s.IsFree == constmap.Enable {
			timeline.Tags = utils.Unshift(timeline.Tags, "免费")
		} else {
			var prices beans.ScenicPrices
			_ = json.Unmarshal([]byte(s.ScenicPrices), &prices)
			timeline.AvgPrice = utils.CurrencyFloat2Int(prices.Adult.MonthlyPriceAvg)
		}
		timeline.Poi = poi
		timeline.Type = constmap.JTimelineScene
		timeline.Scene = BuildJTimelineScene(&s)

		if !strutil.IsBlank(s.Pic) {
			poi.Pic = utils.StaticUrl(s.Pic)
			timeline.Pics = append(timeline.Pics, poi.Pic)
		}

	}

}

func BuildJTimelineScene(s *models.Scenic) *beans.JTimelineScene {
	jscene := new(beans.JTimelineScene)
	jscene.IsFree = s.IsFree == constmap.Enable
	cost := beans.ScenicPlayCosts{}
	if s.PlayCosts != "" {
		_ = json.Unmarshal([]byte(s.PlayCosts), &cost)
	}
	jscene.CostTime = cost
	prices := beans.ScenicPrices{}
	if s.ScenicPrices != "" {
		_ = json.Unmarshal([]byte(s.ScenicPrices), &prices)
	}
	jscene.Prices = prices
	return jscene
}
