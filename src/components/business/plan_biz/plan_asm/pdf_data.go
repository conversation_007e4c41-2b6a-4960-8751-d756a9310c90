package plan_asm

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/function"
	"github.com/golang/freetype/truetype"
	"github.com/wcharczuk/go-chart"
	"image/png"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"roadtrip-api/src/assets"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/chromedp_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"strings"
	"sync"
	"text/template"
	"time"

	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PdfData struct {
	writer sync.Map
}

type PlanSection struct {
	Content beans.PlanSectionContent
	Section models.PlanSection
}

func NewPdfDataGenerator() *PdfData {
	return &PdfData{}
}

func (o *PdfData) watcherStart() *function.Watcher {
	w := function.NewWatcher()
	w.Start()
	return w
}

func (o *PdfData) watcherLog(w *function.Watcher, key string) {
	my_logger.Debugf(key, zap.Duration("timeCost", w.GetElapsedTime()))
	w.Reset()
}

func (o *PdfData) bingPic(key string) (string, error) {
	watcher := o.watcherStart()
	pic, err := chromedp_biz.BingPic(key)
	o.watcherLog(watcher, fmt.Sprintf("BingPic[%v]:%s", err, key))
	return pic, err
}

func (o *PdfData) GeneratePdfData(ctx context.Context, db *gorm.DB, planId uint) (*beans.PlanPdfData, error) {
	var result = &beans.PlanPdfData{
		TopColor: "#ffffff",
	}

	var plan = new(models.Plan)
	db.Preload("Sections").Joins("Ext").Take(&plan, planId)
	if plan.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
	}
	slice.SortBy(plan.Sections, func(a, b models.PlanSection) bool {
		return a.SectionSort < b.SectionSort
	})

	var promptOptions = new(beans.TravelPromptOptions)
	if plan.Ext.PromptOptions.Data != nil {
		promptOptions = plan.Ext.PromptOptions.Data
	}
	startDate := time.Now()
	if promptOptions.StartDate > 0 {
		startDate = time.Unix(promptOptions.StartDate, 0)
	}

	var primeZoneId uint
	var sections []PlanSection
	var zoneIds []uint
	var sceneIds []uint
	var hotelIds []uint
	for _, section := range plan.Sections {

		var sectionContent beans.PlanSectionContent
		if err := json.Unmarshal([]byte(section.Content), &sectionContent); err != nil {
			return nil, err
		}
		sections = append(sections, PlanSection{
			Content: sectionContent,
			Section: section,
		})
		zoneIds = append(zoneIds, sectionContent.PassingZones...)
		slice.ForEach(sectionContent.Timeline, func(index int, v *beans.JTimeline) {
			if v.ItemId > 0 {
				switch v.Type {
				case constmap.JTimelineScene:
					sceneIds = append(sceneIds, v.ItemId)
				case constmap.JTimelineHotel:
					hotelIds = append(hotelIds, v.ItemId)
				}
			}
		})
	}
	// 去重
	zoneIds = slice.Unique(zoneIds)
	sceneIds = slice.Unique(sceneIds)
	hotelIds = slice.Unique(hotelIds)

	if len(zoneIds) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "城市列表匹配失败")
	}
	if true { //匹配主要城市
		first := zoneIds[0]
		for i, v := range slice.ReverseCopy(zoneIds) {
			if v != first || i == 0 {
				primeZoneId = v
				break
			}
		}
	}
	var sceneMap = make(map[uint]*models.Scenic)
	var hotelMap = make(map[uint]*models.Hotel)
	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, 0)
	if len(sceneIds) > 0 {
		var scenes []*models.Scenic
		db.Preload("Pics").Find(&scenes, sceneIds)
		for _, v := range scenes {
			sceneMap[v.ID] = v
		}
	}
	if len(hotelIds) > 0 {
		var hotels []*models.Hotel
		db.Find(&hotels, hotelIds)
		for _, v := range hotels {
			hotelMap[v.ID] = v
		}
	}

	// ----------------- 基础查询完毕，调用其他服务拼装数据 ------------------

	slice.ForEach(zoneIds, func(index int, zoneId uint) {
		if zone, ok := zoneMap[zoneId]; ok {
			result.Zones = append(result.Zones, &beans.ZoneSpecialityData{
				ZoneId:   zoneId,
				ZoneName: utils.TrimCitySuffix(zone.Name),
			})
		}
	})

	var daysTimelines [][]*beans.JTimeline
	slice.ForEach(sections, func(index int, section PlanSection) {
		sec := &beans.PlanPdfSection{
			SectionTitle: section.Section.SectionTitle,
		}
		var dayTimelines []*beans.JTimeline
		slice.ForEach(section.Content.Timeline, func(index int, tl *beans.JTimeline) {
			dayTimelines = append(dayTimelines, tl)
			v := &beans.PlanPdfTimeline{
				Type:   tl.Type,
				ItemId: tl.ItemId,
				Title:  tl.Title,
				Desc:   tl.Desc,
				Time:   tl.Time,
			}

			if v.ItemId > 0 {
				if tl.Type == constmap.JTimelineScene {
					if scene, ok := sceneMap[tl.ItemId]; ok {
						v.Pics = append(v.Pics, utils.StaticUrl(scene.Pic))
						for _, p := range scene.Pics {
							if p.Main == constmap.Disable && p.Url != "" {
								v.Pics = append(v.Pics, utils.StaticUrl(p.Url))
							}
							if len(v.Pics) >= 3 {
								break
							}
						}
						v.Scene = &beans.PlanPdfSceneDistrict{
							Tags: tl.Tags,
						}
					}
				}
				if tl.Type == constmap.JTimelineHotel {
					if hotel, ok := hotelMap[tl.ItemId]; ok {
						v.Pics = append(v.Pics, utils.StaticUrl(hotel.Pic))
						v.BusinessArea = hotel.BusinessArea
					}
				}
			}

			sec.Timelines = append(sec.Timelines, v)
		})
		daysTimelines = append(daysTimelines, dayTimelines)
		result.Sections = append(result.Sections, sec)
	})

	watcher := o.watcherStart()
	wayMap, wayCost, distance, mapPic, err := GetStaticMap(ctx, plan, sections, sceneMap)
	o.watcherLog(watcher, "GetStaticMap")
	if err != nil {
		my_logger.Errorf("GetStaticMap", zap.Uint("PlanId", plan.ID), zap.Error(err))
	}
	my_logger.Debugf("GetStaticMap", zap.Float64("高速费", wayCost))

	planCost := CalcPlanCost(ctx, db, beans.PlanDetailReq{
		AiReqid:      plan.AiReqid,
		PlanId:       plan.ID,
		DisableCache: true,
	}, startDate, daysTimelines, wayMap)

	result.Fee = fmt.Sprintf("预计费用%.2f元/人", utils.CurrencyInt2Float(planCost.Total))

	tasks := parallel_task.NewPool(4)
	defer tasks.Release()

	var locker = new(sync.Mutex)

	//主要城市
	tasks.AddTask(func() error {
		watcher := o.watcherStart()
		err := o.DifyPrimeZone(ctx, locker, db, result, primeZoneId, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
		o.watcherLog(watcher, "DifyPrimeZone")
		return err
	})

	//特色景点
	tasks.AddTask(func() error {
		watcher := o.watcherStart()
		err := o.DifyPrimeScenes(ctx, locker, db, result, primeZoneId, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
		o.watcherLog(watcher, "DifyPrimeScenes")
		return err
	})

	//每日行程
	for i := range sections {
		i := i
		tasks.AddTask(func() error {
			watcher := o.watcherStart()
			err := o.DifySection(ctx, locker, db, result, i, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
			o.watcherLog(watcher, "DifySection")
			return err
		})
	}

	// 城市文化、特产
	zoneSpecialityMap := zone_biz.NewZoneBiz().GetSpeciality(db, slice.Map(result.Zones, func(index int, item *beans.ZoneSpecialityData) uint {
		return item.ZoneId
	}))
	for i, v := range result.Zones {
		zoneId := v.ZoneId
		if zoneSpeciality, ok := zoneSpecialityMap[zoneId]; ok {
			result.Zones[i] = zoneSpeciality
		}
		//tasks.AddTask(func() error {
		//	watcher := o.watcherStart()
		//	err := o.DifyZone(ctx, locker, db, result, zoneId, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
		//	o.watcherLog(watcher, "DifyZone")
		//	return err
		//})
	}

	if err = tasks.Wait(); err != nil {
		return nil, err
	}

	result.MapPic = utils.Base64Image(mapPic)

	if zone, ok := zoneMap[primeZoneId]; ok {
		result.TopPic = utils.StaticUrl(zone.Pic)
	} else {
		// 找不到城市主图就取第一个景点图片
		for _, v := range sections {
			if result.TopPic != "" {
				break
			}
			for _, vv := range v.Content.Timeline {
				if vv.ItemId > 0 && vv.Type == constmap.JTimelineScene {
					if scene, ok := sceneMap[vv.ItemId]; ok && scene.Pic != "" {
						result.TopPic = utils.StaticUrl(scene.Pic)
						break
					}
				}
			}
		}
	}
	if result.TopPic == "" {
		result.TopColor = "#000000"
	} else {
		if img, err := utils.GetPicFromURL(result.TopPic); err != nil {
			my_logger.Errorf("GetPicFromURL", zap.String("picUrl", result.TopPic), zap.Error(err))
		} else {
			color := utils.GetPicBottomHalfMainColor(img)
			if utils.ColorGt(color, "#808080") {
				result.TopColor = "#000000"
			} else {
				result.TopColor = "#ffffff"
			}
		}
	}

	base64Pic := func(u string, callback func(b string)) {
		tasks.AddTask(func() error {
			watcher := o.watcherStart()
			resp, err := http.DefaultClient.Get(u)
			o.watcherLog(watcher, fmt.Sprintf("base64Pic[%v]:%s", err, u))
			if err != nil {
				my_logger.Errorf("base64Pic", zap.String("url", u), zap.Error(err))
				return nil
			}
			defer resp.Body.Close()

			b, _ := io.ReadAll(resp.Body)
			callback(utils.Base64Image(b))
			return nil
		})
	}

	for i, v := range result.Sections {
		v := v
		for i2, v2 := range v.Timelines {
			v2 := v2
			if v2.ItemId > 0 {
				if v2.Type == constmap.JTimelineScene {
					if scene, ok := sceneMap[v2.ItemId]; ok {
						if v2.Scene != nil {
							if len(v2.Scene.Tags) == 0 {
								v2.Scene.Tags = strings.Split(scene.Tags, ",")
							}
							//填充最佳拍照位置图片
							for i3, v3 := range v2.Scene.BestCaptureSpots {
								v3 := v3
								if v3.Name != "" {
									// 创建索引副本以在闭包中使用
									if pic, err := o.bingPic(zoneMap[scene.ZoneId].Name + scene.Name + " " + v3.Name); err == nil {
										base64Pic(pic, func(b string) {
											// 直接使用索引更新结果，而不是修改循环变量
											v3.Pic = b
										})
									}
								}
								// 仍然在循环中保留这一行，确保即使图片下载失败也能将spot添加到列表中
								v2.Scene.BestCaptureSpots[i3] = v3
							}
						}
					}
				} else if v2.Type == constmap.JTimelineHotel {
					v2.Hotel = &beans.PlanPdfHotel{}
					if hotel, ok := hotelMap[v2.ItemId]; ok {
						v2.Hotel.HotelCost = hotel.CostText
						v2.Hotel.StarDesc = hotel.StarDesc
					}
				}
			} else {
				//text类型填充图片
				if v2.LocationDesc == "" {
					var ids = sections[i].Content.PassingZones
					if len(ids) > 0 {
						var zoneId = ids[len(ids)-1]
						if zone, ok := zoneMap[zoneId]; ok {
							v2.Pics = append(v2.Pics, utils.StaticUrl(zone.Pic))
						}
					}
				} else if pic, err := o.bingPic(v2.LocationDesc); err == nil {
					// 创建索引副本以在闭包中使用
					sectionIdx := i
					timelineIdx := i2
					base64Pic(pic, func(b string) {
						// 直接更新结果数组
						if len(result.Sections[sectionIdx].Timelines[timelineIdx].Pics) > 0 {
							result.Sections[sectionIdx].Timelines[timelineIdx].Pics = append(result.Sections[sectionIdx].Timelines[timelineIdx].Pics, b)
						} else {
							result.Sections[sectionIdx].Timelines[timelineIdx].Pics = []string{b}
						}
					})
				}
			}
			v.Timelines[i2] = v2
		}
		result.Sections[i] = v
	}
	for i, v := range result.Zones {
		v := v
		for i2, v2 := range v.Cultures {
			v2 := v2
			if pic, err := o.bingPic(zoneMap[v.ZoneId].Name + " " + v2.CultureName); err == nil {
				// 创建索引副本以在闭包中使用
				zoneIdx := i
				cultureIdx := i2
				currentCulture := v2 // 创建值的副本
				base64Pic(pic, func(b string) {
					// 直接更新结果数组
					currentCulture.CulturePic = b
					result.Zones[zoneIdx].Cultures[cultureIdx] = currentCulture
				})
			}
			v.Cultures[i2] = v2
		}
		for i2, v2 := range v.Foods {
			v2 := v2
			if pic, err := o.bingPic(zoneMap[v.ZoneId].Name + " " + v2.Name); err == nil {
				// 创建索引副本以在闭包中使用
				zoneIdx := i
				foodIdx := i2
				currentFood := v2 // 创建值的副本
				base64Pic(pic, func(b string) {
					// 直接更新结果数组
					currentFood.Pic = b
					result.Zones[zoneIdx].Foods[foodIdx] = currentFood
				})
			}
			v.Foods[i2] = v2
		}
		for i2, v2 := range v.Specialties {
			v2 := v2
			if pic, err := o.bingPic(zoneMap[v.ZoneId].Name + " " + v2.Name); err == nil {
				// 创建索引副本以在闭包中使用
				zoneIdx := i
				specialtyIdx := i2
				currentSpecialty := v2 // 创建值的副本
				base64Pic(pic, func(b string) {
					// 直接更新结果数组
					currentSpecialty.Pic = b
					result.Zones[zoneIdx].Specialties[specialtyIdx] = currentSpecialty
				})
			}
			v.Specialties[i2] = v2
		}
		result.Zones[i] = v
	}
	for i, v := range result.PrimeScenes {
		if scene, ok := sceneMap[v.SceneId]; ok && v.Pic == "" {
			v.Pic = utils.StaticUrl(scene.Pic)
		}
		result.PrimeScenes[i] = v
	}
	result.Subject = plan.Subject
	result.Subtitle = plan.Subtitle
	result.DayNum = plan.CostDay
	result.SceneNum = plan.SceneCnt
	result.Transport = utils.If(promptOptions.Transport == "", "自驾", promptOptions.Transport)
	result.TotalDistance = fmt.Sprintf("总里程约%.1f公里", distance)
	if qr, err := business.EncodeQrCode(utils.WxMiniPageUrl("pages/details/details", &url.Values{
		"plan_id": []string{convertor.ToString(plan.ID)},
	}), 300, 300); err == nil {
		var buf bytes.Buffer
		_ = png.Encode(&buf, qr)
		result.Qrcode = utils.Base64Image(buf.Bytes())
	}

	if err = tasks.Wait(); err != nil {
		return nil, err
	}

	//新增预算明细
	var cost = models.PlanCostDetail{}
	if err := db.Where(models.PlanCostDetail{AiReqid: plan.AiReqid, State: constmap.PlanCostDetailStateDone}).Take(&cost).Error; err == nil {
		result.CostDetail = cost.Cost.Data
	}

	return result, nil
}

func (o *PdfData) BuildPromptZone(db *gorm.DB, zoneId uint, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) string {
	key := fmt.Sprintf("zone_%d", zoneId)
	o.write(key, "城市:", zoneMap[zoneId].Name)
	return o.loadWriter(key).String()
}

func (o *PdfData) DifySection(ctx context.Context, locker *sync.Mutex, db *gorm.DB, result *beans.PlanPdfData, sectionIdx int, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) error {

	prompt := o.BuildPromptSection(db, sectionIdx, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
	res, err := my_dify.PlanPdfSection(ctx, db, prompt, result.Sections[sectionIdx])
	if err != nil {
		return errors.Wrap(err, "DifySection")
	}

	locker.Lock()
	defer locker.Unlock()

	result.Sections[sectionIdx] = &res.Section
	return nil
}

func (o *PdfData) BuildPromptSection(db *gorm.DB, sectionIdx int, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) string {
	key := fmt.Sprintf("section_%d", sectionIdx)
	section := sections[sectionIdx]
	o.write(key, "【当前行程内容】")
	o.write(key, "标题：", section.Section.SectionTitle)
	o.write(key, "主题：", section.Section.SectionSubject)
	o.write("【时间线】")
	for i, v := range section.Content.Timeline {
		o.write(key, fmt.Sprintf("【内容%d】", i+1))
		o.write(key, "类型:", string(v.Type))
		o.write(key, "标题:", v.Title)
		o.write(key, "描述:", v.Desc)
		o.write(key, "时间:", v.Time)
		o.write(key, "ID:", convertor.ToString(v.ItemId))
	}
	return o.loadWriter(key).String()
}

func (o *PdfData) DifyPrimeScenes(ctx context.Context, locker *sync.Mutex, db *gorm.DB, result *beans.PlanPdfData, primeZoneId uint, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) error {

	prompt, jsonInput := o.BuildPromptPrimeScenes(db, primeZoneId, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost)
	res, err := my_dify.PlanPdfPrimeScenes(ctx, db, prompt, jsonInput)
	if err != nil {
		return errors.Wrap(err, "DifyPrimeScenes")
	}

	locker.Lock()
	defer locker.Unlock()

	result.PrimeScenes = res.Scenes
	return nil
}

func (o *PdfData) BuildPromptPrimeScenes(db *gorm.DB, primeZoneId uint, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) (string, any) {
	key := "primeScenes"
	o.write(key, "【所有景点如下】")
	jsonInput := make([]beans.PlanPdfPrimeScene, 0)
	slice.ForEach(sections, func(index int, section PlanSection) {
		slice.ForEach(section.Content.Timeline, func(index int, tl *beans.JTimeline) {
			if tl.ItemId > 0 && tl.Type == constmap.JTimelineScene {
				if scene, ok := sceneMap[tl.ItemId]; ok {
					o.write(key, fmt.Sprintf("【城市:%s】【景点ID:%d】%s", utils.TrimCitySuffix(zoneMap[scene.ZoneId].Name), scene.ID, scene.Name))
					jsonInput = append(jsonInput, beans.PlanPdfPrimeScene{
						SceneId:   scene.ID,
						SceneName: scene.Name,
					})
				}
			}
		})
	})

	return o.loadWriter(key).String(), jsonInput
}

func (o *PdfData) DifyPrimeZone(ctx context.Context, locker *sync.Mutex, db *gorm.DB, result *beans.PlanPdfData, primeZoneId uint, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) error {
	res, err := my_dify.PlanPdfPrimeZone(ctx, db, o.BuildPromptPrimeZone(db, primeZoneId, plan, promptOptions, sections, zoneMap, sceneMap, hotelMap, wayCost), nil)
	if err != nil {
		return errors.Wrap(err, "DifyPrimeZone")
	}

	locker.Lock()
	defer locker.Unlock()

	result.PrimeZone = res.PrimeZone
	result.Attentions = res.Attentions
	return nil
}

func (o *PdfData) BuildPromptPrimeZone(db *gorm.DB, primeZoneId uint, plan *models.Plan, promptOptions *beans.TravelPromptOptions,
	sections []PlanSection, zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel, wayCost float64) string {
	key := "primeZone"
	if primeZoneId > 0 {
		if zone, ok := zoneMap[primeZoneId]; ok {
			o.write(key, "【主要城市】")
			var name = []string{zone.Name}
			for zone.Parent != nil {
				zone = zone.Parent
				name = append([]string{zone.Name}, name...)
			}
			o.write(key, strings.Join(name, ""))
		}
	}
	return o.loadWriter(key).String()
}

func (o *PdfData) loadWriter(key string) *strings.Builder {
	if _, ok := o.writer.Load(key); !ok {
		o.writer.Store(key, &strings.Builder{})
	}
	val, _ := o.writer.Load(key)
	bd, _ := val.(*strings.Builder)
	return bd
}
func (o *PdfData) write(key string, strs ...string) {
	bd := o.loadWriter(key)
	for _, str := range strs {
		bd.WriteString(str)
	}
	bd.WriteByte('\n')
}

type tmpLabel struct {
	Content   string //标签内容，字符最大数目为15
	Font      int    //0：微软雅黑；1：宋体；2：Times New Roman;3：Helvetica
	Bold      int    //0：非粗体；1：粗体
	FontSize  int    //字体大小，可选值[1,72]
	FontColor string //字体颜色，取值范围：[0x000000, 0xffffff]
	BgColor   string //背景色，取值范围：[0x000000, 0xffffff]
	Points    []beans.WaymapPoint
}

func newTmpLabel() tmpLabel {
	v := tmpLabel{}
	v.Font = -1
	v.Bold = -1
	return v
}

func (o tmpLabel) BuildLabel() string {
	var str strings.Builder
	if o.Content != "" {
		str.WriteString(o.Content)
	}
	str.WriteByte(',')
	if o.Font > -1 {
		str.WriteString(fmt.Sprintf("%d", o.Font))
	}
	str.WriteByte(',')
	if o.Bold > -1 {
		str.WriteString(fmt.Sprintf("%d", o.Bold))
	}
	str.WriteByte(',')
	if o.FontSize > 0 {
		str.WriteString(fmt.Sprintf("%d", o.FontSize))
	}
	str.WriteByte(',')
	if o.FontColor != "" {
		str.WriteString(o.FontColor)
	}
	str.WriteByte(',')
	if o.BgColor != "" {
		str.WriteString(o.BgColor)
	}
	str.WriteByte(':')
	slice.ForEach(o.Points, func(index int, item beans.WaymapPoint) {
		str.WriteString(utils.JoinPoi(item[0], item[1]))
		if index < len(o.Points)-1 {
			str.WriteByte(';')
		}
	})
	return str.String()
}

type tmpMarker struct {
	Size   string //small,mid,large
	Color  string //0xFFFF00
	Label  string
	PicUrl string //自定义图片，忽略其他样式配置
	Points []beans.WaymapPoint
}

func newTmpMarker() tmpMarker {
	return tmpMarker{}
}

func (o tmpMarker) BuildMarker() string {
	var str strings.Builder
	if o.PicUrl != "" {
		str.WriteString(fmt.Sprintf("-1,%s,0", o.PicUrl))
	} else {
		if o.Size != "" {
			str.WriteString(o.Size)
		}
		str.WriteByte(',')
		if o.Color != "" {
			str.WriteString(o.Color)
		}
		str.WriteByte(',')
		if o.Label != "" {
			str.WriteString(o.Label)
		}
	}
	str.WriteByte(':')
	slice.ForEach(o.Points, func(index int, item beans.WaymapPoint) {
		str.WriteString(utils.JoinPoi(item[0], item[1]))
		if index < len(o.Points)-1 {
			str.WriteByte(';')
		}
	})
	return str.String()
}

type tmpPath struct {
	Style struct {
		Weight           int     //2-15
		Color            string  //0xFFFF00
		Transparency     float64 //0-1
		Fillcolor        string  //0xFFFF00 多边形的填充颜色，此值不为空时折线封闭成多边形。
		FillTransparency float64 //0-1 填充面透明度
	}
	Points []beans.WaymapPoint
}

func newTmpPath() tmpPath {
	v := tmpPath{}
	v.Style.FillTransparency = -1
	v.Style.Transparency = -1
	return v
}

func (o tmpPath) BuildPath() string {
	var str strings.Builder
	if o.Style.Weight > 0 {
		str.WriteString(convertor.ToString(o.Style.Weight))
	}
	str.WriteByte(',')
	if o.Style.Color != "" {
		str.WriteString(o.Style.Color)
	}
	str.WriteByte(',')
	if o.Style.Transparency > -1 {
		str.WriteString(fmt.Sprintf("%.2f", o.Style.Transparency))
	}
	str.WriteByte(',')
	if o.Style.Fillcolor != "" {
		str.WriteString(o.Style.Fillcolor)
	}
	str.WriteByte(',')
	if o.Style.FillTransparency > -1 {
		str.WriteString(fmt.Sprintf("%.2f", o.Style.FillTransparency))
	}
	str.WriteByte(':')
	slice.ForEach(o.Points, func(index int, item beans.WaymapPoint) {
		str.WriteString(utils.JoinPoi(item[0], item[1]))
		if index < len(o.Points)-1 {
			str.WriteByte(';')
		}
	})
	return str.String()
}
func GetStaticMap(ctx context.Context, plan *models.Plan, sections []PlanSection, sceneMap map[uint]*models.Scenic) (*beans.WaymapOut, float64, float64, []byte, error) {
	// 按天分组提取 waypoints
	var dayTasks = make([][]*beans.JourneyPoi, 0)
	slice.ForEach(sections, func(day int, section PlanSection) {
		var waypoints []*beans.JourneyPoi
		slice.ForEach(section.Content.Timeline, func(_ int, timeline *beans.JTimeline) {
			if timeline.Type == constmap.JTimelineScene || timeline.Type == constmap.JTimelineHotel {
				waypoints = append(waypoints, timeline.Poi)
			}
		})
		if len(waypoints) == 0 {
			return
		}

		dayTasks = append(dayTasks, waypoints)
	})
	if len(dayTasks) == 0 {
		return nil, 0, 0, nil, utils.NewErrorStr(constmap.ErrorSystem, "没有可绘制的地图")
	}
	way, err := plan_biz.Waymap(ctx, beans.PlanDetailReq{
		AiReqid:      plan.AiReqid,
		PlanId:       plan.ID,
		DisableCache: true, //不走缓存
	}, dayTasks)
	if err != nil {
		return nil, 0, 0, nil, err
	}
	pathColors := []string{"0x1890FF", "0xFACA14"}

	var cost float64
	var distance float64
	paths := slice.Map(way.Routes, func(index int, item beans.WaymapResp) string {
		cost += item.Cost
		distance += item.Distance
		v := newTmpPath()
		v.Points = item.Polyline
		v.Style.Weight = 10
		v.Style.Color = pathColors[index%2]
		return v.BuildPath()
	})

	var markers []string
	//slice.ForEach(sections, func(index int, item PlanSection) {
	//	slice.ForEach(item.Content.Timeline, func(_ int, tl *beans.JTimeline) {
	//		if tl.Type == constmap.JTimelineScene && tl.ItemId > 0 {
	//			if scene, ok := sceneMap[tl.ItemId]; ok {
	//				v := newTmpMarker()
	//				v.Points = []beans.WaymapPoint{
	//					{scene.Lng, scene.Lat},
	//				}
	//				markers = append(markers, v.BuildMarker())
	//			}
	//		}
	//	})
	//})

	req := &go_amap.StaticMapRequest{
		Size:    "790*273",
		Scale:   2,
		Paths:   strings.Join(paths, "|"),
		Markers: strings.Join(markers, "|"),
	}
	b, err := amap.StaticMap(req)
	// 转为公里
	return way, cost, distance / 1000, b, err
}

func RenderPlanHtml(tx *gorm.DB, plan *models.Plan) (*bytes.Buffer, error) {
	funcMap := template.FuncMap{
		"join": strings.Join,
		"divide": func(a, b interface{}) float64 {
			a1, _ := convertor.ToFloat(a)
			b1, _ := convertor.ToFloat(b)
			return a1 / b1
		},
		"sub": func(a, b interface{}) int64 {
			a1, _ := convertor.ToInt(a)
			b1, _ := convertor.ToInt(b)
			return a1 - b1
		},
		"add": func(a, b interface{}) float64 {
			a1, _ := convertor.ToFloat(a)
			b1, _ := convertor.ToFloat(b)
			return a1 + b1
		},
		"format_money": func(value interface{}) string {
			f, _ := convertor.ToFloat(value)
			return utils.FormatMoney(f)
		},
	}
	var tmpl *template.Template
	var err error

	data := &beans.PlanPdfData{}

	// 先检查当前工作目录是否存在src/assets/pdf目录，如果存在则优先读取该目录下的文件
	if _, err = os.Stat("src/assets/pdf"); err == nil {
		tmpl, err = template.New("").Funcs(funcMap).ParseGlob("src/assets/pdf/*.gohtml")
	} else {
		tmpl, err = template.New("").Funcs(funcMap).ParseFS(assets.PdfFs, "pdf/*.gohtml")
	}
	//tmpl, err = template.New("").Funcs(funcMap).ParseFS(assets.PdfFs, "pdf/*.gohtml")

	if err != nil {
		return nil, err
	}

	cacheKey := fmt.Sprintf("plan_pdf_%d", plan.ID)
	if !my_cache.Get(cacheKey, data) {
		data, err = NewPdfDataGenerator().GeneratePdfData(context.Background(), tx, plan.ID)

		if err != nil {
			return nil, err
		}

		_ = my_cache.SetDefault(cacheKey, data)
	}

	if err != nil {
		return nil, err
	}

	if buf, err := assets.PdfFs.ReadFile("pdf/main.css"); err == nil {
		data.CssStyle = string(buf)
	} else {
		my_logger.Errorf("pdf css read conf file", zap.Error(err))
	}

	if config.Config.App.Debug {
		if buf, err := os.ReadFile("src/assets/pdf/main.css"); err == nil {
			data.CssStyle = string(buf)
		}
	}

	//data.TopPic = strings.ReplaceAll(data.TopPic, "resources", "yiban/static/resources")
	if len(data.Zones) > 0 {
		data.Zones = data.Zones[len(data.Zones)-1:]
	}

	if data.CostDetail != nil {
		if len(data.CostDetail.Specialities) > 0 {
			data.CostTotalSpecialities = slice.ReduceBy(data.CostDetail.Specialities, int64(0), func(index int, item *beans.PlanCostZoneSpecialty, agg int64) int64 {
				return agg + item.Fee
			})
		}
		data.ConstTotalTraffic = data.CostDetail.Fuel + data.CostDetail.Highway

		hotelValue, _ := convertor.ToFloat(data.CostDetail.Hotel)
		foodValue, _ := convertor.ToFloat(data.CostDetail.Food)
		ticketValue, _ := convertor.ToFloat(data.CostDetail.Ticket)
		specialitiesValue, _ := convertor.ToFloat(data.CostTotalSpecialities)
		trafficValue, _ := convertor.ToFloat(data.ConstTotalTraffic)
		values := []chart.Value{
			{
				Value: hotelValue,
				Label: "住宿",
			},
			{
				Value: foodValue,
				Label: "餐饮费用",
			},
			{
				Value: ticketValue,
				Label: "门票",
			},
			{
				Value: specialitiesValue,
				Label: "其他",
			},
			{
				Value: trafficValue,
				Label: "交通费用",
			},
		}
		//values = values[:1]

		//绘制饼状图
		pie := chart.PieChart{
			Width:  800,
			Height: 400,
			Values: values,
			Canvas: chart.Style{
				Padding: chart.Box{
					Left:   20,
					Top:    20,
					Bottom: 20,
					Right:  20,
				},
			},
		}

		fontPath := filepath.Join(config.Config.App.FontDir, config.Config.App.DefaultFont)
		if cnt, err := os.ReadFile(fontPath); err == nil {
			if t, err := truetype.Parse(cnt); err == nil {
				pie.Font = t
			}
		}

		pieBuf := bytes.NewBuffer([]byte{})
		if err = pie.Render(chart.PNG, pieBuf); err == nil {
			data.ChartCostImage = "data:image/png;base64," + base64.StdEncoding.EncodeToString(pieBuf.Bytes())
		}
	}

	buf := bytes.NewBuffer([]byte{0})
	if err := tmpl.ExecuteTemplate(buf, "index.gohtml", data); err != nil {
		return nil, err
	}

	return buf, nil
}
