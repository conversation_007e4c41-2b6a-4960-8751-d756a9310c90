package plan_asm

import (
	"context"
	"encoding/json"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/hotel_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

type SavePlanReq struct {
	UserId uint
	State  int
	PlanId uint
}

// 拼装models.plan结构及关联的models.PlanSection
func BuildSubmitPlan(db *gorm.DB, in SavePlanReq, chatMsg *ai.ChatCompletionResponse) (*models.Plan, error) {
	var (
		err           error
		journeyData   *beans.RenderTravelJourney
		provider      string
		promptOptions *beans.TravelPromptOptions
		plan          = new(models.Plan)
		sections      []models.PlanSection
	)
	if in.PlanId > 0 {
		var curPlan = new(models.Plan)
		if db.Preload("Sections").Preload("Ext").Where("id=?", in.PlanId).Take(&curPlan).Error != nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "行程不存在")
		}
		// 从现有计划中提取数据
		provider = curPlan.AiProvider

		// 解析现有的 PromptOptions
		if curPlan.Ext.PromptOptions.Data != nil {
			promptOptions = curPlan.Ext.PromptOptions.Data
		}

		// 重建 journeyData 结构
		journeyData = &beans.RenderTravelJourney{
			BgPic:     utils.StaticUrl(curPlan.BgPic),
			Title:     curPlan.Subject,
			Subtitle:  curPlan.Subtitle,
			Notice:    curPlan.Notice,
			FitFor:    curPlan.FitFor,
			FitMonths: curPlan.FitMonths,
			List:      make([]*beans.Journey, 0, len(curPlan.Sections)),
		}

		// 从现有 sections 重建 journeyData.List
		for _, section := range curPlan.Sections {
			var sectionContent beans.PlanSectionContent
			if section.Content != "" {
				if err = json.Unmarshal([]byte(section.Content), &sectionContent); err != nil {
					continue
				}
			}

			journeyDay := &beans.Journey{
				Title:      section.SectionTitle,
				Content:    section.SectionDesc,
				Subject:    section.SectionSubject,
				Scenic:     sectionContent.Scenic,
				Speciality: sectionContent.Speciality,
				Timeline:   sectionContent.Timeline,
			}

			// 重建 WayPoints
			for _, zoneId := range sectionContent.PassingZones {
				journeyDay.WayPoints = append(journeyDay.WayPoints, &beans.JourneyPoi{Id: zoneId})
			}

			// 重建 ScenicSpots
			for _, sceneId := range sectionContent.RecScenes {
				journeyDay.ScenicSpots = append(journeyDay.ScenicSpots, &beans.JourneyPoi{Id: sceneId})
			}

			journeyData.List = append(journeyData.List, journeyDay)
		}

		if journeyData == nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "行程内容已失效.")
		}

		plan = &models.Plan{
			BgPic:             utils.UnWrapStaticUrl(journeyData.BgPic),
			UserId:            utils.If(curPlan.UserId == in.UserId, in.UserId, 0),
			Subject:           journeyData.Title,
			Subtitle:          journeyData.Subtitle,
			Notice:            journeyData.Notice,
			FitFor:            journeyData.FitFor,
			FitMonths:         journeyData.FitMonths,
			AiReqid:           curPlan.AiReqid,
			AiProvider:        provider,
			CostDay:           len(journeyData.List),
			IntegralCostState: curPlan.IntegralCostState,
			IntegralCost:      curPlan.IntegralCost,
			State:             utils.If(in.State == constmap.Enable, constmap.Enable, constmap.Disable),
		}
		if promptOptions != nil {
			plan.From = promptOptions.From
			plan.To = promptOptions.To
		}
		var sceneCnt int
		for i, v := range journeyData.List {
			var sectionContent beans.PlanSectionContent
			sectionContent.Scenic = v.Scenic
			sectionContent.Speciality = v.Speciality
			sectionContent.Timeline = v.Timeline
			slice.ForEach(v.Timeline, func(_ int, item *beans.JTimeline) {
				if item.Type == constmap.JTimelineScene {
					sceneCnt++
				}
			})
			for _, vv := range v.WayPoints {
				sectionContent.PassingZones = append(sectionContent.PassingZones, vv.Id)
			}
			for _, vv := range v.ScenicSpots {
				sectionContent.RecScenes = append(sectionContent.RecScenes, vv.Id)
			}
			b, _ := json.Marshal(sectionContent)
			sections = append(sections, models.PlanSection{
				SectionSort:    i + 1,
				SectionTitle:   v.Title,
				SectionDesc:    v.Content,
				SectionSubject: v.Subject,
				Content:        string(b),
			})
		}
		plan.SceneCnt = sceneCnt
		if promptOptions != nil {
			plan.Ext.PromptOptions.Data = promptOptions
		}
		plan.Sections = sections
	} else if chatMsg != nil {
		a := ai.NewByProvider(chatMsg.Provider)
		if a == nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "请求内容已失效")
		}
		journeyData, err = plan_biz.ParseAIJourney(db, a, chatMsg)
		if err != nil {
			return nil, err
		}
		if chatMsg.PromptOptions != nil {
			promptOptions = chatMsg.PromptOptions
		} else if promptOptions, err = a.ParseOptions(chatMsg.Content); err != nil {
			my_logger.Errorf("ai.ParseOptions", zap.String("aiReqid", chatMsg.RequestId), zap.Error(err))
		}

		provider = chatMsg.Provider

		if journeyData == nil {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "请求内容已失效.")
		}

		plan = &models.Plan{
			BgPic:      utils.UnWrapStaticUrl(journeyData.BgPic),
			UserId:     in.UserId,
			Subject:    journeyData.Title,
			Subtitle:   journeyData.Subtitle,
			Notice:     journeyData.Notice,
			FitFor:     journeyData.FitFor,
			FitMonths:  journeyData.FitMonths,
			AiReqid:    chatMsg.RequestId,
			AiProvider: provider,
			CostDay:    len(journeyData.List),
			// 旧缓存是0，当作已扣减
			IntegralCostState: utils.If(chatMsg.IntegralCostState == 0 || chatMsg.IntegralCostState == constmap.Enable, constmap.Enable, constmap.Disable),
			IntegralCost:      chatMsg.IntegralCost,
			State:             utils.If(in.State == constmap.Enable, constmap.Enable, constmap.Disable),
		}
		if promptOptions != nil {
			plan.From = promptOptions.From
			plan.To = promptOptions.To
		}
		var sceneCnt int
		for i, v := range journeyData.List {
			var sectionContent beans.PlanSectionContent
			sectionContent.Scenic = v.Scenic
			sectionContent.Speciality = v.Speciality
			sectionContent.Timeline = v.Timeline
			slice.ForEach(v.Timeline, func(_ int, item *beans.JTimeline) {
				if item.Type == constmap.JTimelineScene {
					sceneCnt++
				}
			})
			for _, vv := range v.WayPoints {
				sectionContent.PassingZones = append(sectionContent.PassingZones, vv.Id)
			}
			for _, vv := range v.ScenicSpots {
				sectionContent.RecScenes = append(sectionContent.RecScenes, vv.Id)
			}
			b, _ := json.Marshal(sectionContent)
			sections = append(sections, models.PlanSection{
				SectionSort:    i + 1,
				SectionTitle:   v.Title,
				SectionDesc:    v.Content,
				SectionSubject: v.Subject,
				Content:        string(b),
			})
		}
		plan.SceneCnt = sceneCnt
		if promptOptions != nil {
			plan.Ext.PromptOptions.Data = promptOptions
		}
		plan.Sections = sections
	} else {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "行程内容已失效")
	}
	return plan, nil
}

func GetCostIntegralAmount(db *gorm.DB, isReplan bool) (int64, error) {
	cfg := constmap.SysConfigPlanCostAmount
	if isReplan {
		cfg = constmap.SysConfigReplanCostAmount
	}
	if val, err := sys_config_biz.GetConfig(db, cfg); err != nil {
		return 0, err
	} else {
		return val.(int64), nil
	}
}

// 扣除积分
func CostIntegral(tx *gorm.DB, userId uint, costAmount int64, aiReqid string) (*models.AccountTransaction, error) {
	if costAmount == 0 {
		return nil, nil
	}
	trans, err := account_biz.CreateAccountTransaction(tx, userId, constmap.CurrencyIntegral, int(costAmount),
		constmap.AccountLogDecrAmount, constmap.AccountLogSubPlanCost, "")
	if err != nil {
		return nil, err
	}
	if err = account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, &beans.AccountLogExtra{
		AiReqid: aiReqid,
	}); err != nil {
		return nil, err
	}
	return trans, nil
}

func SubmitPlan(tx *gorm.DB, plan *models.Plan) error {
	var err error
	if err = tx.Omit(clause.Associations).Create(plan).Error; err != nil {
		return err
	}
	plan.Ext.PlanId = plan.ID
	if err = tx.Omit(clause.Associations).Create(&plan.Ext).Error; err != nil {
		return err
	}
	for i, v := range plan.Sections {
		v.ID = 0
		v.PlanId = plan.ID
		plan.Sections[i] = v
	}
	if err = tx.Omit(clause.Associations).Create(&plan.Sections).Error; err != nil {
		return err
	}
	return nil
}

// SubmitPlans 批量插入多个Plan记录，提高性能
func SubmitPlans(tx *gorm.DB, plans []*models.Plan) error {
	if len(plans) == 0 {
		return nil
	}

	// 批量插入Plans
	if err := tx.Omit(clause.Associations).Create(plans).Error; err != nil {
		return err
	}

	// 准备批量插入的PlanExts和PlanSections
	var planExts []models.PlanExt
	var planSections []models.PlanSection

	for _, plan := range plans {
		// 设置PlanExt的PlanId
		plan.Ext.PlanId = plan.ID
		planExts = append(planExts, plan.Ext)

		// 设置PlanSections的PlanId
		for _, section := range plan.Sections {
			section.ID = 0
			section.PlanId = plan.ID
			planSections = append(planSections, section)
		}
	}

	// 批量插入PlanExts
	if len(planExts) > 0 {
		if err := tx.Omit(clause.Associations).Create(&planExts).Error; err != nil {
			return err
		}
	}

	// 批量插入PlanSections
	if len(planSections) > 0 {
		if err := tx.Omit(clause.Associations).Create(&planSections).Error; err != nil {
			return err
		}
	}

	return nil
}

// 计算行程费用
func CalcPlanCost(ctx context.Context, db *gorm.DB, in beans.PlanDetailReq, startDate time.Time, daysTimelines [][]*beans.JTimeline, wayMap *beans.WaymapOut) *beans.PlanCost {
	if startDate.IsZero() {
		startDate = time.Now()
	}
	out := &beans.PlanCost{}
	hotelIds := make([]uint, 0)
	sceneIds := make([]uint, 0)
	dayTasks := make([][]*beans.JourneyPoi, 0)
	for _, dayTimelines := range daysTimelines {
		waypoints := make([]*beans.JourneyPoi, 0)
		for _, timeline := range dayTimelines {

			if timeline.Type == constmap.JTimelineScene {
				sceneIds = append(sceneIds, timeline.ItemId)
			} else if timeline.Type == constmap.JTimelineHotel {
				hotelIds = append(hotelIds, timeline.ItemId)
			}

			if timeline.Type == constmap.JTimelineScene || timeline.Type == constmap.JTimelineHotel {
				waypoints = append(waypoints, timeline.Poi)
			}
		}
		if len(waypoints) == 0 {
			continue
		}
		dayTasks = append(dayTasks, waypoints)
	}

	if wayMap == nil {
		if w, err := plan_biz.Waymap(ctx, in, dayTasks); err != nil {
			my_logger.Errorf("waymap error", zap.Error(err))
		} else {
			wayMap = w
		}
	}
	if wayMap != nil {
		var (
			distance float64
			cost     float64
		)
		slice.ForEach(wayMap.Routes, func(index int, item beans.WaymapResp) {
			distance += item.Distance
			cost += item.Cost
		})
		out.Distance += distance
		out.Highway += utils.CurrencyFloat2Int(cost)
	}

	sceneMap := scenics_biz.LoadScenes(db, sceneIds, false)
	month := startDate.Month()
	hotelPriceMap := hotel_biz.GetAvgPrice(db, datetime.BeginOfMonth(startDate), datetime.EndOfMonth(startDate), hotelIds)

	for _, dayTimelines := range daysTimelines {
		for _, timeline := range dayTimelines {
			if timeline.Type == constmap.JTimelineScene {
				if scene, ok := sceneMap[timeline.ItemId]; ok {
					prices := scenics_biz.UnmarshalPrices(scene.ScenicPrices)
					var matched bool
					for _, v := range prices.Adult.MonthlyPrices {
						if v.Month == int(month) {
							out.Ticket += utils.CurrencyFloat2Int(v.Price)
							matched = true
							break
						}
					}
					if !matched {
						out.Ticket += utils.CurrencyFloat2Int(prices.Adult.MonthlyPriceAvg)
					}
				}
			} else if timeline.Type == constmap.JTimelineHotel {
				if price, ok := hotelPriceMap[timeline.ItemId]; ok {
					out.Hotel += price
				}
			}
		}
	}

	out.Total = out.Highway + out.Ticket + out.Hotel
	return out
}
