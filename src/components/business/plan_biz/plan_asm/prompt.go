package plan_asm

import (
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"strconv"
	"strings"
	"time"
)

func BuildPrompt(in *beans.TravelPromptOptions, db *gorm.DB) string {
	var prompt strings.Builder
	prompt.WriteString(fmt.Sprintf("从%s到%s\n", in.From, in.To))
	prompt.WriteString("预算：灵活安排\n")
	if in.Interest != "" {
		var interests []string
		for _, v := range strings.Split(in.Interest, ",") {
			interests = append(interests, constmap.TravelInterest(v).ToString())
		}
		prompt.WriteString(fmt.Sprintf("游玩主题：%s\n", strings.Join(interests, "、")))
	}
	if in.Strength != "" {
		prompt.WriteString(fmt.Sprintf("节奏强度：%s\n", in.Strength))
	}
	if in.Accommodation != "" {
		prompt.WriteString(fmt.Sprintf("住宿要求：%s\n", in.Accommodation))
	}
	if in.Days != "" && in.Days != "0" {
		prompt.WriteString(fmt.Sprintf("行程天数：%s\n", in.Days))
	} else {
		prompt.WriteString("行程天数：至少3天\n")
	}
	if in.ExtraScenes != "" {
		var (
			sceneIds  []int
			sceneList []models.Scenic
		)
		for _, v := range strutil.SplitAndTrim(in.ExtraScenes, ",", " ") {
			if sceneId, _ := strconv.Atoi(v); sceneId > 0 {
				sceneIds = append(sceneIds, sceneId)
			}
		}
		if len(sceneIds) > 0 {
			db.Find(&sceneList, sceneIds)
		}
		var zoneIds = make([]uint, 0, len(sceneList))
		for _, v := range sceneList {
			zoneIds = append(zoneIds, v.ZoneId)
		}
		zoneMap := zone_biz.NewZoneBiz().GetZoneNamesKebabCase(db, "", zoneIds)
		if len(sceneList) > 0 {
			var scenes = make([]string, 0, len(sceneList))
			for _, v := range sceneList {
				scenes = append(scenes, fmt.Sprintf("%s%s", zoneMap[v.ZoneId], v.Name))
			}
			prompt.WriteString(fmt.Sprintf("想玩的景点：%s\n", strings.Join(scenes, "、")))
		}
	}
	if in.TravelDate != "" {
		prompt.WriteString(fmt.Sprintf("出行时间：%s\n", in.TravelDate))
	} else if in.StartDate > 0 {
		prompt.WriteString(fmt.Sprintf("出行时间：%s\n", time.Unix(in.StartDate, 0).Format(constmap.DateFmtLong)))
	} else {
		prompt.WriteString("出行时间：灵活安排\n")
	}
	if in.People != "" {
		prompt.WriteString(fmt.Sprintf("出行人数：%s\n", in.People))
	}
	if in.Transport != "" {
		prompt.WriteString(fmt.Sprintf("交通方式：%s\n", in.Transport))
	}
	prompt.WriteString(fmt.Sprintf("想怎么玩：除了起点，%s\n", in.HowPlay.ToString()))

	//高德搜索途径城市
	//if in.HowPlay == constmap.TravelHowPlayRouting {
	//	if zones, _ := zone_biz.NewZoneBiz().SearchWayZones(db, in.From, in.To); len(zones) > 0 {
	//		prompt.WriteString("期望路线：")
	//		slice.ForEach(zones, func(_ int, item models.Zone) {
	//			prompt.WriteString(fmt.Sprintf("%s、", item.Name))
	//		})
	//		prompt.WriteString("\n")
	//	}
	//}

	return prompt.String()
}
