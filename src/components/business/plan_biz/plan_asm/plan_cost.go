package plan_asm

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/business/hotel_biz"
	"roadtrip-api/src/components/business/plan_biz"
	"roadtrip-api/src/components/business/scenics_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

func BuildPlanSections(db *gorm.DB, planId uint, aiReqid string) (*beans.PlanTop, []beans.PlanSection, *beans.TravelPromptOptions) {
	sections := make([]beans.PlanSection, 0)
	options := new(beans.TravelPromptOptions)
	top := new(beans.PlanTop)
	if planId > 0 {
		var plan models.Plan
		db.Preload("Ext").Preload("Sections").Take(&plan, planId)
		if plan.ID == 0 {
			return top, sections, options
		}
		options.From = plan.From
		options.To = plan.To
		if plan.Ext.PromptOptions.Data != nil {
			*options = *plan.Ext.PromptOptions.Data
		}
		slice.SortBy(plan.Sections, func(a, b models.PlanSection) bool {
			return a.SectionSort < b.SectionSort
		})
		for _, v := range plan.Sections {
			var cnt beans.PlanSectionContent
			_ = json.Unmarshal([]byte(v.Content), &cnt)
			section := beans.PlanSection{
				Subject: v.SectionSubject,
				Title:   v.SectionTitle,
				Content: cnt,
			}
			sections = append(sections, section)
		}
		top.Subject = plan.Subject
		top.Subtitle = plan.Subtitle
		top.FitFor = plan.FitFor
		top.FitMonths = plan.FitMonths
		top.Notice = plan.Notice
		top.AiReqid = plan.AiReqid
		top.Poster = utils.StaticUrl(plan.Poster)
		top.UserId = plan.UserId

	} else {
		chatMsg, ok := plan_biz.GetDetailCc(aiReqid)
		if !ok {
			return top, sections, options
		}
		aiModel := ai.NewByProvider(chatMsg.Provider)
		journey, err := plan_biz.ParseAIJourney(db, aiModel, chatMsg)
		if err != nil {
			my_logger.Errorf("ParseAIJourney", zap.Error(err))
			return top, sections, options
		}
		if chatMsg.PromptOptions != nil {
			options = chatMsg.PromptOptions
		} else if opt, err := aiModel.ParseOptions(chatMsg.Content); err == nil {
			options = opt
		}
		for _, v := range journey.List {
			section := beans.PlanSection{
				Title:   v.Title,
				Subject: v.Subject,
			}
			cnt := beans.PlanSectionContent{
				Scenic:       v.Scenic,
				Speciality:   v.Speciality,
				PassingZones: slice.Map(v.WayPoints, func(index int, item *beans.JourneyPoi) uint { return item.Id }),
				Timeline:     v.Timeline,
			}
			section.Content = cnt

			sections = append(sections, section)
		}
		top.Subject = journey.Title
		top.Subtitle = journey.Subtitle
		top.FitFor = journey.FitFor
		top.FitMonths = journey.FitMonths
		top.Notice = journey.Notice
		top.AiReqid = aiReqid

	}
	return top, sections, options
}

func EventPlanUpdated(db *gorm.DB, planId uint, aiReqid string, force bool) error {
	checkCanRun := func() bool {
		ctx, unlock := context.WithCancel(context.Background())
		defer unlock()
		my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "EventPlanUpdated", aiReqid), constmap.TimeDur1m)

		var costDetail = new(models.PlanCostDetail)
		db.Where(models.PlanCostDetail{AiReqid: aiReqid}).Take(&costDetail)

		//不是初始态
		if !force && costDetail.ID > 0 && costDetail.State != constmap.PlanCostDetailStateNoStart {
			return false
		}
		// 更新为运行中
		if costDetail.ID == 0 {
			if err := db.Create(&models.PlanCostDetail{
				AiReqid: aiReqid,
				State:   constmap.PlanCostDetailStateRunning,
			}).Error; err != nil {
				my_logger.Errorf("EventPlanUpdated create plan_cost_detail error", zap.String("aiReqid", aiReqid), zap.Error(err))
				return false
			}
		} else if costDetail.ID > 0 && db.Model(&costDetail).Updates(models.PlanCostDetail{
			State: constmap.PlanCostDetailStateRunning,
		}).RowsAffected == 0 {
			my_logger.Infof("EventPlanUpdated update plan_cost_detail error", zap.String("aiReqid", aiReqid))
			return false
		}
		return true
	}
	if !checkCanRun() {
		my_logger.Infof("EventPlanUpdated checkCanRun fail", zap.String("aiReqid", aiReqid))
		return nil
	}
	planTop, sections, options := BuildPlanSections(db, planId, aiReqid)

	var zoneIds []uint
	var sceneIds []uint
	var hotelIds []uint

	for _, v := range sections {
		zoneIds = append(zoneIds, v.Content.PassingZones...)
		for _, vv := range v.Content.Timeline {
			if vv.Type == constmap.JTimelineScene {
				sceneIds = append(sceneIds, vv.ItemId)
			}
			if vv.Type == constmap.JTimelineHotel {
				hotelIds = append(hotelIds, vv.ItemId)
			}
		}
	}

	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, 0)
	sceneMap := scenics_biz.LoadScenes(db, sceneIds, false)
	hotelMap := hotel_biz.LoadHotels(db, hotelIds, false)

	if err := calcPlanCostDetail(db, planId, aiReqid, sections, options, zoneMap, sceneMap, hotelMap); err != nil {
		my_logger.Errorf("calcPlanCostDetail", zap.Uint("planId", planId), zap.String("aiReqid", aiReqid), zap.Error(err))
	}
	if err := calcPlanTags(db, planTop, sections, zoneMap, sceneMap); err != nil {
		my_logger.Errorf("calcPlanTags", zap.Uint("planId", planId), zap.String("aiReqid", aiReqid), zap.Error(err))
	}
	if err := calcPrimeScenes(db, planTop.AiReqid, sections, zoneMap, sceneMap); err != nil {
		my_logger.Errorf("calcPrimeScenes", zap.Uint("planId", planId), zap.String("aiReqid", aiReqid), zap.Error(err))
	}

	return nil
}

// 行程预算明细
func calcPlanCostDetail(db *gorm.DB, planId uint, aiReqid string, sections []beans.PlanSection, options *beans.TravelPromptOptions,
	zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic, hotelMap map[uint]*models.Hotel) error {
	var planCost = new(beans.PlanCost)

	dayTasks := make([][]*beans.JourneyPoi, 0)
	for _, section := range sections {
		waypoints := make([]*beans.JourneyPoi, 0)
		for _, timeline := range section.Content.Timeline {
			if timeline.Type == constmap.JTimelineScene || timeline.Type == constmap.JTimelineHotel {
				waypoints = append(waypoints, timeline.Poi)
			}
		}
		if len(waypoints) == 0 {
			continue
		}
		dayTasks = append(dayTasks, waypoints)
	}
	if w, err := plan_biz.Waymap(context.Background(), beans.PlanDetailReq{
		AiReqid:      aiReqid,
		PlanId:       planId,
		DisableCache: true,
	}, dayTasks); err != nil {
		my_logger.Errorf("waymap error", zap.Error(err))
	} else {
		for _, route := range w.Routes {
			planCost.Highway += utils.CurrencyFloat2Int(route.Cost)
			planCost.Distance += route.Distance
		}
		// 7.9L/100km （95# 7.57元/升)
		planCost.Fuel = utils.CurrencyFloat2Int(7.57 * 7.9 * (planCost.Distance / 1000) / 100)
	}

	start := time.Now().AddDate(0, 0, 1)
	if options.StartDate > 0 {
		start = time.Unix(options.StartDate, 0)
	}
	month := start.Month()
	hotelPriceMap := hotel_biz.GetAvgPrice(db, start, start.AddDate(0, 0, len(sections)-1), maputil.Keys(hotelMap))
	zoneIds := make([]uint, 0)

	for _, section := range sections {
		zoneIds = append(zoneIds, section.Content.PassingZones...)
		for _, timeline := range section.Content.Timeline {
			if timeline.Type == constmap.JTimelineScene {
				if scene, ok := sceneMap[timeline.ItemId]; ok {
					var scenePrices = &beans.PlanCostZoneScenic{
						ZoneId:  scene.ZoneId,
						SceneId: scene.ID,
						Name:    scene.Name,
					}
					prices := scenics_biz.UnmarshalPrices(scene.ScenicPrices)
					scenePrices.IsFree = prices.IsFree

					if !prices.IsFree {
						var matched bool
						for _, v := range prices.Adult.MonthlyPrices {
							if v.Month == int(month) {
								scenePrices.Fee = utils.CurrencyFloat2Int(v.Price)
								planCost.Ticket += scenePrices.Fee
								matched = true
								break
							}
						}
						if !matched {
							scenePrices.Fee = utils.CurrencyFloat2Int(prices.Adult.MonthlyPriceAvg)
							planCost.Ticket += scenePrices.Fee
						}
					}

					planCost.Scenes = append(planCost.Scenes, scenePrices)
				}
			} else if timeline.Type == constmap.JTimelineHotel {
				if price, ok := hotelPriceMap[timeline.ItemId]; ok {
					planCost.Hotel += price
				}
			}
		}
	}

	var specialityCost int64
	var zoneSpecialityMap = zone_biz.NewZoneBiz().GetSpeciality(db, zoneIds)
	for zoneId, zoneSpeciality := range zoneSpecialityMap {
		planCost.Food += zoneSpeciality.FoodsCost.BreakfastCent + zoneSpeciality.FoodsCost.LunchCent + zoneSpeciality.FoodsCost.DinnerCent
		specialities := slice.Map(zoneSpeciality.Specialties, func(index int, item *beans.ZoneSpecialitySpecialty) *beans.PlanCostZoneSpecialty {
			specialityCost += item.FeeCent
			v := &beans.PlanCostZoneSpecialty{
				ZoneId: zoneId,
				Pic:    item.Pic,
				Name:   item.Name,
				Fee:    item.FeeCent,
			}
			return v
		})
		planCost.Specialities = append(planCost.Specialities, specialities...)
	}

	planCost.Total = planCost.Food + planCost.Fuel + planCost.Ticket + planCost.Hotel + planCost.Highway + specialityCost

	var costDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{AiReqid: aiReqid}).Take(costDetail)

	if costDetail.ID > 0 {
		if db.Model(costDetail).Updates(models.PlanCostDetail{
			AiReqid: aiReqid,
			State:   constmap.PlanCostDetailStateDone,
			Cost:    utils.Marshaller[beans.PlanCost]{planCost},
		}).RowsAffected == 0 {
			return fmt.Errorf("UpdatePlanCostDetail fail")
		}
	} else {
		costDetail = &models.PlanCostDetail{
			AiReqid: aiReqid,
			State:   constmap.PlanCostDetailStateDone,
			Cost:    utils.Marshaller[beans.PlanCost]{planCost},
		}
		if err := db.Create(costDetail).Error; err != nil {
			return errors.Wrap(err, "CreatePlanCostDetail fail")
		}
	}
	return nil
}

// 生成行程标签
func calcPlanTags(db *gorm.DB, planTop *beans.PlanTop, sections []beans.PlanSection,
	zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic) error {
	var costDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{
		AiReqid: planTop.AiReqid,
	}).Take(costDetail)

	if costDetail.ID == 0 {
		return fmt.Errorf("calcPlanTags query PlanCostDetail fail")
	}

	var zoneIds []uint
	for _, section := range sections {
		zoneIds = append(zoneIds, section.Content.PassingZones...)
	}
	slice.Unique(zoneIds)
	var zoneNames []string
	for _, v := range zoneIds {
		if z, ok := zoneMap[v]; ok {
			zoneNames = append(zoneNames, z.Name)
		}
	}

	planCost := costDetail.Cost.Data

	var prompt strings.Builder
	prompt.WriteString(fmt.Sprintf("行程标题:%s\n", planTop.Subject))
	prompt.WriteString(fmt.Sprintf("行程副标题:%s\n", planTop.Subtitle))
	prompt.WriteString(fmt.Sprintf("途径城市:%s\n", strings.Join(zoneNames, "、")))
	prompt.WriteString(fmt.Sprintf("人均费用:%.2f元\n", utils.CurrencyInt2Float(planCost.Total)))

	for _, v := range sections {
		prompt.WriteString(fmt.Sprintf("## %s\n", v.Title))
		prompt.WriteString(fmt.Sprintf("主题:%s\n", v.Subject))
		for _, timeline := range v.Content.Timeline {
			if timeline.Type == constmap.JTimelineScene {
				if scene, ok := sceneMap[timeline.ItemId]; ok {
					prompt.WriteString(fmt.Sprintf("### 景点:%s\n", scene.Name))
					if z, ok := zoneMap[scene.ZoneId]; ok {
						prompt.WriteString(fmt.Sprintf("所在城市:%s\n", z.Name))
					}
					prompt.WriteString(fmt.Sprintf("景点标签:%s\n", strings.Join(timeline.Tags, "、")))
				}
			}
		}
	}

	rsp, err := my_dify.PlanTags(context.Background(), db, prompt.String())
	if err != nil {
		return err
	}

	if db.Model(&costDetail).Updates(models.PlanCostDetail{
		MainTags: strings.Join(rsp.MainTags, ","),
		SubTags:  strings.Join(rsp.DeputyTags, ","),
	}).RowsAffected == 0 {
		return fmt.Errorf("UpdatePlanCostDetail fail")
	}

	return nil
}

// 生成主要景点
func calcPrimeScenes(db *gorm.DB, aiReqid string, sections []beans.PlanSection,
	zoneMap map[uint]*models.Zone, sceneMap map[uint]*models.Scenic) error {

	var prompt strings.Builder
	prompt.WriteString("【所有景点如下】\n")
	jsonInput := make([]beans.PlanPdfPrimeScene, 0)
	for _, v := range sections {
		for _, vv := range v.Content.Timeline {
			if vv.Type == constmap.JTimelineScene {
				if scene, ok := sceneMap[vv.ItemId]; ok {
					prompt.WriteString(fmt.Sprintf("【城市:%s】【景点ID:%d】%s\n", utils.TrimCitySuffix(zoneMap[scene.ZoneId].Name), scene.ID, scene.Name))
					jsonInput = append(jsonInput, beans.PlanPdfPrimeScene{
						SceneId:   scene.ID,
						SceneName: scene.Name,
					})
				}
			}
		}
	}

	rsp, err := my_dify.PlanPdfPrimeScenes(context.Background(), db, prompt.String(), jsonInput)
	if err != nil {
		return err
	}
	var primeScenes []beans.PlanPdfPrimeScene
	for _, v := range rsp.Scenes {
		if scene, ok := sceneMap[v.SceneId]; ok {
			v.Pic = scene.Pic
			primeScenes = append(primeScenes, v)
		}
	}

	var planCostDetail = new(models.PlanCostDetail)
	db.Where(models.PlanCostDetail{
		AiReqid: aiReqid,
	}).Take(planCostDetail)

	if planCostDetail.ID == 0 {
		return fmt.Errorf("calcPrimeScenes getPlanCostDetail fail fail")
	}

	planCostDetail.PrimeScenes.Data = &primeScenes

	if db.Model(&planCostDetail).Updates(models.PlanCostDetail{
		PrimeScenes: planCostDetail.PrimeScenes,
	}).RowsAffected == 0 {
		return fmt.Errorf("UpdatePlanCostDetail fail")
	}
	return nil
}
