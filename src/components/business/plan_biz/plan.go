package plan_biz

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"

	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

func GetDetailCc(aiReqId string) (*ai.ChatCompletionResponse, bool) {
	aiResp := new(ai.ChatCompletionResponse)
	return aiResp, my_cache.Get(fmt.Sprintf(constmap.RKAiResp, aiReqId), aiResp)
}
func SetDetailCc(aiResp *ai.ChatCompletionResponse) error {
	return my_cache.Set(fmt.Sprintf(constmap.RKAiResp, aiResp.RequestId), aiResp, constmap.TimeDur30d)
}

func RefreshDetailExpCc(aiReqid string) {
	my_cache.RedisClient().Expire(fmt.Sprintf(constmap.RKAiResp, aiReqid), constmap.TimeDur30d) //刷新过期时间
}

func LoadPlanCost(planCost string) *beans.PlanCost {
	if planCost != "" {
		var cost = new(beans.PlanCost)
		if err := json.Unmarshal([]byte(planCost), cost); err != nil {
			return nil
		}
		return cost
	}
	return nil
}

func LoadPlans(db *gorm.DB, planIds []uint) map[uint]*models.Plan {
	if len(planIds) == 0 {
		return map[uint]*models.Plan{}
	}
	var plans []*models.Plan
	db.Find(&plans, planIds)
	return slice.KeyBy(plans, func(plan *models.Plan) uint {
		return plan.ID
	})
}
