package zone_biz

import (
	"context"
	"fmt"
	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
)

type zoneBiz struct{}

func NewZoneBiz() *zoneBiz {
	return &zoneBiz{}
}

func (o *zoneBiz) GetZonesCc(db *gorm.DB) map[uint]models.Zone {
	var zones constmap.GenericList[models.Zone]
	if !my_cache.Get(constmap.RKZones, &zones) {
		db.Model(&zones).
			Omit("created_at", "updated_at", "deleted_at", "ad_code").
			Where(models.Zone{State: constmap.Enable}).Find(&zones)

		_ = my_cache.SetDefault(constmap.RKZones, &zones)
	}
	return slice.KeyBy(zones, func(v models.Zone) uint { return v.ID })
}

func (o *zoneBiz) NormalizeZoneNames(zones []models.Zone) []models.Zone {
	regThree := regexp.MustCompile("(自治县|蒙古族|俄罗斯族|塔塔尔族|撒拉族|乌孜别克族|锡伯族|鄂温克族|德昂族|布朗族|保安族|阿昌族|普米族|基诺族|珞巴族|布依族|傈僳族|土家族|哈尼族|景颇族|朝鲜族)*")
	regTwo := regexp.MustCompile("(白族|黎族|苗族|藏族|哈萨克|彝族|回族|壮族|傣族|侗族|羌族)*")
	areaSpecial := typeset.NewTypeSet(false, "阿克苏地区", "塔城地区", "大兴安岭地区", "和田地区", "阿里地区", "阿勒泰地区")
	countySpecial := typeset.NewTypeSet(false, "定安县", "临高县", "澄迈县", "屯昌县")
	return slice.Map(slice.Filter(zones, func(_ int, v models.Zone) bool {
		return v.Name != "重庆郊县" && v.Name != "新星市"
	}), func(_ int, v models.Zone) models.Zone {
		str := utils.TrimCitySuffix(v.Name)
		str = regThree.ReplaceAllString(str, "")
		str = regTwo.ReplaceAllString(str, "")
		if areaSpecial.Has(str) {
			v.Name = strutil.ReplaceWithMap(str, map[string]string{"地区": ""})
			return v
		}
		if countySpecial.Has(str) {
			v.Name = strutil.ReplaceWithMap(str, map[string]string{"县": ""})
			return v
		}
		switch str {
		case "阿拉善盟":
			str = "阿拉善"
		case "克孜勒苏柯尔克孜":
			str = "克孜勒苏"
		case "博尔塔拉蒙古":
			str = "博尔塔拉"
		case "重庆城区":
			str = "重庆"
		case "天津城区":
			str = "天津"
		case "巴音郭楞蒙古":
			str = "巴音郭楞"
		case "神农架林区":
			str = "神农架"
		}
		v.Name = str
		return v
	})
}

func (o *zoneBiz) GetZoneNamesKebabCase(db *gorm.DB, sep string, zoneIds []uint) map[uint]string {
	zoneIds = typeset.NewTypeSet[uint](false, zoneIds...).Values() //去重
	zids := append([]uint{}, zoneIds...)
	var zones []*models.Zone
	var zoneMap = make(map[uint]*models.Zone)
	for len(zids) > 0 {
		db.Find(&zones, zids)
		zids = zids[:0]
		slice.ForEach(zones, func(_ int, item *models.Zone) {
			if item.ParentId > 0 {
				zids = append(zids, item.ParentId)
			}
			zoneMap[item.ID] = item
		})
	}
	maputil.ForEach(zoneMap, func(_ uint, value *models.Zone) {
		if p, ok := zoneMap[value.ParentId]; ok {
			value.Parent = p
		}
	})
	ret := make(map[uint]string, len(zoneIds))
	slice.ForEach(zoneIds, func(_ int, item uint) {
		zone, ok := zoneMap[item]
		if !ok {
			return
		}
		var s = make([]string, 0, 3)
		s = append(s, zone.Name)
		for zone.Parent != nil {
			zone = zone.Parent
			s = append(s, zone.Name)
		}
		slice.Reverse(s)
		ret[item] = strings.Join(s, sep)
	})
	return ret
}

func (o *zoneBiz) IpLocation(ctx *gin.Context, db *gorm.DB) (*models.Zone, bool) {
	takeDefault := func(zoneId uint) *models.Zone {
		if zoneId < 1 {
			zoneId = constmap.DefaultZoneId
		}
		var zone = new(models.Zone)
		db.Where("id=?", zoneId).Take(&zone)
		return zone
	}
	ip := utils.Ip(ctx)
	//if config.IsDebug(){
	//	ip = "*************"
	//}
	if ip == "" || ip == "127.0.0.1" {
		return takeDefault(0), false
	}
	_, _, city, err := utils.IpLoc.QueryIP(ip)
	if err != nil {
		my_logger.Infof("ip loc error", zap.Error(err))
		return takeDefault(0), false
	}
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []any{
					map[string]any{
						"term": map[string]any{
							"level": constmap.ZoneLevelCity,
						},
					},
					map[string]any{
						"term": map[string]any{
							"type": constmap.PoiTypeZone,
						},
					},
					map[string]any{
						"match": map[string]any{
							"name": utils.TrimCitySuffix(city),
						},
					},
				},
			},
		},
		"size": 1,
	}
	if res, err := es.Search[es2.Zone](constmap.EsIndexPoi, query); err != nil || len(res.Hits.Hits) == 0 {
		if err != nil {
			my_logger.Infof("ip loc error", zap.Error(err))
		}
		return takeDefault(0), false
	} else {
		var zoneId uint
		for _, v := range res.Hits.Hits {
			zoneId = v.Source.ObjId
		}
		return takeDefault(zoneId), true
	}
}

func (o *zoneBiz) GetZoneMap(db *gorm.DB, zoneIds []uint) map[uint]models.Zone {
	ret := make(map[uint]models.Zone)
	if len(zoneIds) == 0 {
		return ret
	}
	var list []models.Zone
	db.Find(&list, zoneIds)
	ret = slice.ReduceBy(o.NormalizeZoneNames(list), ret, func(index int, item models.Zone, agg map[uint]models.Zone) map[uint]models.Zone {
		agg[item.ID] = item
		return agg
	})
	return ret
}

// 获取所有地区，一直查询到`level`层级
func (o *zoneBiz) GetZonesToTop(db *gorm.DB, zoneIds []uint, level int) map[uint]*models.Zone {
	ids := append([]uint{}, zoneIds...)
	var zones []*models.Zone
	var zoneMap = make(map[uint]*models.Zone)
	if len(ids) == 0 {
		return zoneMap
	}
	for len(ids) > 0 {
		db.Find(&zones, ids)
		ids = ids[:0]
		slice.ForEach(zones, func(_ int, item *models.Zone) {
			if item.ParentId > 0 && (level == 0 || level > 0 && item.Level > level) {
				ids = append(ids, item.ParentId)
			}
			zoneMap[item.ID] = item
		})
	}
	maputil.ForEach(zoneMap, func(_ uint, value *models.Zone) {
		if p, ok := zoneMap[value.ParentId]; ok {
			value.Parent = p
		}
	})
	return zoneMap
}

// 由上至下查询地区，知道达到`toLevel`层级,返回：入参zoneIds为key的map、由顶到下所有地区的id列表
func (o *zoneBiz) GetZonesToBottom(db *gorm.DB, zoneIds []uint, toLevel int) (map[uint]*beans.Zone, []uint) {
	var zones []models.Zone
	db.Find(&zones, zoneIds)

	allZoneIds := make([]uint, 0)
	zoneMap := make(map[uint]*beans.Zone)

	var bottomZones []*beans.Zone
	slice.ForEach(zones, func(_ int, item models.Zone) {
		z := &beans.Zone{
			Id:       item.ID,
			Name:     item.Name,
			Level:    item.Level,
			ParentId: item.ParentId,
			Children: make([]*beans.Zone, 0),
		}
		allZoneIds = append(allZoneIds, item.ID)
		zoneMap[item.ID] = z
		if toLevel == 0 || item.Level != toLevel {
			bottomZones = append(bottomZones, z)
		}
	})

	var toBottom func(zs []*beans.Zone)
	toBottom = func(zs []*beans.Zone) {
		if len(zs) == 0 {
			return
		}
		zmap := make(map[uint]*beans.Zone)
		zids := slice.Map(zs, func(_ int, item *beans.Zone) uint {
			zmap[item.Id] = item
			return item.Id
		})
		zones = zones[:0]
		db.Where("parent_id in ?", zids).Find(&zones)

		bottomZones = bottomZones[:0]
		slice.ForEach(zones, func(_ int, item models.Zone) {
			if parent, ok := zmap[item.ParentId]; ok {
				z := &beans.Zone{
					Id:       item.ID,
					Name:     item.Name,
					Level:    item.Level,
					ParentId: item.ParentId,
					Children: make([]*beans.Zone, 0),
				}
				allZoneIds = append(allZoneIds, item.ID)
				parent.Children = append(parent.Children, z)
				if toLevel == 0 || item.Level != toLevel {
					bottomZones = append(bottomZones, z)
				}
			}
		})
		toBottom(bottomZones)
	}
	toBottom(bottomZones)

	return zoneMap, allZoneIds
}

// 搜索经过城市
func (o *zoneBiz) SearchWayZones(db *gorm.DB, from, to string) ([]models.Zone, error) {
	fromRes, err := amap.Search(&go_amap.PlaceRequest{
		Keywords: from,
		PageSize: 1,
	})
	if err != nil {
		return nil, err
	}

	if len(fromRes.Pois) == 0 {
		return nil, nil
	}

	toRes, err := amap.Search(&go_amap.PlaceRequest{
		Keywords: to,
		PageSize: 1,
	})
	if err != nil {
		return nil, err
	}

	if len(toRes.Pois) == 0 {
		return nil, nil
	}

	res, err := amap.Driving(fromRes.Pois[0].Location, toRes.Pois[0].Location, nil, "cities")
	if err != nil {
		return nil, err
	}

	if len(res.Route.Paths) == 0 {
		return nil, nil
	}

	adcodeMap := make(map[string]int)
	for i, v := range res.Route.Paths[0].Steps {
		for j, vv := range v.Cities {
			if _, ok := adcodeMap[vv.Adcode]; !ok {
				adcodeMap[vv.Adcode] = i*100 + j
			}
		}
	}

	var zones []models.Zone
	db.Where("ad_code in ? AND state=?", maputil.Keys(adcodeMap), constmap.Enable).Find(&zones)
	slice.SortBy(zones, func(a, b models.Zone) bool {
		return adcodeMap[a.AdCode] < adcodeMap[b.AdCode]
	})

	return zones, nil
}

// 城市美食、特产、民俗文化
func (o *zoneBiz) GetSpeciality(db *gorm.DB, zoneIds []uint) map[uint]*beans.ZoneSpecialityData {

	if len(zoneIds) == 0 {
		return make(map[uint]*beans.ZoneSpecialityData)
	}

	zoneIds = slice.Unique(zoneIds)

	ckeys := make(map[string]uint)
	for _, v := range zoneIds {
		ckeys[fmt.Sprintf(constmap.RKZoneSpeciality, v)] = v
	}

	rsp, err := my_cache.MGetSet(ckeys, func(missKeys map[string]uint) (map[string]*utils.Marshaller[beans.ZoneSpecialityData], error) {

		ret := make(map[string]*utils.Marshaller[beans.ZoneSpecialityData])

		var list []*models.ZoneSpeciality
		db.Where("zone_id in ?", maputil.Values(missKeys)).Find(&list)
		for _, v := range list {
			ret[fmt.Sprintf(constmap.RKZoneSpeciality, v.ZoneId)] = &v.Data
		}
		return ret, nil
	}, constmap.TimeDur30d)
	if err != nil {
		my_logger.Errorf("GetSpeciality", zap.Error(err))
	}
	ret := make(map[uint]*beans.ZoneSpecialityData)
	for k, v := range rsp {
		ret[ckeys[k]] = v.Data
	}
	return ret
}

func (o *zoneBiz) MakeHotZones(db *gorm.DB) {
	if hots, err := my_dify.HotZones(context.Background(), db); err != nil || len(hots.Cities) == 0 {
		my_logger.Errorf("[hotZones]dify.HotZones", zap.Error(err))
	} else {
		var zs = make([]models.Zone, 0)
		db.Where(models.Zone{Level: constmap.ZoneLevelCity, State: constmap.Enable}).Find(&zs)
		zs = slice.Filter(zs, func(index int, v models.Zone) bool {
			return slice.ContainBy(hots.Cities, func(item string) bool {
				return strings.Contains(v.Name, item)
			})
		})

		zs = o.NormalizeZoneNames(zs)

		cacheData := slice.Map(zs, func(index int, item models.Zone) beans.HotZone {
			return beans.HotZone{
				Id:   item.ID,
				Name: item.Name,
				Lng:  item.Lng,
				Lat:  item.Lat,
			}
		})
		if err := my_cache.Set(constmap.RKHotZones, utils.NewGenericList(cacheData), constmap.TimeDur40d); err != nil {
			my_logger.Errorf("[hotZones]cache.Set", zap.Error(err))
		} else {
			my_logger.Infof("[hotZones]cache.Set", zap.Any("data", hots))
		}
	}
}
