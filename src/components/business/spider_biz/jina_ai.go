package spider_biz

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"io"
	"net/http"
	"net/url"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

type JinaAi struct {
	apiKey string
}

type jinaResp struct {
	Message string `json:"message"`
	Code    int    `json:"code"`
	Status  int    `json:"status"`
	Data    struct {
		Title       string `json:"title"`
		Description string `json:"description"`
		Url         string `json:"url"`
		Content     string `json:"content"`
		Warning     string `json:"warning"`
		Usage       struct {
			Tokens int `json:"tokens"`
		} `json:"usage"`
	} `json:"data"`
	Meta struct {
		Usage struct {
			Tokens int `json:"tokens"`
		} `json:"usage"`
	} `json:"meta"`
}

var jinaUrl *url.URL

func NewJinaAi() *JinaAi {
	if jinaUrl == nil {
		jinaUrl, _ = url.Parse("https://r.jina.ai/")
	}
	return &JinaAi{
		apiKey: config.Config.JinaAi.ApiKey,
	}
}

func (o *JinaAi) Crawl(url string) (*CrawlResponse, error) {
	b, err := json.Marshal(map[string]string{
		"url": url,
	})
	if err != nil {
		return nil, err
	}
	buf := utils.NewBytesBuffer(bytes.NewBuffer(b))
	req := &http.Request{
		URL:    jinaUrl,
		Method: string(constmap.HttpMethodPost),
		Header: map[string][]string{
			"Content-Type": {"application/json"},
			"Accept":       {"application/json"},
			"X-Base":       {"final"}, //解析301
			//"X-No-Cache": {"true"},//避免缓存
			"Authorization": {fmt.Sprintf("Bearer %s", config.Config.JinaAi.ApiKey)},
		},
		Body: buf,
	}
	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, err
	}
	resp := new(CrawlResponse)
	b, err = io.ReadAll(rsp.Body)
	if err != nil {
		return nil, err
	}
	resp.Raw = b
	apiRsp := new(jinaResp)
	err = json.Unmarshal(b, apiRsp)
	if err != nil {
		return nil, err
	}
	if apiRsp.Code != 200 {
		return nil, fmt.Errorf("[%d][%d]%s", apiRsp.Code, apiRsp.Status, apiRsp.Message)
	}
	if strutil.ContainsAny(url, []string{
		"xiaohongshu.com",
		"xhslink.com",
	}) {
		resp.Content = apiRsp.Data.Description
	} else {
		resp.Content = apiRsp.Data.Content
	}
	//my_logger.Infof("jina crawl", zap.String("url", url), zap.Any("data", apiRsp))
	return resp, nil
}
