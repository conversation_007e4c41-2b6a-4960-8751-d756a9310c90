package scenics_biz

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
)

func LevelText(l int) string {
	return utils.If(l == 0, "", fmt.Sprintf("%dA", l))
}

// 根据景点查酒店
func QueryHotelsByPoi(db *gorm.DB, km float64, scene *models.Scenic, callback func(uint) (bool, error)) error {

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"type": constmap.PoiTypeHotel,
						},
					},
					{
						"term": map[string]any{
							"state": constmap.Enable,
						},
					},
					{
						"term": map[string]any{
							"zone_id": scene.ZoneId,
						},
					},
					{
						"geo_distance": map[string]any{
							"distance": fmt.Sprintf("%.1fkm", km),
							"location": map[string]any{
								"lat": scene.Lat,
								"lon": scene.Lng,
							},
						},
					},
				},
			},
		},
		"sort": []map[string]any{
			{
				"_geo_distance": map[string]any{
					"location": map[string]any{
						"lat": scene.Lat,
						"lon": scene.Lng,
					},
					"order": "asc",
					"unit":  "km",
				},
			},
			{"_score": map[string]any{"order": "desc"}},
		},
		"size": 100,
	}
	page := 1
	for {
		query["from"] = (page - 1) * 100
		page++
		searchResp, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
		if err != nil {
			return err
		}
		if len(searchResp.Hits.Hits) == 0 {
			break
		}
		for _, hit := range searchResp.Hits.Hits {
			if stop, err := callback(hit.Source.ObjId); stop || err != nil {
				return err
			}
		}
	}
	return nil
}

func LoadScenes(db *gorm.DB, sceneIds []uint, loadPic bool) map[uint]*models.Scenic {
	ret := make(map[uint]*models.Scenic)
	if len(sceneIds) == 0 {
		return ret
	}
	q := db.Where("id in ?", sceneIds)
	if loadPic {
		q = q.Preload("Pics")
	}
	var scenics []*models.Scenic
	q.Find(&scenics)
	slice.ForEach(scenics, func(index int, item *models.Scenic) {
		ret[item.ID] = item
	})
	return ret
}

func LoadExt(db *gorm.DB, sceneIds []uint) map[uint]*models.ScenicExt {
	ret := make(map[uint]*models.ScenicExt)
	if len(sceneIds) == 0 {
		return ret
	}
	var exts []*models.ScenicExt
	db.Where("scenic_id in ?", sceneIds).Find(&exts)
	slice.ForEach(exts, func(index int, item *models.ScenicExt) {
		ret[item.ScenicId] = item
	})
	return ret
}

func UnmarshalPrices(priceString string) beans.ScenicPrices {
	prices := beans.ScenicPrices{}
	if priceString != "" {
		_ = json.Unmarshal([]byte(priceString), &prices)
	}
	return prices
}
