package ai

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/fileutil"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/russross/blackfriday/v2"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"go.uber.org/zap"
	"golang.org/x/net/html"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/doubao"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	utils2 "roadtrip-api/src/utils"
	"strings"
)

type Doubao struct {
	Section      int
	JourneyIndex int
}

func NewDoubao() *Doubao {
	return &Doubao{JourneyIndex: -1}
}

func (d *Doubao) GetProvider() string {
	return "doubao"
}

func (d *Doubao) ChatCompletionMessage(prompt string) (*ChatCompletionResponse, error) {
	var str = `
#角色
假如你是一位专业的旅行规划师，你将根据用户的需求，根据以下规则一步步执行任务。
#规则
1.本次旅行的主题
2.每日行程主题，途经城市、景点、美食等
3.每日行程的简单介绍
4.注意事项
5.适合季节，人群

#输出
请严格按照格式要求输出，不要输出多余的内容。
# [主题]
[xxxxx]

# [行程安排]
## [第一天，xxxxx]
当日主题：[xxxxx]
[xxxxx]

# [注意事项]
[xxxxx]

# [适合季节和人群]
适合季节：[xxxxx]
适合人群：[xxxxx]
`

	messages := []*model.ChatCompletionMessage{
		{
			Role: model.ChatMessageRoleUser,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(prompt),
			},
		},
		{
			Role: model.ChatMessageRoleSystem,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(str),
			},
		},
	}
	res, err := doubao.ChatCompletionMessage(messages)

	var content string

	if err != nil {
		return nil, err
	} else if len(res.Choices) == 0 {
		return nil, utils2.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	content = *res.Choices[0].Message.Content.StringValue
	input, _ := convertor.ToJson(messages)
	_ = fileutil.WriteStringToFile("./tmp/doubao.md", fmt.Sprintf("%s\n\n%s\n\n\n\n", input, content), true)

	my_logger.Infof("doubao response", zap.Any("data", res))

	data := ChatCompletionResponse{
		RequestId: fmt.Sprintf("%s:%s", d.GetProvider(), res.ID),
		Content:   content,
		Provider:  d.GetProvider(),
	}

	return &data, nil
}

func (d *Doubao) Parse(text string, db *gorm.DB, data *beans.RenderTravelJourney) error {
	text = string(blackfriday.Run([]byte(text)))
	node, err := html.Parse(strings.NewReader(text))
	if err != nil {
		return err
	}
	data.List = make([]*beans.Journey, 0)

	d.do(node, data)

	return nil
}

func (d *Doubao) do(node *html.Node, data *beans.RenderTravelJourney) {
	d.parseTitle(node, data)
	d.parseJourney(node, data)
	d.parseNotice(node, data)
}

// 匹配注意事项
func (d *Doubao) parseNotice(root *html.Node, data *beans.RenderTravelJourney) {
	recursionNode(root, func(node2 *html.Node) bool {
		if node2.Type == html.ElementNode {
			switch node2.Data {
			case "h2", "h3":
				text := getNodeContent(node2)
				if isNotice(text) {
					d.Section = SectionNote
					RemoveNode(node2)
					return true
				}
			case "ol", "ul":
				if d.Section == SectionNote {
					text := getNodeContent(node2)
					data.Notice = text
					RemoveNode(node2)

					d.Section = SectionEnd
					return true

				}
			}
		}

		return false
	})
}

// 匹配行程段和景点
func (d *Doubao) parseJourney(root *html.Node, data *beans.RenderTravelJourney) {
	recursionNode(root, func(node *html.Node) bool {
		if node.Type == html.ElementNode {
			switch node.Data {
			case "h2", "h3":
				title := getNodeContent(node)
				if isDayJourneyTitle(title) {
					d.Section = SectionStartJourney
					d.JourneyIndex += 1

					data.List = append(data.List, &beans.Journey{
						Title: title,
					})
					RemoveNode(node)
					return true
				} else if isIgnore(title) {
					d.Section = SectionEndJourney
				}
			default:
				if d.Section == SectionStartJourney {
					title := getNodeContent(node)
					data.List[d.JourneyIndex].Content += title
					RemoveNode(node)
					return true
				}
			}
		}

		return false
	})

}

// 匹配主题
func (d *Doubao) parseTitle(root *html.Node, data *beans.RenderTravelJourney) {
	recursionNode(root, func(node2 *html.Node) bool {
		if node2.Type == html.ElementNode {
			switch node2.Data {
			case "h1":
				data.Title = getNodeContent(node2)
				RemoveNode(node2)
				return true
			case "h2", "p":
				title := getNodeContent(node2)
				if !isDayJourneyTitle(title) && !isIgnore(title) && strutil.IsBlank(data.Title) {
					data.Title = title
					RemoveNode(node2)
					return true
				}
			}
		}

		return false
	})
}
