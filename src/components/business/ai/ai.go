package ai

import (
	"bufio"
	"context"
	"encoding/json"
	"golang.org/x/net/html"
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"strings"
)

const (
	SectionTitle        = 2 //当前解析段落（标题）
	SectionNote         = 3 //当前解析段落（备注）
	SectionStartJourney = 4 //当前解析段落（开始行程）
	SectionEndJourney   = 5 //当前解析段落（结束行程）
	SectionEnd          = 6 //当前解析段落（结束）
)

type ContentType string

const (
	ContentTypePlan  = ""
	ContentTypeHotel = "hotel"
)

type ChatCompletionResponse struct {
	beans.PlanLockData
	From          string                       `json:"from"`          //【废弃】
	To            string                       `json:"to"`            //【废弃】
	Accommodation constmap.TravelAccommodation `json:"accommodation"` //【废弃】住宿条件
	ContentType   ContentType                  `json:"content_type"`
	RequestId     string                       `json:"request_id"`
	Content       string                       `json:"content"`
	Provider      string                       `json:"provider"`
	PlanCost      *beans.PlanCost              `json:"plan_cost"` //成本信息
	UserId        uint                         `json:"user_id"`
}

type ChatContextCreateResponse struct {
	ContextId string `json:"context_id"`
	TTL       int    `json:"ttl"` //过期秒数
}

type ChatContextCompletionResponse struct {
	RequestId   string      `json:"request_id"`
	Provider    string      `json:"provider"`
	Content     string      `json:"content"`
	ContentType ContentType `json:"content_type"`
	NeedMore    bool        `json:"need_more"` //是否需要更多对话
}

type ChatContextStreamingResponse struct {
	RequestId string
	Provider  string
	Reader    *bufio.Reader
}

type ChatStreamRequest struct {
	ConversationId string
	User           string
	SysPrompt      string
	Prompt         string
	Url            string
	ForceGenerate  bool
	City           string
	Location       string
	ChatType       string
}

type tmpChatCompletionResponse struct {
	*ChatCompletionResponse
	Options *beans.TravelPromptOptions `json:"options"`
}

func (t *ChatCompletionResponse) MarshalBinary() (data []byte, err error) {
	return json.Marshal(t)
}

func (t *ChatCompletionResponse) UnmarshalBinary(data []byte) error {
	var tmp = new(tmpChatCompletionResponse)
	if err := json.Unmarshal(data, tmp); err != nil {
		return err
	}
	// 兼容下旧的options字段
	if tmp.Options != nil {
		tmp.PromptOptions = tmp.Options
	}
	if tmp.PromptOptions != nil {
		tmp.Options = tmp.PromptOptions
	}
	*t = *tmp.ChatCompletionResponse
	return nil
}

type IAi interface {
	GetProvider() string

	ChatCompletionMessage(prompt string) (*ChatCompletionResponse, error)

	ChatContextCreate(ttl int) (*ChatContextCreateResponse, error)

	ChatContextCompletion(ctxId, sysPrompt, userInput string) (*ChatContextCompletionResponse, error)

	ChatContextStreaming(ctx context.Context, db *gorm.DB, req *ChatStreamRequest) (*ChatContextStreamingResponse, error)

	ChatSuggestions(ctx context.Context, db *gorm.DB, user, messageId string) ([]string, error)

	Parse(node string, db *gorm.DB, data *beans.RenderTravelJourney) error

	ParseOptions(node string) (*beans.TravelPromptOptions, error)
}

func isNotice(text string) bool {
	return regexp.MustCompile("注意事项").MatchString(text)
}

func isDayJourneyTitle(text string) bool {
	return regexp.MustCompile("第[一二三四五六七八九十]+天").MatchString(text) ||
		regexp.MustCompile("Day\\d+").MatchString(text)
}

func isIgnore(text string) bool {
	return regexp.MustCompile("推荐|介绍|信息|交通|住宿|注意").MatchString(text)
}

func isJourneyInOl(text string) bool {
	return regexp.MustCompile("行程安排|旅行方案|行程规划|行程路线").MatchString(text)
}

func isHotelText(txt string) bool {
	return regexp.MustCompile("酒店|客栈|入住|住宿").MatchString(txt)
}

func getNodeContent(node *html.Node) string {
	var str = strings.Builder{}

	var traverse func(node2 *html.Node)
	traverse = func(n *html.Node) {
		if n.Type == html.TextNode {
			str.WriteString(n.Data)
		}
		for c := n.FirstChild; c != nil; c = c.NextSibling {
			traverse(c)
		}
	}
	traverse(node)

	return str.String()
}

func recursionNode(node *html.Node, callback func(node2 *html.Node) bool) bool {
	if stop := callback(node); stop {
		return true
	}

	for c := node.FirstChild; c != nil; {
		next := c.NextSibling
		if recursionNode(c, callback) {
			c = next
			continue
		}
		c = next
	}

	return false
}

func RemoveNode(node *html.Node) {
	if node.Parent != nil {
		node.Parent.RemoveChild(node)
	}
}

func RemoveAllChildren(node *html.Node) {
	for c := node.FirstChild; c != nil; {
		next := c.NextSibling
		RemoveNode(c)
		c = next
	}
}

func NewDefaultAI() IAi {
	return NewDoubao2()
}

func NewByProvider(provider string) IAi {
	switch provider {
	//case "doubao":
	//	return NewDoubao()
	//case "moonshot":
	//	return NewMoonshot()
	case "dify":
		return NewDify()
	case "yj2":
		return NewDoubao2()
	}
	return nil
}
