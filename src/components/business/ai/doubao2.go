package ai

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/fileutil"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"os"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/doubao"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

type Doubao2 struct {
	lv1Exp      *regexp.Regexp // 一级标题
	lv2Exp      *regexp.Regexp // 二级标题
	lv3Exp      *regexp.Regexp // 三级标题
	lv4Exp      *regexp.Regexp // 四级标题
	colonExp    *regexp.Regexp // 首字符冒号
	fitForMonth *regexp.Regexp // 适合月份
}

func NewDoubao2() *Doubao2 {
	return &Doubao2{
		lv1Exp:      regexp.MustCompile("^(#\\s*\\[?([^#]+)\\]?|【([^#]+)】)"),  // 一级标题: #[主题]、【主题】
		lv2Exp:      regexp.MustCompile("^##\\s*[\\[【]?([^#\\]】]+)[\\]】]?"),   // 二级标题: ## [第一天，xxxxx]
		lv3Exp:      regexp.MustCompile("^###\\s*[\\[【]?([^#\\]】]+)[\\]】]?"),  // 三级标题: ### [第一天，xxxxx]
		lv4Exp:      regexp.MustCompile("^####\\s*[\\[【]?([^#\\]】]+)[\\]】]?"), // 四级标题: #### [第一天，xxxxx]
		colonExp:    regexp.MustCompile("[:：]"),
		fitForMonth: regexp.MustCompile("\\d+\\s*月?\\s*[[:punct:]、～至]\\s*\\d+\\s*月"), // (10~12月) (10月～12月) (4月、5月) (4-5月) (9 月 至 10 月)
	}
}

func (d *Doubao2) GetProvider() string {
	return "yj2"
}

func (d *Doubao2) ChatContextCreate(ttl int) (*ChatContextCreateResponse, error) {
	buf1, err := os.ReadFile(config.Config.Doubao.CtxPrompt)
	if err != nil {
		return nil, err
	}

	buf2, err := os.ReadFile(config.Config.Doubao.Output)
	if err != nil {
		return nil, err
	}
	messages := []*model.ChatCompletionMessage{
		{
			Role: model.ChatMessageRoleSystem,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(string(buf1) + "\n" + string(buf2)),
			},
		},
	}
	res, err := doubao.ChatContextCreate(messages, ttl)
	if err != nil {
		return nil, err
	}
	input, _ := convertor.ToJson(messages)
	_ = fileutil.WriteStringToFile(fmt.Sprintf("./tmp/%s_ctx.md", d.GetProvider()), fmt.Sprintf("%s\n%s", res.ID, input), true)
	return &ChatContextCreateResponse{
		ContextId: res.ID,
		TTL:       ttl,
	}, nil
}

func (d *Doubao2) ChatContextCompletion(ctxId, sysPrompt, userInput string) (*ChatContextCompletionResponse, error) {
	var messages []*model.ChatCompletionMessage
	if !strutil.IsBlank(userInput) {
		messages = append(messages, &model.ChatCompletionMessage{
			Role: model.ChatMessageRoleUser,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(userInput),
			},
		})
	}

	if !strutil.IsBlank(sysPrompt) {
		if buf, err := os.ReadFile(config.Config.Doubao.UsysPrompt); err != nil {
			my_logger.Errorf("doubao system prompt read conf file", zap.Error(err))
		} else {
			messages = append(messages, &model.ChatCompletionMessage{
				Role: model.ChatMessageRoleSystem,
				Content: &model.ChatCompletionMessageContent{
					StringValue: volcengine.String(string(buf) + "\n" + sysPrompt),
				},
			})
		}
	}
	if len(messages) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "messages empty")
	}

	res, err := doubao.ChatContextCompletion(ctxId, messages)

	var content string

	if err != nil {
		my_logger.Errorf("doubao context response", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorIsGot, constmap.ErrorMsgSystem)
	} else if len(res.Choices) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	content = *res.Choices[0].Message.Content.StringValue

	my_logger.Infof("doubao context response", zap.Any("data", res))

	data := &ChatContextCompletionResponse{
		RequestId: fmt.Sprintf("%s:%s", d.GetProvider(), res.ID),
		Content:   content,
		Provider:  d.GetProvider(),
	}
	input, _ := convertor.ToJson(messages)
	_ = fileutil.WriteStringToFile(fmt.Sprintf("./tmp/%s_ctx.md", d.GetProvider()), fmt.Sprintf("%s\n%s\n%s\n\n%s\n\n\n\n", ctxId, data.RequestId, input, content), true)

	if !strutil.ContainsAny(content, []string{"【行程安排】", "[行程安排]"}) {
		data.NeedMore = true
	}

	return data, nil
}

func (d *Doubao2) ChatContextStreaming(ctx context.Context, db *gorm.DB, req *ChatStreamRequest) (*ChatContextStreamingResponse, error) {
	var messages []*model.ChatCompletionMessage
	if !strutil.IsBlank(req.Prompt) {
		messages = append(messages, &model.ChatCompletionMessage{
			Role: model.ChatMessageRoleUser,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(req.Prompt),
			},
		})
	}

	if !strutil.IsBlank(req.SysPrompt) {
		if buf, err := os.ReadFile(config.Config.Doubao.UsysPrompt); err != nil {
			my_logger.Errorf("doubao system prompt read conf file", zap.Error(err))
		} else {
			messages = append(messages, &model.ChatCompletionMessage{
				Role: model.ChatMessageRoleSystem,
				Content: &model.ChatCompletionMessageContent{
					StringValue: volcengine.String(string(buf) + "\n" + req.SysPrompt),
				},
			})
		}
	}
	if len(messages) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "messages empty")
	}

	res, err := doubao.ChatContextStreamingCompletion(ctx, req.ConversationId, messages)

	if err != nil {
		my_logger.Errorf("doubao context response", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorIsGot, constmap.ErrorMsgSystem)
	}

	return &ChatContextStreamingResponse{
		RequestId: utils.UUID(),
		Provider:  d.GetProvider(),
		Reader:    res.Reader,
	}, nil
}

func (d *Doubao2) ChatCompletionMessage(prompt string) (*ChatCompletionResponse, error) {
	buf1, err := os.ReadFile(config.Config.Doubao.Prompt)
	if err != nil {
		return nil, err
	}

	buf2, err := os.ReadFile(config.Config.Doubao.Output)
	if err != nil {
		return nil, err
	}

	messages := []*model.ChatCompletionMessage{
		{
			Role: model.ChatMessageRoleUser,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(prompt),
			},
		},
		{
			Role: model.ChatMessageRoleSystem,
			Content: &model.ChatCompletionMessageContent{
				StringValue: volcengine.String(string(buf1) + "\n" + string(buf2)),
			},
		},
	}
	res, err := doubao.ChatCompletionMessage(messages)

	var content string

	if err != nil {
		my_logger.Errorf("doubao response", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorIsGot, constmap.ErrorMsgSystem)
	} else if len(res.Choices) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, constmap.ErrorMsgParam)
	}
	content = *res.Choices[0].Message.Content.StringValue

	my_logger.Infof("doubao response", zap.Any("data", res))

	data := ChatCompletionResponse{
		RequestId: fmt.Sprintf("%s:%s", d.GetProvider(), res.ID),
		Content:   content,
		Provider:  d.GetProvider(),
	}
	input, _ := convertor.ToJson(messages)
	_ = fileutil.WriteStringToFile(fmt.Sprintf("./tmp/%s.md", d.GetProvider()), fmt.Sprintf("%s\n%s\n\n%s\n\n\n\n", data.RequestId, input, content), true)

	if !strutil.ContainsAny(content, []string{"【行程安排】", "[行程安排]"}) {
		return nil, errors.New("行程生成失败，建议设置更多选项后重试")
	}

	return &data, nil
}

func (d *Doubao2) ChatSuggestions(ctx context.Context, db *gorm.DB, user, messageId string) ([]string, error) {
	panic("implement me")
}

func (d *Doubao2) ParseOptions(text string) (*beans.TravelPromptOptions, error) {
	lines := strings.Split(text, "\n")
	var (
		lv2Lines []string
	)

	pattern := regexp.MustCompile(`- \*\*(.*?)\*\*`)

	var match bool
	for _, v := range lines {
		v = strutil.Trim(v)
		if v == "" {
			continue
		}
		v = pattern.ReplaceAllString(v, "### $1")

		//匹配markdown一级标题
		if mat := d.lv1Exp.FindStringSubmatch(v); len(mat) >= 2 {
			if match {
				break
			} else if strings.TrimLeft(strutil.Trim(mat[1], "【】[]"), "- ") == "行程选项" {
				match = true
				continue
			}
		}
		if match {
			lv2Lines = append(lv2Lines, v)
		}
	}

	if !match || len(lv2Lines) == 0 {
		return nil, errors.New("未匹配到行程选项")
	}
	ret := &beans.TravelPromptOptions{}
	rg := regexp.MustCompile("[ :：【】\\[\\]]*")
	for _, v := range lv2Lines {
		if strutil.IsBlank(v) {
			continue
		}
		v = strutil.Trim(v)
		if strings.Index(v, "出发地") == 0 {
			ret.From = rg.ReplaceAllString(strings.ReplaceAll(v, "出发地", ""), "")
		} else if strings.Index(v, "目的地") == 0 {
			ret.To = rg.ReplaceAllString(strings.ReplaceAll(v, "目的地", ""), "")
		} else if strings.Index(v, "出发日期") > -1 {
			sd := rg.ReplaceAllString(strings.ReplaceAll(v, "出发日期", ""), "")
			if t, err := time.Parse(constmap.DateFmtLong, sd); err == nil {
				ret.StartDate = t.Unix()
			} else {
				ret.StartDate = time.Now().AddDate(0, 0, 1).Unix()
			}
		} else if strings.Index(v, "兴趣偏好") > -1 {
			ret.Interest = rg.ReplaceAllString(strings.ReplaceAll(v, "兴趣偏好", ""), "")
		} else if strings.Index(v, "节奏强度") > -1 {
			ret.Strength = rg.ReplaceAllString(strings.ReplaceAll(v, "节奏强度", ""), "")
		} else if strings.Index(v, "住宿条件") > -1 {
			ret.Accommodation = rg.ReplaceAllString(strings.ReplaceAll(v, "住宿条件", ""), "")
		} else if strings.Index(v, "交通方式") > -1 {
			ret.Transport = rg.ReplaceAllString(strings.ReplaceAll(v, "交通方式", ""), "")
		} else if strings.Index(v, "出游方式") > -1 {
			ret.HowPlay =
				utils.If(strings.Index(rg.ReplaceAllString(strings.ReplaceAll(v, "出游方式", ""), ""), "目的地") > -1,
					constmap.TravelHowPlayDestination, constmap.TravelHowPlayRouting)
		}
	}
	if strings.Index(ret.From, "xx") > -1 || strings.Index(ret.To, "xx") > -1 {
		return nil, errors.New("[未匹配到行程选项]")
	}
	return ret, nil
}

func (d *Doubao2) Parse(text string, db *gorm.DB, data *beans.RenderTravelJourney) error {
	lines := strings.Split(text, "\n")
	var (
		err      error
		lv2Lines []string
	)

	pattern := regexp.MustCompile(`- \*\*(.*?)\*\*`)

	lv1Lines := []string{
		"【主题】",
		"【行程安排】",
		"【注意事项】",
		"【适合季节和人群】",
		"【行程预算】",
	}

	for _, v := range lines {
		if d.isBlankLine(v) {
			continue
		}
		v = pattern.ReplaceAllString(v, "### $1")

		//匹配markdown一级标题
		if strutil.ContainsAny(v, lv1Lines) {
			if len(lv2Lines) > 0 {
				err = d.processLv2(lv2Lines, data)
				if err != nil {
					return err
				}
				lv2Lines = lv2Lines[:0]
			}
		}
		lv2Lines = append(lv2Lines, v)
	}
	if len(lv2Lines) > 0 {
		err = d.processLv2(lv2Lines, data)
		if err != nil {
			return err
		}
	}

	return nil
}

func (d *Doubao2) processLv2(lines []string, data *beans.RenderTravelJourney) error {
	var mat []string
	for i, v := range lines {
		if mat = d.lv1Exp.FindStringSubmatch(v); len(mat) >= 2 {
			lines = lines[i:]
			break
		}
	}
	if len(mat) < 2 {
		return nil
	}

	var err error
	my_logger.Infof("match %s", zap.String("subject", mat[1]))
	switch strings.TrimLeft(strutil.Trim(mat[1], "【】[]"), "- ") {
	case "主题":
		err = d.parseTitle(lines[1:], data)
	case "行程安排":
		err = d.parseJourney(lines[1:], data)
	case "注意事项":
		err = d.parseNotice(lines[1:], data)
	case "适合季节和人群":
		err = d.parseFitFor(lines[1:], data)
	case "行程预算":
		err = d.parseBudget(lines[1:], data)
	}
	return err
}

func (d *Doubao2) parseTitle(lines []string, data *beans.RenderTravelJourney) error {
	rg := regexp.MustCompile("[ :：【】\\[\\]]*")
	for _, v := range lines {
		if strings.Index(v, "标题") == 0 {
			data.Title = rg.ReplaceAllString(strings.ReplaceAll(v, "标题", ""), "")
		} else if strings.Index(v, "子标题") == 0 {
			data.Subtitle = rg.ReplaceAllString(strings.ReplaceAll(v, "子标题", ""), "")
		}
	}
	return nil
}

func (d *Doubao2) isBlankLine(v string) bool {
	return strutil.IsBlank(v) || strings.Index(v, "---") == 0
}

func (d *Doubao2) parseJourney(lines []string, data *beans.RenderTravelJourney) error {
	var (
		err          error
		sectionLines []string
	)
	for _, v := range lines {

		if d.lv2Exp.MatchString(v) {
			if len(sectionLines) > 0 {
				err = d.parseJourneySection(sectionLines, data)
				if err != nil {
					return err
				}
				sectionLines = sectionLines[:0]
			}
		}
		sectionLines = append(sectionLines, v)
	}
	if len(sectionLines) > 0 {
		err = d.parseJourneySection(sectionLines, data)
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *Doubao2) parseJourneySection(lines []string, data *beans.RenderTravelJourney) error {
	mat := d.lv2Exp.FindStringSubmatch(lines[0])
	if len(mat) < 2 {
		return errors.New("not found section title")
	}
	var (
		journey = &beans.Journey{
			Title:    strutil.Trim(mat[1], ":：[]【】"),
			Timeline: make([]*beans.JTimeline, 0),
		}
		subLines []string
		//rg       = regexp.MustCompile("[ \"#\\-\\*]*")//去除 - **上午** 这种markdown
	)

	hotelBlockStart := false
	hotelLineStart := 0
	hotelLines := make([]string, 0)
	rghotel := regexp.MustCompile("^\\s*```.*\\s*$")
	for i, v := range lines {
		if rghotel.MatchString(v) {
			hotelLines = append(hotelLines, v)
			if hotelBlockStart {
				lines = append(lines[:hotelLineStart], lines[i+1:]...)
				break
			} else {
				hotelLineStart = i
				hotelBlockStart = true
			}
		} else if hotelBlockStart {
			hotelLines = append(hotelLines, v)
		}
	}
	hotelCondLine := strutil.Trim(strings.Join(hotelLines, ""))
	if !strutil.IsBlank(hotelCondLine) {
		journey.HotelConds = &beans.HotelSearchConditions{}
		_ = json.Unmarshal([]byte(utils.RemoveJsonTail(hotelCondLine)), journey.HotelConds)
	}

	var startMatchTimeline bool
	for _, v := range lines[1:] {
		if strings.Contains(v, "景点名字") {
			continue
		}
		if d.lv3Exp.MatchString(v) {
			startMatchTimeline = true
			if len(subLines) > 0 {
				if err := d.parseJourneyTimeline(subLines, journey); err != nil {
					return err
				}
				subLines = subLines[:0]
			}
		} else if strings.Index(v, "当日主题") == 0 {
			v = strings.Replace(v, "当日主题", "", 1)
			if d.colonExp.MatchString(v) {
				v = d.colonExp.ReplaceAllString(v, "")
			}
			journey.Subject = strutil.Trim(v, "【】[]")
			continue
		} else if strings.Index(v, "特产") == 0 {
			v = strings.Replace(v, "特产", "", 1)
			if d.colonExp.MatchString(v) {
				v = d.colonExp.ReplaceAllString(v, "")
			}
			str := strutil.Trim(v, "无")
			if str != "" && journey.Speciality != "" {
				journey.Speciality += fmt.Sprintf("、%s", str)
			} else if str != "" {
				journey.Speciality = str
			}
			continue
		} else if strings.Index(v, "城市") == 0 {
			v = strings.Replace(v, "城市", "", 1)
			if d.colonExp.MatchString(v) {
				v = d.colonExp.ReplaceAllString(v, "")
			}
			if v == "无" {
				continue
			}

			if v != "" && journey.Zones != "" {
				journey.Zones += fmt.Sprintf("、%s", v)
			} else if v != "" {
				journey.Zones = strutil.Trim(v, "- []【】")
			}
			continue
		}
		if startMatchTimeline {
			subLines = append(subLines, v)
		}
	}
	if len(subLines) > 0 {
		if err := d.parseJourneyTimeline(subLines, journey); err != nil {
			return err
		}
	}

	//一天的行程里面匹配到景点了，把时间的节点删除
	//timelines := make([]*beans.JTimeline, 0)
	//re := regexp.MustCompile("^(早晨|上午|中午|下午|傍晚|晚上)")
	//slice.ForEach(journey.Timeline, func(_ int, item *beans.JTimeline) {
	//	if !re.MatchString(item.Title) {
	//		timelines = append(timelines, item)
	//	}
	//})
	//if len(timelines) > 0 {
	//	journey.Timeline = timelines
	//}
	data.List = append(data.List, journey)
	return nil
}

func (d *Doubao2) parseJourneyTimeline(lines []string, data *beans.Journey) error {
	var subLines []string
	var timeline = &beans.JTimeline{
		Pics: make([]string, 0),
		Type: constmap.JTimelineText,
	}

	if mat := d.lv3Exp.FindStringSubmatch(lines[0]); len(mat) < 2 {
		return errors.New("match lv3 failed")
	} else {
		timeline.Title = mat[1]
		if tmp := utils.SplitBySymbol(timeline.Title, 2); len(tmp) == 2 {
			timeline.Time = tmp[0]
		} else {
			timeline.Time = mat[1]
		}
	}

	if isHotelText(timeline.Title) {
		return nil
	}

	data.Timeline = append(data.Timeline, timeline)

	for _, v := range lines[1:] {
		v = strutil.Trim(v, "- 【】[].。")
		if v == "行程内容" { //模型会输出单独一行"[行程内容]"
			continue
		}
		if len(subLines) == 0 && !d.lv4Exp.MatchString(v) {
			timeline.Desc += v + "。"
			continue
		}
		if d.lv4Exp.MatchString(v) {
			if len(subLines) > 0 {
				if err := d.parseTimelineScene(timeline, subLines, data); err != nil {
					return err
				}
			}
			subLines = subLines[:0]
		}
		subLines = append(subLines, v)
	}
	if strutil.IsBlank(timeline.Desc) {

		tmp := utils.SplitBySymbol(timeline.Title, -1)
		if len(tmp) > 2 {
			timeline.Title = strings.Join(tmp[0:2], "：")
		}
	}

	if timeline.Desc == timeline.Title {
		timeline.Desc = ""
	}

	if len(subLines) > 0 {
		if err := d.parseTimelineScene(timeline, subLines, data); err != nil {
			return err
		}
	}
	return nil
}

func (d *Doubao2) parseTimelineScene(firstTimeline *beans.JTimeline, lines []string, data *beans.Journey) error {
	var timeline = &beans.JTimeline{
		Pics: make([]string, 0),
		Type: constmap.JTimelineScene,
		Time: firstTimeline.Time,
	}
	if mat := d.lv4Exp.FindStringSubmatch(lines[0]); len(mat) < 2 {
		return errors.New("match lv4 failed")
	} else {
		timeline.Title = mat[1]
	}
	if timeline.Title == "无" && firstTimeline.Title == firstTimeline.Time && !isHotelText(timeline.Title) { //未输出景点
		firstTimeline.Desc = ""
		for _, v := range lines[1:] {
			v = strings.TrimRight(strutil.Trim(v, "- "), ".。")
			if strutil.IsBlank(v) {
				continue
			}
			firstTimeline.Desc += v + "。"
		}
		return nil
	}
	if isHotelText(timeline.Title) {
		return nil
	}
	for _, v := range lines[1:] {
		v = strings.TrimRight(strutil.Trim(v, "- "), ".。")
		if strutil.IsBlank(v) {
			continue
		}
		timeline.Desc += v + "。"
	}
	data.Timeline = append(data.Timeline, timeline)
	return nil
}

func (d *Doubao2) parseNotice(lines []string, data *beans.RenderTravelJourney) error {
	data.Notice = strutil.Trim(strings.Join(lines, "\n"))
	return nil
}

func (d *Doubao2) parseFitFor(lines []string, data *beans.RenderTravelJourney) error {
	for _, v := range lines {
		if strings.Contains(v, "适合季节") {
			v = strings.Replace(v, "适合季节：", "", 1)
			//匹配季节
			var seasons []string
			if strings.Contains(v, "全年") || strings.Contains(v, "四季") {
				seasons = append(seasons, "全年")
			} else {
				for _, sv := range []string{"春", "夏", "秋", "冬"} {
					if strings.Contains(v, sv) {
						seasons = append(seasons, sv)
					}
				}
			}
			if len(seasons) > 0 {
				data.FitFor = strings.Join(seasons, "")
				return nil
			}
			//未匹配到季节，匹配月份
			mat := d.fitForMonth.FindStringSubmatch(v)
			if len(mat) > 0 {
				data.FitFor = strutil.RemoveWhiteSpace(mat[0], true)
				return nil
			}
		} else if strings.Index(v, "游玩月份") > -1 {
			v = strings.Replace(v, "游玩月份", "", 1)
			v = d.colonExp.ReplaceAllString(v, "")
			v = strutil.Trim(v, "【】[]")
			v = strings.ReplaceAll(v, "，", ",")
			if m, err := utils.ToArray[int](v, ","); err == nil {
				data.FitMonths = utils.Join(m, ",")
			}
		}
	}
	return nil
}

func (d *Doubao2) parseBudget(lines []string, data *beans.RenderTravelJourney) error {
	v := regexp.MustCompile("\\d+(\\.\\d+)?\\s*元").FindStringSubmatch(lines[0])
	if len(v) > 0 {
		data.Budget = strutil.Trim(strutil.RemoveWhiteSpace(v[0], true))
	}
	if len(lines) > 1 {
		data.BudgetDetail = strings.Join(lines[1:], "\n")
	}
	return nil
}
