package chat_asm

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
	"roadtrip-api/src/components/business/ai"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

const (
	chatListExpireDur = constmap.TimeDur30d
	chatExpireSec     = constmap.TimeSec7d
	chatExpireDur     = constmap.TimeDur7d
)

type ContextChatListItem struct {
	CreatedAt int64
	ContextId string
	Title     string
	Expire    int64
}
type ContextChatItem struct {
	CreatedAt   int64
	Role        string
	RequestId   string
	NeedMore    bool
	Content     string
	ContentType ai.ContentType
}

type ContextChat struct {
	ContextId string
	List      []*ContextChatItem
}

func (z *ContextChatItem) MarshalBinary() (data []byte, err error) {
	return json.Marshal(z)
}

func (z *ContextChatItem) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, z)
}

func ContextChatList(userId uint) ([]*ContextChatListItem, error) {
	ckey := fmt.Sprintf(constmap.RKCtxChatList, userId)
	var list constmap.GenericList[*ContextChatListItem]
	if !my_cache.Get(ckey, &list) {
		return make([]*ContextChatListItem, 0), nil
	}
	now := time.Now().Unix()
	ret := slice.FilterMap(list, func(_ int, item *ContextChatListItem) (*ContextChatListItem, bool) {
		if item.Expire < now {
			return nil, false
		}
		return item, true
	})
	return ret, nil
}

func ContextChatListSave(userId uint, contextId, newContextId, title string) error {
	list, err := ContextChatList(userId)
	if err != nil {
		return err
	}
	var chat *ContextChatListItem
	for _, v := range list {
		if v.ContextId == contextId {
			chat = v
			break
		}
	}

	if chat == nil {
		chat = &ContextChatListItem{
			CreatedAt: time.Now().Unix(),
			ContextId: contextId,
			Expire:    time.Now().Add((chatExpireSec - constmap.TimeSec1h) * time.Second).Unix(), //过期时间往前推一点
		}
		list = utils.Unshift(list, chat)
	} else {
		chat.Expire = time.Now().Add((chatExpireSec - constmap.TimeSec1h) * time.Second).Unix() //过期时间往前推一点
	}
	if strutil.IsBlank(chat.Title) {
		chat.Title = title
	}
	if newContextId != "" {
		chat.ContextId = newContextId
	}
	if len(list) > 10 {
		list = list[0:10]
	}

	resConv := constmap.GenericList[*ContextChatListItem](list)
	return my_cache.Set(fmt.Sprintf(constmap.RKCtxChatList, userId),
		&resConv,
		chatListExpireDur)
}

func ContextChatCreate(userId uint) (*ai.ChatContextCreateResponse, error) {
	rsp, err := ai.NewDefaultAI().ChatContextCreate(chatExpireSec)
	if err != nil {
		my_logger.Errorf("ContextChatCreate", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "新会话创建失败")
	}
	return rsp, nil
}

func ContextChatSave(userId uint, contextId, newContextId string, chats ...*ContextChatItem) error {
	ckey := fmt.Sprintf(constmap.RLCtxChat, userId, contextId)
	pipe := my_cache.RedisClient().Pipeline()
	pipe.RPush(ckey, slice.Map(chats, func(index int, item *ContextChatItem) interface{} {
		return interface{}(item)
	})...)
	pipe.Expire(ckey, chatExpireDur)
	if newContextId != "" {
		pipe.Rename(ckey, fmt.Sprintf(constmap.RLCtxChat, userId, newContextId))
	}
	_, err := pipe.Exec()
	return err
}

func ContextChatDetail(userId uint, contextId string) (*ContextChat, error) {
	ckey := fmt.Sprintf(constmap.RLCtxChat, userId, contextId)
	cmd := my_cache.RedisClient().LRange(ckey, 0, -1)
	if err := cmd.Err(); err != nil {
		my_logger.Errorf("ContextChatDetail", zap.Error(err))
		return nil, utils.NewErrorStr(constmap.ErrorSystem, constmap.ErrorMsgSystem)
	}
	rsp := &ContextChat{
		ContextId: contextId,
		List:      make([]*ContextChatItem, 0),
	}
	slice.ForEach(cmd.Val(), func(index int, item string) {
		if strutil.IsBlank(item) {
			return
		}
		v := &ContextChatItem{}
		if err := json.Unmarshal([]byte(item), &v); err != nil {
			my_logger.Errorf("ContextChatDetail", zap.Error(err))
		} else {
			rsp.List = append(rsp.List, v)
		}
	})
	return rsp, nil
}

func ContextChatPrompt(userId uint, contextId, sysPrompt, prompt, title string) (*ai.ChatContextCompletionResponse, error) {
	rsp, err := ai.NewDefaultAI().ChatContextCompletion(contextId, sysPrompt, prompt)
	if err != nil {
		my_logger.Errorf("ContextChatPrompt", zap.Error(err))
		return nil, err
	}
	ContextChatSaveChat(rsp, userId, contextId, utils.If(strutil.IsBlank(prompt), title, prompt), "")
	return rsp, nil
}

func ContextChatSaveChat(rsp *ai.ChatContextCompletionResponse, userId uint, contextId, prompt, newContextId string) {
	now := time.Now().Unix()
	if err := ContextChatSave(userId, contextId, newContextId, &ContextChatItem{
		CreatedAt: now,
		Role:      "user",
		Content:   prompt,
	}, &ContextChatItem{
		CreatedAt:   now,
		Role:        "system",
		RequestId:   rsp.RequestId,
		NeedMore:    rsp.NeedMore,
		Content:     rsp.Content,
		ContentType: rsp.ContentType,
	}); err != nil {
		my_logger.Infof("ContextChatPrompt", zap.Error(err))
	}
	if err := ContextChatListSave(userId, contextId, newContextId, prompt); err != nil {
		my_logger.Infof("ContextChatPrompt", zap.Error(err))
	}
}
