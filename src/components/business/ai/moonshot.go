package ai

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"net/http"
	"os"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
)

type Moonshot struct {
}

func NewMoonshot() *Moonshot {
	return &Moonshot{}
}

func (m Moonshot) GetProvider() string {
	return "moonshot"
}

func (m Moonshot) ChatCompletionMessage(prompt string) (*ChatCompletionResponse, error) {
	messages := gin.H{
		"model": "moonshot-v1-8k",
		"messages": []gin.H{
			{
				"role":    "user",
				"content": prompt,
			},
			{
				"role":    "system",
				"content": "给出旅行规划方案",
			},
			{
				"role":    "assistant",
				"content": "请输出json格式",
			},
		},
		"response_format": gin.H{
			"type": "json_object",
		},
	}

	buf, _ := json.Marshal(messages)

	url := "https://api.moonshot.cn/v1/chat/completions"
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(buf))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+config.Config.Moonshot.ApiKey)

	client := &http.Client{}

	var resp *http.Response
	var err error
	if resp, err = client.Do(req); err != nil {
		return nil, err
	} else if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}
	defer resp.Body.Close()

	if buf, err = io.ReadAll(resp.Body); err != nil {
		return nil, err
	}

	_ = os.WriteFile("./tmp/moonshot.json", buf, os.ModePerm)

	var b struct {
		Id      string `json:"id"`
		Object  string `json:"object"`
		Model   string `json:"model"`
		Choices []struct {
			Index   int `json:"index"`
			Message struct {
				Role    string `json:"role"`
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	}
	if err := json.Unmarshal(buf, &b); err != nil {
		return nil, err
	}

	if len(b.Choices) == 0 {
		return nil, errors.New("没找到您要的答案")
	}

	data := ChatCompletionResponse{
		RequestId: fmt.Sprintf("%s:%s", m.GetProvider(), b.Id),
		Content:   b.Choices[0].Message.Content,
		Provider:  m.GetProvider(),
	}

	my_logger.Infof("moonshot response", zap.Any("data", data))

	return &data, nil
}

func (m Moonshot) Parse(node string, db *gorm.DB, data *beans.RenderTravelJourney) error {
	panic("implement me")
}
