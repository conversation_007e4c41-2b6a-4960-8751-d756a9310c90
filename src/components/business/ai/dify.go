package ai

import (
	"context"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/utils"
)

type Dify struct {
}

func (d Dify) GetProvider() string {
	return "dify"
}

func (d Dify) ChatCompletionMessage(prompt string) (*ChatCompletionResponse, error) {
	panic("implement me")
}

func (d Dify) ChatContextCreate(ttl int) (*ChatContextCreateResponse, error) {
	panic("implement me")
}

func (d Dify) ChatContextCompletion(ctxId, sysPrompt, userInput string) (*ChatContextCompletionResponse, error) {
	panic("implement me")
}

func (d Dify) ChatContextStreaming(ctx context.Context, db *gorm.DB, req *ChatStreamRequest) (*ChatContextStreamingResponse, error) {
	rsp, err := my_dify.ChatStreaming(ctx, db, &my_dify.DifyChatRequest{
		User:  req.User,
		Query: req.Prompt,
		Inputs: map[string]any{
			"sys_prompt":     req.SysPrompt,
			"url":            req.Url,
			"force_generate": utils.If(req.ForceGenerate, 1, 0),
			"city":           req.City,
			"location":       req.Location,
			"chat_type":      req.ChatType,
		},
		ConversationId: req.ConversationId,
		ResponseMode:   "streaming", //streaming/blocking
	})
	if err != nil {
		return nil, err
	}
	return &ChatContextStreamingResponse{
		RequestId: utils.UUID(),
		Provider:  d.GetProvider(),
		Reader:    rsp.Reader,
	}, nil
}

func (d Dify) ChatContextStreamingHotel(ctx context.Context, db *gorm.DB, req *ChatStreamRequest) (*ChatContextStreamingResponse, error) {
	rsp, err := my_dify.ChatStreamingHotel(ctx, db, &my_dify.DifyChatRequest{
		User:  req.User,
		Query: req.Prompt,
		Inputs: map[string]any{
			"sys_prompt": req.SysPrompt,
			"city":       req.City,
			"location":   req.Location,
		},
		ConversationId: req.ConversationId,
		ResponseMode:   "streaming", //streaming/blocking
	})
	if err != nil {
		return nil, err
	}
	return &ChatContextStreamingResponse{
		RequestId: utils.UUID(),
		Provider:  d.GetProvider(),
		Reader:    rsp.Reader,
	}, nil
}

func (d Dify) ChatSuggestions(ctx context.Context, db *gorm.DB, user, messageId string) ([]string, error) {
	return my_dify.ChatSuggestions(ctx, db, user, messageId)
}

func (d Dify) Parse(node string, db *gorm.DB, data *beans.RenderTravelJourney) error {
	return NewDoubao2().Parse(node, db, data)
}

func (d Dify) ParseOptions(text string) (*beans.TravelPromptOptions, error) {
	return NewDoubao2().ParseOptions(text)
}

func NewDify() *Dify {
	return &Dify{}
}
