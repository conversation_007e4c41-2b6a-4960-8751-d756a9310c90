package qiyeweixin_biz

import (
	"bytes"
	"encoding/json"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"roadtrip-api/src/config"
)

func SendQiyeWeixinMessage(subject, message string) error {
	content := "# " + subject + "\n" + message

	data := gin.H{
		"msgtype": "markdown",
		"markdown": gin.H{
			"content": content,
		},
	}
	jsonData, _ := convertor.ToJson(data)
	resp, err := http.Post(config.Config.QiyeWeixin.Robot.Webhook, "application/json", bytes.NewBufferString(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return errors.New(resp.Status)
	}

	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	var rsp struct {
		ErrCode int    `json:"errcode"`
		ErrMsg  string `json:"errmsg"`
	}
	if err := json.Unmarshal(respData, &rsp); err != nil {
		return err
	}
	if rsp.ErrCode != 0 {
		return errors.New(rsp.ErrMsg)
	}
	return nil
}
