package poi_biz

import (
	"gorm.io/gorm"
	"regexp"
	"roadtrip-api/src/models"
	"sync"
)

var onceLoad sync.Once
var names []string

func SimplifyZones(db *gorm.DB) []string {
	onceLoad.Do(func() {
		re := regexp.MustCompile("市|自治区|自治州|地区|城区|盟|县|旗|省|特别行政区")

		var zones []models.Zone
		db.Find(&zones)
		for _, i := range zones {
			names = append(names, re.ReplaceAllString(i.Name, ""))
		}
	})

	return names
}
