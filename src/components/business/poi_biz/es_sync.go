package poi_biz

import (
	"encoding/json"
	"fmt"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
	"time"

	"roadtrip-api/src/utils/typeset"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/mathutil"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type esSync struct{}

var EsSyncBiz esSync

func (o esSync) FetchCityZone(db *gorm.DB, start time.Time, force bool) {
	type bulkDoc struct {
		es2.Zone
		Id string
	}
	var nextId uint

	for {
		var where strings.Builder
		var whereArgs []any
		where.WriteString("id>? and state=?")
		whereArgs = append(whereArgs, nextId, constmap.Enable)
		if !force {
			where.WriteString(" and updated_at>?")
			whereArgs = append(whereArgs, start)
		}
		var list []models.Zone
		if err := db.Limit(100).
			Where(where.String(), whereArgs...).
			Order("id asc").Find(&list).Error; err != nil {

			my_logger.Errorf("fetch zone error", zap.Error(err))
			break
		}
		if len(list) == 0 {
			break
		}

		var items []bulkDoc

		list = zone_biz.NewZoneBiz().NormalizeZoneNames(list)

		// 收集所有需要查询的zone ID
		zoneIds := make([]uint, 0)
		for _, zone := range list {
			zoneIds = append(zoneIds, zone.ID)
		}

		// 使用zone_biz批量查询zone信息到省级
		zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, constmap.ZoneLevelProvince)

		var keywordBuilder strings.Builder
		for _, zone := range list {
			keywordBuilder.Reset()
			// 构建Keywords：从当前级别一直到顶级，有啥拼啥
			buildZoneKeywords(zoneMap, zone.ID, &keywordBuilder)

			items = append(items, bulkDoc{
				Zone: es2.Zone{
					PoiModel: es2.PoiModel{
						Name:     zone.Name,
						Location: []float64{zone.Lng, zone.Lat},
						Type:     constmap.PoiTypeZone,
						ObjId:    zone.ID,
						Keywords: keywordBuilder.String(),
					},
					Level: zone.Level,
				},
				Id: utils.EsPoiId(constmap.PoiTypeZone, zone.ID),
			})

			nextId = zone.ID
		}

		var bulkBody strings.Builder
		for _, vk := range slice.Chunk(items, 100) {
			for _, v := range vk {
				bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexPoi, v.Id))
				b, _ := json.Marshal(v.Zone)
				bulkBody.Write(append(b, '\n'))
			}
		}
		if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
			my_logger.Errorf("es error", zap.Error(err))
		}
	}
}

// 从zone及其父级关系中构建完整的地区关键词
func buildZoneKeywords(zoneMap map[uint]*models.Zone, zoneId uint, builder *strings.Builder) {

	if zone, exists := zoneMap[zoneId]; exists {
		// 从当前zone开始，一直遍历到parent==nil，有啥拼啥
		var names []string
		for current := zone; current != nil; current = current.Parent {
			names = append(names, current.Name)
		}
		// 反向拼接，从顶级到当前级别
		for i := len(names) - 1; i >= 0; i-- {
			builder.WriteString(names[i])
		}
	}
	// 如果zone不存在，builder保持为空
}

func (o esSync) FetchHotel(db *gorm.DB, start time.Time, force bool) {
	var nextId uint
	for {
		var where strings.Builder
		var whereArgs []any
		where.WriteString("hotels.id>?")
		whereArgs = append(whereArgs, nextId)
		if !force {
			where.WriteString(" and hotels.updated_at>?")
			whereArgs = append(whereArgs, start)
		}
		var list []models.Hotel
		db.Limit(100).Where(where.String(), whereArgs...).
			Order("hotels.id asc").
			Find(&list)
		if len(list) == 0 {
			break
		}

		nextId = list[len(list)-1].ID

		o.syncHotel(db, list)
	}
}

func (o esSync) SyncHotelByIds(db *gorm.DB, ids []uint) {
	var hotels []models.Hotel
	db.Find(&hotels, ids)
	o.syncHotel(db, hotels)
}

func (o esSync) syncHotel(db *gorm.DB, list []models.Hotel) {
	type bulkDoc struct {
		es2.Hotel
		Id string
	}
	if len(list) == 0 {
		return
	}

	var items []bulkDoc

	// 收集所有需要查询的ZoneId
	zoneIds := make([]uint, 0)
	for _, hotel := range list {
		if hotel.ZoneId > 0 {
			zoneIds = append(zoneIds, hotel.ZoneId)
		}
	}

	// 使用zone_biz批量查询zone信息到省级
	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, constmap.ZoneLevelProvince)

	var keywordBuilder strings.Builder
	for _, hotel := range list {
		my_logger.Infof("sync hotel", zap.Uint("id", hotel.ID))

		keywordBuilder.Reset()
		// 构建Keywords：地区层级+酒店名+地址
		buildZoneKeywords(zoneMap, hotel.ZoneId, &keywordBuilder)
		keywordBuilder.WriteString(hotel.Name)
		keywordBuilder.WriteString(hotel.Address)

		// 获取城市名称用于CityName字段
		var cityName string
		if zone, exists := zoneMap[hotel.ZoneId]; exists {
			cityName = utils.TrimCitySuffix(zone.Name)
		}

		be := bulkDoc{
			Hotel: es2.Hotel{
				PoiModel: es2.PoiModel{
					Location: []float64{hotel.Lng, hotel.Lat},
					Type:     constmap.PoiTypeHotel,
					Name:     hotel.Name,
					ObjId:    hotel.ID,
					Keywords: keywordBuilder.String(),
				},
				CityName:  cityName,
				ZoneId:    hotel.ZoneId,
				State:     hotel.State,
				Star:      hotel.Star,
				Score:     hotel.Score,
				BrandName: hotel.BrandName,
				AvgPrice:  hotel.AvgPrice,
				GuestType: constmap.GuestTypeMainland,
			},
			Id: utils.EsPoiId(constmap.PoiTypeHotel, hotel.ID),
		}
		if hotel.GuestType != "" {
			be.GuestType = mathutil.Max(slice.FilterMap(strings.Split(hotel.GuestType, ","), func(_ int, v string) (constmap.GuestType, bool) {
				if v == "" {
					return 0, false
				}
				n, _ := convertor.ToInt(v)
				return constmap.GuestType(int(n)), true
			})...)
		}

		items = append(items, be)
	}

	var bulkBody strings.Builder
	for _, vk := range slice.Chunk(items, 100) {
		for _, v := range vk {
			bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexPoi, v.Id))
			b, _ := json.Marshal(v.Hotel)
			bulkBody.Write(append(b, '\n'))
		}
	}
	if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
		my_logger.Errorf("es error", zap.Error(err))
	}
}

func (o esSync) FetchScenicSpots(db *gorm.DB, start time.Time, force bool) {
	var nextId uint
	for {
		var where strings.Builder
		var whereArgs []any
		where.WriteString("scenics.id>?")
		whereArgs = append(whereArgs, nextId)
		if !force {
			where.WriteString(" and scenics.updated_at>?")
			whereArgs = append(whereArgs, start)
		}
		var list []models.Scenic
		db.Limit(100).Where(where.String(), whereArgs...).
			Order("scenics.id asc").
			Find(&list)
		if len(list) == 0 {
			break
		}

		nextId = list[len(list)-1].ID

		o.syncScenic(db, list)
	}
}

func (o esSync) SyncScenicByIds(db *gorm.DB, ids []uint) {
	var list []models.Scenic
	db.Where("id in ?", ids).Find(&list)
	o.syncScenic(db, list)
}

func (o esSync) syncScenic(db *gorm.DB, list []models.Scenic) {
	if len(list) == 0 {
		return
	}
	type bulkDoc struct {
		es2.Scenic
		Id string
	}
	var (
		items []bulkDoc
		tags  []string
		cate  []string
		otas  []models.ScenicOta
	)
	db.Where("scenic_id in ?", slice.ReduceBy(list, make([]uint, 0), func(_ int, v models.Scenic, agg []uint) []uint {
		return append(agg, v.ID)
	})).Find(&otas)

	// 收集所有需要查询的ZoneId
	zoneIds := make([]uint, 0)
	for _, scenic := range list {
		if scenic.ZoneId > 0 {
			zoneIds = append(zoneIds, scenic.ZoneId)
		}
	}

	// 使用zone_biz批量查询zone信息到省级
	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, zoneIds, constmap.ZoneLevelProvince)

	var keywordBuilder strings.Builder
	for _, scenic := range list {
		my_logger.Infof("sync scenic", zap.Uint("id", scenic.ID))

		if scenic.Tags != "" {
			tags = strings.Split(scenic.Tags, ",")
		}
		if scenic.Cate != "" {
			cate = strings.Split(scenic.Cate, ",")
		}

		otaSet := slice.ReduceBy(otas, typeset.NewTypeSet[string](false), func(_ int, v models.ScenicOta, agg *typeset.TypeSet[string]) *typeset.TypeSet[string] {
			if v.ScenicId == scenic.ID {
				agg.Add(v.OtaCode)
			}
			return agg
		})
		otaIdSet := slice.ReduceBy(otas, typeset.NewTypeSet[string](false), func(_ int, v models.ScenicOta, agg *typeset.TypeSet[string]) *typeset.TypeSet[string] {
			if v.ScenicId == scenic.ID {
				agg.Add(v.OtaId)
			}
			return agg
		})

		// 构建Keywords：地区层级+景点名+地址
		keywordBuilder.Reset()
		buildZoneKeywords(zoneMap, scenic.ZoneId, &keywordBuilder)
		keywordBuilder.WriteString(scenic.Name)
		keywordBuilder.WriteString(scenic.Address)

		// 获取城市名称用于CityName字段
		var cityName string
		if zone, exists := zoneMap[scenic.ZoneId]; exists {
			cityName = utils.TrimCitySuffix(zone.Name)
		}

		items = append(items, bulkDoc{
			Scenic: es2.Scenic{
				PoiModel: es2.PoiModel{
					Location: []float64{scenic.Lng, scenic.Lat},
					Type:     constmap.PoiTypeScenic,
					Name:     scenic.Name,
					ObjId:    scenic.ID,
					Ota:      otaSet.Values(),
					OtaId:    otaIdSet.Values(),
					Keywords: keywordBuilder.String(),
				},
				CityName: cityName,
				ZoneId:   scenic.ZoneId,
				State:    scenic.State,
				Level:    scenic.Level,
				Llm:      scenic.Llm,
				IsFree:   scenic.IsFree == constmap.Enable,
				Tags:     tags,
				Cate:     cate,
			},
			Id: utils.EsPoiId(constmap.PoiTypeScenic, scenic.ID),
		})
	}

	var bulkBody strings.Builder
	for _, vk := range slice.Chunk(items, 100) {
		for _, v := range vk {
			bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexPoi, v.Id))
			b, _ := json.Marshal(v.Scenic)
			bulkBody.Write(append(b, '\n'))
		}
	}
	if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
		my_logger.Errorf("es error", zap.Error(err))
	}
}
