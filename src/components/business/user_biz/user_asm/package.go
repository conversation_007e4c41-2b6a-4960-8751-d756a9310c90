package user_asm

import (
	"context"
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"gorm.io/gorm"
)

func AddUserPackage(tx *gorm.DB, payment *models.OrderPayment, isNew int, pkg *models.Package) error {
	ctx, unlock := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "addUserPkg", convertor.ToString(payment.UserId)), constmap.TimeDur1m)
	defer unlock()

	var amount int
	var exp time.Time
	if isNew == constmap.Enable {
		var cnt = user_biz.GetUserPackageCount(tx, payment.UserId)
		if !user_biz.CanUsePackageNew(cnt, pkg) {
			// 校验异常
			return tx.Create(&models.UserPackage{
				UserId:    payment.UserId,
				PaymentId: payment.ID,
				PackageId: pkg.ID,
				State:     constmap.UserPackageStateAbnormal,
				Remark:    "首充校验失败",
				Version:   1,
			}).Error
		}
		amount = pkg.Amount
		exp = user_biz.GetPackageExp(pkg.ExpNum, pkg.ExpUnit)
	} else {
		amount = pkg.Amount
		exp = user_biz.GetPackageExp(pkg.ExpNum, pkg.ExpUnit)
	}

	var upkgSum = new(models.UserPackageSummary)
	tx.Where(models.UserPackageSummary{UserId: payment.UserId}).Take(&upkgSum)

	var upkgs []*models.UserPackage
	tx.Where(models.UserPackage{
		UserId: payment.UserId,
		State:  constmap.UserPackageStateUsing,
	}).Find(&upkgs)

	if !user_biz.CanChangePackage(upkgSum, upkgs, pkg.ID) {
		return utils.NewErrorStr(constmap.ErrorSystem, "当前套餐不可切换")
	}

	if upkgSum.ID == 0 {
		upkgSum = &models.UserPackageSummary{
			UserId:      payment.UserId,
			PackageId:   pkg.ID,
			PackageName: pkg.Name,
			Version:     1,
		}
		if err := tx.Create(&upkgSum).Error; err != nil {
			return err
		}
	}

	if tx.Model(&upkgSum).Where("version=?", upkgSum.Version).Updates(map[string]any{
		"package_id":   pkg.ID,
		"package_name": pkg.Name,
		"version":      utils.IncrVersion(upkgSum.Version),
		"expire_at":    utils.If(upkgSum.ExpireAt.Before(exp), exp, upkgSum.ExpireAt),
	}).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "更新套餐汇总失败")
	}

	var upkg = &models.UserPackage{
		UserId:      payment.UserId,
		PaymentId:   payment.ID,
		PayPrice:    payment.NeedPayAmount,
		PackageId:   pkg.ID,
		PackageName: pkg.Name,
		TotalAmount: amount,
		Amount:      amount,
		State:       constmap.UserPackageStateUsing,
		ExpireAt:    exp,
		Version:     1,
	}
	if err := tx.Create(upkg).Error; err != nil {
		return err
	}

	if err := tx.Create(&models.UserPackageExt{
		UserPackageId: upkg.ID,
		Extra: convertor.ToString(&beans_asm.UserPackageExtra{
			IsNew:   isNew,
			Package: *pkg,
		}),
	}).Error; err != nil {
		return err
	}

	trans, err := account_biz.CreateAccountTransaction(tx, payment.UserId, constmap.CurrencyIntegral, amount,
		constmap.AccountLogIncrAmount, constmap.AccountLogSubBuyPackage, "")
	if err != nil {
		return err
	}
	if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, &beans.AccountLogExtra{
		PaymentId:     payment.ID,
		UserPackageId: upkg.ID,
	}); err != nil {
		return err
	}

	return nil
}

func ClearPackageAmount(db *gorm.DB, upkg *models.UserPackage) error {
	return db.Transaction(func(tx *gorm.DB) error {
		var amount = upkg.Amount
		if amount < 1 {
			if tx.Model(&upkg).Where("version=?", upkg.Version).Updates(map[string]any{
				"state":   constmap.UserPackageStateComplete,
				"version": utils.IncrVersion(upkg.Version),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新套餐失败")
			}
		} else {
			if tx.Model(&upkg).Where("version=?", upkg.Version).Updates(map[string]any{
				"state":         constmap.UserPackageStateExpired,
				"amount":        0,
				"expire_amount": gorm.Expr("expire_amount+?", amount),
				"version":       utils.IncrVersion(upkg.Version),
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新套餐失败")
			}
			trans, err := account_biz.CreateAccountTransaction(tx, upkg.UserId, constmap.CurrencyIntegral, amount,
				constmap.AccountLogDecrAmount, constmap.AccountLogSubPackageExpire, "")
			if err != nil {
				return err
			}
			if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, &beans.AccountLogExtra{
				UserPackageId: upkg.ID,
			}); err != nil {
				return err
			}
		}
		return nil
	})
}

func CostPackageAmount(tx *gorm.DB, userId uint, costAmount int, subType constmap.AccountLogSub) (*models.AccountTransaction, error) {

	if costAmount < 1 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "扣减数额无效")
	}

	ctx, unlock := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "costPkg", convertor.ToString(userId)), constmap.TimeDur1m)
	defer unlock()

	acct, err := account_biz.GetAccount(tx, userId, constmap.CurrencyIntegral)
	if err != nil {
		return nil, err
	}
	if acct.Amount < costAmount {
		return nil, utils.NewErrorStr(constmap.ErrorAccountAmountNotEnough, "余额不足")
	}

	var upkgs []*models.UserPackage
	tx.Where(models.UserPackage{
		UserId: userId,
		State:  constmap.UserPackageStateUsing,
	}).Order("expire_at ASC").Find(&upkgs)

	var pkgUseAmount int

	now := time.Now()
	for _, v := range upkgs {
		if v.ExpireAt.Before(now) || v.Amount < 1 {
			continue
		}
		var usedAmount = v.Amount
		if pkgUseAmount+usedAmount > costAmount {
			usedAmount = costAmount - pkgUseAmount
		}
		pkgUseAmount += usedAmount
		update := map[string]any{
			"amount":      v.Amount - usedAmount,
			"used_amount": v.UsedAmount + usedAmount,
			"version":     utils.IncrVersion(v.Version),
		}
		if v.Amount-usedAmount < 1 {
			update["state"] = constmap.UserPackageStateComplete
		}
		if tx.Model(&v).Where("version=?", v.Version).Updates(update).RowsAffected == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "[1]扣减套餐失败")
		}
		if pkgUseAmount >= costAmount {
			break
		}
	}

	trans, err := account_biz.CreateAccountTransaction(tx, userId, constmap.CurrencyIntegral, costAmount,
		constmap.AccountLogDecrAmount, subType, "")
	if err != nil {
		return nil, err
	}
	if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
		return nil, err
	}

	return trans, nil
}
