package user_asm

import (
	"context"
	"fmt"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/convertor"
	"gorm.io/gorm"
)

func GenerateInviteCode(db *gorm.DB, user *models.User) error {

	if user.InviteCode != "" {
		return nil
	}

	ctx, unlock := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "GenInviteCode", ""), constmap.TimeDur1m)
	defer unlock()

	var inviteCode string
	var cnt int64
	for {
		inviteCode = utils.GenInviteCode(6)
		if db.Model(&models.User{}).Where(models.User{
			InviteCode: inviteCode,
		}).Count(&cnt); cnt > 0 {
			continue
		}

		if err := db.Transaction(func(tx *gorm.DB) error {

			if db.Model(user).Updates(models.User{
				InviteCode: inviteCode,
			}).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新邀请码失败")
			}

			// 初始化积分账户
			val, err := sys_config_biz.GetConfig(tx, constmap.SysConfigInitIntegralAmount)
			if err != nil {
				return err
			}
			if v, ok := val.(string); !ok {
				return utils.NewErrorStr(constmap.ErrorSystem, "获取初始积分配置失败")
			} else if amount, err := convertor.ToInt(v); err != nil {
				return err
			} else {
				trans, err := account_biz.CreateAccountTransaction(tx, user.ID, constmap.CurrencyIntegral, int(amount),
					constmap.AccountLogIncrAmount, constmap.AccountLogSubInitUser, "")
				if err != nil {
					return err
				}
				if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
					return err
				}
			}
			return nil
		}); err != nil {
			return err
		}
		break
	}
	return nil
}
