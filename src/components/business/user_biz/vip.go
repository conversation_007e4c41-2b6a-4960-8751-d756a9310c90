package user_biz

import (
	"encoding/json"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"time"
)

// 用户会员是否有效
func IsUserVipValid(userVip *models.UserVip) bool {
	now := time.Now()
	return userVip.State == constmap.Enable && userVip.Start.Before(now) && userVip.End.After(now)
}

// 会员内容是否有变更
func IsUserVipChanged(userVip *models.UserVip, latestVipConf *models.VipConf) (*beans.UserVipExtra, bool) {
	var extra beans.UserVipExtra
	_ = json.Unmarshal([]byte(userVip.Extra), &extra)
	return &extra, extra.VipConfId != latestVipConf.ID || userVip.Price != latestVipConf.SalePrice
}
