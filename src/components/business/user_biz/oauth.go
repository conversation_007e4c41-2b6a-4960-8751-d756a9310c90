package user_biz

import (
	"fmt"
	"gorm.io/gorm"
	"roadtrip-api/src/models"
)

// GetUserOauth 根据用户ID和类型获取用户OAuth信息
func GetUserOauth(db *gorm.DB, userId int64, typ int) (*models.UserOauth, error) {
	var oauth models.UserOauth
	// 从数据库中查询指定用户的OAuth信息
	db.Where(models.UserOauth{UserId: uint(userId), Type: typ}).Take(&oauth)
	if oauth.ID == 0 {
		return nil, fmt.Errorf("user oauth:%d not found", userId)
	}
	return &oauth, nil
}
