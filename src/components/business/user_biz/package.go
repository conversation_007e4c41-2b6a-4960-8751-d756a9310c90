package user_biz

import (
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

// 获取所有已购买套餐数量
func GetUserPackageCount(db *gorm.DB, userId uint) int64 {
	var cnt int64
	db.Model(&models.UserPackage{}).Where(models.UserPackage{
		UserId: userId,
	}).Where("state <> ?", constmap.UserPackageStateAbnormal).Count(&cnt)
	return cnt
}

func CanUsePackageNew(userPackageCnt int64, pkg *models.Package) bool {
	return pkg.NewUsable == constmap.Enable && userPackageCnt == 0
}

func CanChangePackage(userPackageSummary *models.UserPackageSummary, upkgs []*models.UserPackage, packageId uint) bool {
	now := time.Now()
	// 未充值套餐或原有套餐已用完
	if userPackageSummary == nil ||
		userPackageSummary.ID == 0 ||
		userPackageSummary.ExpireAt.Before(now) ||
		userPackageSummary.PackageId == packageId {
		return true
	}

	// 有未使用的积分
	_, ok := slice.FindBy(upkgs, func(index int, item *models.UserPackage) bool {
		return item.State == constmap.UserPackageStateUsing && item.Amount > 0 && item.ExpireAt.After(now)
	})
	return !ok
}

func GetPackageExp(expNum int, expUnit constmap.PackageExpUnit) time.Time {
	var exp time.Time
	switch expUnit {
	default:
		exp = time.Time{}
	case constmap.PackageExpUnitWeek:
		exp = time.Now().AddDate(0, 0, expNum*7)
	case constmap.PackageExpUnitMonth:
		exp = time.Now().AddDate(0, expNum, 0)
	case constmap.PackageExpUnitSeason:
		exp = time.Now().AddDate(0, expNum*3, 0)
	case constmap.PackageExpUnitYear:
		exp = time.Now().AddDate(expNum, 0, 0)
	}
	return utils.GetEndOfDay(exp)
}
