package business

import (
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"math"
	"os"
	"path"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func Download(filepath string, ctx *gin.Context, handler func(ctx2 *gin.Context) error) error {
	var contentType = "application/octet-stream"
	switch path.Ext(filepath) {
	case "zip":
		contentType = "application/zip"
	}
	ctx.Header("Content-Type", contentType)
	ctx.Header("Content-Disposition", "attachment; filename="+path.Base(filepath))

	if handler != nil {
		return handler(ctx)
	} else {
		ctx.File(filepath)
	}

	return nil
}

func GetAdminLoginUser(ctx *gin.Context) (*models.AdminSession, error) {
	u, ok := ctx.Get(constmap.ContextUser)

	if !ok {
		return nil, utils.NewErrorStr(constmap.ErrorNotLogin, constmap.ErrorMsgNotLogin)
	}

	return u.(*models.AdminSession), nil
}

// MoveUploadFile 将上传的临时文件迁移到真正的目录下去
func MoveUploadFile(db *gorm.DB, resId uint) (url string, err error) {
	var tmp models.UploadTmp
	if err = db.Where("id=?", resId).First(&tmp).Error; err != nil {
		return "", err
	}

	return UploadToOss(&tmp)
}

func UploadToOss(res *models.UploadTmp) (url string, err error) {
	if res.ID == 0 {
		return "", utils.NewErrorStr(constmap.ErrorUpload, constmap.ErrorMsgUpload)
	} else if _, err := os.Stat(res.Url); err != nil {
		return "", utils.NewErrorStr(constmap.ErrorUpload, constmap.ErrorMsgUpload)
	}

	dir := path.Join(constmap.QiniuPrefix, utils.If(config.IsProduction(), "prod", "dev"), datetime.FormatTimeToStr(time.Now(), "yyyymmdd"))
	filename := path.Base(res.Url)

	url = path.Join(dir, filename)

	if err = qiniu_biz.UploadFile(res.Url, url); err != nil {
		return "", err
	}

	return url, err
}

func UploadToTmpOss(res *models.UploadTmp, exp time.Duration) (url string, err error) {
	if res.ID == 0 {
		return "", utils.NewErrorStr(constmap.ErrorUpload, constmap.ErrorMsgUpload)
	} else if _, err := os.Stat(res.Url); err != nil {
		return "", utils.NewErrorStr(constmap.ErrorUpload, constmap.ErrorMsgUpload)
	}

	dir := path.Join(constmap.QiniuPrefix, utils.If(config.IsProduction(), "prod", "dev"), datetime.FormatTimeToStr(time.Now(), "yyyymmdd"))
	filename := path.Base(res.Url)

	url = path.Join(dir, filename)

	if err = qiniu_biz.UploadTmp(res.Url, url, int(exp.Hours()/24)); err != nil {
		return "", err
	}

	return url, err
}

// 设置上传文件有效期 0表示永久有效
func SetUploadFileExpire(file string, exp time.Duration) error {
	return qiniu_biz.SetObjectExpire(file, utils.If(exp.Hours() > 0, int(math.Ceil(exp.Hours()/24)), 0))
}
