package account_biz

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/convertor"
	"gorm.io/gorm"
)

func GetAccount(tx *gorm.DB, userId uint, currency constmap.CurrencyType) (*models.UserAccount, error) {
	var account = new(models.UserAccount)
	tx.Where(models.UserAccount{
		UserId:   userId,
		Currency: currency,
	}).Take(&account)
	if account.ID == 0 {
		account.UserId = userId
		account.Currency = currency
		account.Version = 1
		if err := tx.Create(&account).Error; err != nil {
			return nil, err
		}
	}
	return account, nil
}

// accountChange 账户资金变动（私有）
func accountChange(tx *gorm.DB, userId uint, currency constmap.CurrencyType,
	typ constmap.AccountLog, subTyp constmap.AccountLogSub, amount int, extra *beans.AccountLogExtra, remark string) error {

	account, err := GetAccount(tx, userId, currency)
	if err != nil {
		return err
	}
	if (typ == constmap.AccountLogDecrAmount || typ == constmap.AccountLogConvAmount2Lock) && account.Amount < amount {
		return utils.NewErrorStr(constmap.ErrorAccountAmountNotEnough, "余额不足")
	} else if (typ == constmap.AccountLogDecrLockAmount || typ == constmap.AccountLogConvLock2Amount) && account.LockAmount < amount {
		return utils.NewErrorStr(constmap.ErrorAccountAmountNotEnough, "冻结余额不足")
	}

	logRemark := remark
	if logRemark == "" {
		logRemark = business.AccountLogSubText(subTyp)
	}

	log := models.UserAccountLog{
		UserAccountId:    account.ID,
		Type:             typ,
		SubType:          subTyp,
		ChangeAmount:     amount,
		BeforeAmount:     account.Amount,
		BeforeLockAmount: account.LockAmount,
		Remark:           logRemark,
	}
	if extra != nil {
		log.Extra = convertor.ToString(extra)
	}

	update := map[string]any{
		"version": utils.IncrVersion(account.Version),
	}
	switch typ {
	case constmap.AccountLogIncrAmount:
		update["amount"] = gorm.Expr("amount+?", amount)
		log.AfterAmount = account.Amount + amount
		log.AfterLockAmount = account.LockAmount
	case constmap.AccountLogDecrAmount:
		update["amount"] = gorm.Expr("amount-?", amount)
		log.AfterAmount = account.Amount - amount
	case constmap.AccountLogIncrLockAmount:
		update["lock_amount"] = gorm.Expr("lock_amount+?", amount)
		log.AfterLockAmount = account.LockAmount + amount
	case constmap.AccountLogDecrLockAmount:
		update["lock_amount"] = gorm.Expr("lock_amount-?", amount)
		log.AfterLockAmount = account.LockAmount - amount
	case constmap.AccountLogConvAmount2Lock:
		update["amount"] = gorm.Expr("amount-?", amount)
		update["lock_amount"] = gorm.Expr("lock_amount+?", amount)
		log.AfterAmount = account.Amount - amount
		log.AfterLockAmount = account.LockAmount + amount
	case constmap.AccountLogConvLock2Amount:
		update["amount"] = gorm.Expr("amount+?", amount)
		update["lock_amount"] = gorm.Expr("lock_amount-?", amount)
		log.AfterAmount = account.Amount + amount
		log.AfterLockAmount = account.LockAmount - amount
	default:
		return utils.NewErrorStr(constmap.ErrorSystem, "未知账户操作")
	}
	if tx.Model(&account).Where("version=?", account.Version).Updates(update).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "账户更新失败")
	}
	if err := tx.Create(&log).Error; err != nil {
		return utils.NewError(err)
	}
	return nil
}

// accountChangeReverse 账户资金反向变动（回退用，私有）
func accountChangeReverse(tx *gorm.DB, userId uint, currency constmap.CurrencyType,
	logType constmap.AccountLog, logSubType constmap.AccountLogSub, amount int, extra *beans.AccountLogExtra) error {
	var reverseType constmap.AccountLog
	switch logType {
	case constmap.AccountLogIncrAmount:
		reverseType = constmap.AccountLogDecrAmount
	case constmap.AccountLogDecrAmount:
		reverseType = constmap.AccountLogIncrAmount
	case constmap.AccountLogIncrLockAmount:
		reverseType = constmap.AccountLogDecrLockAmount
	case constmap.AccountLogDecrLockAmount:
		reverseType = constmap.AccountLogIncrLockAmount
	case constmap.AccountLogConvAmount2Lock:
		reverseType = constmap.AccountLogConvLock2Amount
	case constmap.AccountLogConvLock2Amount:
		reverseType = constmap.AccountLogConvAmount2Lock
	default:
		return utils.NewErrorStr(constmap.ErrorSystem, "不支持的账户回退操作类型")
	}
	remark := business.AccountLogSubText(logSubType) + "【退回】"
	return accountChange(tx, userId, currency, reverseType, logSubType, amount, extra, remark)
}
