package account_biz

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"fmt"
	"math/rand"
	"time"

	"gorm.io/gorm"
)

// genPlatformNo 生成唯一平台交易号（不超过15位，碰撞概率极低）
func genPlatformNo(userId uint) string {
	now := time.Now().UnixNano() / 1e6 // 毫秒
	randPart := rand.Intn(10000)       // 4位随机数
	// 格式: 毫秒后10位+用户ID后3位+4位随机数，最多15位
	return fmt.Sprintf("%010d%03d%04d", now%1e10, userId%1000, randPart)
}

// CreateAccountTransaction 创建账户交易流水，幂等（仅写入，不执行资金变动）
func CreateAccountTransaction(tx *gorm.DB, userId uint, currency constmap.CurrencyType, amount int,
	logType constmap.AccountLog, logSubType constmap.AccountLogSub, remark string) (*models.AccountTransaction, error) {

	if amount < 1 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "金额无效")
	}

	// 自动生成平台交易号
	transactionNo := genPlatformNo(userId)

	// 获取账户
	account, err := GetAccount(tx, userId, currency)
	if err != nil {
		return nil, err
	}

	trans := models.AccountTransaction{
		UserId:            userId,
		UserAccountId:     account.ID,
		TransactionNo:     transactionNo,
		Amount:            amount,
		AccountLogType:    logType,
		AccountLogSubType: logSubType,
		State:             constmap.TransactionStateInit, // 初始化
		Remark:            remark,
		Version:           1, // 乐观锁初始值
	}
	if err := tx.Create(&trans).Error; err != nil {
		return nil, err
	}
	return &trans, nil
}

// ExecuteAccountTransaction 执行账户交易（资金变动），幂等（只对未执行的流水执行）
func ExecuteAccountTransaction(tx *gorm.DB, transactionNo string, extra *beans.AccountLogExtra) error {
	var trans models.AccountTransaction
	if err := tx.Where(models.AccountTransaction{TransactionNo: transactionNo}).Take(&trans).Error; err != nil {
		return err
	}
	if trans.State != constmap.TransactionStateInit {
		// 已执行
		if trans.State == constmap.TransactionStateSuccess {
			return nil
		}
		return utils.NewErrorStr(constmap.ErrorSystem, "交易不可重复执行")
	}

	// 资金变动
	if err := accountChange(tx, trans.UserId, trans.Currency,
		trans.AccountLogType, trans.AccountLogSubType, trans.Amount, extra, trans.Remark); err != nil {
		return err
	}

	// 乐观锁更新状态
	result := tx.Model(&trans).Where("version=?", trans.Version).Updates(map[string]interface{}{
		"state":   constmap.TransactionStateSuccess,
		"version": utils.IncrVersion(trans.Version),
	})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "交易状态未更新，可能已被其他并发操作处理")
	}
	return nil
}

// GetAccountTransaction 查询账户交易流水
func GetAccountTransaction(tx *gorm.DB, transactionNo string) (*models.AccountTransaction, error) {
	var trans models.AccountTransaction
	if err := tx.Where(models.AccountTransaction{TransactionNo: transactionNo}).Take(&trans).Error; err != nil {
		return nil, err
	}
	return &trans, nil
}
