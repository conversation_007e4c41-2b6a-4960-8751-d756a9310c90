package account_biz

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"fmt"
	"math/rand"
	"time"

	"gorm.io/gorm"
)

// genReversalBatchNo 生成唯一回退批次号（不超过15位，碰撞概率极低）
func genReversalBatchNo(transactionNo string) string {
	now := time.Now().UnixNano() / 1e6 // 毫秒
	randPart := rand.Intn(10000)       // 4位随机数
	// 格式: 毫秒后10位+交易号后3位+4位随机数，最多15位
	var tail string
	if len(transactionNo) > 3 {
		tail = transactionNo[len(transactionNo)-3:]
	} else {
		tail = transactionNo
	}
	return fmt.Sprintf("%010d%s%04d", now%1e10, tail, randPart)
}

// CreateAccountTransactionReversal 创建回退交易，幂等（仅写入，不执行资金变动）
func CreateAccountTransactionReversal(tx *gorm.DB, transactionNo string, amount int, remark string) (*models.AccountTransactionReversal, error) {
	if amount < 1 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "金额无效")
	}

	// 查主交易单ID
	var trans models.AccountTransaction
	if err := tx.Where(models.AccountTransaction{
		TransactionNo: transactionNo,
	}).Take(&trans).Error; err != nil {
		return nil, err
	}
	// 金额校验，防止超额回退
	if trans.Amount < trans.ReversedAmount+amount {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "回退总金额不能超过原交易金额")
	}
	// 自动生成回退批次号
	batchNo := genReversalBatchNo(transactionNo)
	reversal := models.AccountTransactionReversal{
		AccountTransactionId: trans.ID,
		AccountTransactionNo: transactionNo,
		BatchNo:              batchNo,
		Amount:               amount,
		State:                constmap.TransactionStateInit, // 初始化
		Remark:               remark,
		Version:              1, // 乐观锁初始值
	}
	if err := tx.Create(&reversal).Error; err != nil {
		return nil, err
	}
	return &reversal, nil
}

// ExecuteAccountTransactionReversal 执行回退交易（资金回退），只需传入批次号
func ExecuteAccountTransactionReversal(tx *gorm.DB, batchNo string) error {
	var reversal models.AccountTransactionReversal
	if err := tx.Where(models.AccountTransactionReversal{BatchNo: batchNo}).Take(&reversal).Error; err != nil {
		return err
	}
	if reversal.State != constmap.TransactionStateInit {
		// 已执行
		if reversal.State == constmap.TransactionStateSuccess {
			return nil
		}
		return utils.NewErrorStr(constmap.ErrorSystem, "回退交易不可重复执行")
	}

	// 查询原交易
	var trans models.AccountTransaction
	if err := tx.Where(models.AccountTransaction{
		TransactionNo: reversal.AccountTransactionNo,
	}).Take(&trans).Error; err != nil {
		return err
	}

	// 回退金额校验
	if trans.Amount < trans.ReversedAmount+reversal.Amount {
		result := tx.Model(&reversal).Where("version=?", reversal.Version).Updates(map[string]interface{}{
			"state":   constmap.TransactionStateFail,
			"version": utils.IncrVersion(reversal.Version),
		})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "回退单状态未更新，可能已被其他并发操作处理")
		}
		return utils.NewErrorStr(constmap.ErrorSystem, "回退总金额不能超过原交易金额")
	}

	// 资金回退（严格反操作）
	if err := accountChangeReverse(tx, trans.UserId, trans.Currency, trans.AccountLogType, trans.AccountLogSubType, reversal.Amount, nil); err != nil {
		result := tx.Model(&reversal).Where("version=?", reversal.Version).Updates(map[string]interface{}{
			"state":   constmap.TransactionStateFail,
			"remark":  reversal.Remark + " 失败：" + err.Error(),
			"version": utils.IncrVersion(reversal.Version),
		})
		if result.Error != nil {
			return result.Error
		}
		if result.RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "回退单状态未更新，可能已被其他并发操作处理")
		}
		return err
	}

	// 乐观锁更新主交易单回退金额
	result := tx.Model(&trans).Where("version=?", trans.Version).Updates(map[string]interface{}{
		"reversed_amount": gorm.Expr("reversed_amount+?", reversal.Amount),
		"version":         utils.IncrVersion(trans.Version),
	})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "交易单回退金额未更新，可能已被其他并发操作处理")
	}

	// 乐观锁更新回退单状态
	result = tx.Model(&reversal).Where("version=?", reversal.Version).Updates(map[string]interface{}{
		"state":   constmap.TransactionStateSuccess,
		"version": utils.IncrVersion(reversal.Version),
	})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "回退单状态未更新，可能已被其他并发操作处理")
	}
	return nil
}

// GetAccountTransactionReversal 查询回退交易
func GetAccountTransactionReversal(tx *gorm.DB, batchNo string) (*models.AccountTransactionReversal, error) {
	var reversal models.AccountTransactionReversal
	if err := tx.Where(models.AccountTransactionReversal{BatchNo: batchNo}).Take(&reversal).Error; err != nil {
		return nil, err
	}
	return &reversal, nil
}
