package ota_asm

import (
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/validators"
	"strings"
	"sync"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type ceekeeAsm struct {
	concurNum int //调用详情接口并发数
}

func (o *ceekeeAsm) Code() string {
	return constmap.OtaCodeCeekee
}

func (o *ceekeeAsm) ApplyRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
	refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) error {

	orderId, _ := convertor.ToInt(thirdOrder.ThirdOrderId)
	res, err := my_ota.NewCeeKee().OrderCancel(my_ota.CeekeeOrderCancelReq{
		OrderSerialNo: orderId,
		Reason:        my_ota.CeekeeEnumReasonTravelCancellation,
	})
	if err != nil {
		return err
	}
	refund.OutRefundNo = convertor.ToString(res.SerialNo)
	return nil
}

func (o *ceekeeAsm) QueryRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
	refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) (*beans.OtaRefundCheckResp, error) {

	orderId, _ := convertor.ToInt(thirdOrder.ThirdOrderId)
	res, err := my_ota.NewCeeKee().OrderDetail(my_ota.CeekeeOrderDetailReq{
		OrderSerialNo: orderId,
	})

	if err != nil {
		return nil, err
	}

	rsp := &beans.OtaRefundCheckResp{}
	// 已退款状态才更新三方订单状态
	if res.HotelOrderStatus != my_ota.CeekeeEnumOrderState15Refunded {
		// 超时未退款
		if time.Now().Sub(refund.CreatedAt) > constmap.TimeDur1d {
			return nil, errors.New("思客超时未处理")
		}
		return rsp, nil
	}

	rsp.RefundOk = true
	rsp.RefundAmount = utils.CurrencyFloat2Int(res.BasicInfo.TotalRefundAmount)
	rsp.YbRefundAmount = utils.GetOtaPrice(detail.OtaPriceRate, rsp.RefundAmount, true)
	rsp.PenaltyAmount = detail.OtaAmount - rsp.RefundAmount

	return rsp, nil
}

func (o *ceekeeAsm) hotelOrderPreCheckCeekee(db *gorm.DB, request *beans.HotelOrderPreloadDto) (*beans.HotelRoom, error) {
	var c int64
	if db.Model(&models.HotelOta{}).
		Where(models.HotelOta{HotelId: request.HotelId, OtaId: request.OtaId, OtaCode: constmap.OtaCodeCeekee}).
		Count(&c); c == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "酒店关联数据异常")
	}
	otaHotelId, err := convertor.ToInt(request.OtaId)
	if err != nil {
		return nil, err
	}
	start := constmap.DateTime{Time: time.Unix(request.Start, 0)}
	end := constmap.DateTime{Time: time.Unix(request.End, 0)}

	cli := my_ota.NewCeeKee()
	res, err := cli.HotelDataValidate(my_ota.CeekeeHotelDataValidateReq{
		HotelId:         otaHotelId,
		ArrivalDate:     start,
		DepartureDate:   end,
		LastArrivalTime: constmap.DateTimeMinute{Time: datetime.BeginOfDay(start.Time).Add(18 * time.Hour)}, // 入住当日18:00
		PolicyId:        request.PolicyId,
		RoomNums:        request.Num,
	})
	if err != nil {
		return nil, err
	}
	if !res.IsCanBooking {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "选中日期酒店无法预定")
	}
	rooms, err := o.QueryHotelRoomPolicy(&beans.QueryHotelRoomPolicyDto{
		OtaId:           request.OtaId,
		Start:           request.Start,
		End:             request.End,
		LoadPolicyPrice: false,
	})
	if err != nil {
		return nil, err
	}
	var room *beans.HotelRoom
	if v, ok := slice.FindBy(rooms, func(_ int, item beans.HotelRoom) bool {
		return item.Id == request.RoomId
	}); ok {
		v.Policy = slice.Filter(v.Policy, func(_ int, v beans.HotelRoomPolicy) bool {
			return v.Id == request.PolicyId
		})
		room = &v
	}
	if room == nil || len(room.Policy) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "房型政策不存在")
	}
	return room, nil
}
func (o *ceekeeAsm) QueryHotelRoomPolicy(req *beans.QueryHotelRoomPolicyDto) ([]beans.HotelRoom, error) {
	hotelId, _ := convertor.ToInt(req.OtaId)
	res, err := my_ota.NewCeeKee().HotelRoomRateList(my_ota.CeekeeHotelRoomRateListReq{
		HotelId:       hotelId,
		ArrivalDate:   constmap.DateTime{Time: time.Unix(req.Start, 0)},
		DepartureDate: constmap.DateTime{Time: time.Unix(req.End, 0)},
	})
	if err != nil {
		return nil, err
	}
	var rooms = make([]beans.HotelRoom, 0)
	for _, room := range res.Rooms {
		roomItem := o.BuildHotelRoom(room, req.LoadPolicyPrice, req.PriceRate)
		rooms = append(rooms, roomItem)
	}
	return rooms, nil
}
func (o *ceekeeAsm) LoadHotelPolicyPrice(policy beans.HotelRoomPolicy, priceRate int64) beans.HotelRoomPolicy {
	policy.SalesPrice = utils.GetOtaPrice(priceRate, policy.SalesPrice, true)
	policy.SuggestPrice = utils.GetOtaPrice(priceRate, policy.SuggestPrice, true)
	policy.PriceDaily = slice.Map(policy.PriceDaily, func(_ int, v beans.HotelRoomPriceDaily) beans.HotelRoomPriceDaily {
		v.SalePrice = utils.GetOtaPrice(priceRate, v.SalePrice, true)
		return v
	})
	return policy
}
func (o *ceekeeAsm) loadHotelSubOrderFee(num int, policy beans.HotelRoomPolicy, subOrder *beans.SubOrderFee) {
	policy.PriceDaily = slice.Map(policy.PriceDaily, func(_ int, v beans.HotelRoomPriceDaily) beans.HotelRoomPriceDaily {
		if !v.Valid {
			return v
		}
		totalPrice := v.SalePrice * int64(num)
		subOrder.FeeTotalPrice += totalPrice

		subOrder.FeeItems = append(subOrder.FeeItems, beans.SubOrderFeeItem{
			TotalPrice: totalPrice,
			SalePrice:  v.SalePrice,
			Name:       v.Date.Format(constmap.DateFmtCnMonDay),
			Num:        num,
		})
		return v
	})
}
func (o *ceekeeAsm) HotelOrderPreload(db *gorm.DB, request *beans.HotelOrderPreloadDto) (beans.IFaceHotelOrderPreloadRes, error) {
	var hotel models.Hotel

	if db.Select("id,name").Take(&hotel, request.HotelId).Error != nil {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "酒店不存在")
	}

	room, err := o.hotelOrderPreCheckCeekee(db, request)
	if err != nil {
		return nil, err
	}
	subOrder := &beans.SubOrderFee{
		OrderSubType: constmap.OrderSubTypeHotel,
		FeeItems:     make([]beans.SubOrderFeeItem, 0),
		FeeName:      hotel.Name,
	}
	room.Policy[0] = o.LoadHotelPolicyPrice(room.Policy[0], request.PriceRate)
	o.loadHotelSubOrderFee(request.Num, room.Policy[0], subOrder)

	resp := &beans.HotelOrderPreloadRes{
		SubOrderFee: subOrder,
		HotelRoom:   room,
	}
	return resp, err
}
func (o *ceekeeAsm) GenOrderDetailTicket(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	return nil, fmt.Errorf("[%s]method %s not implemented", o.Code(), "GenOrderDetailTicket")
}

func (o *ceekeeAsm) OrderPay(req *beans_asm.TravelOrderPayDto) (*beans.TravelOrderPayResp, error) {
	oid, _ := convertor.ToInt(req.ThirdOrder.ThirdOrderId)
	if res, err := my_ota.NewCeeKee().OrderPay(my_ota.CeekeeOrderPayReq{
		OrderSerialNo: oid,
	}); err != nil {
		return nil, err
	} else if !res.Result {
		return nil, fmt.Errorf("三方订单状态:[%d]确认失败,%s", res.HotelOrderStatus, res.ErrMsg)
	}
	return &beans.TravelOrderPayResp{
		ThirdOrderState: constmap.ThirdPayStateNeedConfirm,
	}, nil
}

func (o *ceekeeAsm) OrderSubmit(req *beans_asm.TravelOrderSubmitDto) (*beans_asm.TravelOrderSubmitResp, error) {
	detail := req.OrderDetail
	dext := req.OrderDetailExt
	if detail == nil || dext == nil {
		return nil, fmt.Errorf("need order detail and ext")
	}
	var ext beans.OrderDetailExtHotel
	_ = json.Unmarshal([]byte(dext.Ext), &ext)
	peoples, _ := beans_asm.UnmarshalPeoplesFrmDetailExt(detail.Ext)
	hotelId, _ := convertor.ToInt(ext.OtaId)

	orderResp, err := my_ota.NewCeeKee().OrderSubmit(my_ota.CeekeeOrderSubmitReq{
		PolicyId:     ext.PolicyId,
		TotalPrice:   int(utils.CurrencyInt2Float(int64(detail.OtaAmount))),
		CheckInDate:  constmap.Date{Time: time.Unix(detail.DateStart, 0)},
		CheckOutDate: constmap.Date{Time: time.Unix(detail.DateEnd, 0)},
		RoomCount:    detail.Quantity,
		HotelId:      hotelId,
		Customers: slice.Map(peoples, func(index int, item beans.TravelPeople) my_ota.CeekeeCustomerInfo {
			return my_ota.CeekeeCustomerInfo{
				CustomerName: item.Name,
				CustomerType: "1",
				IdentityType: my_ota.IdCardYj2Ceekee(item.IdType),
				IdentityNo:   item.IdNo,
				RoomIndex:    index + 1,
			}
		}),
		Contact: my_ota.CeekeeContactInfo{
			Name:     req.ContactsName,
			PhoneNum: req.ContactsTel,
		},
		LateArrivalTime: constmap.DateTime{Time: time.Unix(detail.DateStart, 0)},
		OrderNo:         detail.SubOrderNo,
	})
	if err != nil {
		return nil, err
	}
	rsp := &beans_asm.TravelOrderSubmitResp{
		ThirdOrderId: convertor.ToString(orderResp.OrderSerialNo),
	}
	rsp.ThirdOrder = models.OrderThirdOrder{
		OrderId:        detail.OrderId,
		OrderDetailId:  detail.ID,
		ThirdOrderId:   rsp.ThirdOrderId,
		ThirdOrderType: constmap.ThirdOrderTypeCeekee,
		ThirdPayState:  constmap.ThirdPayStateWaitPay,
	}
	return rsp, nil
}
func (o *ceekeeAsm) GenOrderDetailHotel(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	peoples := pv.Peoples
	if len(peoples) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请填写至少一位出行人信息")
	}
	for i, v := range peoples {
		if v.IdType == constmap.IdTypeIdCard {
			if !validators.IsIdCardNo(v.IdNo) {
				return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d人的证件号码错误", i+1))
			}
		}
	}
	extra, err := beans.GetProductExtraHotel(pv)
	if err != nil {
		return nil, err
	}
	room, err := o.hotelOrderPreCheckCeekee(db, &beans.HotelOrderPreloadDto{
		HotelId:         pv.ProductId,
		Num:             pv.Num,
		OtaId:           extra.OtaId,
		RoomId:          extra.OtaRoomId,
		PolicyId:        extra.OtaPolicyId,
		Start:           extra.Start,
		End:             extra.End,
		LoadPolicyPrice: false,
	})
	if err != nil {
		return nil, err
	}
	policy := room.Policy[0]
	var hotel models.Hotel
	db.Take(&hotel, pv.ProductId)
	if hotel.ID == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "酒店不存在")
	}
	if len(peoples) > policy.Person*pv.Num {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "旅客人数超出限制")
	}
	if len(peoples) < pv.Num {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请至少保证每间房入住一人")
	}
	if policy.MinAmount > pv.Num || policy.MaxAmount < pv.Num {
		return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("房间数量超出限制(%d-%d)", policy.MinAmount, policy.MaxAmount))
	}
	if day, ok := slice.FindBy(policy.PriceDaily, func(_ int, v beans.HotelRoomPriceDaily) bool {
		return !v.Valid || v.Quota < pv.Num
	}); ok {
		return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("酒店日期(%s)不可预定", day.Date.Format(constmap.DateFmtCnMonDay)))
	}

	priceRate := utils.GetOtaPriceRate(constmap.OtaCodeCeekee)
	otaAmount := policy.SalesPrice * int64(pv.Num)
	totalAmount := utils.GetOtaPrice(priceRate, otaAmount, true)
	subOrderFee := &beans.SubOrderFee{
		OrderSubType: constmap.OrderSubTypeHotel,
		FeeItems:     make([]beans.SubOrderFeeItem, 0),
	}
	frontAmount := pv.SalePrice * int64(pv.Num)
	if totalAmount > frontAmount {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "房型价格有变化，请刷新重试")
	}
	totalAmount = frontAmount //使用前端传入的单价
	policy = o.LoadHotelPolicyPrice(policy, priceRate)
	o.loadHotelSubOrderFee(pv.Num, policy, subOrderFee)
	detail := &models.OrderDetail{
		State:        constmap.OrderDetailStateWaitPay,
		OrderSubType: constmap.OrderSubTypeHotel,
		ProductType:  constmap.ProductTypeCkHotel,
		ProductId:    pv.ProductId,
		Pic:          utils.UnWrapStaticUrl(hotel.Pic),
		ProductName:  hotel.Name,
		SkuId:        0,
		SkuName:      room.Name + room.Policy[0].Name,
		SkuPrice:     policy.SalesPrice,
		UserId:       userId,
		Quantity:     pv.Num,
		Amount:       totalAmount,
		OtaAmount:    otaAmount,
		SubOrderNo:   utils.GenOrderNo(),
		DateStart:    extra.Start,
		DateEnd:      extra.End,
		OtaPriceRate: priceRate,
	}
	ext := beans.OrderDetailExtHotel{
		OtaCode:   constmap.OtaCodeCeekee,
		ProductId: pv.ProductId,
		Start:     extra.Start,
		End:       extra.End,
		OtaId:     extra.OtaId,
		RoomId:    extra.OtaRoomId,
		PolicyId:  extra.OtaPolicyId,
		FeeItems:  subOrderFee.FeeItems,
		Room:      room,
	}
	b, _ := json.Marshal(ext)
	detail.Ext = &models.OrderDetailExt{Ext: string(b)}
	b, _ = json.Marshal(peoples)
	detail.Ext.Peoples = string(b)
	rooms := slice.Map(peoples, func(index int, item beans.TravelPeople) beans.OrderDetailRoom {
		return beans.OrderDetailRoom{
			RoomIndex: index + 1,
			PeopleIdx: index,
		}
	})
	b, _ = json.Marshal(rooms)
	detail.Ext.Rooms = string(b)
	return detail, nil
}

type ceekeeSyncProgress struct {
	ZoneId uint
	Page   int
}

var Ceekee = &ceekeeAsm{
	concurNum: 1,
}

// 设置调用详情接口并发数
func (o *ceekeeAsm) WithConcurrencyNum(n int) *ceekeeAsm {
	return &ceekeeAsm{concurNum: n}
}

func (o *ceekeeAsm) getZones(db *gorm.DB, ckZones []my_ota.CeekeeGetCityListItem) (map[string]models.Zone, map[int]*my_ota.CeekeeGetCityListItem) {
	var zones []models.Zone
	db.Where(models.Zone{Level: constmap.ZoneLevelCity}).Find(&zones)
	zoneMap := o.normalizeZoneNames(zones)
	ckMap := slice.KeyBy(
		slice.Map(
			slice.Filter(
				ckZones,
				func(_ int, item my_ota.CeekeeGetCityListItem) bool {
					return item.CityId > 0
				},
			),
			func(_ int, item my_ota.CeekeeGetCityListItem) *my_ota.CeekeeGetCityListItem {
				return &item
			},
		),
		func(item *my_ota.CeekeeGetCityListItem) int {
			return item.CityId
		},
	)
	maputil.ForEach(ckMap, func(key int, value *my_ota.CeekeeGetCityListItem) {
		if value.ParentCityId > 0 {
			if p, ok := ckMap[value.ParentCityId]; ok {
				value.Parent = p
			}
		}
	})
	return zoneMap, ckMap
}

func (o *ceekeeAsm) SyncHotel(db *gorm.DB, zoneIds ...uint) {
	var err error
	var zones []models.Zone
	var logTag = "[ceekee]"
	db.Where(models.Zone{Level: constmap.ZoneLevelCity}).Find(&zones, zoneIds)
	if len(zones) == 0 {
		my_logger.Errorf(logTag + " no zone found")
		return
	}
	cli := my_ota.NewCeeKee()
	rzone, err := cli.GetCityList()
	if err != nil {
		my_logger.Errorf(fmt.Sprintf("%s GetCityList error", logTag), zap.Error(err))
		return
	}
	zoneNameMap := o.normalizeZoneNames(zones)
	ckZoneNameMap := slice.KeyBy(slice.Filter(rzone.CityList, func(_ int, v my_ota.CeekeeGetCityListItem) bool {
		return v.ParentCityId == 0
	}), func(v my_ota.CeekeeGetCityListItem) string {
		return utils.TrimCitySuffix(v.City)
	})
	var notMatched []string
	for n := range zoneNameMap {
		_, ok := ckZoneNameMap[n]
		if !ok {
			notMatched = append(notMatched, n)
		}
	}
	if len(notMatched) > 0 {
		my_logger.Errorf(fmt.Sprintf("%s zone has unmatched", logTag), zap.Any("notMatched", notMatched))
		return
	}

	for n, zone := range zoneNameMap {
		ckZone, ok := ckZoneNameMap[n]
		if !ok {
			my_logger.Errorf(fmt.Sprintf("%s zone not matched", logTag), zap.String("zone", n))
			return
		}
		var retry int
		for {
			//失败后重试
			if err = o.doSearch(db, logTag, ckZone, zone); err != nil && retry < 2 {
				retry++
				my_logger.Infof(fmt.Sprintf("%s doSearch error", logTag), zap.Int("retry", retry), zap.Error(err))
				time.Sleep(time.Minute)
				continue
			}
			break
		}
	}
	my_logger.Infof(fmt.Sprintf("%s task done.", logTag))
}

func (o *ceekeeAsm) doSearch(db *gorm.DB, logTag string, ckZone my_ota.CeekeeGetCityListItem, zone models.Zone) error {
	cli := my_ota.NewCeeKee()
	page := 1
	pageSize := 10
	var totalPage int
	var total int
	tasks := parallel_task.NewPool(o.concurNum)
	defer tasks.Release()
	//检索进度
	ckey := fmt.Sprintf("ceekee:prog:%d", zone.ID)
	my_cache.Get(ckey, &page)
	if page < 1 {
		page = 1
	}
	for page < 1001 {
		my_logger.Infof(fmt.Sprintf("%ssearch hotels", logTag), zap.String("zone", ckZone.City),
			zap.Int("page", page), zap.Int("totalPage", totalPage), zap.Int("total", total))
		res, err := cli.WithRetry(2).HotelSearch(my_ota.CeekeeHotelSearchReq{
			CityCode: ckZone.CityId,
			Page:     page,
			PageSize: pageSize,
		})
		if err != nil {
			my_logger.Errorf(fmt.Sprintf("%s ceekee search error", logTag), zap.Error(err))
			return err
		}
		_ = my_cache.Set(ckey, convertor.ToString(page), constmap.TimeDur30d)
		total = res.PageTotalCount
		totalPage = res.PageTotalCount / pageSize
		page++
		if len(res.HotelList) == 0 {
			break
		}

		var hotelOtas []models.HotelOta
		var otas []models.HotelOtaCeekee
		otaIds := slice.Map(res.HotelList, func(_ int, v my_ota.CeekeeHotelSearchListItem) string {
			return convertor.ToString(v.HotelId)
		})
		db.Select("id,ota_id").Where("ota_id in ? and ota_code=?", otaIds, constmap.OtaCodeCeekee).Find(&hotelOtas)
		db.Omit("Raw").Where("hotel_id in ?", otaIds).Find(&otas)
		hotelOtaMap := slice.KeyBy(hotelOtas, func(v models.HotelOta) string {
			return v.OtaId
		})
		otaMap := slice.KeyBy(otas, func(v models.HotelOtaCeekee) string {
			return v.HotelId
		})
		for _, v := range res.HotelList {
			otaId := convertor.ToString(v.HotelId)
			//已经存在的ota记录不再处理
			if hotelOta, ok := hotelOtaMap[otaId]; ok && hotelOta.ID > 0 {
				continue
			}
			ota := &models.HotelOtaCeekee{
				HotelId: convertor.ToString(v.HotelId),
			}
			if oo, ok := otaMap[otaId]; ok {
				ota = &oo
			}
			if zone.ID > 0 {
				ota.ZoneId = zone.ID
			}
			tasks.AddTask(func() error {
				time.Sleep(time.Millisecond * 500)
				if err = o.syncHotel(models.New(), nil, ota); err != nil {
					my_logger.Errorf(fmt.Sprintf("%s syncHotel error", logTag), zap.Int64("ckHotelId", v.HotelId), zap.Error(err))
					return err
				}
				return nil
			})
		}
		if err = tasks.Wait(); err != nil {
			my_logger.Errorf(fmt.Sprintf("%s syncHotel error", logTag), zap.Errors("errs", tasks.Errors))
			return err
		}
	}
	return nil
}

func (o *ceekeeAsm) IncrSync(db *gorm.DB) {
	cli := my_ota.NewCeeKee().WithRetry(3)
	tmToday := datetime.BeginOfDay(time.Now())
	tm := constmap.DateTime{Time: tmToday.AddDate(0, 0, -1)}
	tmToday = tmToday.Add(time.Hour * 3) //多查一段时间防止遗漏
	duration := 1800
	pageSize := 100
	for tm.Before(tmToday) {
		var lastRecordId int64
		var sum int
		var curPage int
		for {
			res, err := cli.HotelIncrement(my_ota.CeekeeHotelIncrementReq{
				StartTime:    tm,
				Duration:     duration,
				PageSize:     pageSize,
				LastRecordId: lastRecordId,
			})
			if err != nil {
				my_logger.Errorf("ota_asm.Ceekee.IncrSync err", zap.Error(err))
				return
			}
			if len(res.HotelIncrementList) == 0 {
				break
			}
			sum += len(res.HotelIncrementList)
			curPage++
			my_logger.Infof("ota_asm.Ceekee.IncrSync", zap.Int("page", curPage), zap.Int("sum", sum))
			lastRecordId = res.LastRecordId
			hotelIds := slice.Map(res.HotelIncrementList, func(_ int, v my_ota.CeekeeHotelIncrementListItem) int64 {
				return v.HotelId
			})
			err = o.SyncHotelByCKIds(db, hotelIds)
			if err != nil {
				my_logger.Errorf("ota_asm.Ceekee.IncrSync SyncHotelByCKIds err", zap.Error(err))
				return
			}
		}

		tm.Time = tm.Add(time.Second * time.Duration(duration))
	}
}

func (o *ceekeeAsm) SyncHotelByCsv(db *gorm.DB, file string, frmLine int) {
	fs, err := os.OpenFile(file, os.O_RDONLY, 0666)
	if err != nil {
		my_logger.Errorf("SyncHotelByCsv openFile error", zap.Error(err))
		return
	}
	cr := csv.NewReader(fs)
	head, err := cr.Read()
	if err != nil {
		my_logger.Errorf("SyncHotelByCsv read csv error", zap.Error(err))
		return
	}
	var idCol = -1
	for i, v := range head {
		if strutil.Trim(v) == "思客id" {
			idCol = i
			break
		}
	}
	if idCol == -1 {
		my_logger.Errorf("SyncHotelByCsv match HotelId column failed")
		return
	}
	var lineCnt int
	var chunkSize = utils.If(config.IsDebug(), 10, 1000)
	var ids []int64
	for {
		lineCnt++
		line, err := cr.Read()
		if lineCnt < frmLine {
			continue
		}
		if err == io.EOF {
			break
		} else if err != nil {
			my_logger.Errorf("SyncHotelByCsv read csv error", zap.Error(err))
			return
		}
		if len(line) < (idCol + 1) {
			continue
		}
		if lineCnt%chunkSize == 0 {
			my_logger.Infof("SyncHotelByCsv", zap.Int("lineCnt", lineCnt))
		}
		hotelId, err := convertor.ToInt(strutil.Trim(line[idCol]))
		if err != nil {
			my_logger.Errorf("SyncHotelByCsv parse HotelId error", zap.Int("lineCnt", lineCnt), zap.Error(err))
			continue
		}
		ids = append(ids, hotelId)
		if len(ids) >= chunkSize {
			err = o.SyncHotelByCKIds(db, ids)
			if err != nil {
				my_logger.Errorf("SyncHotelByCsv SyncHotelByCKIds error", zap.Int("lineCnt", lineCnt), zap.Error(err))
			}
			ids = ids[:0]
		}
	}
	if len(ids) > 0 {
		err = o.SyncHotelByCKIds(db, ids)
		if err != nil {
			my_logger.Errorf("SyncHotelByCsv SyncHotelByCKIds error", zap.Int("lineCnt", lineCnt), zap.Error(err))
		}
	}
	my_logger.Infof("SyncHotelByCsv task done.")
}

func (o *ceekeeAsm) SyncHotelByCKIds(db *gorm.DB, ckHotelIds []int64) error {
	if len(ckHotelIds) == 0 {
		return nil
	}
	ckHotelIds = slice.Unique(ckHotelIds)
	var otas []*models.HotelOtaCeekee
	db.Omit("Raw").Where("hotel_id in ?", ckHotelIds).Find(&otas)
	otaMap := slice.KeyBy(otas, func(item *models.HotelOtaCeekee) string { return item.HotelId })
	cli := my_ota.NewCeeKee()
	res, err := cli.GetCityList()
	if err != nil {
		return err
	}
	zoneMap, ckZoneMap := o.getZones(db, res.CityList)
	tasks := parallel_task.NewPool(o.concurNum)
	defer tasks.Release()
	var mx sync.Mutex

	for _, v := range ckHotelIds {
		tasks.AddTask(func(v int64) func() error {
			return func() error {
				time.Sleep(time.Millisecond * 500)
				hotelIdStr := convertor.ToString(v)
				mx.Lock()
				ota := otaMap[hotelIdStr]
				mx.Unlock()

				var hotelResp *my_ota.CeekeeHotelInfoResp
				if ota == nil {
					ckHotel, err := cli.HotelInfo(my_ota.CeekeeHotelInfoReq{HotelId: v})
					if err != nil {
						my_logger.Errorf("SyncHotelByCKIds HotelInfo failed", zap.Int64("ckHotelId", v), zap.Error(err))
						return nil
					}
					ota = &models.HotelOtaCeekee{
						HotelId:  hotelIdStr,
						CkZoneId: ckHotel.CityId,
					}
					hotelResp = ckHotel
				}
				if ota.CkZoneId > 0 && ota.ZoneId == 0 {
					mx.Lock()
					zone := o.matchZone(ota.CkZoneId, ckZoneMap, zoneMap)
					mx.Unlock()
					ota.ZoneId = utils.If(zone.Level == constmap.ZoneLevelDistrict, zone.ParentId, zone.ID)
				}
				err = o.syncHotel(models.New(), hotelResp, ota)
				if err != nil {
					my_logger.Errorf("SyncHotelByCKIds sync hotel error", zap.Error(err))
				}
				return nil
			}
		}(v))
	}
	return tasks.Wait()
}

func (o *ceekeeAsm) RepairHotel(db *gorm.DB) {
	var nextId uint
	var list []models.HotelOtaCeekee
	for {
		db.Model(&list).Select("id,zone_id,hotel_id").
			Where("id>?", nextId).Order("id asc").Limit(10000).Find(&list)
		if len(list) == 0 {
			break
		}
		nextId = list[len(list)-1].ID
		chunk := slice.FilterMap(list, func(index int, item models.HotelOtaCeekee) (int64, bool) {
			oid, _ := convertor.ToInt(item.HotelId)
			return oid, item.ZoneId == 0
		})
		if len(chunk) > 0 {
			if err := o.SyncHotelByCKIds(db, chunk); err != nil {
				my_logger.Errorf("SyncHotelByCKIds sync hotel error", zap.Error(err))
			}
		}
	}
}
func (o *ceekeeAsm) normalizeZoneNames(zones []models.Zone) map[string]models.Zone {
	zones = zone_biz.NewZoneBiz().NormalizeZoneNames(zones)
	return slice.KeyBy(zones, func(v models.Zone) string {
		return v.Name
	})
}

func (o *ceekeeAsm) matchZone(ckZoneId int, ckZoneMap map[int]*my_ota.CeekeeGetCityListItem,
	zoneMap map[string]models.Zone) models.Zone {
	var ckZone my_ota.CeekeeGetCityListItem
	for {
		z, ok := ckZoneMap[ckZoneId]
		if !ok {
			break
		}
		if z.ParentCityId > 0 {
			ckZoneId = z.ParentCityId
			continue
		}
		ckZone = *z
		break
	}
	return zoneMap[utils.TrimCitySuffix(ckZone.City)]
}

func (o *ceekeeAsm) syncHotel(db *gorm.DB, ckHotel *my_ota.CeekeeHotelInfoResp, ota *models.HotelOtaCeekee) error {
	ckHotelId, err := convertor.ToInt(ota.HotelId)
	if err != nil {
		return err
	}
	cli := my_ota.NewCeeKee()
	if ckHotel == nil {
		ckHotel, err = cli.WithRetry(3).HotelInfo(my_ota.CeekeeHotelInfoReq{HotelId: ckHotelId})
		if err != nil {
			return err
		}
	}
	b, _ := json.Marshal(ckHotel)
	ota.Raw = string(b)
	ota.CkZoneId = ckHotel.CityId
	ota.HotelName = ckHotel.HotelName
	brand := models.HotelBrand{
		BrandName: ckHotel.BrandName,
	}
	if ckHotel.BrandName != "" {
		db.Take(&brand, brand)
		if brand.ID == 0 {
			db.Create(&brand)
		}
	}
	hotelUpdata := models.Hotel{
		ZoneId:       ota.ZoneId,
		Name:         ckHotel.HotelName,
		Lat:          ckHotel.Geo.Lat,
		Lng:          ckHotel.Geo.Lon,
		District:     ckHotel.District,
		BusinessArea: ckHotel.Business,
		Address:      ckHotel.Address,
		Pic:          ckHotel.LogUrl,
		Star:         ckHotel.StarRating,
		StarDesc:     ckHotel.StarRateDesc,
		GoodRate:     int(ckHotel.ReviewGoodRate),
		Score:        ckHotel.ReviewScore,
		Tel:          ckHotel.TelephoneNum,
		BrandId:      brand.ID,
		BrandName:    brand.BrandName,
		GuestType:    convertor.ToString(my_ota.CeekeeGuestType2Yj(ckHotel.GuestType)),
		OpenUpTime:   ckHotel.OpenYear,
	}
	var hotel models.Hotel
	hotelOta := models.HotelOta{OtaId: ota.HotelId, OtaCode: constmap.OtaCodeCeekee}
	db.Take(&hotelOta, hotelOta)
	if hotelOta.HotelId > 0 {
		db.Take(&hotel, hotelOta.HotelId)
	} else {
		hotel, err = o.matchHotel(db, ckHotel)
		if err != nil {
			ota.MatchState = constmap.Disable
			ota.Reason = err.Error()
		} else {
			ota.MatchState = constmap.Enable
			ota.Reason = ""
			hotelOta.HotelId = hotel.ID
		}
	}

	err = db.Transaction(func(tx *gorm.DB) error {
		if ota.ID == 0 {
			err = tx.Create(&ota).Error
		} else {
			if tx.Select("ZoneId", "CkZoneId", "HotelName", "MatchState", "Reason", "Raw").Updates(&ota).RowsAffected == 0 {
				err = fmt.Errorf("update ota[%s] fail", ota.HotelId)
			}
		}
		if err != nil {
			return err
		}
		var hotelPics []models.HotelPic
		if hotel.ID > 0 {
			if tx.Model(&hotel).Omit(clause.Associations).Updates(hotelUpdata).RowsAffected == 0 {
				err = fmt.Errorf("update hotel[%d] fail", hotel.ID)
			}
			hotelPics = slice.Map(ckHotel.HotelImages.ImageList, func(_ int, v my_ota.CeekeeHotelImage) models.HotelPic {
				return models.HotelPic{
					HotelId: hotel.ID,
					Url:     v.Src,
					Main:    utils.If(v.Type == "Logo", constmap.Enable, constmap.Disable),
				}
			})
		} else {
			hotel = hotelUpdata
			hotel.State = constmap.Enable
			err = tx.Create(&hotel).Error
			if err != nil {
				return err
			}
			hotelPics = slice.Map(ckHotel.HotelImages.ImageList, func(_ int, v my_ota.CeekeeHotelImage) models.HotelPic {
				return models.HotelPic{
					HotelId: hotel.ID,
					Url:     v.Src,
					Main:    utils.If(v.Type == "Logo", constmap.Enable, constmap.Disable),
				}
			})
		}
		if len(hotelPics) > 0 {
			tx.Unscoped().Delete(&models.HotelPic{}, &models.HotelPic{HotelId: hotel.ID})
			err = tx.Create(&hotelPics).Error
		}
		if err != nil {
			return err
		}
		hotelOta.HotelId = hotel.ID
		if hotelOta.ID == 0 {
			err = tx.Create(&hotelOta).Error
		}
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	poi_biz.EsSyncBiz.SyncHotelByIds(db, []uint{hotel.ID})
	return nil
}

func (o *ceekeeAsm) matchHotel(db *gorm.DB, ckHotel *my_ota.CeekeeHotelInfoResp) (models.Hotel, error) {
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{"term": map[string]any{"type": constmap.PoiTypeHotel}},
					{"term": map[string]any{"state": constmap.Enable}},
				},
				"filter": []map[string]any{
					{
						"geo_distance": map[string]any{
							"distance": "50m",
							"location": map[string]float64{
								"lon": ckHotel.Geo.Lon,
								"lat": ckHotel.Geo.Lat,
							},
						},
					},
				},
			},
		},
		"size": 100,
	}
	res, err := es.Search[es2.Hotel](constmap.EsIndexPoi, query)
	if err != nil {
		return models.Hotel{}, err
	}
	if len(res.Hits.Hits) == 0 {
		return models.Hotel{}, fmt.Errorf("no match hotel in ES")
	}
	ids := slice.Map(res.Hits.Hits, func(_ int, v es.Hit[es2.Hotel]) uint { return v.Source.ObjId })
	var list []models.Hotel
	db.Where("id in ?", ids).Find(&list)
	type cond struct {
		Distance   float64
		Similarity float64
	}
	type matcher struct {
		models.Hotel
		cond
	}
	var matchItems []matcher
	for _, v := range list {
		var similarity = utils.CosineSimilarity(v.Name, ckHotel.HotelName)
		distance := utils.CalculateDistance(v.Lng, v.Lat, ckHotel.Geo.Lon, ckHotel.Geo.Lat)
		if strings.Contains(v.Name, ckHotel.HotelName) || strings.Contains(ckHotel.HotelName, v.Name) || similarity >= 0.95 {
			matchItems = append(matchItems, matcher{Hotel: v, cond: cond{Distance: distance, Similarity: similarity}})
		}
	}
	if len(matchItems) == 0 {
		return models.Hotel{}, fmt.Errorf("no similar hotel")
	}
	//按匹配度排序
	slice.SortBy(matchItems, func(a, b matcher) bool {
		if a.Similarity > b.Similarity {
			return true
		} else {
			return a.Distance <= b.Distance
		}
	})
	hotel := matchItems[0]
	my_logger.Debugf("[ceekee]matchHotel", zap.Any("cond", hotel.cond),
		zap.Any("hotel", hotel.Hotel),
		zap.Any("ckHotel", ckHotel))
	return hotel.Hotel, nil
}

func (o *ceekeeAsm) BuildHotelRoom(room my_ota.CeekeeHotelRoomRateListRoom, loadOtaPrice bool, priceRate int64) beans.HotelRoom {
	roomItem := beans.HotelRoom{
		Id:      room.Id,
		Name:    room.Name,
		Pic:     room.ImageSrc,
		BedDesc: room.BedAndAreaDesc,
		Attrs:   make([]string, 0),
		Policy:  make([]beans.HotelRoomPolicy, 0),
	}
	roomItem.Attrs = append(roomItem.Attrs, utils.If(room.HasWindowEnum > 0, "有窗", "无窗"))
	if room.Floor != "" {
		roomItem.Attrs = append(roomItem.Attrs, utils.If(strings.Contains(room.Floor, "层"), room.Floor, room.Floor+"层"))
	}
	if room.RoomArea != "" {
		roomItem.Attrs = append(roomItem.Attrs, room.RoomArea)
	}
	if room.Broadnet != "" {
		roomItem.Attrs = append(roomItem.Attrs, room.Broadnet)
	}
	for _, policy := range room.PolicyInfos {
		policyItem := beans.HotelRoomPolicy{
			Id:             policy.PolicyId,
			Name:           policy.PolicyName,
			CanCancel:      policy.CancelRule != my_ota.CeekeeEnumCancelRule0,
			CancelRule:     policy.CancelRule.ToString(),
			CancelDesc:     policy.CancelRulePolicyDec,
			InstantConfirm: policy.IsInstantConfirmation,
			ConfirmDesc:    my_ota.CeekeeConfirmDesc(policy.IsInstantConfirmation, policy.InstantConfirmationDec),
			SalesPrice:     utils.CurrencyFloat2Int(policy.SalesPrice),
			RemainRooms:    policy.RemainingRoomCount,
			Tags:           append([]string{}, policy.PolicyTags...),
			MinAmount:      policy.MinAmount,
			MaxAmount:      policy.MaxAmount,
			Person:         policy.Person,
			Breakfast:      policy.Breakfastr.ToString(),
			TicketType:     policy.TicketType,
			NeedIdCard:     policy.IsNeedIdentityCard,
			NeedEnName:     policy.IsNeedEnglishName,
			SuggestPrice:   utils.CurrencyFloat2Int(policy.SuggestSalePrice),
			PriceDaily: slice.Map(policy.PriceDailys, func(_ int, p my_ota.CeekeeHotelRoomPriceDaily) beans.HotelRoomPriceDaily {
				return beans.HotelRoomPriceDaily{
					Date:      constmap.DateUnixStamp{Time: p.Daily.Time},
					SalePrice: utils.CurrencyFloat2Int(p.SalesPrice),
					Quota:     p.Quota,
					Valid:     p.IsValid,
				}
			}),
			CancelPolicy: slice.Map(policy.CancelPolicyInfos, func(_ int, p my_ota.CeekeeHotelRoomCancelPolicy) beans.HotelRoomCancelPolicy {
				typ := my_ota.CancelPolicyCeekee2Yj(p.CutType)
				ret := beans.HotelRoomCancelPolicy{
					//ceekee的时间给反了
					DateFrom: constmap.DateUnixStamp{Time: p.DateTo.Time},
					DateTo:   constmap.DateUnixStamp{Time: p.DateFrom.Time},
					Type:     typ,
					TypeDesc: typ.ToString(),
					Value:    p.CutValue,
				}
				ret.Text = ret.GetText(priceRate)
				return ret
			}),
		}
		if loadOtaPrice {
			policyItem = o.LoadHotelPolicyPrice(policyItem, priceRate)
		}
		roomItem.Policy = append(roomItem.Policy, policyItem)
	}
	return roomItem
}

func (o *ceekeeAsm) GetHotelProductType() constmap.ProductType {
	return constmap.ProductTypeCkHotel
}
func (o *ceekeeAsm) GetTicketProductType() constmap.ProductType {
	return ""
}

func (o *ceekeeAsm) UnmarshalOrderExtHotel(ext string) (beans.OrderDetailExtHotel, error) {
	v := beans.OrderDetailExtHotel{}
	err := json.Unmarshal([]byte(ext), &v)
	return v, err
}

func (o *ceekeeAsm) UnmarshalOrderExtTicket(ext string) (beans.OrderDetailExtTicket, error) {
	return beans.OrderDetailExtTicket{}, utils.NewErrorStr(constmap.ErrorSystem, "未支持的类型")
}

func (o *ceekeeAsm) UnmarshalOrderExtTuan(ext string) (beans.OrderDetailExtTuan, error) {
	return beans.OrderDetailExtTuan{}, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}
