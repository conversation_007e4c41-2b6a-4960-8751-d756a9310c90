package ota_asm

import (
	"encoding/json"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

type ybAsm struct{}

func (y *ybAsm) UnmarshalOrderExtTuan(ext string) (beans.OrderDetailExtTuan, error) {
	var v beans.OrderDetailExtTuan
	err := json.Unmarshal([]byte(ext), &v)
	slice.SortBy(v.RefundRules, func(a, b beans.TuanRefundRule) bool {
		return a.Day > b.Day
	})
	return v, err
}

// Code 返回OTA的代码
func (y *ybAsm) Code() string {
	return constmap.OtaCodeYb
}

// GetHotelProductType 返回酒店产品的类型
func (y *ybAsm) GetHotelProductType() constmap.ProductType {
	return ""
}

// GetTicketProductType 返回门票产品的类型
func (y *ybAsm) GetTicketProductType() constmap.ProductType {
	return ""
}

// HotelOrderPreload 预览订单使用，组装订单信息
func (y *ybAsm) HotelOrderPreload(db *gorm.DB, request *beans.HotelOrderPreloadDto) (beans.IFaceHotelOrderPreloadRes, error) {
	// 实现预览订单的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// GenOrderDetailHotel 下单使用，生成酒店子订单
func (y *ybAsm) GenOrderDetailHotel(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	// 实现生成酒店子订单的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// GenOrderDetailTicket 下单使用，生成门票子订单
func (y *ybAsm) GenOrderDetailTicket(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	// 实现生成门票子订单的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// LoadHotelPolicyPrice OTA酒店政策价格转换为YJ平台价格
func (y *ybAsm) LoadHotelPolicyPrice(policy beans.HotelRoomPolicy, priceRate int64) beans.HotelRoomPolicy {
	// 实现转换酒店政策价格的逻辑
	return policy
}

// OrderSubmit OTA下单
func (y *ybAsm) OrderSubmit(req *beans_asm.TravelOrderSubmitDto) (*beans_asm.TravelOrderSubmitResp, error) {
	// 实现下单的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// OrderPay OTA支付
func (y *ybAsm) OrderPay(req *beans_asm.TravelOrderPayDto) (*beans.TravelOrderPayResp, error) {
	// 实现支付的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// QueryHotelRoomPolicy 查询酒店房型政策
func (y *ybAsm) QueryHotelRoomPolicy(req *beans.QueryHotelRoomPolicyDto) ([]beans.HotelRoom, error) {
	// 实现查询酒店房型政策的逻辑
	return nil, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// ApplyRefund 申请退款
func (y *ybAsm) ApplyRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt, refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) error {
	// 实现申请退款的逻辑
	return nil
}

func (y *ybAsm) QueryRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt, refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) (*beans.OtaRefundCheckResp, error) {
	// 实现查询退款状态的逻辑
	return &beans.OtaRefundCheckResp{
		RefundOk:       true,
		RefundAmount:   refund.RefundAmount,
		YbRefundAmount: refund.RefundAmount,
		PenaltyAmount:  refundSuborder.ApplyRefundAmount - refund.RefundAmount,
	}, nil
}

// UnmarshalOrderExtHotel 解析酒店订单扩展信息
func (y *ybAsm) UnmarshalOrderExtHotel(ext string) (beans.OrderDetailExtHotel, error) {
	// 实现解析酒店订单扩展信息的逻辑
	return beans.OrderDetailExtHotel{}, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

// UnmarshalOrderExtTicket 解析门票订单扩展信息
func (y *ybAsm) UnmarshalOrderExtTicket(ext string) (beans.OrderDetailExtTicket, error) {
	// 实现解析门票订单扩展信息的逻辑
	return beans.OrderDetailExtTicket{}, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}
