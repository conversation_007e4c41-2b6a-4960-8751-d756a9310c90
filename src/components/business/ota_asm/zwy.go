package ota_asm

import (
	"encoding/json"
	"errors"
	"fmt"
	"regexp"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"roadtrip-api/src/validators"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/suifengtec/gocoord"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type zwyAsm struct{}

func (o *zwyAsm) UnmarshalOrderExtTuan(ext string) (beans.OrderDetailExtTuan, error) {
	return beans.OrderDetailExtTuan{}, utils.NewErrorStr(constmap.ErrorParam, "未实现")
}

var Zwy = new(zwyAsm)

func (o *zwyAsm) Code() string {
	return constmap.OtaCodeZwy
}
func (o *zwyAsm) HotelOrderPreload(db *gorm.DB, request *beans.HotelOrderPreloadDto) (beans.IFaceHotelOrderPreloadRes, error) {
	return nil, fmt.Errorf("[%s]method %s not implemented", o.Code(), "SubOrderFee")
}
func (o *zwyAsm) SyncScenic(db *gorm.DB) {
	client := my_ota.NewZwy()
	var page int
	q := my_ota.ZwyScenicListReq{
		ResultNum: 20,
	}
	for {
		q.Page = page
		var (
			res *my_ota.ZwyScenicListResp
			err error
		)
		for retry := 0; retry < 3; retry++ {
			res, err = client.QueryScenicList(q)
			my_logger.Errorf("[zwy]", zap.Any("res", res), zap.Error(err))
			if err == nil {
				break
			}
			my_logger.Errorf("[zwy] query scenic list fail,", zap.Int("retry", retry), zap.Error(err))
		}
		if err != nil {
			my_logger.Errorf("[zwy] query scenic list finally fail,", zap.Error(err))
			return
		}
		if len(res.Results) == 0 {
			break
		}
		var (
			forUpdate []models.ScenicOtaZwy
			forCreate []models.ScenicOtaZwy
			viewIds   = slice.FlatMap(res.Results, func(_ int, v my_ota.ZwyScenicListRespItem) []string { return []string{v.ViewId} })
		)
		db.Where("view_id in ?", viewIds).Find(&forUpdate)
		forUpdateMap := slice.KeyBy(forUpdate, func(v models.ScenicOtaZwy) string { return v.ViewId })
		for _, v := range res.Results {
			if v.ViewType != 0 {
				continue
			}
			b, _ := json.Marshal(v)
			if sv, ok := forUpdateMap[v.ViewId]; ok {
				sv.Raw = string(b)
				sv.ViewName = v.ViewName
				forUpdateMap[v.ViewId] = sv
			} else {
				forCreate = append(forCreate, models.ScenicOtaZwy{
					ViewId:     v.ViewId,
					ViewName:   v.ViewName,
					MatchState: constmap.Disable,
					Raw:        string(b),
				})
			}
		}
		for _, v := range forUpdateMap {
			if db.Updates(v).RowsAffected == 0 {
				my_logger.Errorf("[zwy] update scenic fail,", zap.String("view_id", v.ViewId))
			}
		}
		if len(forCreate) > 0 {
			if err = db.Create(&forCreate).Error; err != nil {
				my_logger.Errorf("[zwy] create scenic fail,", zap.Error(err))
			}
		}
		page++
	}
	o.matchScenic(db)
	my_logger.Info("[zwy] sync scenic task done.")
}

func (o *zwyAsm) matchScenic(db *gorm.DB) {
	var (
		err    error
		nextId uint
	)
	for {
		var list []models.ScenicOtaZwy
		if err = db.Where("id>?", nextId).Order("id asc").Limit(100).Find(&list).Error; err != nil {
			my_logger.Errorf("[zwy] query scenic fail,", zap.Error(err))
			return
		}
		if len(list) == 0 {
			break
		}
		for _, v := range list {
			if err = o.matchScenicItem(db, v); err != nil {
				v.Reason = err.Error()
				db.Updates(v)
			}
			nextId = v.ID
		}
	}
}

func (o *zwyAsm) matchScenicItem(db *gorm.DB, otaItem models.ScenicOtaZwy) error {
	var err error
	var otaScenic my_ota.ZwyScenicListRespItem
	if err = json.Unmarshal([]byte(otaItem.Raw), &otaScenic); err != nil {
		return fmt.Errorf("unmarshal raw data fail:%s", err.Error())
	}

	if otaScenic.Lon == 0 || otaScenic.Lat == 0 {
		return fmt.Errorf("lon/lat must gt 0")
	}

	position := gocoord.BD09ToGCJ02(gocoord.Position{Lon: otaScenic.Lon, Lat: otaScenic.Lat})
	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{"term": map[string]any{"type": constmap.PoiTypeScenic}},
					{"term": map[string]any{"state": constmap.Enable}},
				},
				"filter": []map[string]any{
					{
						"geo_distance": map[string]any{
							"distance": "1000m",
							"location": map[string]float64{
								"lon": position.Lon,
								"lat": position.Lat,
							},
						},
					},
				},
			},
		},
		"size": 100,
	}
	res, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)
	if err != nil {
		return err
	}
	if len(res.Hits.Hits) == 0 {
		return fmt.Errorf("no match scenic in ES")
	}
	var ids []uint
	for _, v := range res.Hits.Hits {
		ids = append(ids, v.Source.ObjId)
	}
	var list []models.Scenic
	db.Where("id in ?", ids).Find(&list)
	type matcher struct {
		models.Scenic
		Distance   float64
		TelMatch   bool
		Similarity float64
	}
	var matchItems []matcher
	re := regexp.MustCompile(`[\(\)\- ]*`) //去除电话号码中标点字符
	for _, v := range list {
		var similarity = utils.CosineSimilarity(v.Name, otaScenic.ViewName)
		var matchTel bool
		distance := utils.CalculateDistance(v.Lng, v.Lat, position.Lon, position.Lat)
		if v.Tel != "" && otaScenic.Phone != "" {
			ta, tb := strings.Split(v.Tel, ";"), strings.Split(otaScenic.Phone, ";")
			taS := typeset.NewTypeSet[string](false)
			slice.ForEach(ta, func(index int, item string) {
				taS.Add(re.ReplaceAllString(item, ""))
			})
			slice.ForEachWithBreak(tb, func(index int, item string) bool {
				if taS.Has(re.ReplaceAllString(item, "")) {
					matchTel = true
				}
				return !matchTel
			})
		}
		if matchTel {
			matchItems = append(matchItems, matcher{Distance: distance, Scenic: v, TelMatch: true, Similarity: similarity})
		} else if strings.Contains(v.Name, otaScenic.ViewName) || strings.Contains(otaScenic.ViewName, v.Name) || similarity >= 0.9 {
			matchItems = append(matchItems, matcher{Distance: distance, Scenic: v, Similarity: similarity})
		}
	}
	if len(matchItems) == 0 {
		return fmt.Errorf("no similarity scenic")
	}
	//按匹配度排序
	slice.SortBy(matchItems, func(a, b matcher) bool {
		if a.TelMatch {
			return true
		} else if b.TelMatch {
			return false
		} else if a.Similarity > b.Similarity {
			return true
		} else {
			return a.Distance <= b.Distance
		}
	})
	err = db.Transaction(func(tx *gorm.DB) error {
		if tx.Where("id=?", otaItem.ID).Select("reason", "match_state").Updates(&models.ScenicOtaZwy{MatchState: constmap.Enable, Reason: ""}).RowsAffected == 0 {
			return fmt.Errorf("update match state fail[%d]", otaItem.ID)
		}
		scenicOta := models.ScenicOta{
			ScenicId: matchItems[0].ID,
			OtaId:    otaItem.ViewId,
			OtaCode:  constmap.OtaCodeZwy,
		}
		if tx.Model(&models.Scenic{}).Where("id=?", scenicOta.ScenicId).Updates(models.Scenic{
			Desc: otaScenic.Description,
			Tel:  otaScenic.Phone,
			Pic:  otaScenic.ViewImg,
		}).RowsAffected == 0 {
			return fmt.Errorf("update scenic fail[%d]", scenicOta.ScenicId)
		}
		tx.Take(&scenicOta, scenicOta)
		if scenicOta.ID > 0 { //已有关联关系
			return nil
		}
		scenicOta.OtaId = otaItem.ViewId
		if err = tx.Create(&scenicOta).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	poi_biz.EsSyncBiz.SyncScenicByIds(db, []uint{matchItems[0].ID})
	return nil
}
func (o *zwyAsm) GetHotelProductType() constmap.ProductType {
	return ""
}
func (o *zwyAsm) GetTicketProductType() constmap.ProductType {
	return constmap.ProductTypeZwyTicket
}
func (o *zwyAsm) OrderPay(req *beans_asm.TravelOrderPayDto) (*beans.TravelOrderPayResp, error) {
	oid, _ := convertor.ToInt(req.ThirdOrder.ThirdOrderId)
	if _, err := my_ota.NewZwy().OrderPay(my_ota.ZwyOrderPayReq{
		OrderId: oid,
	}); err != nil {
		return nil, err
	}
	return &beans.TravelOrderPayResp{
		ThirdOrderState: constmap.ThirdPayStateWaitPrint,
	}, nil
}
func (o *zwyAsm) OrderSubmit(req *beans_asm.TravelOrderSubmitDto) (*beans_asm.TravelOrderSubmitResp, error) {
	detail := req.OrderDetail
	ext := req.OrderDetailExt
	if req.OrderDetail == nil || req.OrderDetailExt == nil {
		return nil, fmt.Errorf("need order detail and ext")
	}
	var extra beans.OrderDetailExtTicket
	_ = json.Unmarshal([]byte(ext.Ext), &extra)
	peoples, _ := beans_asm.UnmarshalPeoplesFrmDetailExt(ext)
	ticketId, _ := convertor.ToInt(extra.OtaId)
	orderAddReq := my_ota.ZwyOrderAddReq{
		InfoId:        ticketId,
		LinkMan:       req.ContactsName,
		LinkPhone:     req.ContactsTel,
		Num:           detail.Quantity,
		OrderSourceId: detail.SubOrderNo,
		Peoples: slice.Map(peoples, func(index int, item beans.TravelPeople) my_ota.ZwyOrderPeoples {
			return my_ota.ZwyOrderPeoples{
				LinkCreditNo:   item.IdNo,
				LinkCreditType: my_ota.IdCardYj2Zwy(item.IdType),
				LinkMan:        item.Name,
				LinkPhone:      item.Phone,
			}
		}),
		TravelDate: time.Unix(detail.DateStart, 0).Format(constmap.DateFmtLong),
	}
	orderResp, err := my_ota.NewZwy().OrderAdd(orderAddReq)
	if err != nil {
		return nil, err
	}
	rsp := &beans_asm.TravelOrderSubmitResp{
		ThirdOrderId: convertor.ToString(orderResp.OrderId),
	}
	thirdPayMap := map[int]constmap.ThirdPayState{
		0: constmap.ThirdPayStateNeedConfirm,
		1: constmap.ThirdPayStateWaitPay,
	}
	rsp.ThirdOrder = models.OrderThirdOrder{
		OrderId:        detail.OrderId,
		OrderDetailId:  detail.ID,
		ThirdOrderId:   rsp.ThirdOrderId,
		ThirdOrderType: constmap.ThirdOrderTypeZwy,
		ThirdPayState:  thirdPayMap[orderResp.OrderState],
	}
	return rsp, nil
}

func (o *zwyAsm) ApplyRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
	refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) error {

	orderId, _ := convertor.ToInt(thirdOrder.ThirdOrderId)
	cli := my_ota.NewZwy()

	invalidStates := []constmap.ThirdPayState{
		constmap.ThirdPayStateRefund,
		constmap.ThirdPayStateNeedConfirm,
		constmap.ThirdPayStateWaitPay,
	}

	if slice.Contain(invalidStates, thirdOrder.ThirdPayState) {
		return fmt.Errorf("[zwy]ApplyRefund invalid state:%d", thirdOrder.ThirdPayState)
	}

	rsp, err := cli.OrderCancel(my_ota.ZwyOrderCancelReq{
		CancelMemo:     fmt.Sprintf("退款单号[%s]", refund.RefundNo),
		OrderId:        orderId,
		CancelNum:      refundSuborder.RefundQuantity,
		SourceCancelId: refund.RefundNo,
	})
	if err != nil {
		return err
	}
	refund.OutRefundNo = rsp.CancelId

	return err
}

func (o *zwyAsm) QueryRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
	refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) (*beans.OtaRefundCheckResp, error) {

	orderId, _ := convertor.ToInt(thirdOrder.ThirdOrderId)
	cli := my_ota.NewZwy()
	res, err := cli.OrderDetail(my_ota.ZwyOrderDetailReq{
		OrderId: orderId,
	})

	if err != nil {
		return nil, err
	}
	rsp := &beans.OtaRefundCheckResp{}

	// 已退款状态(3)才更新三方订单状态
	if res.OrderState != 3 {
		// 超时未退款
		if time.Now().Sub(refund.CreatedAt) > constmap.TimeDur1d {
			return nil, errors.New("自我游超时未处理")
		}
		return rsp, nil
	}
	rsp.RefundOk = true
	rsp.RefundAmount = detail.OtaAmount - utils.CurrencyFloat2Int(res.SettlementPrice)
	rsp.YbRefundAmount = utils.GetOtaPrice(detail.OtaPriceRate, rsp.RefundAmount, true)
	return rsp, nil
}

func (o *zwyAsm) LoadHotelPolicyPrice(policy beans.HotelRoomPolicy, priceRate int64) beans.HotelRoomPolicy {
	return policy
}
func (o *zwyAsm) GenOrderDetailHotel(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	return nil, fmt.Errorf("[%s]method %s not implemented", o.Code(), "GenOrderDetailHotel")
}
func (o *zwyAsm) QueryHotelRoomPolicy(req *beans.QueryHotelRoomPolicyDto) ([]beans.HotelRoom, error) {
	return nil, fmt.Errorf("[%s]method %s not implemented", o.Code(), "QueryHotelRoomPolicy")
}
func (o *zwyAsm) GenOrderDetailTicket(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	peoples := pv.Peoples
	if len(peoples) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorParam, "请填写至少一位出行人信息")
	}
	for i, v := range peoples {
		if v.IdType == constmap.IdTypeIdCard {
			if !validators.IsIdCardNo(v.IdNo) {
				return nil, utils.NewErrorStr(constmap.ErrorParam, fmt.Sprintf("第%d人的证件号码错误", i+1))
			}
		}
	}
	extra, err := beans.GetProductExtraTicket(pv)
	if err != nil {
		return nil, err
	}
	ticketId, err := convertor.ToInt(extra.OtaId)
	if err != nil {
		return nil, err
	}
	cli := my_ota.NewZwy()
	targetDate := extra.DateT.Format(constmap.DateFmtLong)
	calendarRes, err := cli.QueryProductPrice(my_ota.ZwyProductPriceReq{
		ProductNo:     ticketId,
		TravelDate:    targetDate,
		EndTravelDate: targetDate,
	})
	if err != nil {
		my_logger.Errorf("[zwy]QueryProductPrice fail", zap.Int64("productNo", ticketId), zap.Error(err))
		return nil, err
	}
	ticketPrices := slice.Filter(calendarRes.TicketPrices, func(index int, item my_ota.ZwyProductPriceTicket) bool { return item.Date == targetDate })
	if len(ticketPrices) == 0 {
		return nil, fmt.Errorf("日期不可预定")
	}
	ticketPrice := ticketPrices[0]
	if ticketPrice.Num < pv.Num {
		return nil, fmt.Errorf("当日余票不足")
	}

	productDetail, err := cli.QueryProductDetail(my_ota.ZwyProductDetailReq{
		ProductNo: ticketId,
	})
	if err != nil {
		my_logger.Errorf("[zwy]QueryProductDetail fail", zap.Int64("productNo", ticketId), zap.Error(err))
		return nil, err
	}
	if len(peoples) != pv.Num*(productDetail.PeopleNum+productDetail.ChildrenNum+productDetail.OldmanNum) {
		return nil, fmt.Errorf("旅客人数与门票要求不匹配")
	}

	productName := productDetail.ProductName
	if pv.ProductType == constmap.ProductTypeZwyTicket {
		var scene models.Scenic
		db.Take(&scene, pv.ProductId)
		if scene.ID > 0 {
			productName = scene.Name
		}
	}
	priceRate := utils.GetOtaPriceRate(constmap.OtaCodeZwy)
	salePrice := utils.CurrencyFloat2Int(ticketPrice.SalePrice)
	otaAmount := salePrice * int64(pv.Num)
	totalAmount := utils.GetOtaPrice(priceRate, otaAmount, true)

	detail := &models.OrderDetail{
		State:        constmap.OrderDetailStateWaitPay,
		OrderSubType: constmap.OrderSubTypeTicket,
		ProductType:  constmap.ProductTypeZwyTicket,
		ProductId:    pv.ProductId,
		SkuId:        0,
		Pic:          utils.UnWrapStaticUrl(productDetail.Img),
		ProductName:  productName,
		SkuName:      productDetail.ProductName,
		SkuPrice:     (utils.GetOtaPrice(priceRate, salePrice, true)),
		UserId:       userId,
		Quantity:     pv.Num,
		Amount:       (totalAmount),
		OtaAmount:    (otaAmount),
		ScenicId:     pv.ProductId,
		SubOrderNo:   utils.GenOrderNo(),
		DateStart:    extra.Date,
		DateEnd:      extra.Date,
		OtaPriceRate: priceRate,
	}
	ext := beans.OrderDetailExtTicket{
		ProductId:    pv.ProductId,
		Date:         extra.Date,
		OtaId:        extra.OtaId,
		OtaCode:      constmap.OtaCodeZwy,
		OtaSalePrice: salePrice,
		CanRefund:    constmap.Enable,
		CancelDay:    productDetail.CancelDay,
	}
	b, _ := json.Marshal(ext)
	detail.Ext = &models.OrderDetailExt{Ext: string(b)}
	b, _ = json.Marshal(peoples)
	detail.Ext.Peoples = string(b)
	return detail, nil
}

func (o *zwyAsm) UnmarshalOrderExtHotel(ext string) (beans.OrderDetailExtHotel, error) {
	return beans.OrderDetailExtHotel{}, utils.NewErrorStr(constmap.ErrorSystem, "未支持的类型")
}

func (o *zwyAsm) UnmarshalOrderExtTicket(ext string) (beans.OrderDetailExtTicket, error) {
	v := beans.OrderDetailExtTicket{}
	err := json.Unmarshal([]byte(ext), &v)
	return v, err
}
