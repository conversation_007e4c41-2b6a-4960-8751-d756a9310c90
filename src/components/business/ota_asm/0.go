package ota_asm

import (
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/beans/beans_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"

	"gorm.io/gorm"
)

type IOta interface {
	Code() string
	GetHotelProductType() constmap.ProductType
	GetTicketProductType() constmap.ProductType
	//预览订单使用，组装订单信息
	HotelOrderPreload(db *gorm.DB, request *beans.HotelOrderPreloadDto) (beans.IFaceHotelOrderPreloadRes, error)
	//下单使用，生成酒店子订单
	GenOrderDetailHotel(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error)
	//下单使用，生成门票子订单
	GenOrderDetailTicket(db *gorm.DB, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error)
	//OTA酒店政策价格转换为YJ平台价格
	LoadHotelPolicyPrice(policy beans.HotelRoomPolicy, priceRate int64) beans.HotelRoomPolicy
	//OTA下单
	OrderSubmit(req *beans_asm.TravelOrderSubmitDto) (*beans_asm.TravelOrderSubmitResp, error)
	//OTA支付
	OrderPay(req *beans_asm.TravelOrderPayDto) (*beans.TravelOrderPayResp, error)
	//查询酒店房型政策
	QueryHotelRoomPolicy(req *beans.QueryHotelRoomPolicyDto) ([]beans.HotelRoom, error)

	ApplyRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
		refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) error

	QueryRefund(tx *gorm.DB, thirdOrder *models.OrderThirdOrder, detail *models.OrderDetail, detailExt *models.OrderDetailExt,
		refund *models.RefundSubPayment, refundSuborder *models.RefundSuborder) (*beans.OtaRefundCheckResp, error)

	UnmarshalOrderExtHotel(ext string) (beans.OrderDetailExtHotel, error)
	UnmarshalOrderExtTicket(ext string) (beans.OrderDetailExtTicket, error)
	UnmarshalOrderExtTuan(ext string) (beans.OrderDetailExtTuan, error)
}

func NewOta(code string) (IOta, error) {
	switch code {
	case constmap.OtaCodeCeekee:
		return &ceekeeAsm{concurNum: 1}, nil
	case constmap.OtaCodeZwy:
		return &zwyAsm{}, nil
	case constmap.OtaCodeYb:
		return &ybAsm{}, nil
	default:
		return nil, fmt.Errorf("unknown ota[%s]", code)
	}
}

func ThirdOrderType2OtaCode(typ constmap.ThirdOrderType) string {
	switch typ {
	case constmap.ThirdOrderTypeZwy:
		return constmap.OtaCodeZwy
	case constmap.ThirdOrderTypeCeekee:
		return constmap.OtaCodeCeekee
	}
	return ""
}

func GenOrderDetail(db *gorm.DB, productType constmap.ProductType, userId uint, pv beans.TravelProduct) (*models.OrderDetail, error) {
	ota, err := NewOta(beans.ProductType2OtaCode(productType))
	if err != nil {
		return nil, err
	}
	var detail *models.OrderDetail
	switch beans.ProductType2OrderSubType(pv.ProductType) {
	case constmap.OrderSubTypeHotel:
		detail, err = ota.GenOrderDetailHotel(db, userId, pv)
	case constmap.OrderSubTypeTicket:
		detail, err = ota.GenOrderDetailTicket(db, userId, pv)
	default:
		err = fmt.Errorf("暂未支持的类型:%s", productType)
	}
	return detail, err
}
