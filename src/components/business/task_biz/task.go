package task_biz

import (
	"fmt"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

type DoTaskReq struct {
	UserId    uint
	HelperUid uint
	Task      *models.Task
}

// 不关联父任务的独立任务
func DoTask(tx *gorm.DB, in DoTaskReq) error {
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "doTask", convertor.ToString(in.UserId)),
		constmap.TimeDur1m)
	if err != nil {
		return err
	}
	defer unlocker()

	task := in.Task
	var utask = new(models.UserTask)
	tx.Where(models.UserTask{
		UserId: in.UserId,
		TaskId: task.ID,
	}).Take(&utask)

	var utaskLogs []*models.UserTaskLog

	if utask.ID > 0 && utask.State != constmap.UserTaskProcessing {
		return nil
	}
	if utask.ID == 0 {
		utask = &models.UserTask{
			UserId:       in.UserId,
			TaskId:       task.ID,
			ParentTaskId: task.ParentTaskId,
			State:        constmap.UserTaskProcessing,
			MaxTimes:     task.MaxTimes,
			CondTimes:    0,
			Version:      1,
		}
		if err := tx.Create(&utask).Error; err != nil {
			return err
		}
	}
	update := map[string]any{
		"version":    utils.IncrVersion(utask.Version),
		"cond_times": gorm.Expr("cond_times+1"),
	}
	utask.CondTimes += 1
	if utask.CondTimes >= utask.MaxTimes {
		update["state"] = constmap.UserTaskDone
		utask.State = constmap.UserTaskDone
	}
	if tx.Model(&utask).Where("version=? AND cond_times<max_times", utask.Version).Updates(update).RowsAffected == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "任务更新失败")
	}

	taskLog := &models.UserTaskLog{
		UserTaskId: utask.ID,
		UserId:     in.UserId,
		TaskId:     task.ID,
		HelperUid:  in.HelperUid,
		Type:       constmap.UserTaskLogDo,
	}

	var cnt int64
	if tx.Model(&models.UserTaskLog{}).Where(models.UserTaskLog{
		UserTaskId: utask.ID,
		UserId:     in.UserId,
		TaskId:     task.ID,
		HelperUid:  in.HelperUid,
		Type:       constmap.UserTaskLogDo,
	}).Count(&cnt); cnt > 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "助力人不能重复助力")
	}

	switch task.RewardType {
	case constmap.TaskRewardAccountIntegral:
		if trans, err := account_biz.CreateAccountTransaction(tx, in.UserId, constmap.CurrencyIntegral,
			task.RewardAmount, constmap.AccountLogIncrAmount, constmap.AccountLogSubShareMini, ""); err != nil {
			return err
		} else if err = account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
			return err
		} else {
			extra := beans.UserTaskLogExtra{
				TaskName:             task.Name,
				CondType:             task.CondType,
				CondTimes:            utask.CondTimes,
				RewardType:           task.RewardType,
				RewardAmount:         task.RewardAmount,
				AccountTransactionNo: trans.TransactionNo,
			}
			taskLog.Extra = convertor.ToString(extra)
		}
	}

	utaskLogs = append(utaskLogs, taskLog)

	if len(utaskLogs) > 0 {
		if err := tx.Create(&utaskLogs).Error; err != nil {
			return err
		}
	}

	return nil
}

func LoadUserTasks(db *gorm.DB, userId uint, taskIds []uint, where *beans.BeWhere) map[uint]*models.UserTask {
	if len(taskIds) == 0 {
		return map[uint]*models.UserTask{}
	}
	var userTasks []*models.UserTask
	query := db.Where("user_id = ? AND task_id IN ?", userId, taskIds)

	// 应用额外的查询条件
	if where != nil && where.Where.Len() > 0 {
		query = query.Where(where.Where.String(), where.Args...)
	}

	query.Order("id ASC").Find(&userTasks) //正序排序保留最近一条
	return slice.KeyBy(userTasks, func(userTask *models.UserTask) uint {
		return userTask.TaskId
	})
}
