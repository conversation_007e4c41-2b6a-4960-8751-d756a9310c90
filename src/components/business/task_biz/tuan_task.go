package task_biz

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
)

type DoTuanTaskReq struct {
	UserId   uint
	Tuan     *models.Tuan
	Task     *models.Task
	TaskCond constmap.TaskCond
	ScenicId uint
	Lng      float64
	Lat      float64
}

type DoTuanTaskResp struct {
	OrderIds []uint //完成的订单
}

func DoTuanTask(tx *gorm.DB, in DoTuanTaskReq) (*DoTuanTaskResp, error) {
	unlocker, err := my_cache.Mutex(fmt.Sprintf(constmap.RKMutex, "doTuanTask", convertor.ToString(in.UserId)),
		constmap.TimeDur1m)
	if err != nil {
		return nil, err
	}
	defer unlocker()

	parentTask := in.Task
	var childTasks []*models.Task
	tx.Where("parent_task_id=? and state=?", parentTask.ID, constmap.Enable).Find(&childTasks)

	allTaskIds := slice.Map(childTasks, func(_ int, item *models.Task) uint {
		return item.ID
	})
	allTaskIds = append(allTaskIds, parentTask.ID)

	var orderDetails []models.OrderDetail
	tx.Where(models.OrderDetail{
		ProductId:   in.Tuan.ID,
		ProductType: constmap.ProductTypeTuan,
		UserId:      in.UserId,
	}).Where("state in ?", []uint{constmap.OrderDetailStateSuccess, constmap.OrderDetailStateComplete}).Find(&orderDetails)

	if len(orderDetails) == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorNoOrder, "您还未购买该商品")
	}
	orderIds := slice.Map(orderDetails, func(_ int, item models.OrderDetail) uint {
		return item.OrderId
	})
	detailRefunds := orders.GetRefundsByDetails(tx, orderDetails)

	var utasks []*models.UserTask
	tx.Where("user_id=? AND task_id in ? AND order_id in ?", in.UserId, allTaskIds, orderIds).Find(&utasks)

	utaskMap := slice.ReduceBy(utasks, make(map[string]*models.UserTask), func(index int, item *models.UserTask, agg map[string]*models.UserTask) map[string]*models.UserTask {
		agg[fmt.Sprintf("%d_%d", item.TaskId, item.OrderId)] = item
		return agg
	})

	var out = &DoTuanTaskResp{
		OrderIds: make([]uint, 0),
	}

	orderSet := typeset.NewTypeSet[uint](false)

	//订单有退款不可打卡
	slice.ForEach(orderDetails, func(index int, item models.OrderDetail) {
		if _, ok := detailRefunds[item.ID]; ok {
			orderSet.Add(item.OrderId)
		}
	})

	var utaskLogs []*models.UserTaskLog
	for _, odetail := range orderDetails {
		if orderSet.Has(odetail.OrderId) {
			continue
		}

		var parentUtask *models.UserTask
		slice.ForEach(utasks, func(_ int, item *models.UserTask) {
			if item.TaskId == parentTask.ID && item.OrderId == odetail.OrderId {
				parentUtask = item
			}
		})
		if parentUtask == nil {
			continue
		}
		orderSet.Add(odetail.OrderId)

		var scene = new(models.Scenic)
		tx.Where(models.Scenic{State: constmap.Enable}).Take(&scene, in.ScenicId)
		if scene.ID == 0 {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, "景点已下线")
		}

		var kilo = 1.0
		if utils.CalculateDistance(in.Lng, in.Lat, scene.Lng, scene.Lat) > kilo {
			return nil, utils.NewErrorStr(constmap.ErrorSystem, fmt.Sprintf("打卡距离需在%s以内", utils.DistanceStr(kilo)))
		}

		for _, task := range childTasks {
			if in.TaskCond != constmap.TaskCondSceneSign {
				continue
			}
			if in.ScenicId != task.ScenicId {
				continue
			}

			utask, ok := utaskMap[fmt.Sprintf("%d_%d", task.ID, odetail.OrderId)]
			if ok && utask.State != constmap.UserTaskProcessing {
				continue
			}
			if !ok {
				utask = &models.UserTask{
					UserId:       in.UserId,
					TaskId:       task.ID,
					ParentTaskId: task.ParentTaskId,
					OrderId:      odetail.OrderId,
					State:        constmap.UserTaskProcessing,
					MaxTimes:     task.MaxTimes,
					CondTimes:    1,
					Version:      1,
				}

			}
			if utask.ID == 0 {
				if utask.CondTimes >= utask.MaxTimes {
					utask.State = constmap.UserTaskDone
				}
				if err := tx.Create(&utask).Error; err != nil {
					return nil, err
				}
			} else {
				update := map[string]any{
					"version":    utils.IncrVersion(utask.Version),
					"cond_times": gorm.Expr("cond_times+1"),
				}
				if utask.CondTimes+1 >= utask.MaxTimes {
					update["state"] = constmap.UserTaskDone
				}
				if tx.Model(&utask).Where("version=? AND cond_times<max_times", utask.Version).Updates(update).RowsAffected == 0 {
					return nil, utils.NewErrorStr(constmap.ErrorSystem, "任务更新失败")
				}
			}

			utaskLogs = append(utaskLogs, &models.UserTaskLog{
				UserTaskId: utask.ID,
				UserId:     in.UserId,
				TaskId:     task.ID,
				Type:       constmap.UserTaskLogDo,
				ScenicId:   in.ScenicId,
			})

			if utask.State != constmap.UserTaskProcessing {
				utaskLogs = append(utaskLogs, &models.UserTaskLog{
					UserTaskId: utask.ID,
					UserId:     in.UserId,
					TaskId:     task.ID,
					Type:       constmap.UserTaskLogCond,
					ScenicId:   in.ScenicId,
				})
			}

			update := map[string]any{
				"version": utils.IncrVersion(parentUtask.Version),
			}

			// 所有任务均完成
			var cnt int64
			tx.Model(models.UserTask{}).Where(models.UserTask{
				UserId:       in.UserId,
				ParentTaskId: parentUtask.TaskId,
				OrderId:      parentUtask.OrderId,
				State:        constmap.UserTaskDone,
			}).Count(&cnt)

			if parentTask.CondType == constmap.TaskCondSceneSignAll && int(cnt) == len(childTasks) {
				update["cond_times"] = gorm.Expr("cond_times+1")
				update["state"] = constmap.UserTaskWaitReward
			}
			if tx.Model(&parentUtask).Where("version=?", parentUtask.Version).Updates(update).RowsAffected == 0 {
				return nil, utils.NewErrorStr(constmap.ErrorSystem, "主任务更新失败")
			}

			utaskLogs = append(utaskLogs, &models.UserTaskLog{
				UserTaskId: parentUtask.ID,
				UserId:     in.UserId,
				TaskId:     parentTask.ID,
				Type:       constmap.UserTaskLogDo,
				ScenicId:   in.ScenicId,
			})
			if parentUtask.State == constmap.UserTaskWaitReward {
				utaskLogs = append(utaskLogs, &models.UserTaskLog{
					UserTaskId: parentUtask.ID,
					UserId:     in.UserId,
					TaskId:     parentTask.ID,
					Type:       constmap.UserTaskLogCond,
					ScenicId:   in.ScenicId,
				})
				var order models.Order
				tx.Take(&order, odetail.OrderId)
				if err := orders.VerifyOrder(tx, order); err != nil {
					my_logger.Errorf("VerifyOrder", zap.Uint("orderId", odetail.OrderId), zap.Error(err))
					return nil, utils.NewErrorStr(constmap.ErrorSystem, "订单更新失败")
				}
			}

			out.OrderIds = append(out.OrderIds, odetail.OrderId)
		}
	}

	if len(utaskLogs) > 0 {
		if err := tx.Create(&utaskLogs).Error; err != nil {
			return nil, err
		}
	}

	return out, nil
}
