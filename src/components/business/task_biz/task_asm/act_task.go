package task_asm

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/account_biz"
	"roadtrip-api/src/components/business/activity_biz"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type DoActivityTaskReq struct {
	UserId     uint
	ActivityId uint
	PlanId     uint //分享的行程id
	HelperUid  uint //助力人uid
	AiReqid    string
	Cond       constmap.TaskCond
	Remark     string
}

type DoActivityTaskResp struct {
	RewardType   constmap.TaskReward
	RewardAmount int
}

type DoTaskErrorCode int

const (
	DoTaskErrorNoJoin   DoTaskErrorCode = 1 + iota //未参于活动
	DoTaskErrorNoTask                              //活动未关联任务
	DoTaskErrorDoneTask                            //已完成任务
)

type DoActivityTaskError struct {
	Msg  string
	Code DoTaskErrorCode
}

func (o DoActivityTaskError) Error() string {
	return o.Msg
}

func ActionEvent(db *gorm.DB, in DoActivityTaskReq) error {
	var userId uint
	if in.Cond == constmap.TaskCondShareMini {
		if in.UserId == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "需要uid")
		}
		if in.HelperUid == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "需要助力人uid")
		}
		if in.UserId == in.HelperUid {
			return utils.NewErrorStr(constmap.ErrorParam, "不能助力自己")
		}
		var tasks []*models.Task
		db.Where("parent_task_id=0").Where(models.Task{
			State:    constmap.Enable,
			CondType: constmap.TaskCondShareMini,
		}).Find(&tasks)
		for _, v := range tasks {
			if err := db.Transaction(func(tx *gorm.DB) error {
				return task_biz.DoTask(tx, task_biz.DoTaskReq{
					UserId:    in.UserId,
					HelperUid: in.HelperUid,
					Task:      v,
				})
			}); err != nil {
				return err
			}
		}
		return nil
	}
	if in.Cond == constmap.TaskCondSharePlan || in.Cond == constmap.TaskCondSavePlan {
		if in.PlanId == 0 {
			return nil
		}
		var plan models.Plan
		db.Take(&plan, in.PlanId)
		if plan.ID == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "行程不存在")
		}
		userId = plan.UserId
	} else if in.Cond == constmap.TaskCondMakePlan || in.Cond == constmap.TaskCondShareAct {
		if in.UserId == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "需要uid")
		}
		userId = in.UserId
	} else if in.Cond == constmap.TaskCondPlanLike {
		if in.UserId == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "需要uid")
		}
		if in.HelperUid == 0 {
			return utils.NewErrorStr(constmap.ErrorParam, "需要助力人uid")
		}
		userId = in.UserId
	} else {
		return utils.NewErrorStr(constmap.ErrorParam, "不支持的类型")
	}

	uact := activity_biz.FindValidUserActs(db, userId, nil)

	in.UserId = userId
	slice.ForEach(uact, func(index int, item models.UserActivity) {
		in.ActivityId = item.ActivityId
		err := db.Transaction(func(tx *gorm.DB) error {
			if _, err := DoActivityTask(tx, in); err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			my_logger.Errorf("[TaskActionEvent]", zap.Uint("activityId", item.ActivityId), zap.Error(err))
		}
	})
	return nil
}

func DoActivityTask(tx *gorm.DB, in DoActivityTaskReq) (*DoActivityTaskResp, error) {

	ctx, unlocker := context.WithCancel(context.Background())
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "doActTask", convertor.ToString(in.UserId)), constmap.TimeDur1m)
	defer unlocker()

	var out = new(DoActivityTaskResp)
	var activity = new(models.Activity)
	tx.Take(&activity, in.ActivityId)
	if err := activity_biz.CheckValid(activity); err != nil {
		return nil, utils.NewError(err)
	}
	taskMap := task_biz.LoadRelateTasks(tx, []uint{activity.ID}, constmap.TaskRelateActivity)
	if len(taskMap) == 0 { //活动未绑定任务
		my_logger.Infof("[DoActivityTask] activity don't has task", zap.Uint("activity_id", in.ActivityId))
		return nil, DoActivityTaskError{
			Msg:  "活动未关联任务",
			Code: DoTaskErrorNoTask,
		}
	}
	var cnt int64
	if tx.Model(&models.UserActivity{}).Where(models.UserActivity{
		UserId:     in.UserId,
		ActivityId: in.ActivityId,
	}).Count(&cnt); cnt == 0 { //未参加活动
		my_logger.Infof("[DoActivityTask] user not join activity", zap.Uint("user_id", in.UserId), zap.Uint("activity_id", in.ActivityId))
		return nil, DoActivityTaskError{
			Msg:  "未参于活动",
			Code: DoTaskErrorNoJoin,
		}
	}

	if activity.Uuid == constmap.ActWeekPk {
		return doWeekPkPlanLike(tx, in, activity)
	}

	ptask := taskMap[activity.ID]
	var task models.Task
	tx.Where("(id=? OR parent_task_id=?) AND state=? AND cond_type=?",
		ptask.ID, ptask.ID, constmap.Enable, in.Cond).
		Take(&task)
	if task.ID == 0 { //没有相关任务
		my_logger.Infof("[DoActivityTask] activity don't has task", zap.Uint("activity_id", in.ActivityId), zap.Int("cond", int(in.Cond)))
		return nil, DoActivityTaskError{
			Msg:  "活动未关联任务",
			Code: DoTaskErrorNoTask,
		}
	}

	var utask models.UserTask
	switch task.IntervalType {
	case constmap.TaskIntervalOnce:
		tx.Where(models.UserTask{
			UserId: in.UserId,
			TaskId: task.ID,
		}).Take(&utask)
		if utask.ID == 0 {
			utask = models.UserTask{
				UserId:       in.UserId,
				TaskId:       task.ID,
				ParentTaskId: task.ParentTaskId,
				State:        constmap.UserTaskProcessing,
				MaxTimes:     task.MaxTimes,
				Version:      1,
			}
		}
	case constmap.TaskIntervalDay:
		tm := time.Now()
		tx.Where("`date`=?", tm.Format(constmap.DateFmtLong)).Where(models.UserTask{
			UserId: in.UserId,
			TaskId: task.ID,
		}).Take(&utask)
		if utask.ID == 0 {
			utask = models.UserTask{
				UserId:       in.UserId,
				TaskId:       task.ID,
				ParentTaskId: task.ParentTaskId,
				Date:         tm,
				State:        constmap.UserTaskProcessing,
				MaxTimes:     task.MaxTimes,
				Version:      1,
			}
		}
	case constmap.TaskIntervalWeek:
		tm := datetime.BeginOfWeek(time.Now())
		tx.Where("`date`=?", tm.Format(constmap.DateFmtLong)).Where(models.UserTask{
			UserId: in.UserId,
			TaskId: task.ID,
		}).Take(&utask)
		if utask.ID == 0 {
			utask = models.UserTask{
				UserId:       in.UserId,
				TaskId:       task.ID,
				ParentTaskId: task.ParentTaskId,
				Date:         tm,
				State:        constmap.UserTaskProcessing,
				MaxTimes:     task.MaxTimes,
				Version:      1,
			}
		}
	default:
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "任务配置信息错误")
	}

	if utask.State == constmap.UserTaskDone { //已完成
		my_logger.Infof("[DoActivityTask] user task has done")
		return out, nil
	} else if utask.ID == 0 {
		if err := tx.Create(&utask).Error; err != nil {
			return nil, utils.NewError(err)
		}
	}

	userTaskLogs := make([]*models.UserTaskLog, 0)

	var userTaskLog *models.UserTaskLog

	switch in.Cond {

	case constmap.TaskCondJoinAct:
		userTaskLog = &models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     utask.UserId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogDo,
			ActivityId: in.ActivityId,
		}

	case constmap.TaskCondTaskAccIntegral:
		//nothing

	case constmap.TaskCondSharePlan:
		if in.HelperUid == 0 || in.HelperUid == utask.UserId {
			return nil, DoActivityTaskError{
				Msg:  "无效的分享",
				Code: DoTaskErrorDoneTask,
			}
		}
		fallthrough
	case constmap.TaskCondSavePlan:
		userTaskLog = &models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     utask.UserId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogDo,
			ActivityId: in.ActivityId,
			PlanId:     in.PlanId,
			HelperUid:  in.HelperUid,
		}

	case constmap.TaskCondMakePlan:
		userTaskLog = &models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     utask.UserId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogDo,
			ActivityId: in.ActivityId,
			AiReqid:    in.AiReqid,
		}

	case constmap.TaskCondShareAct:
		if in.HelperUid == in.UserId { //无效的分享
			return out, nil
		}
		userTaskLog = &models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     utask.UserId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogDo,
			ActivityId: in.ActivityId,
			HelperUid:  in.HelperUid,
		}
	}

	if userTaskLog != nil {
		if tx.Model(&models.UserTaskLog{}).Where(userTaskLog).Count(&cnt); cnt > 0 {
			my_logger.Infof("[DoActivityTask] user has task log")
			return nil, DoActivityTaskError{
				Msg:  "已完成任务",
				Code: DoTaskErrorDoneTask,
			}
		}
	}

	var ext beans.UserTaskLogExtra
	update := map[string]any{
		"cond_times": gorm.Expr("cond_times+1"),
		"version":    utils.IncrVersion(utask.Version),
	}
	if task.RewardType == constmap.TaskRewardActIntegral {
		//积分类型的奖励直接给不必用户领取

		update["reward_times"] = gorm.Expr("reward_times+1")
		update["reward_acc_amount"] = gorm.Expr("reward_acc_amount+?", task.RewardAmount)
		if utask.RewardTimes+1 >= task.MaxTimes {
			update["state"] = constmap.UserTaskDone
		}
		ext = beans.UserTaskLogExtra{
			TaskName:     task.Name,
			CondType:     task.CondType,
			RewardType:   task.RewardType,
			RewardAmount: task.RewardAmount,
		}
		out.RewardType = task.RewardType
		out.RewardAmount = task.RewardAmount
	}
	if userTaskLog != nil {
		b, _ := json.Marshal(ext)
		userTaskLog.Extra = string(b)
		userTaskLogs = append(userTaskLogs, userTaskLog)
	}

	if tx.Model(&utask).Where("version=?", utask.Version).Updates(update).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "任务记录更新失败")
	}

	var parentTask = new(models.Task)
	if task.ParentTaskId > 0 {
		tx.Where("id=? AND state=?", task.ParentTaskId, constmap.Enable).Take(&parentTask)
	}

	if parentTask.ID > 0 && parentTask.CondType == constmap.TaskCondTaskAccIntegral &&
		task.RewardType == constmap.TaskRewardActIntegral {
		//父任务累加积分按单轮任务处理
		var parentUtask = new(models.UserTask)
		var sendSubMsg bool
		tx.Where(models.UserTask{
			UserId: in.UserId,
			TaskId: parentTask.ID,
		}).Take(&parentUtask)

		if parentUtask.ID == 0 {
			parentUtask = &models.UserTask{
				UserId:        in.UserId,
				TaskId:        parentTask.ID,
				ParentTaskId:  parentTask.ParentTaskId,
				State:         constmap.UserTaskProcessing,
				MaxTimes:      parentTask.MaxTimes,
				CondAccAmount: task.RewardAmount,
				Version:       1,
			}
			if parentUtask.CondAccAmount >= parentTask.CondAmount {
				parentUtask.State = constmap.UserTaskWaitReward
				sendSubMsg = true
			}
			if err := tx.Create(&parentUtask).Error; err != nil {
				return nil, utils.NewError(err)
			}
		} else if parentUtask.ID > 0 && parentUtask.MaxTimes > parentUtask.CondTimes {
			update = map[string]any{
				"cond_acc_amount": gorm.Expr("cond_acc_amount+?", task.RewardAmount),
				"version":         utils.IncrVersion(parentUtask.Version),
			}
			if parentUtask.CondAccAmount+task.RewardAmount >= parentTask.CondAmount && parentUtask.State == constmap.UserTaskProcessing {
				update["state"] = constmap.UserTaskWaitReward
				sendSubMsg = true
			}
			if tx.Model(&parentUtask).Where("version=?", parentUtask.Version).Updates(update).RowsAffected == 0 {
				return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新主任务记录失败")
			}
		}
		ext = beans.UserTaskLogExtra{
			TaskName:     task.Name,
			CondType:     task.CondType,
			RewardType:   task.RewardType,
			RewardAmount: task.RewardAmount,
		}
		b, _ := json.Marshal(ext)
		userTaskLogs = append(userTaskLogs, &models.UserTaskLog{
			UserTaskId: parentUtask.ID,
			UserId:     in.UserId,
			TaskId:     parentTask.ID,
			Type:       constmap.UserTaskLogDo,
			ActivityId: in.ActivityId,
			PlanId:     in.PlanId,
			HelperUid:  in.HelperUid,
			Extra:      string(b),
		})

		if sendSubMsg {
			_ = my_queue.Light(constmap.EventSubMsg, gin.H{
				"type":        "activity",
				"activity_id": activity.ID,
				"user_id":     in.UserId,
			})
		}
	}
	if len(userTaskLogs) > 0 {
		if err := tx.Create(&userTaskLogs).Error; err != nil {
			return nil, utils.NewError(err)
		}
	}
	return out, nil
}

// 用户兑换后标记任务完成
func RewardAirCode(db *gorm.DB, code string, userId uint) error {
	var task models.Task
	db.Where(models.Task{
		RewardId: code,
	}).Take(&task)
	if task.ID == 0 {
		return nil
	}
	var utask models.UserTask
	db.Where(models.UserTask{
		UserId: userId,
		TaskId: task.ID,
		State:  constmap.UserTaskWaitReward,
	}).Take(&utask)
	if utask.ID == 0 {
		return nil
	}
	err := db.Transaction(func(tx *gorm.DB) error {
		if tx.Model(&utask).Updates(models.UserTask{
			RewardCode: code,
			State:      constmap.UserTaskDone,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "更新任务记录失败")
		}
		if err := tx.Create(&models.UserTaskLog{
			UserTaskId: utask.ID,
			UserId:     userId,
			TaskId:     utask.TaskId,
			Type:       constmap.UserTaskLogReward,
		}).Error; err != nil {
			return err
		}
		return nil
	})
	return err
}

// 周榜pk点赞
func doWeekPkPlanLike(tx *gorm.DB, in DoActivityTaskReq, activity *models.Activity) (*DoActivityTaskResp, error) {
	var actPlan = new(models.ActivityPlan)
	tx.Where(models.ActivityPlan{
		UserId:     in.UserId,
		ActivityId: activity.ID,
		PlanId:     in.PlanId,
	}).Take(&actPlan)
	if actPlan.ID == 0 {
		return nil, DoActivityTaskError{
			Code: DoTaskErrorNoJoin,
			Msg:  "[1]未参与活动",
		}
	}

	var actPlanRank = new(models.ActivityPlanRank)
	tx.Where(models.ActivityPlanRank{
		ActivityPlanId: actPlan.ID,
		Date:           utils.BeginOfWeek(time.Now()),
	}).Take(actPlanRank)
	if actPlanRank.ID == 0 {
		return nil, DoActivityTaskError{
			Code: DoTaskErrorNoJoin,
			Msg:  "[2]未参与活动",
		}
	}
	var out = &DoActivityTaskResp{}
	var cnt int64
	if tx.Model(&models.ActivityPlanLike{}).Where(models.ActivityPlanLike{
		ActivityPlanRankId: actPlanRank.ID,
		UserId:             in.HelperUid,
	}).Count(&cnt); cnt > 0 {
		return out, nil
	}

	if err := tx.Create(&models.ActivityPlanLike{
		ActivityPlanRankId: actPlanRank.ID,
		UserId:             in.HelperUid,
	}).Error; err != nil {
		return nil, err
	}

	if tx.Model(&actPlanRank).Updates(map[string]any{
		"score": gorm.Expr("score+1"),
	}).RowsAffected == 0 {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, "更新周榜记录失败")
	}

	return out, nil
}

// 周榜结算
func DoWeekPkSettle(tx *gorm.DB, activity *models.Activity, nowWeek time.Time) error {
	taskMap := task_biz.LoadRelateTasks(tx, []uint{activity.ID}, constmap.TaskRelateActivity)
	if len(taskMap) == 0 {
		return utils.NewErrorStr(constmap.ErrorSystem, "活动未关联有效任务")
	}
	if nowWeek.IsZero() {
		nowWeek = utils.BeginOfWeek(time.Now())
	}
	lastWeek := utils.BeginOfWeek(nowWeek.AddDate(0, 0, -7))
	ptask := taskMap[activity.ID]
	var ctasks []*models.Task
	tx.Where(models.Task{
		ParentTaskId: ptask.ID,
		State:        constmap.Enable,
	}).Find(&ctasks)

	if err := weekPKSettleLastWeek(tx, activity, lastWeek, &ptask, ctasks); err != nil {
		return err
	}

	// 开启本周榜单；sql比较多不需要在事务里
	go WeekPKGenRank(models.New(), activity, nowWeek)

	return nil
}

// 开启本周榜单
func WeekPKGenRank(db *gorm.DB, activity *models.Activity, nowWeek time.Time) {
	var nextId uint
	var rankPlans []*models.ActivityPlan
	var ranks []*models.ActivityPlanRank
	if nowWeek.Before(activity.Start) && activity.Start.Sub(nowWeek) > 7*24*time.Hour ||
		nowWeek.After(activity.End) || nowWeek.Before(utils.BeginOfWeek(time.Now())) {
		return
	}
	for {
		db.Where("id>?", nextId).Where(models.ActivityPlan{
			ActivityId: activity.ID,
		}).Order("id ASC").Limit(100).Find(&rankPlans)
		if len(rankPlans) == 0 {
			return
		}
		nextId = rankPlans[len(rankPlans)-1].ID
		ranks = slice.Map(rankPlans, func(_ int, plan *models.ActivityPlan) *models.ActivityPlanRank {
			return &models.ActivityPlanRank{
				ActivityId:     activity.ID,
				ActivityPlanId: plan.ID,
				Date:           nowWeek,
			}
		})
		if err := db.Create(&ranks).Error; err != nil {
			my_logger.Errorf("保存周榜记录失败", zap.Error(err))
			return
		}
		rankPlans = rankPlans[:0]
	}
}

func weekPKSettleLastWeek(tx *gorm.DB, activity *models.Activity, lastWeek time.Time, ptask *models.Task, ctasks []*models.Task) error {
	if lastWeek.After(activity.End) {
		return nil
	}
	if lastWeek.Before(utils.BeginOfWeek(activity.Start)) { // 活动在非周一开始时lastWeek会早于活动开始时间
		return nil
	} else if lastWeek.Equal(utils.BeginOfWeek(time.Now())) { //本周的等下周运行
		return nil
	}
	var list []*models.ActivityPlanRank
	tx.Where(models.ActivityPlanRank{
		ActivityId: activity.ID,
		Date:       lastWeek,
	}).Order("score DESC, id ASC").Limit(ptask.RankSettleNum).
		Find(&list)
	if len(list) == 0 {
		return nil
	}
	settleList := slice.Map(list, func(_ int, rank *models.ActivityPlanRank) *models.ActivityPlanRankSettle {
		return &models.ActivityPlanRankSettle{
			ActivityId:     rank.ActivityId,
			ActivityPlanId: rank.ActivityPlanId,
			Date:           rank.Date,
			Score:          rank.Score,
		}
	})
	if err := tx.Create(&settleList).Error; err != nil {
		return errors.Wrap(err, "创建周榜结算记录失败")
	}
	actPlanIds := slice.Map(list, func(_ int, rank *models.ActivityPlanRank) uint {
		return rank.ActivityPlanId
	})
	var actPlans []*models.ActivityPlan
	tx.Where(models.ActivityPlan{
		ActivityId: activity.ID,
	}).Find(&actPlans, actPlanIds)
	userIds := slice.Map(actPlans, func(_ int, plan *models.ActivityPlan) uint {
		return plan.UserId
	})

	tasks := append(ctasks, ptask)

	var cnt int64
	var userTaskLogs []*models.UserTaskLog
	for _, userId := range userIds {
		var uact = new(models.UserActivity)
		tx.Where(models.UserActivity{
			UserId:     userId,
			ActivityId: activity.ID,
		}).Take(uact)
		uactExt := uact.Ext.Data
		mergeReward := func(typ constmap.TaskReward, amount int64) []beans.UserActivityReward {
			for i, reward := range uactExt.Rewards {
				if reward.RewardType == typ {
					reward.Amount += amount
					uactExt.Rewards[i] = reward
					return uactExt.Rewards
				}
			}
			return append(uactExt.Rewards, beans.UserActivityReward{
				RewardType: typ,
				Amount:     amount,
			})
		}

		for _, task := range tasks {
			var utask *models.UserTask
			if task.RewardDuplicate == constmap.Disable {
				// 有未领取的奖励不再下发新的奖励
				if tx.Model(&models.UserTask{}).Where(models.UserTask{
					TaskId: task.ID,
					UserId: userId,
					State:  constmap.UserTaskWaitReward,
				}).Count(&cnt); cnt > 0 {
					continue
				}
			}
			if task.RewardType == constmap.TaskRewardAirWifi {

				utask = &models.UserTask{
					UserId:          userId,
					TaskId:          task.ID,
					ParentTaskId:    task.ParentTaskId,
					Date:            lastWeek,
					State:           constmap.UserTaskWaitReward,
					MaxTimes:        task.MaxTimes,
					CondTimes:       1,
					CondAccAmount:   1,
					RealProductName: task.RealProductName,
					Version:         1,
				}
				if err := tx.Create(&utask).Error; err != nil {
					return errors.Wrap(err, "CreateUserTask")
				}
			} else if task.RewardType == constmap.TaskRewardAccountIntegral {
				utask = &models.UserTask{
					UserId:          userId,
					TaskId:          task.ID,
					ParentTaskId:    task.ParentTaskId,
					Date:            lastWeek,
					State:           constmap.UserTaskWaitReward,
					MaxTimes:        task.MaxTimes,
					CondTimes:       1,
					CondAccAmount:   1,
					RealProductName: task.RealProductName,
					RewardTimes:     1,
					RewardAccAmount: task.RewardAmount,
					Version:         1,
				}
				if err := tx.Create(&utask).Error; err != nil {
					return errors.Wrap(err, "CreateUserTask")
				}
				trans, err := account_biz.CreateAccountTransaction(tx, userId, constmap.CurrencyIntegral, task.RewardAmount,
					constmap.AccountLogIncrAmount, constmap.AccountLogSubActReward, "")
				if err != nil {
					return errors.Wrap(err, "CreateAccountTransaction")
				}
				if err := account_biz.ExecuteAccountTransaction(tx, trans.TransactionNo, nil); err != nil {
					return errors.Wrap(err, "ExecuteAccountTransaction")
				}
				if tx.Model(&utask).Where(models.UserTask{Version: utask.Version}).Updates(models.UserTask{
					State:   constmap.UserTaskDone,
					Version: utils.IncrVersion(utask.Version),
				}).RowsAffected == 0 {
					return utils.NewErrorStr(constmap.ErrorSystem, "[10]更新任务记录失败")
				}

			}

			if utask != nil {
				uactExt.Rewards = mergeReward(task.RewardType, int64(task.RewardAmount))
				ext := beans.UserTaskLogExtra{
					TaskName:     task.Name,
					CondType:     task.CondType,
					RewardType:   task.RewardType,
					RewardAmount: task.RewardAmount,
				}
				b, _ := json.Marshal(ext)
				userTaskLogs = append(userTaskLogs, &models.UserTaskLog{
					UserTaskId: utask.ID,
					UserId:     userId,
					TaskId:     task.ID,
					Type:       utils.If(utask.State == constmap.UserTaskDone, constmap.UserTaskLogReward, constmap.UserTaskLogCond),
					ActivityId: activity.ID,
					Extra:      string(b),
				})
			}
		}

		uact.Ext.Data = uactExt
		if tx.Model(&uact).Updates(models.UserActivity{
			Ext: uact.Ext,
		}).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "更新用户活动记录失败")
		}
	}
	if len(userTaskLogs) > 0 {
		if err := tx.Create(&userTaskLogs).Error; err != nil {
			return errors.Wrap(err, "CreateUserTaskLog")
		}
	}

	return nil
}
