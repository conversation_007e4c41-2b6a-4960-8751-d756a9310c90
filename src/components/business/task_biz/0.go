package task_biz

import (
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

func SaveTaskRelate(tx *gorm.DB, taskId, relateId uint, typ constmap.TaskRelateType) error {
	if err := tx.Unscoped().Where("relate_id=? and type=?", relateId, typ).
		Delete(&models.TaskRelates{}).Error; err != nil {
		return err
	}

	err := tx.Create(&models.TaskRelates{
		RelateId: relateId,
		Type:     typ,
		TaskId:   taskId,
	}).Error

	return err
}

// 任务外键id => 任务
func LoadRelateTasks(db *gorm.DB, relateIds []uint, typ constmap.TaskRelateType) map[uint]models.Task {
	if len(relateIds) == 0 {
		return make(map[uint]models.Task)
	}
	type task struct {
		models.Task
		RelateId uint
	}
	tasks := make([]task, 0)

	db.Model(&models.Task{}).
		Select("tr.relate_id, tasks.*").
		Joins("RIGHT JOIN task_relates tr ON tasks.id=tr.task_id AND tr.relate_id in ? and tr.type=?",
			relateIds, typ).
		Where(models.Task{State: constmap.Enable}).
		Find(&tasks)
	return slice.ReduceBy(tasks, make(map[uint]models.Task), func(index int, item task, agg map[uint]models.Task) map[uint]models.Task {
		agg[item.RelateId] = item.Task
		return agg
	})
}
