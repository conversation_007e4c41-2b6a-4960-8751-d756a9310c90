package holiday_biz

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"io"
	"net/http"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

// 获取节假日
func GetHoliday(start, end time.Time) ([]beans.Holiday, error) {
	var ret = make([]beans.Holiday, 0)
	type apiitem struct {
		Date     string `json:"date"`
		Status   int    `json:"status"` //1上班， 2放假
		Festival string `json:"festival"`
	}
	type apiresp struct {
		Code *int      `json:"code"`
		Msg  string    `json:"msg"`
		Data []apiitem `json:"data"`
	}
	for start.Year() <= end.Year() {
		yy := start.Year()
		start = start.AddDate(1, 0, 0)
		ckey := fmt.Sprintf(constmap.RKHoliday, yy)
		var gret constmap.GenericList[beans.Holiday]
		if my_cache.Get(ckey, &gret) {
			ret = append(ret, gret...)
			continue
		}
		resp, err := http.Get(`https://oneapi.coderbox.cn/openapi/public/holiday?date=` + convertor.ToString(yy))
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()
		if resp.StatusCode != 200 {
			return nil, fmt.Errorf("query holiday fail:%s", resp.Status)
		}
		b, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}
		var rsp apiresp
		err = json.Unmarshal(b, &rsp)
		if err != nil {
			return nil, err
		}
		if rsp.Code == nil || *rsp.Code != 0 {
			return nil, fmt.Errorf("query holiday fail:[%s]%s", convertor.ToString(rsp.Code), rsp.Msg)
		}
		slice.ForEach(rsp.Data, func(_ int, item apiitem) {
			tm, _ := time.ParseInLocation(constmap.DateFmtLong, item.Date, time.Local)
			gret = append(gret, beans.Holiday{
				Date:     constmap.DateUnixStamp{Time: tm},
				WorkDay:  utils.If(item.Status == 1, constmap.Enable, constmap.Disable),
				Festival: item.Festival,
			})
		})
		if err := my_cache.Set(ckey, &gret, utils.If(len(gret) == 0, constmap.TimeDur1d, constmap.TimeDur30d)); err != nil {
			my_logger.Errorf("set cache fail", zap.Error(err))
		}
		ret = append(ret, gret...)
	}
	return ret, nil
}
