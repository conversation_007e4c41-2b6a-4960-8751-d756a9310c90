package activity_biz

import (
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"gorm.io/gorm"
)

func CheckValid(activity *models.Activity) error {
	now := time.Now()
	if activity.ID == 0 {
		return utils.NewErrorStr(constmap.ErrorParam, "活动不存在")
	} else if activity.State != constmap.Enable {
		return utils.NewErrorStr(constmap.ErrorParam, "活动已下架")
	} else if activity.Start.After(now) {
		return utils.NewErrorStr(constmap.ErrorParam, "活动未开始")
	} else if activity.End.Before(now) {
		return utils.NewErrorStr(constmap.ErrorParam, "活动已结束")
	}
	return nil
}

func FindValidUserActs(db *gorm.DB, userId uint, where *beans.BeWhere) []models.UserActivity {
	var uact []models.UserActivity
	q := db.Joins("LEFT JOIN activities act ON user_activities.activity_id=act.id").
		Where("user_activities.user_id=? AND act.start<now() AND act.end>now() AND act.state=?", userId, constmap.Enable)

	// 应用额外的查询条件
	if where != nil && where.Where.Len() > 0 {
		q = q.Where(where.Where.String(), where.Args...)
	}

	q.Find(&uact)
	return uact
}

func FindValidActs(db *gorm.DB, where string, whereArgs []any) []models.Activity {
	var acts []models.Activity
	q := db.Where("state=? AND start<now() AND end>now()", constmap.Enable)
	if where != "" {
		q.Where(where, whereArgs...)
	}
	q.Find(&acts)
	return acts
}
