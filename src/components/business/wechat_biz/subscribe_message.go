package wechat_biz

import (
	"fmt"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/subscribeMessage/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/power"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/business/task_biz"
	"roadtrip-api/src/components/business/user_biz"
	"roadtrip-api/src/components/my_wechat"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func SendSubscribeMsg(db *gorm.DB, payload gin.H) error {
	req := &request.RequestSubscribeMessageSend{}
	typ := convertor.ToString(payload["type"])

	userId, err := convertor.ToInt(payload["user_id"])
	if err != nil {
		return err
	}

	oauth, err := user_biz.GetUserOauth(db, userId, constmap.OauthTypeMpWeixin)
	if err != nil {
		return err
	}

	switch typ {
	case "activity":
		actId, err := convertor.ToInt(payload["activity_id"])
		if err != nil {
			return err
		}

		var activity models.Activity

		db.Take(&activity, actId)

		if activity.ID == 0 {
			return fmt.Errorf("activity:%d not found", actId)
		}
		var rewardTitle = "获得奖励"
		taskMap := task_biz.LoadRelateTasks(db, []uint{activity.ID}, constmap.TaskRelateActivity)
		if taskMap[activity.ID].RewardType == constmap.TaskRewardAirWifi && taskMap[activity.ID].RewardId != "" {
			var flowCard models.FlowCard
			db.Where(models.FlowCard{Code: taskMap[activity.ID].RewardId}).Take(&flowCard)
			rewardTitle = flowCard.Title
		}
		req.ToUser = oauth.OpenId
		req.TemplateID = activity.MpTplId
		req.Page = fmt.Sprintf("/pages/activity/activity?code=%s", activity.Uuid)
		req.Data = &power.HashMap{
			"thing1": power.HashMap{
				"value": rewardTitle,
			},
			"thing2": power.HashMap{
				"value": activity.Title,
			},
			"thing3": power.HashMap{
				"value": "点击领取",
			},
		}
	case "plan_pdf_done":
		var plan models.Plan
		if db.Take(&plan, payload["plan_id"]).Error != nil {
			return nil
		}

		var tpl *beans.TplMsgKeys
		if value, err := sys_config_biz.GetConfig(db, constmap.SysConfigTplMsgKeys); err != nil {
			return err
		} else {
			tpl = value.(*beans.TplMsgKeys)
		}

		req.ToUser = oauth.OpenId
		req.TemplateID = tpl.PlanPdf
		req.Page = "/pages/mine/plans/plans"
		req.Data = &power.HashMap{
			"thing1": power.HashMap{
				"value": fmt.Sprintf("导出行程%s", plan.Subject),
			},
			"time3": power.HashMap{
				"value": time.Now().Format(constmap.DateFmtLongMinute),
			},
			"phrase4": power.HashMap{
				"value": "已完成",
			},
			"thing5": power.HashMap{
				"value": fmt.Sprintf("点击查看详情"),
			},
		}
	}
	if req.TemplateID == "" || req.ToUser == "" {
		return fmt.Errorf("template id or to user is empty")
	}
	req.MiniProgramState = utils.If(config.IsProduction(), "", utils.If(config.IsDebug(), "developer", "trial"))
	_, err = my_wechat.SendSubscribeMessage(req)
	return err
}
