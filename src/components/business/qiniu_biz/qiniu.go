package qiniu_biz

import (
	"context"
	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/storage"
	"github.com/qiniu/go-sdk/v7/storagev2/credentials"
	"github.com/qiniu/go-sdk/v7/storagev2/http_client"
	"github.com/qiniu/go-sdk/v7/storagev2/objects"
	"github.com/qiniu/go-sdk/v7/storagev2/uploader"
	"io"
	"path"
	"roadtrip-api/src/config"
)

func getMac() *credentials.Credentials {
	return credentials.NewCredentials(config.Config.Qiniu.AccessKey, config.Config.Qiniu.SecretKey)
}

func getUploadManager() *uploader.UploadManager {
	options := uploader.UploadManagerOptions{
		Options: http_client.Options{
			Credentials: getMac(),
		},
	}
	return uploader.NewUploadManager(&options)
}

func getBucketManager() *storage.BucketManager {
	return storage.NewBucketManager(getMac(), nil)
}

func getObjectsManager() *objects.ObjectsManager {
	return objects.NewObjectsManager(&objects.ObjectsManagerOptions{
		Options: http_client.Options{Credentials: getMac()},
	})
}

func IsExists(key string) bool {
	bucket := getObjectsManager().Bucket(config.Config.Qiniu.Bucket)
	options := &objects.ListObjectsOptions{
		Prefix: key,
	}
	iter := bucket.List(context.Background(), options)
	defer iter.Close()
	var objectInfo objects.ObjectDetails
	for iter.Next(&objectInfo) {
		return true
	}

	return false
}

func Delete(key string) error {
	bucket := getObjectsManager().Bucket(config.Config.Qiniu.Bucket)
	return bucket.Object(key).Delete().Call(context.Background())
}

func List(prefix string, marker func() string, callback func(details *objects.ObjectDetails) error) error {
	bucket := getObjectsManager().Bucket(config.Config.Qiniu.Bucket)
	options := &objects.ListObjectsOptions{
		Prefix: prefix,
		Marker: marker(),
	}

	iter := bucket.List(context.Background(), options)
	defer iter.Close()

	var objectInfo objects.ObjectDetails
	for iter.Next(&objectInfo) {
		if err := callback(&objectInfo); err != nil {
			return err
		}
	}

	return iter.Error()
}

func UploadReader(r io.Reader, destFile string) error {
	filename := path.Base(destFile)

	options := uploader.ObjectOptions{
		BucketName: config.Config.Qiniu.Bucket,
		ObjectName: &destFile,
		FileName:   filename,
	}
	return getUploadManager().UploadReader(context.Background(), r, &options, nil)
}

func UploadFile(localFile string, destFile string) error {
	filename := path.Base(destFile)
	if filename == "" {
		filename = path.Base(localFile)
	}

	options := uploader.ObjectOptions{
		BucketName: config.Config.Qiniu.Bucket,
		ObjectName: &destFile,
		FileName:   filename,
	}
	err := getUploadManager().UploadFile(context.Background(), localFile, &options, nil)
	return err
}

func UploadTmp(src, dst string, expDays int) error {
	filename := path.Base(dst)
	if filename == "" {
		filename = path.Base(src)
	}

	options := uploader.ObjectOptions{
		BucketName: config.Config.Qiniu.Bucket,
		ObjectName: &dst,
		FileName:   filename,
	}
	err := getUploadManager().UploadFile(context.Background(), src, &options, nil)
	if err != nil {
		return err
	}
	if expDays < 1 {
		expDays = 1
	}
	err = SetObjectExpire(dst, expDays)
	return err
}

// 设置文件有效期 0表示永久保存
func SetObjectExpire(objectKey string, expDays int) error {
	return getBucketManager().DeleteAfterDays(config.Config.Qiniu.Bucket, objectKey, expDays)
}

// UploadTokenOptions 上传令牌选项
type UploadTokenOptions struct {
	Expires   int64  // 过期时间（秒），默认3600秒
	KeyPrefix string // 文件名前缀
	MaxSize   int64  // 最大文件大小（字节），默认10MB
	ExpDays   int
}

// UploadTokenResponse 上传令牌响应
type UploadTokenResponse struct {
	Token  string `json:"token"`
	Bucket string `json:"bucket"`
	Domain string `json:"domain,omitempty"`
}

// 生成上传令牌
func GenerateUploadToken(options *UploadTokenOptions) (*UploadTokenResponse, error) {
	if options == nil {
		options = &UploadTokenOptions{}
	}

	// 设置默认值
	if options.Expires <= 0 {
		options.Expires = 3600 // 默认1小时
	}
	if options.MaxSize <= 0 {
		options.MaxSize = 10 * 1024 * 1024 // 默认10MB
	}

	// 创建上传策略
	putPolicy := storage.PutPolicy{
		Scope:           config.Config.Qiniu.Bucket,
		Expires:         uint64(options.Expires),
		DeleteAfterDays: options.ExpDays,
	}

	// 设置文件大小限制
	putPolicy.FsizeLimit = options.MaxSize

	// 如果指定了前缀，设置保存键
	if options.KeyPrefix != "" {
		putPolicy.SaveKey = options.KeyPrefix + "$(etag)$(ext)"
	}

	// 生成令牌
	mac := auth.New(config.Config.Qiniu.AccessKey, config.Config.Qiniu.SecretKey)
	token := putPolicy.UploadToken(mac)

	return &UploadTokenResponse{
		Token:  token,
		Bucket: config.Config.Qiniu.Bucket,
	}, nil
}
