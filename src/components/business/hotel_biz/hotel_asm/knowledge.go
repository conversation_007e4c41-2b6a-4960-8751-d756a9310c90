package hotel_asm

import (
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/components/business/holiday_biz"
	"roadtrip-api/src/components/business/hotel_biz"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/parallel_task"
	"roadtrip-api/src/utils/typeset"
	"strings"
	"time"
)

func Add2Update(hotelIds []uint) {
	my_cache.RedisClient().SAdd(constmap.RSHotelKnowledgeSync, slice.Map(hotelIds, func(index int, item uint) any {
		return any(item)
	})...)
}

// 同步知识库
func StartLoopUpdate(ctx context.Context) {
	const batchSize = 10 // 一次处理的元素数量
	wg := parallel_task.NewPool(batchSize)
	defer wg.Release()

	month := datetime.BeginOfMonth(time.Now()).AddDate(0, 1, 0)
	holidaySet := typeset.NewTypeSet[string](false)

	if holidays, err := holiday_biz.GetHoliday(month, month); err != nil {
		my_logger.Errorf("GetHoliday", zap.Error(err))
	} else {
		for _, v := range holidays {
			if v.WorkDay == constmap.Disable {
				holidaySet.Add(v.Date.Format(constmap.DateFmtLong))
			}
		}
	}

	for {
		// 随机获取多个元素
		strSlice := my_cache.RedisClient().SRandMemberN(constmap.RSHotelKnowledgeSync, batchSize).Val()
		if len(strSlice) == 0 {
			break
		}

		// 将获取的元素转换为接口类型，用于后续删除
		elementsToRemove, err := utils.ToArray[uint](strings.Join(strSlice, ","), ",")
		if err != nil {
			break
		}

		// 处理每个元素
		for _, str := range elementsToRemove {
			id := str
			wg.AddTask(func() error {
				db := models.New()

				if err := hotel_biz.HotelAvgPrice(db, month, holidaySet, false, id); err != nil {
					return err
				}

				_ = hotelRecReason(db, true, id)

				if err := syncKnowledgeHotels(ctx, db, []uint{id}); err != nil {
					my_logger.Errorf("syncKnowledgeHotels", zap.Error(err), zap.Uint("hotelId", uint(id)))
				}

				return nil
			})
		}

		_ = wg.Wait()

		// 无论处理是否成功，都从集合中删除这些元素
		if len(strSlice) > 0 {
			my_cache.RedisClient().SRem(constmap.RSHotelKnowledgeSync, slice.Map(strSlice, func(index int, item string) any {
				return any(item)
			})...)
		}
	}
}

func hotelRecReason(db *gorm.DB, doContinue bool, hotelId uint) error {
	rds := my_cache.RedisClient()
	ckey := fmt.Sprintf(constmap.RKSpider, "hotelRecReasonSet", 0)
	if doContinue && rds.SIsMember(ckey, convertor.ToString(hotelId)).Val() {
		return nil
	}
	segs, err := BuildKnowledgeSegments(db, []uint{hotelId})
	if err != nil {
		return err
	}
	if len(segs) == 0 {
		return nil
	}
	hotelInfo := segs[0]
	var str = new(strings.Builder)
	write := func(ss ...string) {
		for _, s := range ss {
			str.WriteString(s)
		}
		str.WriteByte('\n')
	}
	write("名称：", hotelInfo.Name)
	write("品牌:", hotelInfo.BrandName)
	write("地区:", hotelInfo.ZoneName)
	write("均价:", hotelInfo.AvgPrice)
	write("商圈:", hotelInfo.BusinessArea)
	write("评分:", fmt.Sprintf("%.1f", hotelInfo.Score))
	write("客人类型", hotelInfo.GuestType)
	write("附近3公里的景点:")
	for _, scene := range hotelInfo.NearbyScenes {
		write("    -", scene.Name)
	}

	res, err := my_dify.HotelRecommendReason(context.Background(), db, str.String())
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("HotelRecommendReason[%d]", hotelId))
	}
	hotelExts := hotel_biz.LoadExt(db, []uint{hotelId})
	if ext, ok := hotelExts[hotelId]; ok {
		if db.Model(&ext).Updates(models.HotelExt{
			RecReason: res.RecommendReason,
		}).RowsAffected == 0 {
			return fmt.Errorf("update hotel ext fail:%d", hotelId)
		}
	} else {
		ext := models.HotelExt{
			HotelId:   hotelId,
			RecReason: res.RecommendReason,
		}
		if err := db.Create(&ext).Error; err != nil {
			return errors.Wrap(err, fmt.Sprintf("create hotel ext fail:%d", hotelId))
		}
	}

	return nil
}

func syncKnowledgeHotels(ctx context.Context, db *gorm.DB, hotelIds []uint) error {

	if len(hotelIds) == 0 {
		return nil
	}

	kdge := my_dify.NewKnowledgeBase(db)

	ksegs, err := BuildKnowledgeSegments(db, hotelIds)
	if err != nil {
		return err
	}

	var cfg *beans.DifyKnowledgeHotelKey
	if val, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyKnowledgeHotelKey); err != nil {
		return errors.Wrap(err, "getDifyKnowledgeKeys")
	} else {
		cfg = val.(*beans.DifyKnowledgeHotelKey)
	}

	raiseError := func(unlock func(), err error) error {
		if unlock != nil {
			unlock()
		}
		return err
	}

	for _, kseg := range ksegs {
		cctx, unlock := context.WithCancel(ctx)
		my_cache.RedisLock(cctx, fmt.Sprintf(constmap.RKSpin, "hotelKnowledgeSync", convertor.ToString(kseg.HotelId)), constmap.TimeDur1m)

		cfg.DocId = cfg.DocZoneMap[kseg.ZoneId]
		var hotelSeg models.HotelSegment
		db.Where(models.HotelSegment{HotelId: kseg.HotelId}).Take(&hotelSeg)
		if hotelSeg.ID > 0 {
			if _, err = kdge.UpdateSegment(ctx, cfg.DifyKnowledgeKey, hotelSeg.SegmentId, hotel_biz.BuildSegmentText(kseg)); err != nil {
				return raiseError(unlock, errors.Wrap(err, fmt.Sprintf("UpdateSegment[%d]", kseg.HotelId)))
			}
		} else {
			res, err := kdge.CreateSegments(ctx, cfg.DifyKnowledgeKey, []any{hotel_biz.BuildSegmentText(kseg)})
			if err != nil {
				return raiseError(unlock, errors.Wrap(err, "CreateSegments"))
			}
			var createSegs []models.HotelSegment
			for _, vv := range res.Data {
				createSegs = append(createSegs, models.HotelSegment{
					HotelId:   kseg.HotelId,
					SegmentId: vv.Id,
				})
				break
			}
			if err := db.Create(&createSegs).Error; err != nil {
				return raiseError(unlock, err)
			}
		}

		unlock()
	}

	return nil
}

func BuildKnowledgeSegments(db *gorm.DB, hotelIds []uint) ([]*beans.DifyKnowledgeHotel, error) {
	if len(hotelIds) == 0 {
		return nil, nil
	}
	var hotels []*models.Hotel
	db.Find(&hotels, hotelIds)

	hotelExts := hotel_biz.LoadExt(db, hotelIds)

	var zoneIds []uint
	slice.ForEach(hotels, func(index int, item *models.Hotel) {
		zoneIds = append(zoneIds, item.ZoneId)
	})

	zoneMap := zone_biz.NewZoneBiz().GetZoneMap(db, zoneIds)

	var ksegs []*beans.DifyKnowledgeHotel

	for _, v := range hotels {
		var sceneIds []uint
		if err := hotel_biz.QueryScenesByPoi(db, constmap.SearchRadius, v, func(sceneId uint) (bool, error) {
			sceneIds = append(sceneIds, sceneId)
			return false, nil
		}); err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("querySceneByHotel[%d]", v.ID))
		}
		var scenes []*models.Scenic
		if len(sceneIds) > 0 {
			db.Where("id in ?", sceneIds).Find(&scenes)
		}
		var be = &beans.DifyKnowledgeHotel{
			HotelId:      v.ID,
			Name:         v.Name,
			Lng:          v.Lng,
			Lat:          v.Lat,
			Pic:          utils.StaticUrl(v.Pic),
			ZoneId:       v.ZoneId,
			ZoneName:     zoneMap[v.ZoneId].Name,
			Address:      v.Address,
			Star:         v.Star,
			StarDesc:     v.StarDesc,
			AvgPrice:     fmt.Sprintf("%.2f元", utils.CurrencyInt2Float(v.AvgPrice)),
			Score:        v.Score,
			BusinessArea: v.BusinessArea,
			BrandId:      v.BrandId,
			BrandName:    v.BrandName,
			NearbyScenes: slice.Map(scenes, func(index int, vv *models.Scenic) beans.DifyKnowledgeHotelScene {
				return beans.DifyKnowledgeHotelScene{
					Id:   vv.ID,
					Name: vv.Name,
				}
			}),
		}
		if !strutil.IsBlank(v.GuestType) {
			be.GuestType = strings.Join(slice.Map(strings.Split(v.GuestType, ","), func(index int, item string) string {
				n, _ := convertor.ToInt(item)
				return business.GuestTypeText(constmap.GuestType(int(n)))
			}), ",")
		}
		if ext, ok := hotelExts[v.ID]; ok {
			be.RecReason = ext.RecReason
		}
		ksegs = append(ksegs, be)
	}
	return ksegs, nil
}
