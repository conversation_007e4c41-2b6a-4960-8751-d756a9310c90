package hotel_biz

import (
	"fmt"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/go-redis/redis"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_ota"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
	"time"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
)

// 根据景点查酒店
func QueryScenesByPoi(db *gorm.DB, km float64, hotel *models.Hotel, callback func(uint) (bool, error)) error {

	query := map[string]any{
		"query": map[string]any{
			"bool": map[string]any{
				"must": []map[string]any{
					{
						"term": map[string]any{
							"type": constmap.PoiTypeScenic,
						},
					},
					{
						"term": map[string]any{
							"state": constmap.Enable,
						},
					},
					{
						"term": map[string]any{
							"zone_id": hotel.ZoneId,
						},
					},
					{
						"term": map[string]any{
							"llm": constmap.Enable,
						},
					},
					{
						"geo_distance": map[string]any{
							"distance": fmt.Sprintf("%.1fkm", km),
							"location": map[string]any{
								"lat": hotel.Lat,
								"lon": hotel.Lng,
							},
						},
					},
				},
			},
		},
		"sort": []map[string]any{
			{
				"_geo_distance": map[string]any{
					"location": map[string]any{
						"lat": hotel.Lat,
						"lon": hotel.Lng,
					},
					"order": "asc",
					"unit":  "km",
				},
			},
			{"_score": map[string]any{"order": "desc"}},
		},
		"size": 100,
	}
	page := 1
	for {
		query["from"] = (page - 1) * 100
		page++
		searchResp, err := es.Search[es2.Scenic](constmap.EsIndexPoi, query)
		if err != nil {
			return err
		}
		if len(searchResp.Hits.Hits) == 0 {
			break
		}
		for _, hit := range searchResp.Hits.Hits {
			if stop, err := callback(hit.Source.ObjId); stop || err != nil {
				return err
			}
		}
	}
	return nil
}

func HotelAvgPrice(db *gorm.DB, month time.Time, holiday *typeset.TypeSet[string], doContinue bool, hotelId uint) error {
	rds := my_cache.RedisClient()
	ckey := fmt.Sprintf(constmap.RKSpider, "hotelAvgPriceSet", 0)
	if doContinue && rds.SIsMember(ckey, convertor.ToString(hotelId)).Val() {
		return nil
	}
	var hotel = new(models.Hotel)
	db.Take(&hotel, hotelId)
	if hotel.ID == 0 {
		return nil
	}
	my_logger.Infof("start hotel", zap.String(fmt.Sprintf("[%d]hotelName", hotel.ID), hotel.Name))
	defer func() {
		if doContinue {
			_, _ = rds.Pipelined(func(pipeliner redis.Pipeliner) error {
				pipeliner.SAdd(ckey, convertor.ToString(hotelId))
				pipeliner.Expire(ckey, constmap.TimeDur15d)
				return nil
			})
		}
	}()

	var hotelOta = new(models.HotelOta)
	db.Where(models.HotelOta{HotelId: hotelId, OtaCode: constmap.OtaCodeCeekee}).Take(&hotelOta)
	if hotelOta.ID == 0 {
		my_logger.Infof("hotel ota not found", zap.String(fmt.Sprintf("[%d]hotelName", hotel.ID), hotel.Name))
		return nil
	}

	otaId, _ := convertor.ToInt(hotelOta.OtaId)
	start := datetime.BeginOfMonth(month)
	end := datetime.BeginOfDay(datetime.EndOfMonth(month).Add(time.Hour))
	ota := my_ota.NewCeeKee()

	//当月已经存在的酒店不进行查询
	var c int64
	if db.Where("hotel_id=? and date>=? and date <=?", hotelId, start, end).
		Model(&models.HotelPrice{}).Count(&c); c > 0 {
		return nil
	}

	ignoreErros := typeset.NewTypeSet(false,
		"该酒店已下架",
		"该酒店当日房间已经售罄",
		"该酒店当日房间已售罄！",
	)

	// 记录是否找到了至少一个有效价格
	foundValidPrice := false

	for start.Before(end) {
		res, err := ota.HotelRoomRateList(my_ota.CeekeeHotelRoomRateListReq{
			HotelId:       otaId,
			ArrivalDate:   constmap.DateTime{start},
			DepartureDate: constmap.DateTime{start.AddDate(0, 0, 1)},
		})
		if err != nil {
			if e, ok := err.(my_ota.ApiError); ok && ignoreErros.Has(e.Msg) {
				my_logger.Errorf("ignore ota error", zap.String("hotel", fmt.Sprintf("[%d]%s", hotel.ID, hotel.Name)), zap.Error(err))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("getRoomPolicy[%d]", hotelId))
		}

		var avgPrice float64
		var counter int
		var matched bool
		for _, v := range res.Rooms {
			if strutil.ContainsAny(v.Name, []string{"标准", "双人", "双床"}) {
				matched = true
			} else {
				continue
			}
			for _, policy := range v.PolicyInfos {
				counter++
				avgPrice += policy.SalesPrice
			}
		}
		if !matched {
			for _, v := range res.Rooms {
				for _, policy := range v.PolicyInfos {
					counter++
					avgPrice += policy.SalesPrice
				}
			}
		}

		// 只要有一次counter > 0，就认为找到了有效价格
		if counter > 0 {
			foundValidPrice = true
			avgPrice = avgPrice / float64(counter)

			var update = &models.HotelPrice{
				AvgPrice:   utils.CurrencyFloat2Int(avgPrice),
				IsFestival: utils.If(holiday.Has(start.Format(constmap.DateFmtLong)), constmap.Enable, constmap.Disable),
			}
			var hotelPrice = new(models.HotelPrice)
			if db.Where("hotel_id=? AND `date`=?", hotelId, start.Format(constmap.DateFmtLong)).Take(&hotelPrice).Error == nil {
				if db.Model(&hotelPrice).Updates(update).RowsAffected == 0 {
					return fmt.Errorf("update hotel price fail:%d,%s", hotelId, start.Format(constmap.DateFmtLong))
				}
			} else {
				hotelPrice = update
				hotelPrice.HotelId = hotelId
				hotelPrice.Date = start
				if err := db.Create(&hotelPrice).Error; err != nil {
					return errors.Wrap(err, fmt.Sprintf("create hotel price fail:%d,%s", hotelId, start.Format(constmap.DateFmtLong)))
				}
			}

			// 找到一个有效价格后，可以提前退出循环
			break
		}

		start = start.AddDate(0, 0, 1)
	}

	// 如果没有找到有效价格，则不更新酒店平均价格
	if !foundValidPrice {
		return nil
	}

	var avgPrice int64
	db.Model(&models.HotelPrice{}).Select("CAST(IFNULL(AVG(avg_price), 0) AS SIGNED) avg_price").
		Where(models.HotelPrice{HotelId: hotelId}).Scan(&avgPrice)

	if err := db.Model(&hotel).Updates(models.Hotel{
		AvgPrice: avgPrice,
	}).Error; err != nil {
		return errors.Wrap(err, "update hotel avg price")
	}

	poi_biz.EsSyncBiz.SyncHotelByIds(db, []uint{hotel.ID})

	return nil
}

func LoadExt(db *gorm.DB, hotelIds []uint) map[uint]*models.HotelExt {
	ret := make(map[uint]*models.HotelExt)
	if len(hotelIds) == 0 {
		return ret
	}
	var exts []*models.HotelExt
	db.Where("hotel_id in ?", hotelIds).Find(&exts)
	slice.ForEach(exts, func(index int, item *models.HotelExt) {
		ret[item.HotelId] = item
	})
	return ret
}

func LoadHotels(db *gorm.DB, hotelIds []uint, loadPic bool) map[uint]*models.Hotel {
	ret := make(map[uint]*models.Hotel)
	if len(hotelIds) == 0 {
		return ret
	}
	q := db.Where("id in ?", hotelIds)
	if loadPic {
		q = q.Preload("Pics")
	}
	var hotels []*models.Hotel
	q.Find(&hotels)
	slice.ForEach(hotels, func(index int, item *models.Hotel) {
		ret[item.ID] = item
	})
	return ret
}

func BuildSegmentText(o *beans.DifyKnowledgeHotel) string {
	str := new(strings.Builder)
	write := func(txts ...string) {
		for _, v := range txts {
			str.WriteString(v)
		}
		str.WriteByte('\n')
	}
	write("酒店名称：", o.Name)
	write("ID:", convertor.ToString(o.HotelId))
	write("图片:", utils.StaticUrl(o.Pic))
	write("经纬度:", fmt.Sprintf("%f,%f", o.Lng, o.Lat))
	write("所在市:", fmt.Sprintf("[%d]%s", o.ZoneId, o.ZoneName))
	write("地址:", o.Address)
	write("星级:", fmt.Sprintf("[%d]%s", o.Star, o.StarDesc))
	write("均价:", o.AvgPrice)
	write("评分:", utils.FormatNumber(o.Score, 1))
	write("商圈:", o.BusinessArea)
	write("品牌:", fmt.Sprintf("[%d]%s", o.BrandId, o.BrandName))
	write("客人类型:", o.GuestType)
	write("推荐理由:", o.RecReason)
	write("附近景点:")
	for _, scene := range o.NearbyScenes {
		write("    -", fmt.Sprintf("[%d]%s", scene.Id, scene.Name))
	}
	return str.String()
}

func GetAvgPrice(db *gorm.DB, start, end time.Time, hotelIds []uint) map[uint]int64 {
	ret := make(map[uint]int64)
	if len(hotelIds) == 0 {
		return ret
	}

	// 1. 从HotelPrice表获取指定日期范围内的平均价格
	var prices []models.HotelPrice
	db.Select("hotel_id, cast(ifnull(avg(avg_price),0) as signed) avg_price").
		Where("hotel_id in ?", hotelIds).
		Where("date >= ? and date <= ?", start.Format(constmap.DateFmtLong), end.Format(constmap.DateFmtLong)).
		Group("hotel_id").
		Find(&prices)

	// 将查询结果存入返回map
	foundHotelIds := make([]uint, 0, len(prices))
	slice.ForEach(prices, func(index int, item models.HotelPrice) {
		ret[item.HotelId] = item.AvgPrice
		foundHotelIds = append(foundHotelIds, item.HotelId)
	})

	// 2. 找出没有价格数据的酒店ID
	missingHotelIds := make([]uint, 0)
	for _, id := range hotelIds {
		if !slice.Contain(foundHotelIds, id) {
			missingHotelIds = append(missingHotelIds, id)
		}
	}

	// 3. 如果有未找到价格的酒店，从Hotel表获取它们的AvgPrice
	if len(missingHotelIds) > 0 {
		var hotels []*models.Hotel
		db.Select("id, avg_price").Where("id in ?", missingHotelIds).Find(&hotels)

		slice.ForEach(hotels, func(index int, hotel *models.Hotel) {
			ret[hotel.ID] = hotel.AvgPrice
		})
	}

	return ret
}
