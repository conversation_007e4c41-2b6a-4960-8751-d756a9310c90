package tuan_biz

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	es2 "roadtrip-api/src/models/es"
	"roadtrip-api/src/utils"
	"strings"
	"time"
)

type esSync struct{}

var EsSyncBiz esSync

func (o esSync) SyncTuanByIds(db *gorm.DB, ids []uint) {
	var list []models.Tuan
	db.Joins("Zone").Find(&list, ids)
	o.syncTuan(db, list)
}

func (o esSync) FetchTuan(db *gorm.DB, start time.Time, force bool) {
	var nextId uint
	for {
		var where strings.Builder
		var whereArgs []any
		where.WriteString("tuans.id>?")
		whereArgs = append(whereArgs, nextId)
		if !force {
			where.WriteString(" and tuans.updated_at>?")
			whereArgs = append(whereArgs, start)
		}
		var list []models.Tuan
		db.Limit(100).Where(where.String(), whereArgs...).
			Order("tuans.id asc").
			Joins("Zone").
			Find(&list)
		if len(list) == 0 {
			break
		}

		nextId = list[len(list)-1].ID
		o.syncTuan(db, list)
	}
}

func (o esSync) syncTuan(db *gorm.DB, list []models.Tuan) {
	type bulkDoc struct {
		es2.TuanModel
		Id string
	}
	var items []bulkDoc
	for _, v := range list {
		my_logger.Infof("sync tuan", zap.Uint("id", v.ID))

		doc := bulkDoc{
			Id: utils.EsTuanId(v.ID),
			TuanModel: es2.TuanModel{
				ObjId:    v.ID,
				Name:     v.Name,
				ZoneId:   v.ZoneId,
				CityName: utils.TrimCitySuffix(v.Zone.Name),
				State:    v.State,
				Days:     v.Days,
				Cate:     utils.If(v.Cate == "", []string{}, strings.Split(v.Cate, ",")),
			},
		}
		doc.Keycnt = doc.CityName + doc.Name
		items = append(items, doc)
	}

	var bulkBody strings.Builder
	for _, vk := range slice.Chunk(items, 10) {
		for _, v := range vk {
			bulkBody.WriteString(fmt.Sprintf("{\"index\":{\"_index\":\"%s\",\"_id\":\"%s\"}}\n", constmap.EsIndexTuan, v.Id))
			b, _ := json.Marshal(v.TuanModel)
			bulkBody.Write(append(b, '\n'))
		}
	}
	if _, err := es.Bulk([]byte(bulkBody.String())); err != nil {
		my_logger.Errorf("es error", zap.Error(err))
	}
}
