package banner_biz

import (
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func LoadBanners(db *gorm.DB, pos []constmap.BannerPosition) map[constmap.BannerPosition][]beans.Banner {
	var banners []models.Banner
	db.Where("position in ? AND state=?", pos, constmap.Enable).
		Where("start<=now() AND end>=now()").
		Order("sort desc").
		Find(&banners)
	ret := make(map[constmap.BannerPosition][]beans.Banner)
	slice.ForEach(pos, func(index int, item constmap.BannerPosition) {
		ret[item] = make([]beans.Banner, 0)
	})
	slice.ForEach(banners, func(index int, item models.Banner) {
		ret[item.Position] = append(ret[item.Position], beans.Banner{
			Id:    item.ID,
			Title: item.Title,
			Link:  item.Link,
			Pic:   utils.StaticUrl(item.Pic),
		})
	})
	return ret
}
