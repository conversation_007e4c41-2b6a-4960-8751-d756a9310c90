<script setup>

import {onMounted, ref} from "vue";
import {categories} from "../api/modules";

const props = defineProps({
  multiple: {
    type: Boolean
  },
  type: {
    type: String,
    default: 'tuan'
  }
})

const value = defineModel();
const list = ref([]);

onMounted(() => {
  categories({type: props.type}).then(res => {
    const {data} = res

    list.value = data.list
  })
})

</script>

<template>
  <el-select :multiple="multiple" v-model="value" clearable>
    <el-option v-for="item in list" :key="item.value" :label="item.value" :value="item.value"/>
  </el-select>
</template>

<style lang="scss" scoped>

</style>
