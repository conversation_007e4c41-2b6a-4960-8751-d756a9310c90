<script setup>

import { onMounted, ref } from "vue";
import { taskList } from "@/api/modules/task";
import { Enable } from "@/utils/constmap";

const props = defineProps({
  ignoreIds: { type: Array, default: () => ([]) }
})
const value = defineModel()
const list = ref([])
const loading = ref(false)

function getList(keyword = '') {
  loading.value = true
  const params = {
    only_parent: Enable,
    state: Enable,
    name: keyword,
    id: 0,
  }
  if (keyword == '') {
    params.id = value.value
  }

  taskList(params).then(res => {
    list.value = res.data.list.filter(v => !props.ignoreIds.includes(v.id))
    loading.value = false
  })
}

onMounted(() => {
  getList()
})

</script>

<template>
  <el-select v-model="value" :loading="loading" :remote-method="getList" clearable filterable placeholder="输入或选择任务"
    remote>
    <el-option v-for="item in list" :key="item.id" :label="`[${item.id}]${item.name}`" :value="item.id">
    </el-option>
  </el-select>
</template>

<style lang="scss" scoped></style>
