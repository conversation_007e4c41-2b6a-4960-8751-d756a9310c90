package my_cart

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/models"
	"time"
)

type Item struct {
	ProductId uint `json:"product_id"`
	Quantity  int  `json:"quantity"`
}

type CartInfo struct {
	Total int     `json:"total"`
	List  []*Item `json:"list"`
}

func (c *CartInfo) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, c)
}

func (c *CartInfo) MarshalBinary() (data []byte, err error) {
	return json.Marshal(c)
}

type Cart struct {
	db       *gorm.DB
	userId   uint
	cacheKey string

	info *CartInfo
}

func (c *Cart) init() {
	c.cacheKey = fmt.Sprintf("cart:%d", c.userId)
	c.info = new(CartInfo)
	my_cache.Get(c.cacheKey, c.info)
}

func (c *Cart) Remove(productId uint) error {
	for i, item := range c.info.List {
		if item.ProductId == productId {
			c.info.List = slice.DeleteAt(c.info.List, i)
			break
		}
	}
	return c.Compute()
}

func (c *Cart) Update(productId uint, quantity int) error {
	for i, item := range c.info.List {
		if item.ProductId == productId {
			item.Quantity = quantity
			c.info.List[i] = item
			break
		}
	}
	return c.Compute()
}

func (c *Cart) Add(product *models.ScenicTag, quantity int) error {

	if it, ok := slice.FindBy(c.info.List, func(index int, it *Item) bool {
		return it.ProductId == product.ID
	}); ok {
		it.Quantity += quantity
	} else {
		c.info.List = append(c.info.List, &Item{ProductId: product.ID, Quantity: quantity})
	}
	return c.Compute()
}

func (c *Cart) Compute() error {
	c.info.Total = slice.ReduceBy(c.info.List, 0, func(index int, item *Item, agg int) int {
		return agg + item.Quantity
	})
	return my_cache.Set(c.cacheKey, c.info, time.Hour*24*7)
}

func (c *Cart) Info() *CartInfo {
	return c.info
}

func New(db *gorm.DB, userId uint) *Cart {
	c := &Cart{
		db:     db,
		userId: userId,
	}

	c.init()

	return c
}
