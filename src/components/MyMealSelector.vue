<template>
  <el-select v-model="value" clearable>
    <el-option v-for="item in list" :key="item.id" :label="`${item.category_name}-${item.name}`" :value="item.id"/>
  </el-select>
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {meals} from "../api/modules/meal";

const props = defineProps(['modelValue', 'state'])
const emit = defineEmits(['update:modelValue'])
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const list = ref([])

onMounted(() => {
  const params = {page_size: 100}
  if (props.state) {
    params.state = props.state
  }

  meals(params).then(res => {
    const {data} = res
    list.value = data.list
  })
})

</script>
