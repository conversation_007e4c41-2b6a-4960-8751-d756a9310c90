<template>
  <div>
    <Toolbar :defaultConfig="toolbarConfig"
             :editor="editorRef"
             mode="simple"
             style="border-bottom: 1px solid #ccc"/>
    <Editor v-model="value" :defaultConfig="editorConfig" :style="style" mode="simple"
            @onCreated="(editor) => editorRef = editor"/>
  </div>
</template>
<script setup>

import '@wangeditor/editor/dist/css/style.css'
import {computed, onBeforeUnmount, shallowRef} from "vue";
import {Editor, Toolbar} from "@wangeditor/editor-for-vue";
import {useUserStore} from "../store/modules/users";
import {ElNotification} from "element-plus";

const props = defineProps({
  modelValue: {
    type: String,
  },
  height: {
    type: String,
    default: () => '200px'
  }
})
const emit = defineEmits(['update:modelValue'])
const userStore = useUserStore()
const style = computed(() => {
  return {height: props.height}
})
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

const apiHost = import.meta.env.VITE_BASE_API + '/api/v1/admin/upload'

const editorRef = shallowRef()
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {
    uploadImage: {
      server: apiHost,
      maxFileSize: 10 * 1024 * 1024,
      headers: {
        token: userStore.token,
      },
      fieldName: 'file',
      meta: {
        is_tmp: 2,
      },
      timeout: 1000 * 60,
      customInsert(res, insertFn) {
        if (res.code !== 0) {
          ElNotification.error(res.msg)
          return
        }
        const {data} = res

        insertFn(data.url)
      }
    }
  }
}
const toolbarConfig = {}

onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor === null) {
    return
  }

  editor.destroy()
})

</script>
<style lang="scss" scoped>

</style>
