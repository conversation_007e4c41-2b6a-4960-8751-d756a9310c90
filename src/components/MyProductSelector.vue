<script setup>

import {computed, onMounted, ref} from "vue";
import {products} from "../api/modules/product";
import {formatMoney} from "../utils";

const props = defineProps({
  modelValue: [String, Number],
  gift: Boolean,
})
const emit = defineEmits(['update:modelValue'])

const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const list = ref([])
const loading = ref(false)

function getList(keyword = '') {
  loading.value = true
  const params = {state: 1, keyword, gift: props.gift ? 1 : 2}

  products(params).then(res => {
    list.value = res.data.list
    loading.value = false
  })
}

onMounted(() => {
  getList()
})

</script>

<template>
  <el-select v-model="value" :loading="loading" :remote-method="getList" clearable filterable
             placeholder="输入或选择商品" remote>
    <el-option v-for="item in list" :key="item.id"
               :label="`[${item.id}]${item.name} ${formatMoney(item.price)}`"
               :value="item.id">
    </el-option>
  </el-select>
</template>

<style lang="scss" scoped>

</style>
