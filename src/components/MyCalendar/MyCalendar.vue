<script setup>

import {onMounted, ref} from "vue";

const props = defineProps({
  value: {
    type: Date,
    default: () => new Date(),
  }
})
const emit = defineEmits(['monthChange'])
const toDay = ref(new Date())
const currentMonth = ref(props.value)
const calendar = ref([])
const weekly = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
const model = defineModel({default: () => []})

function onPrev() {
  currentMonth.value.setMonth(currentMonth.value.getMonth() - 1)
  computeCalendar()
  emit('monthChange', currentMonth.value)
}

function onNext() {
  currentMonth.value.setMonth(currentMonth.value.getMonth() + 1)
  computeCalendar()
  emit('monthChange', currentMonth.value)
}

function onCurrent() {
  toDay.value = new Date()
  currentMonth.value = new Date(toDay.value.getFullYear(), toDay.value.getMonth(), 1)
  computeCalendar()
  emit('monthChange', currentMonth.value)
}

function computeCalendar() {
  const list = []
  const firstDayOfMonth = currentMonth.value.getDay()
  for (let i = 0; i < firstDayOfMonth; i++) {
    const first = list[0] ?? []
    if (first.length === 0) {
      list.push(first)
    }
    const t = new Date(currentMonth.value.getFullYear(),
        currentMonth.value.getMonth(),
        currentMonth.value.getDate() - (firstDayOfMonth - i)
    )
    first.push(t)
  }
  let next = 0
  if (list.length === 0) {
    list.push([])
  }
  const iter = new Date(currentMonth.value.getFullYear(), currentMonth.value.getMonth(), currentMonth.value.getDate())
  while (iter.getMonth() === currentMonth.value.getMonth()) {
    let group = list[next]
    if (!group || group.length >= 7) {
      group = []
      next++

      list.push(group)
    }
    group.push(new Date(iter.getFullYear(), iter.getMonth(), iter.getDate()))

    iter.setDate(iter.getDate() + 1)
  }
  const group = list[list.length - 1]
  if (group.length < 7) {
    const next = group[group.length - 1]
    for (let i = 0; i < 7 - group.length; i++) {
      group.push(new Date(next.getFullYear(), next.getMonth(), next.getDate() + i + 1))
    }
  }

  calendar.value = list
}

function inSelected(t1) {
  const index = model.value.findIndex(item => item.getTime() === t1.getTime())
  return index > -1
}

function isToday(item) {
  return item.getMonth() === toDay.value.getMonth()
      && item.getFullYear() === toDay.value.getFullYear()
      && item.getDate() === toDay.value.getDate()
}

function equalMonth(t1, t2) {
  return t1.getFullYear() === t2.getFullYear()
      && t1.getMonth() === t2.getMonth()
}

function equalDate(t1, t2) {
  return equalMonth(t1, t2) && t1.getDate() === t2.getDate()
}

function change(date) {
  if (!equalMonth(date, currentMonth.value)) {
    currentMonth.value = new Date(date.getFullYear(), date.getMonth(), 1)
    computeCalendar()
    return
  }

  const index = model.value.findIndex(item => equalDate(item, date))
  if (index === -1) {
    model.value.push(date)
  } else {
    model.value.splice(index, 1)
  }
}

onMounted(() => {
  currentMonth.value.setDate(1)

  computeCalendar()
})

</script>

<template>
  <div class="calendar">
    <div class="header">
      <div>{{ `${currentMonth.getFullYear()}年${currentMonth.getMonth() + 1}月` }}</div>
      <div class="nav">
        <div @click="onPrev">上月</div>
        <div @click="onCurrent">今天</div>
        <div @click="onNext">下月</div>
      </div>
    </div>
    <table>
      <thead>
      <tr>
        <th v-for="item in weekly">{{ item }}</th>
      </tr>
      </thead>
      <tr v-for="group in calendar">
        <td @click="change(item)" v-for="item in group" :class="{
          notMonth: item.getMonth() !== currentMonth.getMonth(),
          today: isToday(item),
          selected: inSelected(item)
        }">
          <slot>{{ item.getDate() }}</slot>
        </td>
      </tr>
    </table>
  </div>

</template>

<style scoped lang="scss">
$border-color: #dcdfe6;
$active-color: rgb(64, 158, 255);
$not-month-color: rgb(168, 171, 178);

.calendar {
  display: flex;
  flex-direction: column;
  width: 100%;

  .header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $border-color;
    padding: 20px 0;
    align-items: center;

    .nav {
      display: flex;
      border: 1px solid $border-color;

      div {
        border-right: 1px solid $border-color;
        padding: 2px 10px;

        &:last-child {
          border-right: 0;
        }
      }
    }
  }

  table {
    border-collapse: collapse;

    td {
      text-align: center;
      border: 1px solid $border-color;

      &.notMonth {
        color: $not-month-color;
      }

      &.today {
        color: $active-color;
      }

      &.selected {
        background: $active-color;
        color: white;
      }
    }
  }

  .nav div:hover, table td:hover {
    background: $active-color;
    color: white;
    cursor: pointer;
  }
}
</style>