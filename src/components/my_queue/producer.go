package my_queue

import (
	"encoding/json"
	redis_stream "gitee.com/yjsoft-sh/redis-stream/src"
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/constmap"
	"sync"
)

type Payload struct {
	ID      string `json:"id"`
	Event   int    `json:"event"`
	Payload gin.H  `json:"payload"`
}

var once sync.Once
var stream *redis_stream.RedisStream

func Client() *redis_stream.RedisStream {
	once.Do(func() {
		var err error
		stream, err = redis_stream.New(my_cache.RedisClient())
		if err != nil {
			panic(err)
		}
	})

	return stream
}

func (p *Payload) MarshalBinary() (data []byte, err error) {
	return json.Marshal(p)
}

func (p *Payload) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, p)
}

func PdfEvent(eventType int, values gin.H) error {
	return Client().Add(constmap.EventQueuePdf, &Payload{Event: eventType, Payload: values})
}

func Weight(eventType int, values gin.H) error {
	return Client().Add(constmap.EventQueueWeight, &Payload{Event: eventType, Payload: values})
}

func Light(eventType int, values gin.H) error {
	return Client().Add(constmap.EventQueueLight, &Payload{Event: eventType, Payload: values})
}
