package doubao

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/model"
	"github.com/volcengine/volcengine-go-sdk/service/arkruntime/utils"
	"net/http"
	"roadtrip-api/src/config"
	"sync"
)

var client *arkruntime.Client
var once sync.Once

const host = "https://ark.cn-beijing.volces.com"

func initSdk() {
	client = arkruntime.NewClientWithApiKey(config.Config.Doubao.ApiKey)
}

func Sdk() *arkruntime.Client {
	once.Do(initSdk)

	return client
}

// 创建上下文
func ChatContextCreate(messages []*model.ChatCompletionMessage, ttl int) (*model.CreateContextResponse, error) {
	rsp, err := Sdk().CreateContext(context.Background(), model.CreateContextRequest{
		Model:    config.Config.Doubao.Model,
		Mode:     model.ContextModeSession,
		Messages: messages,
		TTL:      &ttl,
	})
	if err != nil {
		return nil, err
	}
	return &rsp, nil
}

// 含上下文缓存的聊天
func ChatContextCompletion(ctxId string, messages []*model.ChatCompletionMessage) (*model.ChatCompletionResponse, error) {
	res, err := Sdk().CreateContextChatCompletion(context.Background(), model.ContextChatCompletionRequest{
		ContextID: ctxId,
		Model:     config.Config.Doubao.Model,
		Messages:  messages,
		//Temperature: 0.5,
	})
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// 含上下文缓存的聊天
func ChatContextStreamingCompletion(ctx context.Context, ctxId string, messages []*model.ChatCompletionMessage) (*utils.ChatCompletionStreamReader, error) {
	res, err := Sdk().CreateContextChatCompletionStream(ctx, model.ContextChatCompletionRequest{
		ContextID: ctxId,
		Model:     config.Config.Doubao.Model,
		Messages:  messages,
		//Temperature: 0.5,
		Stream: true,
	})
	if err != nil {
		return nil, err
	}
	return res, nil
}

func ChatCompletionMessage(message []*model.ChatCompletionMessage) (*model.ChatCompletionResponse, error) {
	url := host + "/api/v3/chat/completions"
	body := model.ChatCompletionRequest{
		Model:       config.Config.Doubao.Model,
		Messages:    message,
		Temperature: 0.5,
		//TopP:        0.7,
	}

	buf, _ := json.Marshal(body)

	client := &http.Client{}

	request, _ := http.NewRequest("POST", url, bytes.NewBuffer(buf))
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Authorization", "Bearer "+config.Config.Doubao.ApiKey)

	resp, err := client.Do(request)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return nil, errors.New(resp.Status)
	}

	var b model.ChatCompletionResponse

	if err = netutil.ParseHttpResponse(resp, &b); err != nil {
		return nil, err
	}

	return &b, nil
}

//func ChatCompletionMessage(message []*model.ChatCompletionMessage) (model.ChatCompletionResponse, error) {
//	req := &model.ChatCompletionRequest{
//		Model:    config.String("doubao.model", ""),
//		Messages: message,
//	}
//
//	return Sdk().CreateChatCompletion(context.Background(), *req)
//}
