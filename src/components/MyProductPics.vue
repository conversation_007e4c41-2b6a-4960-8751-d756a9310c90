<script setup>
import MyUpload from "./MyUpload.vue";
import {Plus} from "@element-plus/icons-vue";
import {MaxImageSize} from "../utils/constmap";
import {ref, toRaw, watch} from "vue";
import MyImage from "./MyImage.vue";

const emit = defineEmits(['change'])
const props = defineProps({
  hasOtherPic: {
    type: <PERSON><PERSON><PERSON>,
    default: () => true,
  },
  hasMain: {
    type: <PERSON><PERSON><PERSON>,
    default: () => true,
  },
  mainPic: {
    type: String,
  },
  pics: {
    type: Array,
    default: () => []
  }
})

const maxPic = 5
const pics = ref(new Array(maxPic).fill(''))
const mainUrl = ref(props.mainPic)
const picUrl = ref([])
const main = ref({})
const remove = ref([])

watch(() => props.mainPic, (val) => {
  if (val !== '') {
    mainUrl.value = val
  }
}, {immediate: true})
watch(() => props.pics, (val) => {
  if (val.length > 0) {
    for (let i = 0; i < val.length; i++) {
      if (Number.isInteger(val[i])) {
        continue
      }
      picUrl.value[i] = val[i]
    }
  }
}, {immediate: true})

function handleMainUploadSuccess(file) {
  main.value = file
  loadImage(true)

  change()
}

function handlePicUploadSuccess(index, file) {
  pics.value[index] = file
  loadImage(false, index)

  change()
}

function loadImage(isMain, index) {
  const reader = new FileReader()
  if (isMain) {
    reader.readAsDataURL(main.value.file)
  } else {
    reader.readAsDataURL(pics.value[index].file)
  }

  reader.onload = () => {
    if (isMain) {
      mainUrl.value = reader.result
    } else {
      picUrl.value[index] = reader.result
    }
  }
}

function closePic(isMain, index) {
  if (isMain) {
    main.value = ''
    mainUrl.value = ''
  } else {
    const url = toRaw(picUrl.value[index])
    if (url.indexOf('http') === 0) {
      remove.value.push(url)
    }

    pics.value[index] = ''
    picUrl.value[index] = ''
  }

  change()
}

function change() {
  const data = {
    main: [],
    pics: [],
    remove: remove.value,
  }

  if (main.value.res_id) {
    data.main.push(main.value.res_id)
  }
  pics.value.forEach(item => {
    if (item && item.res_id) {
      data.pics.push(item.res_id)
    }
  })

  emit('change', data)
}

</script>

<template>
  <div>
    <div v-if="props.hasMain">
      <MyUpload v-if="!mainUrl" :draggable="false" @success="handleMainUploadSuccess">
        <template #default>
          <el-space class="pic" direction="vertical">
            <el-icon>
              <Plus/>
            </el-icon>
            <span style="color: red;">*主图</span>
          </el-space>
        </template>
      </MyUpload>
      <MyImage v-else :src="mainUrl" class="pic" style="height:150px;width: 150px;" @close="closePic(true)"></MyImage>
    </div>

    <el-space v-if="hasOtherPic" style="margin-top: 10px;" wrap>
      <template v-for="(item, i) in new Array(maxPic)" :key="i">
        <MyUpload v-if="!picUrl[i]" @success="(file) => handlePicUploadSuccess(i, file)">
          <template #default>
            <el-space class="pic" direction="vertical">
              <el-icon>
                <Plus/>
              </el-icon>
              辅助图
            </el-space>
          </template>
        </MyUpload>
        <MyImage v-else :src="picUrl[i]" class="pic" fit="contain" style="height:150px;width: 150px;"
                 @close="() => closePic(false, i)"></MyImage>
      </template>

    </el-space>
  </div>
</template>

<style lang="scss" scoped>
.pic {
  width: 150px;
  max-height: 150px;
}
</style>
