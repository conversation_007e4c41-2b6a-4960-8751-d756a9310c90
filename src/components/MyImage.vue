<script setup>
import { CloseBold } from "@element-plus/icons-vue";

const props = defineProps({
  src: String,
  style: [String, Object]
})
const emit = defineEmits(['close'])

</script>

<template>
  <div :style="style" class="pic">
    <el-icon size="30px" @click="emit('close')">
      <CloseBold />
    </el-icon>
    <el-image :src="src" :style="props.style" fit="contain"></el-image>
  </div>
</template>

<style lang="scss" scoped>
.pic {
  position: relative;

  .el-icon {
    position: absolute;
    z-index: 100;
    right: 4px;
    top: 4px;
    cursor: pointer;
  }
}
</style>
