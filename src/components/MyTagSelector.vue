<script setup>

import {ref} from "vue";

const {fetch,create} = defineProps(['fetch','create'])
const loading = ref(false)
const modelValue = defineModel()
const list = ref([])

const afetch = (keyword) => {
  loading.value = true
  return fetch(keyword).then(l => {list.value = l}).finally(()=>{loading.value=false})
}

const addContent = ref('')
const showCreateInput = ref(false)
const doCreate = async() =>{
  if (create && addContent.value!='') {
    const item = await create(addContent.value)
    list.value.push(item)
    modelValue.value.push(item.value)
  }
  addContent.value = ''
  showCreateInput.value = false
}

</script>

<template>
  <el-select :loading="loading" remote :remote-method="afetch" filterable multiple v-model="modelValue">
    <el-option
          v-for="item in list"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
        <template #footer v-if="create">
          <el-button v-if="!showCreateInput" link type="primary" @click="showCreateInput=true">添加</el-button>
          <template v-else>
            <el-input v-model="addContent" size="small">
              <template #append>
                <el-button @click="doCreate">新增</el-button>
              </template>
            </el-input>
          </template>
        </template>
  </el-select>
</template>

<style lang="scss" scoped>

</style>
