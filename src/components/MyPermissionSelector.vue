<template>
  <el-cascader
      v-model="value"
      :options="options"
      :props="selectProps"
      style="width: 100%"
  />
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {permissions} from "../api/modules/staff";

const props = defineProps(['modelValue'])
const emit = defineEmits(['update:modelValue'])
const options = ref([])
const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})
const selectProps = {
  multiple: true,
}

onMounted(() => {
  permissions().then(res => {
    const list = {}

    res.data.list.forEach(item => {
      const p = {
        value: item.group_name,
        label: item.group_name,
        children: []
      }

      if (!list[p.value]) {
        list[p.value] = p
      }

      list[p.value].children.push({
        value: item.id,
        label: item.name,
        children: []
      })
    })
    options.value = Object.values(list)
  })
})

</script>
