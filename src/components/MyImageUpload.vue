<script setup>
import {onMounted, ref} from 'vue';
import MyUpload from './MyUpload.vue';
import {Delete, Plus, ZoomIn} from '@element-plus/icons-vue'
import { ElImageViewer } from 'element-plus'

const props = defineProps({
  canUpload: {
    type: Boolean,
    default: true
  },
  file: {
    type: Object,
    default: null,
  }
})
const resId = defineModel({type: [String, Number], default: 0})
const url = defineModel('url', {type: String, default: ''})
const emits = defineEmits(['update:url', 'remove', 'success'])
const showOverlay = ref(false)
const imageRef = ref(null)
const showImageViewer = ref(false)

function handlePicUploadSuccess(ufile) {
  resId.value = ufile.res_id

  emits('success', ufile)

  loadImage(ufile)
}

function loadImage(ufile) {
  const reader = new FileReader()
  reader.readAsDataURL(ufile.file)

  reader.onload = () => {
    url.value = reader.result
  }
}

function closePic() {
  resId.value = 0
  url.value = ''

  emits('remove')
}

function onPreview(e) {
  if (url.value) {
    showImageViewer.value = true
  }
}

function closeImageViewer() {
  showImageViewer.value = false
}

onMounted(() => {
  if (props.file) {
    loadImage({file: props.file})
  }
})
</script>

<template>
  <div>
    <MyUpload v-if="!url && canUpload" accept="image/*" @success="handlePicUploadSuccess">
      <template #default>
        <div class="pic">
          <el-space class="ppic" direction="vertical">
            <el-icon>
              <Plus/>
            </el-icon>
          </el-space>
        </div>
      </template>
    </MyUpload>
    <div
        v-else
        class="image-container"
        @mouseenter="showOverlay = true"
        @mouseleave="showOverlay = false"
    >
      <!-- 遮罩层 -->
      <div v-if="showOverlay" class="overlay">
        <el-button circle size="small">
          <el-icon>
            <ZoomIn @click="onPreview"/>
          </el-icon>
        </el-button>
        <el-button circle size="small" @click="closePic">
          <el-icon>
            <Delete/>
          </el-icon>
        </el-button>
      </div>
      <el-image ref="imageRef" :src="url" class="pic" fit="contain"
                style="height:150px;width: 150px;"/>
    </div>

    <!-- 图片预览器 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="[url]"
      :initial-index="0"
      @close="closeImageViewer"
      teleported
    />
  </div>
</template>

<style lang="scss" scoped>
@use "@/styles/mix";

.pic {
  width: 150px;
  height: 150px;
  border: 1px solid #cccccc;
  @include mix.center();

  .ppic {
    width: 150px;
    max-height: 150px;
  }
}

.image-container {
  position: relative;
  display: inline-block;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4); /* 黑色半透明遮罩 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

</style>
