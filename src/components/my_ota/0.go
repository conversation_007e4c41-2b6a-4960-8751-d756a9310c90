package my_ota

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
)

const ApiOkCode = "-12321"

type ApiError struct {
	Code string
	Msg  string
}

func (o ApiError) Error() string {
	return fmt.Sprintf("[%s]%s", o.Code, o.Msg)
}

func request(u string, method string, header map[string][]string, query map[string]string, body any, res any) error {
	var queryString strings.Builder
	for i, v := range query {
		queryString.WriteString(fmt.Sprintf("%s=%s&", i, url.QueryEscape(v)))
	}
	if queryString.Len() > 0 {
		if strings.Contains(u, "?") {
			u += "&" + queryString.String()
		} else {
			u += "?" + queryString.String()
		}
	}
	uri, err := url.Parse(u)
	if err != nil {
		return err
	}
	req := &http.Request{
		Method: method,
		URL:    uri,
		Header: header,
	}
	if body != nil {
		if r, ok := body.(io.ReadCloser); ok {
			req.Body = r
		} else {
			b, _ := json.Marshal(body)
			req.Body = io.NopCloser(bytes.NewReader(b))
		}
	}
	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	b, _ := io.ReadAll(rsp.Body)
	if res != nil {
		err = json.Unmarshal(b, &res)
	}
	if rsp.StatusCode != http.StatusOK {
		return fmt.Errorf("status code %d:%s", rsp.StatusCode, b)
	}
	return err
}
