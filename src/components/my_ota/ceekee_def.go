package my_ota

import (
	"encoding/json"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"time"
)

func CeekeeConfirmDesc(isInstant bool, confirmDesc string) string {
	return utils.If(confirmDesc == "", utils.If(isInstant, "立即确认", "需要二次确认"), confirmDesc)
}
func CeekeeGuestType2Yj(g int) constmap.GuestType {
	switch g {
	case 3:
		return constmap.GuestTypeMainland
	case 4:
		return constmap.GuestTypeOverseas
	case 5:
		return constmap.GuestTypeAll
	}
	return 0
}

func IdCardCeekee2Yj(typ CeekeeEnumIdType) constmap.IdType {
	i, ok := map[CeekeeEnumIdType]constmap.IdType{
		CeekeeEnumIdType1:  constmap.IdTypeIdCard,
		CeekeeEnumIdType2:  constmap.IdTypePassport,
		CeekeeEnumIdType3:  constmap.IdTypeSoldier,
		CeekeeEnumIdType5:  constmap.IdTypeTaiwanResident,
		CeekeeEnumIdType9:  constmap.IdTypeGAPassport,
		CeekeeEnumIdType10: constmap.IdTypeTaiwanPassport,
		CeekeeEnumIdType12: constmap.IdTypeGreenCard,
	}[typ]
	return utils.If(ok, i, -1)
}

func IdCardYj2Ceekee(typ constmap.IdType) CeekeeEnumIdType {
	i, ok := map[constmap.IdType]CeekeeEnumIdType{
		constmap.IdTypeIdCard:         CeekeeEnumIdType1,
		constmap.IdTypePassport:       CeekeeEnumIdType2,
		constmap.IdTypeSoldier:        CeekeeEnumIdType3,
		constmap.IdTypeTaiwanResident: CeekeeEnumIdType5,
		constmap.IdTypeGAPassport:     CeekeeEnumIdType9,
		constmap.IdTypeTaiwanPassport: CeekeeEnumIdType10,
		constmap.IdTypeGreenCard:      CeekeeEnumIdType12,
	}[typ]
	return utils.If(ok, i, CeekeeEnumIdTypeNone)
}

func CancelPolicyCeekee2Yj(c CeekeeEnumCancelPolicy) constmap.HotelCancelPolicyType {
	s, ok := map[CeekeeEnumCancelPolicy]constmap.HotelCancelPolicyType{
		CeekeeEnumCancelPolicyFree:       constmap.HotelCancelPolicyFree,
		CeekeeEnumCancelPolicyFixedMoney: constmap.HotelCancelPolicyFixedMoney,
		CeekeeEnumCancelPolicyPercent:    constmap.HotelCancelPolicyPercent,
		CeekeeEnumCancelPolicyFirstNight: constmap.HotelCancelPolicyFirstNight,
		CeekeeEnumCancelPolicyAll:        constmap.HotelCancelPolicyAll,
	}[c]
	return utils.If(ok, s, -1)
}

type CeekeeEnumBreakfast int    //早餐枚举
type CeekeeEnumReason int       //订单取消原因枚举枚举
type CeekeeEnumGender int       //性别
type CeekeeEnumIdType int       //证件类型枚举
type CeekeeEnumOrderState int   //订单状态
type CeekeeEnumCancelRule int   //取消规则
type CeekeeEnumCancelPolicy int //取消政策

type ceekeeCommonParams struct {
	Version   string `json:"Version"`
	PartnerId string `json:"PartnerId"`
	Sign      string `json:"Sign"`
	TimeStamp string `json:"TimeStamp"`
	RandomNum string `json:"RandomNum"`
	MemberId  string `json:"MemberId"`
	Data      any    `json:"Data"`
}

type ceekeeResponse struct {
	Data    json.RawMessage `json:"Data"`
	Msg     string          `json:"Msg"`
	Message string          `json:"Message"`
	Code    string          `json:"Code"` //0为正常值
}

type ceekeeGetTokenResp struct {
	Expire  time.Time
	ExpDate string `json:"ExpDate,omitempty"`
	Token   string `json:"Token,omitempty"`
}

type CeekeeGetCityListItem struct {
	Country      string                 `json:"Country,omitempty"`      //国家名
	CountryId    int                    `json:"CountryId,omitempty"`    //国家Id
	Province     string                 `json:"Province,omitempty"`     //省份名
	ProvinceId   int                    `json:"ProvinceId,omitempty"`   //省份Id
	City         string                 `json:"City,omitempty"`         //城市名
	CityId       int                    `json:"CityId,omitempty"`       //城市ID
	ParentCityId int                    `json:"ParentCityId,omitempty"` //上级城市ID
	Parent       *CeekeeGetCityListItem `json:"-"`
}

type CeekeeGetCityListResp struct {
	CityList []CeekeeGetCityListItem `json:"CityList,omitempty"`
}
type CeekeeHotelSearchFilter struct {
	Type  int    `json:"Type"`  //搜索选项Id (GetHotelFilter接口中，HotelFilter的Type)
	Value string `json:"Value"` //该选项对应的值(GetHotelFilter接口中，FiterResponse的value)
}
type CeekeeHotelSearchReq struct {
	CityCode       int                       `json:"cityCode"`                 //城市Id
	Page           int                       `json:"page,omitempty"`           //页码：从1开始 默认1
	PageSize       int                       `json:"pageSize,omitempty"`       //每页条数 最大支持10条
	DepartureDate  constmap.Date             `json:"departureDate"`            //离店时间
	ArrivalDate    constmap.Date             `json:"arrivalDate"`              //入住时间
	FilterList     []CeekeeHotelSearchFilter `json:"filterList,omitempty"`     //
	Rank           int                       `json:"rank,omitempty"`           //酒店搜索排序SearchRankEnum
	Key            string                    `json:"key,omitempty"`            //搜索词
	ReferencePrice int                       `json:"referencePrice,omitempty"` //查询参考价
	ClientLocation string                    `json:"clientLocation,omitempty"` //客户端经纬度 示例： 31.29780714215392,120.6654095655911
	CoordType      string                    `json:"coordType,omitempty"`      //坐标系类型: bd09ll（百度经纬度坐标）、bd09mc（百度米制坐标）、gcj02ll（国测局经纬度坐标，仅限中国）、wgs84ll（ GPS经纬度）
}
type CeekeeHotelSearchListItem struct {
	HotelId     int64       `json:"HotelId,omitempty"`     //酒店Id
	HotelName   string      `json:"HotelName,omitempty"`   //酒店名称
	HotelNameEn string      `json:"HotelNameEn,omitempty"` //酒店英文名称
	CityName    interface{} `json:"CityName,omitempty"`
	Geo         struct {    //坐标
		Lat  float64 `json:"Lat,omitempty"`  //纬度
		Lon  float64 `json:"Lon,omitempty"`  //经度
		Name string  `json:"Name,omitempty"` //
	} `json:"Geo,omitempty"` //坐标
	RenovationDate       string      `json:"RenovationDate,omitempty"`       //装修时间
	RenovationYear       int         `json:"RenovationYear,omitempty"`       //装修年份
	Address              string      `json:"Address,omitempty"`              //地址
	IsBudget             bool        `json:"IsBudget,omitempty"`             //
	IsProtocol           bool        `json:"IsProtocol,omitempty"`           //
	IsHuazhu             bool        `json:"IsHuazhu,omitempty"`             //
	ExclusiveService     interface{} `json:"ExclusiveService,omitempty"`     //
	HotelLabels          []string    `json:"HotelLabels,omitempty"`          //酒店标签
	ImageSrc             string      `json:"ImageSrc,omitempty"`             //图片url
	Type                 string      `json:"Type,omitempty"`                 //星级类型和连锁类型
	StarRate             int         `json:"StarRate,omitempty"`             //星级
	FacilityList         []int       `json:"FacilityList,omitempty"`         //设施
	District             string      `json:"District,omitempty"`             //行政区
	Business             string      `json:"Business,omitempty"`             //商业区
	ReviewGoodRate       int         `json:"ReviewGoodRate,omitempty"`       //好评率
	ReviewScore          string      `json:"ReviewScore,omitempty"`          //评分
	MinPrice             float64     `json:"MinPrice,omitempty"`             //最小价（以元为单位）
	Distance             float64     `json:"Distance,omitempty"`             //距离 （km）
	DistanceText         string      `json:"DistanceText,omitempty"`         //距离描述
	ZeroPressureRoomRank int         `json:"ZeroPressureRoomRank,omitempty"` //零压房排序
	IsZeroPressureRoom   bool        `json:"IsZeroPressureRoom,omitempty"`   //是否零压房
	IsCurrent            bool        `json:"IsCurrent,omitempty"`            //是否是当前酒店
	CouponServiceDesc    string      `json:"CouponServiceDesc,omitempty"`    //优惠券服务描述
	BrandName            string      `json:"BrandName,omitempty"`            //品牌名称
	BrandCode            int         `json:"BrandCode,omitempty"`            //品牌Code
}
type CeekeeHotelSearchResp struct {
	PageCount      int                         `json:"PageCount,omitempty"`
	Page           int                         `json:"Page,omitempty"`
	PageTotalCount int                         `json:"PageTotalCount,omitempty"`
	HotelList      []CeekeeHotelSearchListItem `json:"HotelList,omitempty"`
}
type CeekeeHotelInfoReq struct {
	HotelId int64 `json:"HotelId"` //酒店id
}
type CeekeeHotelImage struct { //图片详情list
	Name string `json:"Name,omitempty"` //图片名称
	Type string `json:"Type,omitempty"` //图片分类
	Src  string `json:"Src,omitempty"`  //图片URL
}
type CeekeeHotelInfoResp struct {
	HotelId   int64    `json:"HotelId,omitempty"`   //酒店id
	HotelName string   `json:"HotelName,omitempty"` //酒店名
	Geo       struct { //高德坐标
		Lat float64 `json:"Lat,omitempty"`
		Lon float64 `json:"Lon,omitempty"`
	} `json:"Geo,omitempty"`
	OpenYear                string      `json:"OpenYear,omitempty"`                //开业年份
	RenovationYear          string      `json:"RenovationYear,omitempty"`          //装修年份
	Address                 string      `json:"Address,omitempty"`                 //酒店地址
	AdjacentIntersection    string      `json:"AdjacentIntersection,omitempty"`    //临近：如相邻的十字路口描述(近浦东大道)
	LogUrl                  string      `json:"LogUrl,omitempty"`                  //酒店头图
	StarRating              int         `json:"StarRating,omitempty"`              //酒店星级
	StarRateDesc            string      `json:"StarRateDesc,omitempty"`            //酒店星级描述
	District                string      `json:"District,omitempty"`                //行政区 如：浦东新区
	Business                string      `json:"Business,omitempty"`                //商业区
	ReviewGoodRate          float64     `json:"ReviewGoodRate,omitempty"`          //好评率(100)
	ReviewScore             float64     `json:"ReviewScore,omitempty"`             //评分(4.3分)
	CityId                  int         `json:"CityId,omitempty"`                  //城市Id
	CityName                interface{} `json:"CityName,omitempty"`                //城市名
	PrefectureLevelCityId   int         `json:"PrefectureLevelCityId,omitempty"`   //地级市城市Id
	PrefectureLevelCityName string      `json:"PrefectureLevelCityName,omitempty"` //地级市城市名
	TelephoneNum            string      `json:"TelephoneNum,omitempty"`            //酒店电话
	InOutDesc               string      `json:"InOutDesc,omitempty"`               //入离通知
	BrandCode               int         `json:"BrandCode,omitempty"`               //品牌code
	BrandName               string      `json:"BrandName,omitempty"`               //品牌名称
	InvoiceType             int         `json:"InvoiceType,omitempty"`             //发票类型（已废弃）（启用：GetHotelRoomRateList）
	FacilityList            []int       `json:"FacilityList,omitempty"`            //设备（已废弃）
	GuestType               int         `json:"GuestType,omitempty"`               //客人类型；见客人类型枚举 GuestTypeEnum
	HotelImages             struct {    //酒店图片
		ImageTypeList []struct { //图片分类汇总
			Type   string `json:"Type,omitempty"`   //图片分类名称
			Amount int    `json:"Amount,omitempty"` //图片数量
		} `json:"ImageTypeList,omitempty"`
		ImageList []CeekeeHotelImage `json:"ImageList,omitempty"`
	} `json:"HotelImages,omitempty"`
}

type CeekeeHotelRoomRateListReq struct {
	HotelId       int64             `json:"HotelId"`       //酒店id
	ArrivalDate   constmap.DateTime `json:"ArrivalDate"`   //入住日期
	DepartureDate constmap.DateTime `json:"DepartureDate"` //离店日期
	PhoneNumber   string            `json:"PhoneNumber"`   //会员手机号
}
type CeekeeHotelRoomPriceDaily struct {
	Daily      constmap.DateTime `json:"Daily,omitempty"`      //当日日期
	SalesPrice float64           `json:"SalesPrice,omitempty"` //价格
	Quota      int               `json:"Quota,omitempty"`      //剩余房量（每天的房量）
	IsValid    bool              `json:"IsValid,omitempty"`    //是否可订（布尔值）
}
type CeekeeHotelRoomCancelPolicy struct {
	CutType  CeekeeEnumCancelPolicy `json:"CutType,omitempty"`  //扣费类型:0:无费用,1:固定金额；2：比例；3：首晚房费 4：全部房费
	CutValue float64                `json:"CutValue,omitempty"` //扣费值（罚金）单位：元
	DateFrom constmap.DateTime      `json:"DateFrom,omitempty"` //取消开始时 yyyy-MM-dd HH:mm:ss
	DateTo   constmap.DateTime      `json:"DateTo,omitempty"`   //取消结束时 yyyy-MM-dd HH:mm:ss
}
type CeekeeHotelRoomRateListRoomPolicy struct {
	PolicyId               string                        `json:"PolicyId,omitempty"`               //政策id（唯一）
	PolicyName             string                        `json:"PolicyName,omitempty"`             //政策名称
	CancelRule             CeekeeEnumCancelRule          `json:"CancelRule,omitempty"`             //取消规则枚举
	LastCancelTime         string                        `json:"LastCancelTime,omitempty"`         //最晚取消时间（免费、限时取消均可以使用）
	CancelAmount           int                           `json:"CancelAmount,omitempty"`           //
	CancelRuleDec          string                        `json:"CancelRuleDec,omitempty"`          //取消规则枚举描述
	CancelRulePolicyDec    string                        `json:"CancelRulePolicyDec,omitempty"`    //取消规则描述
	IsInstantConfirmation  bool                          `json:"IsInstantConfirmation,omitempty"`  //是否立即确认
	InstantConfirmationDec string                        `json:"InstantConfirmationDec,omitempty"` //即时确认文案
	PayType                int                           `json:"PayType,omitempty"`                //
	SalesPrice             float64                       `json:"SalesPrice,omitempty"`             //售价 （和平均售价一致）
	RemainingRoomCount     int                           `json:"RemainingRoomCount,omitempty"`     //剩余房间数
	PolicyTags             []string                      `json:"PolicyTags,omitempty"`             //预订限制标签
	MinAmount              int                           `json:"MinAmount,omitempty"`              //最小间数限制
	MaxAmount              int                           `json:"MaxAmount,omitempty"`              //最大间数限制
	Person                 int                           `json:"Person,omitempty"`                 //房间最大入住人数
	HasWindowEnum          int                           `json:"HasWindowEnum,omitempty"`          //是否有窗枚举：0-无窗 1-部分有窗 2-有窗 （作废）
	Breakfastr             CeekeeEnumBreakfast           `json:"Breakfastr,omitempty"`             //早餐枚举
	BedType                string                        `json:"BedType,omitempty"`                //床型 (作废)
	TicketType             string                        `json:"TicketType,omitempty"`             //发票类型 （文字：专票；普票）
	PriceDailys            []CeekeeHotelRoomPriceDaily   `json:"PriceDailys,omitempty"`            //价格日历
	CancelPolicyInfos      []CeekeeHotelRoomCancelPolicy `json:"CancelPolicyInfos,omitempty"`      //取消规则政策 :CancelRule 值为 2-限时取消和3-付费取消 的时候 有值，1-免费取消和0-不可取消时候为空
	IsNeedIdentityCard     bool                          `json:"IsNeedIdentityCard,omitempty"`     //是否需要证件信息
	IsNeedEnglishName      bool                          `json:"IsNeedEnglishName,omitempty"`      //是否需要英文姓名
	SuggestSalePrice       float64                       `json:"SuggestSalePrice,omitempty"`       //建议卖价
	IsHostedPrice          bool                          `json:"IsHostedPrice,omitempty"`          //是否是托管协议价
	Gifts                  []struct {                    //礼包信息
		GiftName          string `json:"GiftName,omitempty"`          //礼包名称
		Desc              string `json:"Desc,omitempty"`              //礼包描述
		InventoryDesc     string `json:"InventoryDesc,omitempty"`     //库存描述
		AppointDesc       string `json:"AppointDesc,omitempty"`       //预约描述
		ReceptionTimes    string `json:"ReceptionTimes,omitempty"`    //时间限制
		Price             int    `json:"Price,omitempty"`             //市场价(单位:分)
		AvailableDateType any    `json:"AvailableDateType,omitempty"` //礼包生效规则 不输出则无具体规则
		AdultMax          string `json:"AdultMax,omitempty"`          //最大成年人数限制 不输出或者"0"时以酒店实际为准
		AdultMin          string `json:"AdultMin,omitempty"`          //最小成年人数限制 不输出或者"0"时以酒店实际为准
		ChildMax          string `json:"ChildMax,omitempty"`          //最大未成年人数限制 不输出或者"0"时以酒店实际为准
		ChildMin          string `json:"ChildMin,omitempty"`          //最小未成年人数限制 不输出或者"0"时以酒店实际为准
	} `json:"gifts,omitempty"`
}
type CeekeeHotelRoomRateListRoom struct {
	Id                        string                              `json:"Id,omitempty"`                        //房型id
	Name                      string                              `json:"Name,omitempty"`                      //房型名称
	ImageSrc                  string                              `json:"ImageSrc,omitempty"`                  //房型图片（只有1张）
	BedAndAreaDesc            string                              `json:"BedAndAreaDesc,omitempty"`            //床型和房间描述
	HasWindowEnum             int                                 `json:"HasWindowEnum,omitempty"`             //是否有窗枚举：0-无窗 1-部分有窗 2-有窗
	WindowAndFloorPeoplesDesc string                              `json:"WindowAndFloorPeoplesDesc,omitempty"` //房间参数描述
	Floor                     string                              `json:"Floor,omitempty"`                     //楼层
	RoomArea                  string                              `json:"RoomArea,omitempty"`                  //房间面积（部分酒店可能没有数据）
	BedWidth                  string                              `json:"BedWidth,omitempty"`                  //床宽
	BedType                   string                              `json:"BedType,omitempty"`                   //床型
	Broadnet                  string                              `json:"Broadnet,omitempty"`                  //上网情况（是描述，并非枚举）
	PolicyInfos               []CeekeeHotelRoomRateListRoomPolicy `json:"PolicyInfos,omitempty"`               //政策列表
}
type CeekeeHotelRoomRateListResp struct {
	HotelId      int64                         `json:"HotelId,omitempty"`      //
	StartDate    constmap.DateTime             `json:"StartDate,omitempty"`    //开始日期
	EndDate      constmap.DateTime             `json:"EndDate,omitempty"`      //结束日期
	InvoiceType  string                        `json:"InvoiceType,omitempty"`  //发票类型
	MinimumPrice int                           `json:"MinimumPrice,omitempty"` //
	Rooms        []CeekeeHotelRoomRateListRoom `json:"Rooms,omitempty"`        //房型列表
}

type CeekeeHotelIncrementReq struct {
	StartTime    constmap.DateTime `json:"StartTime"`    //查询起始时间(yyyy-MM-dd hh:mm:ss)，只能是当前时间的前一天
	Duration     int               `json:"Duration"`     //间隔时间，单位：秒，区间：1~1800
	LastRecordId int64             `json:"LastRecordId"` //起始查询id，不传默认第一条开始（上次查询的最大id）
	PageSize     int               `json:"PageSize"`     //每页条数，最大1000
}
type CeekeeHotelIncrementListItem struct { //酒店增量集合
	HotelId      int64  `json:"HotelId,omitempty"`      //酒店Id
	BasicHotelId string `json:"BasicHotelId,omitempty"` //打底酒店Id
}
type CeekeeHotelIncrementResp struct {
	HotelIncrementList []CeekeeHotelIncrementListItem `json:"HotelIncrementList,omitempty"`
	LastRecordId       int64                          `json:"LastRecordId,omitempty"` //本次返回的最大记录ID
}
type CeekeeHotelDataValidateReq struct {
	ArrivalDate     constmap.DateTime       `json:"ArrivalDate"`     //[必填]入住时间
	DepartureDate   constmap.DateTime       `json:"DepartureDate"`   //[必填]离店时间
	LastArrivalTime constmap.DateTimeMinute `json:"LastArrivalTime"` //[必填]最晚到店时间（默认入住当天18:00）
	HotelId         int64                   `json:"HotelId"`         //[必填]酒店ID
	PolicyId        string                  `json:"PolicyId"`        //[必填]政策ID
	RoomNums        int                     `json:"RoomNums"`        //[必填]房间数量
}
type CeekeeHotelPriceDaily struct {
	Daily      constmap.DateTime `json:"Daily,omitempty"`      //当日日期
	SalesPrice float64           `json:"SalesPrice,omitempty"` //价格
	Quota      int               `json:"Quota,omitempty"`      //剩余房量
	IsValid    bool              `json:"IsValid,omitempty"`    //是否可订
}
type CeekeeHotelCancelPolicyInfo struct {
	CutType  int               `json:"CutType,omitempty"`  //扣费类型:0:无费用,1:固定金额；2：比例；3：首晚房费 4：全部房费
	CutValue float64           `json:"CutValue,omitempty"` //扣费值（罚金）单位：元
	DateFrom constmap.DateTime `json:"DateFrom,omitempty"` //取消开始时 yyyy-MM-dd HH:mm:ss
	DateTo   constmap.DateTime `json:"DateTo,omitempty"`   //取消结束时 yyyy-MM-dd HH:mm:ss
}
type CeekeeHotelDataValidateResp struct {
	IsCanBooking            bool                          `json:"IsCanBooking,omitempty"`            //是否能预订
	PayNode                 int                           `json:"PayNode,omitempty"`                 //PayNodeEnum 支付类型：1-预付
	CancelRule              int                           `json:"CancelRule,omitempty"`              //CancelRuleEnum 取消规则枚举
	CancelRuleDesc          string                        `json:"CancelRuleDesc,omitempty"`          //取消规则描述
	RemainingRoomCount      int                           `json:"RemainingRoomCount,omitempty"`      //剩余房间数量
	SalesPrice              int                           `json:"SalesPrice,omitempty"`              //平均卖价
	PriceDailys             []CeekeeHotelPriceDaily       `json:"PriceDailys,omitempty"`             //价格日历
	CancelPolicyInfos       []CeekeeHotelCancelPolicyInfo `json:"CancelPolicyInfos,omitempty"`       //取消规则
	AvailableMaxRoomsCount  int                           `json:"AvailableMaxRoomsCount,omitempty"`  //最大可订间数
	IsNeedIdentityCard      bool                          `json:"IsNeedIdentityCard,omitempty"`      //是否需要证件信息
	IsNeedEnglishName       bool                          `json:"IsNeedEnglishName,omitempty"`       //是否需要英文姓名
	ArrivalEarliestDateTime constmap.DateTime             `json:"ArrivalEarliestDateTime,omitempty"` //最早到店时间(yyyy-MM-dd HH:mm:ss)
	ArrivalLatestDateTime   constmap.DateTime             `json:"ArrivalLatestDateTime,omitempty"`   //最晚到店时间(yyyy-MM-dd HH:mm:ss)
}
type CeekeeCustomerInfo struct {
	CustomerName string           `json:"CustomerName"` //[必填]入住人姓名,港澳台酒店必须传英文名 （中文：全名，英文：姓/名）
	CustomerType string           `json:"CustomerType"` //[必填]0：儿童；1：（默认）成人
	IdentityType CeekeeEnumIdType `json:"IdentityType"` //证件类型 (没有证件默认0)(不可传null)
	IdentityNo   string           `json:"IdentityNo"`   //证件号
	RoomIndex    int              `json:"RoomIndex"`    //房间序号
	PhoneNum     string           `json:"PhoneNum"`     //入住人手机号
}
type CeekeeBookingInfo struct {
	Name     string `json:"Name"`     //[必填]预定人姓名
	PhoneNum string `json:"PhoneNum"` //[必填]预定人手机号
	Email    string `json:"Email"`    //[必填]预定人手邮箱
}
type CeekeeContactInfo struct {
	Name     string `json:"Name"`     //[必填]联系人姓名（中文：全名，英文：姓/名）（必须是真实联系人）
	PhoneNum string `json:"PhoneNum"` //[必填]联系人手机号 （必须传手机号，非手机号会导致下单失败）
}
type CeekeeOrderSubmitReq struct {
	PolicyId        string               `json:"PolicyId"`        //[必填]酒店价格政策
	TotalPrice      int                  `json:"TotalPrice"`      //[必填]订单总价（元）：遇到可订检查检测出变价时 应取可订检查返回的价格计算出总价
	CheckInDate     constmap.Date        `json:"CheckInDate"`     //[必填]入住日期
	CheckOutDate    constmap.Date        `json:"CheckOutDate"`    //[必填]离店日期
	RoomCount       int                  `json:"RoomCount"`       //[必填]房间数
	HotelId         int64                `json:"HotelId"`         //[必填]酒店ID
	Customers       []CeekeeCustomerInfo `json:"Customers"`       //[必填]入住人列表
	Contact         CeekeeContactInfo    `json:"Contact"`         //[必填]联系人（必须是真实联系人）
	LateArrivalTime constmap.DateTime    `json:"LateArrivalTime"` //[必填]预计到店时间(需要在可订检查返回的ArrivalEarliestDateTime到ArrivalLatestDateTime之间，不然有可能导致供应商下单失败)
	OrderNo         string               `json:"OrderNo"`         //[必填]分销商订单号
	BookingInfo     CeekeeBookingInfo    `json:"BookingInfo"`     //预订人信息
}

type CeekeeOrderSubmitResp struct {
	OrderSerialNo int64 `json:"OrderSerialNo,omitempty"` //差旅管家订单号
}

type CeekeeOrderDetailReq struct {
	OrderSerialNo int64  `json:"OrderSerialNo"` //差旅管家订单号(OrderSerialNo、OrderNo不能同时为空)
	OrderNo       string `json:"OrderNo"`       //分销商订单号(OrderSerialNo、OrderNo不能同时为空)
}
type CeekeeOrderDetailResp struct {
	OrderSerialNo    int64                `json:"OrderSerialNo,omitempty"`
	HotelOrderStatus CeekeeEnumOrderState `json:"HotelOrderStatus,omitempty"`
	ContactName      string               `json:"ContactName,omitempty"`  //联系人
	ContactPhone     string               `json:"ContactPhone,omitempty"` //联系人手机号
	BookDate         constmap.DateTime    `json:"BookDate,omitempty"`     //预定时间
	TotalPrice       string               `json:"TotalPrice,omitempty"`   //订单总金额
	BasicInfo        struct {
		HotelId      int64             `json:"HotelId,omitempty"`      //酒店ID
		HotelName    string            `json:"HotelName,omitempty"`    //酒店名称
		CityName     string            `json:"CityName,omitempty"`     //酒店所属城市名称
		HotelAddress string            `json:"HotelAddress,omitempty"` //酒店地址
		HotelPhone   string            `json:"HotelPhone,omitempty"`   //酒店联系方式
		CheckInDate  constmap.DateTime `json:"CheckInDate,omitempty"`  //入住时间
		CheckOutDate constmap.DateTime `json:"CheckOutDate,omitempty"` //离店时间
		PriceList    []struct {        //房型价格
			Name       string   `json:"Name,omitempty"`       //房型名称
			Breakfast  string   `json:"Breakfast,omitempty"`  //早餐类型描述
			BedType    string   `json:"BedType,omitempty"`    //床型描述
			CheckInfo  string   `json:"CheckInfo,omitempty"`  //入住信息
			TotalPrice []string `json:"TotalPrice,omitempty"` //总价["&yen;","146"]
			Amount     int      `json:"Amount,omitempty"`     //数量
			Desc       string   `json:"Desc,omitempty"`       //价格描述：无早 免费取消 预付
		} `json:"PriceList,omitempty"`
		Policy            []string             `json:"Policy,omitempty"`            //政策列表
		Prepay            string               `json:"Prepay,omitempty"`            //取消规则描述
		CancelRule        CeekeeEnumCancelRule `json:"CancelRule,omitempty"`        //CancelRuleEnum 取消规则
		TotalRefundAmount float64              `json:"TotalRefundAmount,omitempty"` //总退款金额
	} `json:"BasicInfo,omitempty"`
	Customers []struct {
		CustomerName  string           `json:"CustomerName,omitempty"`  //入住人姓名
		CustomerType  int              `json:"CustomerType,omitempty"`  //0：儿童；1：成人
		IdentityType  CeekeeEnumIdType `json:"IdentityType,omitempty"`  //IdTypeEnum 证件类型
		IdentityNo    string           `json:"IdentityNo,omitempty"`    //证件号
		RoomIndex     int              `json:"RoomIndex,omitempty"`     //房间序号
		Moblie        string           `json:"Moblie,omitempty"`        //入住人手机号
		CheckinDate   constmap.Date    `json:"CheckinDate,omitempty"`   //入住日期
		CheckoutDate  constmap.Date    `json:"CheckoutDate,omitempty"`  //实际离店日期
		RealPayAmount float64          `json:"RealPayAmount,omitempty"` //原支付金额
		RefundAmount  float64          `json:"RefundAmount,omitempty"`  //退款金额
	} `json:"Customers,omitempty"`
}
type CeekeeEarlyCheckoutData struct {
	RoomIndex     int           `json:"RoomIndex"`     //[必填]房间编号
	PassengerName string        `json:"PassengerName"` //[必填]入住人姓名
	CheckInDate   constmap.Date `json:"CheckInDate"`   //[必填]实际入住时间(yyyy-MM-dd)
	CheckOutDate  constmap.Date `json:"CheckOutDate"`  //[必填]实际离店时间(yyyy-MM-dd)
	Reason        string        `json:"Reason"`        //[必填]提前离店原因
}
type CeekeeOrderCancelReq struct {
	OrderSerialNo      int64                     `json:"OrderSerialNo"`      //[必填]差旅管家订单号
	Reason             CeekeeEnumReason          `json:"Reason"`             //[必填]ReasonEnum 取消原因
	IsCannotCancelRule bool                      `json:"IsCannotCancelRule"` //是否是不可取消的政策
	IsEarlyCheckout    bool                      `json:"IsEarlyCheckout"`    //是否是申请提前离店
	CheckoutDataList   []CeekeeEarlyCheckoutData `json:"CheckoutDataList"`   //提前离店房间集合
}
type CeekeeOrderCancelResp struct {
	SerialNo     int64   `json:"SerialNo,omitempty"`
	CancelAmount float64 `json:"CancelAmount,omitempty"`
}
type CeekeeOrderPayReq struct {
	OrderSerialNo int64 `json:"OrderSerialNo"` //差旅管家订单号
}
type CeekeeOrderPayResp struct {
	HotelOrderStatus int    `json:"HotelOrderStatus,omitempty"` //HotelOrderStatusEnum 订单状态
	Result           bool   `json:"Result,omitempty"`           //成功失败
	ErrMsg           string `json:"ErrMsg,omitempty"`           //失败原因
}
