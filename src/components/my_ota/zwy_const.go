package my_ota

type ZwyOrderStateEnum int
type ZwySubOrderStateEnum string

const (
	ZwyOrderState0WaitConfirm ZwyOrderStateEnum = 0 + iota //新订单（下单成功未确认状态）
	ZwyOrderState1Confirmed                                //已确认（下单成功已确认状态，已确认状态才能调用支付接口）
	ZwyOrderState2Payed                                    //已成功（已支付成功状态）
	ZwyOrderState3Canceled                                 //已取消
	ZwyOrderState4Finish                                   //已完成（已消费状态）
)
const (
	ZwySubOrderStateNone      ZwySubOrderStateEnum = ""  //未定义
	ZwySubOrderState1Noticed  ZwySubOrderStateEnum = "1" //已通知（已支付未出票状态）
	ZwySubOrderState2Delivery ZwySubOrderStateEnum = "2" //已快递（产品需要配送时才有的状态）
	ZwySubOrderState3Printed  ZwySubOrderStateEnum = "3" //已出票
)
