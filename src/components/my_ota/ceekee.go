package my_ota

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/go-redis/redis"
	"go.uber.org/zap"
	"net/http"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
	"strings"
	"sync"
	"time"
)

// http://doc.ceekeecloud.com:8000/web/#/7/53
type ceekee struct {
	once      sync.Once
	host      string
	version   string //分销平台API版本号
	accessKey string
	partnerId string //平台分配给分销商的分销业务ID
	memberId  string //部分特殊的客户需要
	redisCli  *redis.Client
	retry     int
}

var ceekeeCli = new(ceekee)

func NewCeeKee() *ceekee {
	ceekeeCli.once.Do(func() {
		ceekeeCli.host = config.Config.Ota.Ceekee.Host
		ceekeeCli.version = config.Config.Ota.Ceekee.Version
		ceekeeCli.accessKey = config.Config.Ota.Ceekee.AccessKey
		ceekeeCli.memberId = config.Config.Ota.Ceekee.MemberId
		ceekeeCli.partnerId = config.Config.Ota.Ceekee.PartnerId
		ceekeeCli.redisCli = my_cache.RedisClient()
		ceekeeCli.retry = 3
	})
	return ceekeeCli
}

func (o *ceekee) copy() *ceekee {
	return &ceekee{
		host:      o.host,
		version:   o.version,
		accessKey: o.accessKey,
		partnerId: o.partnerId,
		memberId:  o.memberId,
		redisCli:  o.redisCli,
		retry:     o.retry,
	}
}

func (o *ceekee) WithRetry(retry int) *ceekee {
	res := o.copy()
	res.retry = retry
	return res
}

func (o *ceekee) request(uri string, q any) (*ceekeeResponse, error) {
	var err error
	common := ceekeeCommonParams{
		Version:   o.version,
		PartnerId: o.partnerId,
		MemberId:  o.memberId,
		TimeStamp: convertor.ToString(time.Now().Unix())[:10],
		RandomNum: utils.RandNumeral(6),
		Data:      q,
	}
	common.Sign = o.genSign(common)
	var headers = map[string][]string{
		"Content-Type": {"application/json"},
		"PartnerId":    {o.partnerId},
	}
	res := &ceekeeResponse{}
	if uri != "/api/token" {
		token, err := o.getToken(false)
		if err == nil {
			headers["Authorization"] = []string{fmt.Sprintf("Bearer %s", token)}
		} else {
			my_logger.Errorf("[ceekee]getToken error", zap.Error(err))
		}
	}
	for i := 0; i < o.retry; i++ {
		start := time.Now()
		err = request(fmt.Sprintf("%s%s", o.host, uri), http.MethodPost, headers, nil, common, res)

		if res.Code == "401" && (res.Msg+res.Message) == "token验证失败" {
			_, _ = o.getToken(true)
		}

		my_logger.Infof("[ceekee]api request",
			zap.String("uri", uri),
			zap.Any("query", q),
			zap.Duration("cost", time.Since(start)),
			zap.String("code", fmt.Sprintf("[%s]%s", convertor.ToString(res.Code), res.Msg+res.Message)))
		my_logger.Debugf("[ceekee]api response", zap.Any("body", res))
		if err == nil && res.Code == "200" {
			break
		}
	}
	if err != nil {
		return nil, err
	}
	if res.Code != "200" {
		return res, ApiError{
			Code: convertor.ToString(res.Code),
			Msg:  res.Msg + res.Message,
		}
	} else if res.Code == "200" && (res.Message != "" || res.Msg != "") {
		return res, ApiError{
			Code: ApiOkCode,
			Msg:  res.Msg + res.Message,
		}
	}
	return res, nil
}

func (o *ceekee) genSign(data ceekeeCommonParams) string {
	var datastr string
	if data.Data != nil {
		b, _ := json.Marshal(data.Data)
		datastr = fmt.Sprintf("&DATA=%s", b)
	}
	str := fmt.Sprintf("%s%s&PARTNERID=%s&RANDOMNUM=%s&TIMESTAMP=%s", o.accessKey, datastr, data.PartnerId, data.RandomNum, data.TimeStamp)
	return strings.ToUpper(utils.Md5(str))
}

// 获取授权信息
func (o *ceekee) getToken(force bool) (string, error) {
	ckey := "ceekee:token"
	if !force {
		token, _ := o.redisCli.Get(ckey).Result()
		if token != "" {
			return token, nil
		}
	}

	res, err := o.request("/api/token", nil)
	if err != nil {
		return "", err
	}
	var rsp = &ceekeeGetTokenResp{}
	err = json.Unmarshal(res.Data, &rsp)
	if err != nil {
		return "", err
	}
	rsp.Expire, err = time.ParseInLocation(constmap.DateFmtLongFull, rsp.ExpDate, time.Local)
	if err != nil {
		return "", err
	}
	if rsp.Token == "" || rsp.Expire.Before(time.Now()) {
		return "", ApiError{
			Code: "-1",
			Msg:  fmt.Sprintf("getToken invalid:%#v", rsp),
		}
	}
	o.redisCli.Set(ckey, rsp.Token, time.Until(rsp.Expire)-5*time.Minute)
	return rsp.Token, err
}

// 城市列表接口
func (o *ceekee) GetCityList() (*CeekeeGetCityListResp, error) {
	var rsp = &CeekeeGetCityListResp{}
	ckey := "ceekee:citylist"
	b, err := o.redisCli.Get(ckey).Result()
	if b != "" {
		err = json.Unmarshal([]byte(b), &rsp)
		if err == nil {
			return rsp, err
		}
	}
	res, err := o.request("/hotel/resource/GetCityList", nil)
	if err != nil {
		return nil, err
	}
	o.redisCli.Set(ckey, string(res.Data), 24*time.Hour)
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 酒店搜索
func (o *ceekee) HotelSearch(q CeekeeHotelSearchReq) (*CeekeeHotelSearchResp, error) {
	res, err := o.request("/hotel/resource/Search", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeHotelSearchResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 获取酒店详情
func (o *ceekee) HotelInfo(q CeekeeHotelInfoReq) (*CeekeeHotelInfoResp, error) {
	res, err := o.request("/hotel/resource/GetHotelInfo", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeHotelInfoResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 获取房型政策列表
func (o *ceekee) HotelRoomRateList(q CeekeeHotelRoomRateListReq) (*CeekeeHotelRoomRateListResp, error) {
	res, err := o.request("/hotel/resource/GetHotelRoomRateList", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeHotelRoomRateListResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 酒店增量接口, 用于获取酒店的新增及静态信息的变化
func (o *ceekee) HotelIncrement(q CeekeeHotelIncrementReq) (*CeekeeHotelIncrementResp, error) {
	res, err := o.request("/hotel/resource/GetHotelIncr", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeHotelIncrementResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 可定检查接口
func (o *ceekee) HotelDataValidate(q CeekeeHotelDataValidateReq) (*CeekeeHotelDataValidateResp, error) {
	res, err := o.request("/hotel/order/HotelDataValidate", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeHotelDataValidateResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 提交订单接口
//
// 1、港澳台 入住人姓名必须是英文 or 拼音：姓/名
// 2、联系人手机号：必须为真实手机号，不能是客服手机号
// 3、联系人姓名：必须是真实的姓名，不得出现：xx客服，张先生，张女士字样
func (o *ceekee) OrderSubmit(q CeekeeOrderSubmitReq) (*CeekeeOrderSubmitResp, error) {
	res, err := o.request("/hotel/order/SubmitOrder", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeOrderSubmitResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 获取订单详情接口
func (o *ceekee) OrderDetail(q CeekeeOrderDetailReq) (*CeekeeOrderDetailResp, error) {
	res, err := o.request("/hotel/order/GetOrderDetails", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeOrderDetailResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 申请取消订单接口
func (o *ceekee) OrderCancel(q CeekeeOrderCancelReq) (*CeekeeOrderCancelResp, error) {
	res, err := o.request("/hotel/order/CancelOrder", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeOrderCancelResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 确认订单
//
// 该接口会涉及到分销商扣款，所以只有在分销端扣款成功后，即支付成功后进行调用。调用成功后，会通过消息推送或者定时调用订单详细接口进行订单状态同步。
func (o *ceekee) OrderPay(q CeekeeOrderPayReq) (*CeekeeOrderPayResp, error) {
	res, err := o.request("/hotel/order/ConfirmOrder", q)
	if err != nil {
		return nil, err
	}
	var rsp = &CeekeeOrderPayResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}
