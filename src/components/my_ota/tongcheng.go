package my_ota

import (
	"github.com/duke-git/lancet/v2/slice"
	"roadtrip-api/src/config"
	"roadtrip-api/src/utils"
	"strings"
)

// 同程
type TongCheng struct{}

func NewTongCheng() *TongCheng {
	return &TongCheng{}
}

func (o TongCheng) request(u string, signParams []string, header map[string][]string, body, out any) error {
	if strings.Index(u, "http") == -1 {
		u = config.Config.Ota.Tongcheng.Host + u
	}
	var str strings.Builder
	str.WriteString(config.Config.Ota.Tongcheng.AgentId)
	slice.ForEach(signParams, func(i int, v string) { str.WriteString(v) })
	str.WriteString(config.Config.Ota.Tongcheng.ApiKey)
	sign := utils.Md5(str.String())
	header["digitalSign"] = append(header["digitalSign"], sign)
	header["agentAccount"] = append(header["agentAccount"], config.Config.Ota.Tongcheng.AgentId)
	return nil
}
