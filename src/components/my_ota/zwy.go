package my_ota

import (
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"go.uber.org/zap"
	"net/http"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"strconv"
	"sync"
	"time"
)

// https://developers.zowoyoo.com/%E8%87%AA%E6%88%91%E6%B8%B8%E5%BC%80%E6%94%BE%E5%B9%B3%E5%8F%B0%E7%9B%B8%E5%85%B3%E6%8E%A5%E5%8F%A3.html#addUsingPOST_1
type zwy struct {
	once    sync.Once
	host    string
	agentId int64
	apiKey  string
}

var zwyCli = new(zwy)

func NewZwy() *zwy {
	zwyCli.once.Do(func() {
		zwyCli.host = config.Config.Ota.Zwy.Host
		zwyCli.agentId = config.Config.Ota.Zwy.AgentId
		zwyCli.apiKey = config.Config.Ota.Zwy.ApiKey
	})
	return zwyCli
}

func (o *zwy) request(uri string, q zwyReqIFace) (*zwyResponse, error) {
	res := &zwyResponse{}
	start := time.Now()
	err := request(fmt.Sprintf("%s%s", o.host, uri), http.MethodPost, map[string][]string{
		"Content-Type": {"application/json"},
	}, nil, q, res)
	q.SetCommonParams(zwyCommonParams{}) //敏感数据打印日志前脱敏
	my_logger.Infof("[zwy]api request",
		zap.String("uri", uri),
		zap.Any("query", q),
		zap.Duration("cost", time.Since(start)),
		zap.String("code", fmt.Sprintf("[%d]%s", res.State, res.Msg)))
	my_logger.Debugf("[zwy]api response", zap.Any("body", res))
	if err != nil {
		return nil, err
	}
	if res.State != 1 {
		return res, ApiError{
			Code: convertor.ToString(res.State),
			Msg:  res.Msg,
		}
	}
	return res, nil
}

func (o *zwy) getCommonParams() *zwyCommonParams {
	return &zwyCommonParams{
		ApiKey: o.apiKey,
		CustId: o.agentId,
	}
}

// 景点列表(门票-抢购-套餐)
func (o *zwy) QueryScenicList(q ZwyScenicListReq) (*ZwyScenicListResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/view/list", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyScenicListResp{}
	err = json.Unmarshal(res.Data, &rsp)
	for i, v := range rsp.Results {
		v.Lon, _ = strconv.ParseFloat(v.ViewLongitude, 64)
		v.Lat, _ = strconv.ParseFloat(v.ViewLatitude, 64)
		rsp.Results[i] = v
	}
	return rsp, err
}

// 产品列表(门票-抢购-商品-套餐)
func (o *zwy) QueryProductList(q ZwyProductListReq) (*ZwyProductListResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/prod/list", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyProductListResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 产品详情(门票-抢购-商品-套餐)
func (o *zwy) QueryProductDetail(q ZwyProductDetailReq) (*ZwyProductDetailResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/prod/detail", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyProductDetailResp{}
	err = json.Unmarshal(res.Data, &rsp)
	if rsp.ViewLongitude != "" {
		rsp.Lon, _ = strconv.ParseFloat(rsp.ViewLongitude, 64)
	}
	if rsp.ViewLatitude != "" {
		rsp.Lat, _ = strconv.ParseFloat(rsp.ViewLatitude, 64)
	}
	return rsp, err
}

// 产品价格库存(门票-抢购-商品-套餐-线路)
func (o *zwy) QueryProductPrice(q ZwyProductPriceReq) (*ZwyProductPriceResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/prod/price", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyProductPriceResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 下单校验
func (o *zwy) OrderAddCheck(q ZwyOrderAddCheckReq) (*ZwyOrderAddCheckResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/check", q)
	var rsp = &ZwyOrderAddCheckResp{}
	if res != nil {
		rsp.zwyResponse = res
	}
	// caller需要检查
	// - err是否是nil，非nil表示有错误发生，再执行下面的判断
	// 		- rsp.Msg是不是空，非空表示校验不通过
	//      - rsp.Msg是空，说明发生了网络错误，此时err.Error()是网络错误描述
	return rsp, err
}

// 下单
func (o *zwy) OrderAdd(q ZwyOrderAddReq) (*ZwyOrderAddResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/add", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderAddResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 订单取消(即退款)
func (o *zwy) OrderCancel(q ZwyOrderCancelReq) (*ZwyOrderCancelResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/cancel", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderCancelResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 订单详情
func (o *zwy) OrderDetail(q ZwyOrderDetailReq) (*ZwyOrderDetailResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/detail", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderDetailResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 支付
func (o *zwy) OrderPay(q ZwyOrderPayReq) (*ZwyOrderPayResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/pay", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderPayResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 订单列表
func (o *zwy) OrderList(q ZwyOrderListReq) (*ZwyOrderListResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/list", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderListResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 订单重发
func (o *zwy) OrderResend(q ZwyOrderResendReq) (*ZwyOrderResendResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/resend", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyOrderResendResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}

// 余额查询
func (o *zwy) QueryBalance(q ZwyQueryBalanceReq) (*ZwyQueryBalanceResp, error) {
	q.zwyCommonParams = o.getCommonParams()
	res, err := o.request("/api/thirdPaty/order/balance", q)
	if err != nil {
		return nil, err
	}
	var rsp = &ZwyQueryBalanceResp{}
	err = json.Unmarshal(res.Data, &rsp)
	return rsp, err
}
