package my_ota

import (
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

const (
	CeekeeEnumBreakfast2 CeekeeEnumBreakfast = 2 + iota
	CeekeeEnumBreakfast3
	CeekeeEnumBreakfast4
	CeekeeEnumBreakfast5
	CeekeeEnumBreakfast6
	CeekeeEnumBreakfast7
	CeekeeEnumBreakfast8
	CeekeeEnumBreakfast9
	CeekeeEnumBreakfast10
	CeekeeEnumBreakfast11
	CeekeeEnumBreakfast12
)

func (o CeekeeEnumBreakfast) ToString() string {
	s, ok := map[CeekeeEnumBreakfast]string{
		CeekeeEnumBreakfast2:  "无早",
		CeekeeEnumBreakfast3:  "单早",
		CeekeeEnumBreakfast4:  "双早",
		CeekeeEnumBreakfast5:  "三早",
		CeekeeEnumBreakfast6:  "四早",
		CeekeeEnumBreakfast7:  "五早",
		CeekeeEnumBreakfast8:  "六早",
		CeekeeEnumBreakfast9:  "七早",
		CeekeeEnumBreakfast10: "八早",
		CeekeeEnumBreakfast11: "九早",
		CeekeeEnumBreakfast12: "十早",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumReasonPoorQuality CeekeeEnumReason = 1 + iota //酒店质量差
	CeekeeEnumReasonTravelCancellation
	CeekeeEnumReasonInfoError
	CeekeeEnumReasonAppError //酒店信息错误
	CeekeeEnumReasonOther
)

func (o CeekeeEnumReason) ToString() string {
	s, ok := map[CeekeeEnumReason]string{
		CeekeeEnumReasonPoorQuality:        "酒店质量差",
		CeekeeEnumReasonTravelCancellation: "行程取消",
		CeekeeEnumReasonInfoError:          "信息填错",
		CeekeeEnumReasonAppError:           "酒店信息错误",
		CeekeeEnumReasonOther:              "其他",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumGenderFemale CeekeeEnumGender = 0 + iota //女性
	CeekeeEnumGenderMale
)

func (o CeekeeEnumGender) ToString() string {
	s, ok := map[CeekeeEnumGender]string{
		CeekeeEnumGenderFemale: "女性",
		CeekeeEnumGenderMale:   "男性",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumIdTypeNone CeekeeEnumIdType = 0 + iota //无
	CeekeeEnumIdType1
	CeekeeEnumIdType2
	CeekeeEnumIdType3
	CeekeeEnumIdType4
	CeekeeEnumIdType5
	CeekeeEnumIdType6
	CeekeeEnumIdType7
	CeekeeEnumIdType8
	CeekeeEnumIdType9
	CeekeeEnumIdType10
	CeekeeEnumIdType11
	CeekeeEnumIdType12
	CeekeeEnumIdType20 CeekeeEnumIdType = 20
	CeekeeEnumIdType30 CeekeeEnumIdType = 30
)

func (o CeekeeEnumIdType) ToString() string {
	s, ok := map[CeekeeEnumIdType]string{
		CeekeeEnumIdTypeNone: "无",
		CeekeeEnumIdType1:    "身份证",
		CeekeeEnumIdType2:    "护照",
		CeekeeEnumIdType3:    "军官证",
		CeekeeEnumIdType4:    "士兵证",
		CeekeeEnumIdType5:    "台胞证",
		CeekeeEnumIdType6:    "其他",
		CeekeeEnumIdType7:    "回乡证",
		CeekeeEnumIdType8:    "国际海员证",
		CeekeeEnumIdType9:    "港澳通行证",
		CeekeeEnumIdType10:   "台湾通行证",
		CeekeeEnumIdType11:   "港澳台居民居住证",
		CeekeeEnumIdType12:   "外国人永久居住身份证",
		CeekeeEnumIdType20:   "港澳居民居住证",
		CeekeeEnumIdType30:   "台湾居民居住证",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumOrderState10PayExpired  CeekeeEnumOrderState = 10 //超时未支付
	CeekeeEnumOrderState11Canceling   CeekeeEnumOrderState = 11 //取消中
	CeekeeEnumOrderState12NoRooms     CeekeeEnumOrderState = 12 //酒店没有房间了
	CeekeeEnumOrderState15Refunded    CeekeeEnumOrderState = 15 //最终状态（已退款）（线上场景）
	CeekeeEnumOrderState22WaitConfirm CeekeeEnumOrderState = 22 //确认中,调用确认接口中后的状态
	CeekeeEnumOrderState23CanUse      CeekeeEnumOrderState = 23 //待入住
	CeekeeEnumOrderState32WaitPay     CeekeeEnumOrderState = 32 //待付款
	CeekeeEnumOrderState40Using       CeekeeEnumOrderState = 40 //已入住
	CeekeeEnumOrderState41Used        CeekeeEnumOrderState = 41 //已离店
	CeekeeEnumOrderState50Refunding   CeekeeEnumOrderState = 50 //申请退款
	CeekeeEnumOrderState51Refunded    CeekeeEnumOrderState = 51 //已退款 线下场景
)

func (o CeekeeEnumOrderState) ToString() string {
	s, ok := map[CeekeeEnumOrderState]string{
		CeekeeEnumOrderState10PayExpired:  "超时未支付",
		CeekeeEnumOrderState11Canceling:   "申请取消",
		CeekeeEnumOrderState12NoRooms:     "确认无房",
		CeekeeEnumOrderState15Refunded:    "客户主动取消",
		CeekeeEnumOrderState22WaitConfirm: "确认中",
		CeekeeEnumOrderState23CanUse:      "待入住",
		CeekeeEnumOrderState32WaitPay:     "待付款",
		CeekeeEnumOrderState40Using:       "已入住",
		CeekeeEnumOrderState41Used:        "已离店",
		CeekeeEnumOrderState50Refunding:   "申请退款",
		CeekeeEnumOrderState51Refunded:    "已退款",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumCancelRule0 CeekeeEnumCancelRule = 0 + iota
	CeekeeEnumCancelRule1
	CeekeeEnumCancelRule2
	CeekeeEnumCancelRule3
)

func (o CeekeeEnumCancelRule) ToString() string {
	s, ok := map[CeekeeEnumCancelRule]string{
		CeekeeEnumCancelRule0: "不可取消",
		CeekeeEnumCancelRule1: "免费取消",
		CeekeeEnumCancelRule2: "限时取消",
		CeekeeEnumCancelRule3: "付费取消",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}

const (
	CeekeeEnumCancelPolicyFree = 0 + iota
	CeekeeEnumCancelPolicyFixedMoney
	CeekeeEnumCancelPolicyPercent
	CeekeeEnumCancelPolicyFirstNight
	CeekeeEnumCancelPolicyAll
)

func (o CeekeeEnumCancelPolicy) ToString() string {
	s, ok := map[CeekeeEnumCancelPolicy]string{
		CeekeeEnumCancelPolicyFree:       "无费用",
		CeekeeEnumCancelPolicyFixedMoney: "固定费用",
		CeekeeEnumCancelPolicyPercent:    "固定比例",
		CeekeeEnumCancelPolicyFirstNight: "首晚房费",
		CeekeeEnumCancelPolicyAll:        "全部房费",
	}[o]
	return utils.If(ok, s, constmap.UnknownStr)
}
