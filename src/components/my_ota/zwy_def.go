package my_ota

import (
	"encoding/json"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

func IdCardZwy2Yj(typ int) constmap.IdType {
	m := map[int]constmap.IdType{
		0:  constmap.IdTypeIdCard,
		1:  constmap.IdTypeStudent,
		2:  constmap.IdTypeSoldier,
		3:  constmap.IdTypePassport,
		4:  constmap.IdTypeResidenceBooklet,
		5:  constmap.IdTypeGAPassport,
		6:  constmap.IdTypeTaiwanResident,
		7:  constmap.IdTypeTaiwanPassport,
		13: constmap.IdTypeGreenCard,
	}
	i, ok := m[typ]
	return utils.If(ok, i, -1)
}

func IdCardYj2Zwy(typ constmap.IdType) int {
	m := map[constmap.IdType]int{
		constmap.IdTypeIdCard:           0,
		constmap.IdTypeStudent:          1,
		constmap.IdTypeSoldier:          2,
		constmap.IdTypePassport:         3,
		constmap.IdTypeResidenceBooklet: 4,
		constmap.IdTypeGAPassport:       5,
		constmap.IdTypeTaiwanResident:   6,
		constmap.IdTypeTaiwanPassport:   7,
		constmap.IdTypeGreenCard:        13,
	}
	i, ok := m[typ]
	return utils.If(ok, i, -1)
}

type zwyReqIFace interface {
	SetCommonParams(zwyCommonParams)
}

type zwyCommonParams struct {
	zwyReqIFace
	ApiKey string `json:"apikey"`
	CustId int64  `json:"custId"` //分销商帐号
}

func (o *zwyCommonParams) SetCommonParams(q zwyCommonParams) {
	*o = q
}

type zwyResponse struct {
	Data  json.RawMessage `json:"data"`
	Msg   string          `json:"msg"`
	State int             `json:"state"` //0为正常值
}

type ZwyScenicListReq struct {
	*zwyCommonParams
	CityName  string `json:"cityName"`  //城市名称
	Page      int    `json:"page"`      //当前页码：从0开始
	ResultNum int    `json:"resultNum"` //每页个数
	ViewId    string `json:"viewId"`    //景点id
}

type ZwyScenicListResp struct {
	Page       int                     `json:"page"`
	PageCount  int                     `json:"pageCount"`
	ResultNum  int                     `json:"resultNum"`
	Results    []ZwyScenicListRespItem `json:"results"`
	Size       int                     `json:"size"`
	SizeAll    int                     `json:"sizeAll"`
	StartIndex int                     `json:"startIndex"`
}

type ZwyScenicListRespItem struct {
	CityId        string  `json:"cityId"`        //地区id
	CityName      string  `json:"cityName"`      //景点所在地
	Description   string  `json:"description"`   //景区描述
	Phone         string  `json:"phone"`         //景区联系电话
	ViewAddress   string  `json:"viewAddress"`   //景点地址
	ViewId        string  `json:"viewId"`        //景点编号
	ViewImg       string  `json:"viewImg"`       //景区图片
	ViewImgs      string  `json:"viewImgs"`      //景区图片多张，英文逗号分开
	ViewLatitude  string  `json:"viewLatitude"`  //维度
	ViewLongitude string  `json:"viewLongitude"` //经度
	Lon           float64 `json:"lon"`
	Lat           float64 `json:"lat"`
	ViewName      string  `json:"viewName"` //景点名称
	ViewType      int     `json:"viewType"` //景区类型，0、景点 1、酒店 2、场馆 3、餐饮 4、玩乐
}

type ZwyProductListReq struct {
	*zwyCommonParams

	CatIds     string `json:"catIds"`     //主题标签集合，英文逗号分开
	CityId     string `json:"cityId"`     //城市id
	CityName   string `json:"cityName"`   //城市名称
	IsConfirm  string `json:"isConfirm"`  //确认方式 0自动确认订单 1人工确认订单
	IsExpress  string `json:"isExpress"`  //配送方式 0不需要配送 1需要配送
	IsMulti    string `json:"isMulti"`    //多选多打包套餐 1表示是多选多打包套餐
	IsPackage  string `json:"isPackage"`  //打包产品 0 单独产品， 1 打包产品；打包产品treeId必定为1
	IsPay      string `json:"isPay"`      //支付方式 0线下支付 1在线支付
	KeyWord    string `json:"keyWord"`    //关键字，支持景点和产品名称模糊查询
	OrderBy    string `json:"orderBy"`    //排序方式 0 按价格 1 按折扣 2 按销量 3 按推荐值 4 上架时间
	Page       int    `json:"page"`       //当前页码：从0开始
	ProductNos string `json:"productNos"` //产品id集合，英文逗号分开
	ResultNum  int    `json:"resultNum"`  //每页个数
	TagIds     string `json:"tagIds"`     //景点标签集合，英文逗号分开
	TreeId     string `json:"treeId"`     //产品类型 0单一门票 1套餐或联票 5酒店 11实物商品 12预售
	ViewId     string `json:"viewId"`     //景点id
}

type ZwyProductListResp struct {
	Page       int                      `json:"page"`
	PageCount  int                      `json:"pageCount"`
	ResultNum  int                      `json:"resultNum"`
	Results    []ZwyProductListRespItem `json:"results"`
	Size       int                      `json:"size"`
	SizeAll    int                      `json:"sizeAll"`
	StartIndex int                      `json:"startIndex"`
}

type ZwyProductListRespItem struct {
	Appointment   string `json:"appointment"`   //有效期说明，预售产品必有
	AttentCount   int    `json:"attentCount"`   //关注数量
	BusinessHours string `json:"businessHours"` //营业时间说明
	CancelDay     int    `json:"cancelDay"`     //距离游玩日期多少天可以直退，值为0表示当日之前可退；值为正数，表示游玩日期前多少天可以退；值为负数，表示游玩日期后多少天以内可以退,跟退款规则字段有关
	CancelRules   []struct {
		AgreeForcetg     int    `json:"agreeForcetg"`     //是否支持订单完成后做强制退改，0 默认不支持,1 为支持处理
		CancelMoneyCodex string `json:"cancelMoneyCodex"` //分销商可退款，#price#*单价百分比 #price#-单价 #order_money#-总价 #order_money#*总价百分比 如果退款规则不带上运算符，代表不需要手续费，如#price#
		Days             int    `json:"days"`             //有效退款日期，0 表示当天，正数表示前 x 天，负数表示后 x 天
		Hours            int    `json:"hours"`            //有效退款时间， 申请退款需要在这个时间之前
		ReturnMoneyCodex string `json:"returnMoneyCodex"` //供应商退回
	} `json:"cancelRules"` //退款规则
	ChargeInclude   string `json:"chargeInclude"`   //费用包含
	ChargeNoInclude string `json:"chargeNoInclude"` //费用不包含
	ChildrenNum     int    `json:"childrenNum"`     //产品包含儿童数
	CityId          string `json:"cityId"`          //地区id
	CityName        string `json:"cityName"`        //景点所在地
	Conds           []struct {
		CondId          int64   `json:"condId"`          //增值产品编号
		CondTitle       string  `json:"condTitle"`       //增值产品名称
		IsApiece        int     `json:"isApiece"`        //报价方式，1 按照人数报价,2 收费跟人数无关
		IsGotta         int     `json:"isGotta"`         //是否必须购买，0 可选，1 必选
		IsSync          int     `json:"isSync"`          //人数限制，0 可以单独选择数量,1 必须跟订单人数 一致
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价
		StartNum        int     `json:"startNum"`        //最低预订产品数量，0 表示不限制
		TreeId          int     `json:"treeId"`          //增值产品类型，0 住宿 1 餐饮美食 2 租车(接送) 3 娱乐休闲 4 购物 5 游玩项目 6 健康养生 7 导游服务 8 配送服务 9 旅游保险 10 签证服务 11 上车（接待）地点 12 交通
		Unit            string  `json:"unit"`            //增值产品单位，如：张、件、人、晚
	} `json:"conds"` //增值项目列表
	Content          string   `json:"content"`          //产品介绍，富文本
	CreditTypeSet    string   `json:"creditTypeSet"`    //如果支持多个证件类型会以英文逗号分开，证件类型，0 身份证 1 学生证 2 军官证 3 护照 4 户口本(儿童请选择此项) 5 港澳通行证 6 台胞证 7 台湾通行证 8 入台证 9 香港居民往来内地通行证 10 警官证 11 驾驶证 12 海员证 13 外国人在中国永久居留证 14澳门居民往来内地通行证 15港澳居民来往内地通行证 16港澳台居民来往内地通行证 17港澳台居民居住证 18中华人民共和国旅行证 19回乡证 20台胞证 21香港身份证
	CustField        string   `json:"custField"`        //客人预订需要填写的信息，如link_man,link_phone,link_single_phone,link_credit_no,link_ email,link_address link_man:姓名 link_phone:手机号 link_single_phone:每个游玩人手机号都要 link_credit_no:证件号 link_email:邮箱地址 link_address:邮寄地址 (要求快递门票时此项为必填项)
	CustFieldEx      string   `json:"custFieldEx"`      //客人预订需要填写的扩张自定义信息，例子： {"data":[{"signle":"0","datatype":"dchar", "notnull":"0","name":"性别","desc":"填 写男或者女"}],"count":0} signle：0 只填联系人 1 所有人都填 datatype：dchar 字符类型 dnum 数字类型 ddate 日期类型 dsel 选择类型 notnull：0 可以为空 1 不能为空 name : 自定义信息名称 desc：自定义信息提示说明
	DistributionDesc string   `json:"distributionDesc"` //免费配说明
	ExpressPrice     float64  `json:"expressPrice"`     //快递费用
	Goodsunit        string   `json:"goodsunit"`        //单位：件，套...
	GroupInfoIds     []string `json:"groupInfoIds"`     //预约产品集合，需要在线预约的产品才有
	Img              string   `json:"img"`              //标志图
	Imgs             string   `json:"imgs"`             //相关主图，多个图片以逗号分隔开
	ImportantNote    string   `json:"importantNote"`    //重要提示
	IsChangeask      int      `json:"isChangeask"`      //	退改申请类型 0不支持发起退改申请 1支持
	IsConds          int      `json:"isConds"`          //产品是否有增值项目 1 有，0 无
	IsConfirm        int      `json:"isConfirm"`        //是否需要人工确认，0 自动确认 ，1 人工确认
	IsExpress        int      `json:"isExpress"`        //是否支持快递配送 1 支持，0 不支持
	IsFree           int      `json:"isFree"`           //是否支持免费配送方式，1 支持，0不支持
	IsMusku          int      `json:"isMusku"`          //是否为门票多规格，0普通门票 1多规格
	IsOnlinepay      int      `json:"isOnlinepay"`      //1 在线支付（预付），0 酒店前台现付 （返佣）；在线支付的订单必须调用支付接口成功后才能出票
	IsPackage        int      `json:"isPackage"`        //是否是套餐，treeId=1 并且 isPackage=1 是打包套餐，treeId=1 是一般的套餐
	IsPrint          int      `json:"isPrint"`          //是否需要出票 0 不需要 1 需要
	IsSingle         int      `json:"isSingle"`         //客人资料要求，1 要求输入每个客人资料，0 只需要输入一个客人资料
	IsTimeslot       int      `json:"isTimeslot"`       //时段产品类型 0不是 1是
	IsTop            int      `json:"isTop"`            //推荐值
	LimitType        int      `json:"limitType"`        //预定限制类型，下单数量校验类型，0订单，3IP地址，4分销商ID，5会员ID(C端)，8同游玩日同分销商，9同游玩日同会员
	MarketPrice      float64  `json:"marketPrice"`      //市场价
	MaxNum           int      `json:"maxNum"`           //	最大预定数 0不限制
	Muskus           []struct {
		InfoId     int64  `json:"infoId"`     //产品id
		PriceId    int64  `json:"priceId"`    //规格id
		PriceName  string `json:"priceName"`  //	规格名称
		SelectType int    `json:"selectType"` //	选择类型 1可多选
	} `json:"muskus"` //门票多规格信息，isMusku=1时必有值
	OldmanNum       int    `json:"oldmanNum"`   //产品包含老人数
	OrderDesc       string `json:"orderDesc"`   //预订详细说明
	OrderPolicy     string `json:"orderPolicy"` //预订说明
	PackageProducts []struct {
		InfoIdNode      int64  `json:"infoIdNode"`      //子产品ID
		InfoNameNode    string `json:"infoNameNode"`    //子产品名称
		IsDateNode      int    `json:"isDateNode"`      //游玩日期类型，0 游客选择的打包产品的当天有效，1 游客选择的打包产品的延后1天有效,2 延后2天有效，3 延后3天有效，4 延后4天有效，5 延后5天有效，-1不限时间，游客随意选择游玩日期
		NumNode         int    `json:"numNode"`         //包含的数量
		TreeIdNode      int    `json:"treeIdNode"`      //产品类型
		ViewAddressNode string `json:"viewAddressNode"` //子产品景点地址
		ViewIdNode      string `json:"viewIdNode"`      //子产品景点ID
		ViewNameNode    string `json:"viewNameNode"`    //子产品景点名称
	} `json:"packageProducts"` //打包套餐产品
	PayMinute      int    `json:"payMinute"`      //多少分钟后不支付自动取消
	PeopleNum      int    `json:"peopleNum"`      //产品包含成人数
	Peoples        string `json:"peoples"`        //产品人群要求
	PosterImg      string `json:"posterImg"`      //海报图
	PriceEndDate   string `json:"priceEndDate"`   //价格结束时间，因为产品可能设置特定日价格，所以具体需要查询价格接口
	PriceStartDate string `json:"priceStartDate"` //价格开始时间
	ProdDesc       string `json:"prodDesc"`       //商品说明，treeId=11的商品才可能有值
	ProductName    string `json:"productName"`    //产品名称
	ProductNo      int64  `json:"productNo"`      //产品ID
	PubVers        string `json:"pubVers"`        //销售范围
	PurchaseLimit  struct {
		LimitCardDay    int `json:"limitCardDay"`    //同一证件限购天数
		LimitCardGoods  int `json:"limitCardGoods"`  //同一证件限购类型 0按张数限购 1按次数限购
		LimitCardNum    int `json:"limitCardNum"`    //同一证件限购数量，0不限制
		LimitPhoneDay   int `json:"limitPhoneDay"`   //同一手机号限购天数
		LimitPhoneGoods int `json:"limitPhoneGoods"` //同一手机号限购类型 0按张数限购 1按次数限购
		LimitPhoneNum   int `json:"limitPhoneNum"`   //同一手机号限购数量，0不限制
		LimitTimeType   int `json:"limitTimeType"`   //限购日期类型 0预定日期 1游玩日期
	} `json:"purchaseLimit"` //限购规则
	RefundNote      string  `json:"refundNote"`      //退款说明
	SaleEndDate     string  `json:"saleEndDate"`     //预售结束时间，预售产品必有
	SalePrice       float64 `json:"salePrice"`       //零售价
	SaleStartDate   string  `json:"saleStartDate"`   //预售开始时间，预售产品必有
	SettlementPrice float64 `json:"settlementPrice"` //结算价
	Specs           []struct {
		SpecName   string   `json:"specName"`   //规格名称
		SpecValues []string `json:"specValues"` //规格值
	} `json:"specs"` //抢购多规格信息
	StartDate      string   `json:"startDate"`      //最早游玩日期
	StartDay       int      `json:"startDay"`       //提前多少天预定
	StartMaxDay    int      `json:"startMaxDay"`    //最大可预订天数，0不限制，如果是具体的天数，预定的日期不能超过该值
	StartMinute    int      `json:"startMinute"`    //预订后多少分钟生效
	StartNum       int      `json:"startNum"`       //最低预订产品数量，0 表示不限制
	StartTime      string   `json:"startTime"`      //几点之前预定
	State          int      `json:"state"`          //产品状态，0 在线，1 下线
	Themes         []string `json:"themes"`         //主题
	TicketCount    int      `json:"ticketCount"`    //已销售数量
	TicketTypeName string   `json:"ticketTypeName"` //票种，门票才有，0特价票，1普通票，2儿童票，3学生票，4套票，5团队票，6长者票，7中学生票，8情侣票，9女士票，10男士票，11双人票，12三人票，14大学生票，15教师人票，16残疾票，17军人票，18女大学生票，19家庭票(2大1小)，20家庭票(2大2小)，21亲子票(1大1小)，22优待票，23一日票，24二日票
	TicketcodeType int      `json:"ticketcodeType"` //发码类型 0一单一码 1一人一码
	TreeId         int      `json:"treeId"`         //产品类型，0单一门票 1套餐或联票 3线路 5酒店 11实物商品 12预售（抢购）
	UserNote       string   `json:"userNote"`       //使用说明
	ValidityCon    string   `json:"validityCon"`    //有效期限制具体天数或日期，格式为 yyyy-MM-dd 或者数字,跟 validityType 有关联
	ValidityType   int      `json:"validityType"`   //有效期限制，0：游客选定的游玩日期当天有效 1：游客预订日期延后几天有效 2：游客预订日期截止到指定日期有效 3：游客选定日期延后几天有效 4：游客选定日期截止到指定日期有效 5：指定日期xxx到xxx内有效
	VerifyIntype   int      `json:"verifyIntype"`   //0换票入园 1直接入园 2由导游带领入园 ；门票产品才有
	VerifyType     int      `json:"verifyType"`     //0短信凭证 1二维码凭证 3身份证 4实体票 5打印件 6学生证 ；门票产品才有
	ViewAddress    string   `json:"viewAddress"`    //景点地址
	ViewId         string   `json:"viewId"`         //景点编号
	ViewLatitude   string   `json:"viewLatitude"`   //纬度，百度地图
	ViewLongitude  string   `json:"viewLongitude"`  //经度，百度地图
	ViewName       string   `json:"viewName"`       //景点名称
	Views          []string `json:"views"`          //景点列表
	YyLink         string   `json:"yyLink"`         //预约链接，需要在线预约的产品才有
}

type ZwyProductDetailReq struct {
	*zwyCommonParams
	ProductNo int64 `json:"productNo"` //产品id
}

type ZwyProductDetailResp struct {
	Appointment   string `json:"appointment"`   //有效期说明，预售产品必有
	AttentCount   int    `json:"attentCount"`   //关注数量
	BusinessHours string `json:"businessHours"` //营业时间说明
	CancelDay     int    `json:"cancelDay"`     //距离游玩日期多少天可以直退，值为0表示当日之前可退；值为正数，表示游玩日期前多少天可以退；值为负数，表示游玩日期后多少天以内可以退,跟退款规则字段有关
	CancelRules   []struct {
		AgreeForcetg     int    `json:"agreeForcetg"`     //是否支持订单完成后做强制退改，0 默认不支持,1 为支持处理
		CancelMoneyCodex string `json:"cancelMoneyCodex"` //分销商可退款，#price#*单价百分比 #price#-单价 #order_money#-总价 #order_money#*总价百分比 如果退款规则不带上运算符，代表不需要手续费，如#price#
		Days             int    `json:"days"`             //有效退款日期，0 表示当天，正数表示前 x 天，负数表示后 x 天
		Hours            int    `json:"hours"`            //有效退款时间， 申请退款需要在这个时间之前
		ReturnMoneyCodex string `json:"returnMoneyCodex"` //供应商退回
	} `json:"cancelRules"` //退款规则
	ChargeInclude   string `json:"chargeInclude"`   //费用包含
	ChargeNoInclude string `json:"chargeNoInclude"` //费用不包含
	ChildrenNum     int    `json:"childrenNum"`     //产品包含儿童数
	CityId          string `json:"cityId"`          //地区id
	CityName        string `json:"cityName"`        //景点所在地
	Conds           []struct {
		CondId          int64   `json:"condId"`          //增值产品编号
		CondTitle       string  `json:"condTitle"`       //增值产品名称
		IsApiece        int     `json:"isApiece"`        //报价方式，1 按照人数报价,2 收费跟人数无关
		IsGotta         int     `json:"isGotta"`         //是否必须购买，0 可选，1 必选
		IsSync          int     `json:"isSync"`          //人数限制，0 可以单独选择数量,1 必须跟订单人数 一致
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价
		StartNum        int     `json:"startNum"`        //最低预订产品数量，0 表示不限制
		TreeId          int     `json:"treeId"`          //增值产品类型，0 住宿 1 餐饮美食 2 租车(接送) 3 娱乐休闲 4 购物 5 游玩项目 6 健康养生 7 导游服务 8 配送服务 9 旅游保险 10 签证服务 11 上车（接待）地点 12 交通
		Unit            string  `json:"unit"`            //增值产品单位，如：张、件、人、晚
	} `json:"conds"` //增值项目列表
	Content          string   `json:"content"`          //产品介绍，富文本
	CreditTypeSet    string   `json:"creditTypeSet"`    //如果支持多个证件类型会以英文逗号分开，证件类型，0 身份证 1 学生证 2 军官证 3 护照 4 户口本(儿童请选择此项) 5 港澳通行证 6 台胞证 7 台湾通行证 8 入台证 9 香港居民往来内地通行证 10 警官证 11 驾驶证 12 海员证 13 外国人在中国永久居留证 14澳门居民往来内地通行证 15港澳居民来往内地通行证 16港澳台居民来往内地通行证 17港澳台居民居住证 18中华人民共和国旅行证 19回乡证 20台胞证 21香港身份证
	CustField        string   `json:"custField"`        //客人预订需要填写的信息，如link_man,link_phone,link_single_phone,link_credit_no,link_ email,link_address link_man:姓名 link_phone:手机号 link_single_phone:每个游玩人手机号都要 link_credit_no:证件号 link_email:邮箱地址 link_address:邮寄地址 (要求快递门票时此项为必填项)
	CustFieldEx      string   `json:"custFieldEx"`      //客人预订需要填写的扩张自定义信息，例子： {"data":[{"signle":"0","datatype":"dchar", "notnull":"0","name":"性别","desc":"填 写男或者女"}],"count":0} signle：0 只填联系人 1 所有人都填 datatype：dchar 字符类型 dnum 数字类型 ddate 日期类型 dsel 选择类型 notnull：0 可以为空 1 不能为空 name : 自定义信息名称 desc：自定义信息提示说明
	DistributionDesc string   `json:"distributionDesc"` //免费配说明
	ExpressPrice     float64  `json:"expressPrice"`     //快递费用
	Goodsunit        string   `json:"goodsunit"`        //单位：件，套...
	GroupInfoIds     []string `json:"groupInfoIds"`     //预约产品集合，需要在线预约的产品才有
	Img              string   `json:"img"`              //标志图
	Imgs             string   `json:"imgs"`             //相关主图，多个图片以逗号分隔开
	ImportantNote    string   `json:"importantNote"`    //重要提示
	IsChangeask      int      `json:"isChangeask"`      //退改申请类型 0不支持发起退改申请 1支持
	IsConds          int      `json:"isConds"`          //产品是否有增值项目 1 有，0 无
	IsConfirm        int      `json:"isConfirm"`        //是否需要人工确认，0 自动确认 ，1 人工确认
	IsExpress        int      `json:"isExpress"`        //是否支持快递配送 1 支持，0 不支持
	IsFree           int      `json:"isFree"`           //是否支持免费配送方式，1 支持，0不支持
	IsMusku          int      `json:"isMusku"`          //是否为门票多规格，0普通门票 1多规格
	IsOnlinepay      int      `json:"isOnlinepay"`      //1 在线支付（预付），0 酒店前台现付 （返佣）；在线支付的订单必须调用支付接口成功后才能出票
	IsPackage        int      `json:"isPackage"`        //是否是套餐，treeId=1 并且 isPackage=1 是打包套餐，treeId=1 是一般的套餐
	IsPrint          int      `json:"isPrint"`          //是否需要出票 0 不需要 1 需要
	IsSingle         int      `json:"isSingle"`         //客人资料要求，1 要求输入每个客人资料，0 只需要输入一个客人资料
	IsTimeslot       int      `json:"isTimeslot"`       //时段产品类型 0不是 1是
	IsTop            int      `json:"isTop"`            //推荐值
	LimitType        int      `json:"limitType"`        //预定限制类型，下单数量校验类型，0订单，3IP地址，4分销商ID，5会员ID(C端)，8同游玩日同分销商，9同游玩日同会员
	MarketPrice      float64  `json:"marketPrice"`      //市场价
	MaxNum           int      `json:"maxNum"`           //最大预定数 0不限制
	Muskus           []struct {
		InfoId     int64  `json:"infoId"`     //产品id
		PriceId    int64  `json:"priceId"`    //规格id
		PriceName  string `json:"priceName"`  //规格名称
		SelectType int    `json:"selectType"` //选择类型 1可多选
	} `json:"muskus"` //门票多规格信息，isMusku=1时必有值
	OldmanNum       int    `json:"oldmanNum"`   //产品包含老人数
	OrderDesc       string `json:"orderDesc"`   //预订详细说明
	OrderPolicy     string `json:"orderPolicy"` //预订说明
	PackageProducts []struct {
		InfoIdNode      int64  `json:"infoIdNode"`      //子产品ID
		InfoNameNode    string `json:"infoNameNode"`    //子产品名称
		IsDateNode      int    `json:"isDateNode"`      //游玩日期类型，0 游客选择的打包产品的当天有效，1 游客选择的打包产品的延后1天有效,2 延后2天有效，3 延后3天有效，4 延后4天有效，5 延后5天有效，-1不限时间，游客随意选择游玩日期
		NumNode         int    `json:"numNode"`         //包含的数量
		TreeIdNode      int    `json:"treeIdNode"`      //产品类型
		ViewAddressNode string `json:"viewAddressNode"` //子产品景点地址
		ViewIdNode      string `json:"viewIdNode"`      //子产品景点ID
		ViewNameNode    string `json:"viewNameNode"`    //子产品景点名称
	} `json:"packageProducts"` //打包套餐产品
	PayMinute      int    `json:"payMinute"`      //多少分钟后不支付自动取消
	PeopleNum      int    `json:"peopleNum"`      //产品包含成人数
	Peoples        string `json:"peoples"`        //产品人群要求
	PosterImg      string `json:"posterImg"`      //海报图
	PriceEndDate   string `json:"priceEndDate"`   //价格结束时间，因为产品可能设置特定日价格，所以具体需要查询价格接口
	PriceStartDate string `json:"priceStartDate"` //价格开始时间
	ProdDesc       string `json:"prodDesc"`       //商品说明，treeId=11的商品才可能有值
	ProductName    string `json:"productName"`    //产品名称
	ProductNo      int64  `json:"productNo"`      //产品ID
	PubVers        string `json:"pubVers"`        //销售范围
	PurchaseLimit  struct {
		LimitCardDay    int `json:"limitCardDay"`    //同一证件限购天数
		LimitCardGoods  int `json:"limitCardGoods"`  //同一证件限购类型 0按张数限购 1按次数限购
		LimitCardNum    int `json:"limitCardNum"`    //同一证件限购数量，0不限制
		LimitPhoneDay   int `json:"limitPhoneDay"`   //同一手机号限购天数
		LimitPhoneGoods int `json:"limitPhoneGoods"` //同一手机号限购类型 0按张数限购 1按次数限购
		LimitPhoneNum   int `json:"limitPhoneNum"`   //同一手机号限购数量，0不限制
		LimitTimeType   int `json:"limitTimeType"`   //限购日期类型 0预定日期 1游玩日期
	} `json:"purchaseLimit"` //限购规则
	RefundNote      string  `json:"refundNote"`      //退款说明
	SaleEndDate     string  `json:"saleEndDate"`     //预售结束时间，预售产品必有
	SalePrice       float64 `json:"salePrice"`       //零售价
	SaleStartDate   string  `json:"saleStartDate"`   //预售开始时间，预售产品必有
	SettlementPrice float64 `json:"settlementPrice"` //结算价
	Specs           []struct {
		SpecName   string   `json:"specName"`   //规格名称
		SpecValues []string `json:"specValues"` //规格值
	} `json:"specs"` //抢购多规格信息
	StartDate      string   `json:"startDate"`      //最早游玩日期
	StartDay       int      `json:"startDay"`       //提前多少天预定
	StartMaxDay    int      `json:"startMaxDay"`    //最大可预订天数，0不限制，如果是具体的天数，预定的日期不能超过该值
	StartMinute    int      `json:"startMinute"`    //预订后多少分钟生效
	StartNum       int      `json:"startNum"`       //最低预订产品数量，0 表示不限制
	StartTime      string   `json:"startTime"`      //几点之前预定
	State          int      `json:"state"`          //产品状态，0 在线，1 下线
	Themes         []string `json:"themes"`         //主题
	TicketCount    int      `json:"ticketCount"`    //已销售数量
	TicketTypeName string   `json:"ticketTypeName"` //票种，门票才有，0特价票，1普通票，2儿童票，3学生票，4套票，5团队票，6长者票，7中学生票，8情侣票，9女士票，10男士票，11双人票，12三人票，14大学生票，15教师人票，16残疾票，17军人票，18女大学生票，19家庭票(2大1小)，20家庭票(2大2小)，21亲子票(1大1小)，22优待票，23一日票，24二日票
	TicketcodeType int      `json:"ticketcodeType"` //发码类型 0一单一码 1一人一码
	TreeId         int      `json:"treeId"`         //产品类型，0单一门票 1套餐或联票 3线路 5酒店 11实物商品 12预售（抢购）
	UserNote       string   `json:"userNote"`       //使用说明
	ValidityCon    string   `json:"validityCon"`    //有效期限制具体天数或日期，格式为 yyyy-MM-dd 或者数字,跟 validityType 有关联
	ValidityType   int      `json:"validityType"`   //有效期限制，0：游客选定的游玩日期当天有效 1：游客预订日期延后几天有效 2：游客预订日期截止到指定日期有效 3：游客选定日期延后几天有效 4：游客选定日期截止到指定日期有效 5：指定日期xxx到xxx内有效
	VerifyIntype   int      `json:"verifyIntype"`   //0换票入园 1直接入园 2由导游带领入园 ；门票产品才有
	VerifyType     int      `json:"verifyType"`     //0短信凭证 1二维码凭证 3身份证 4实体票 5打印件 6学生证 ；门票产品才有
	ViewAddress    string   `json:"viewAddress"`    //景点地址
	ViewId         string   `json:"viewId"`         //景点编号
	ViewLatitude   string   `json:"viewLatitude"`   //纬度，百度地图
	ViewLongitude  string   `json:"viewLongitude"`  //经度，百度地图
	Lon            float64  `json:"lon"`
	Lat            float64  `json:"lat"`
	ViewName       string   `json:"viewName"` //景点名称
	Views          []string `json:"views"`    //景点列表
	YyLink         string   `json:"yyLink"`   //预约链接，需要在线预约的产品才有
}

type ZwyProductPriceReq struct {
	*zwyCommonParams
	EndTravelDate string `json:"endTravelDate"` //结束日期，不传默认默认只查本月的价格
	PlanId        string `json:"planId"`        //价格计划套餐Id，线路产品可传
	ProductNo     int64  `json:"productNo"`     //产品id
	SpecId        string `json:"specId"`        //规格id，多规格的产品可传
	TimeId        string `json:"timeId"`        //时段/场次Id
	TravelDate    string `json:"travelDate"`    //开始日期
}

type ZwyProductPriceTicket struct {
	Date        string  `json:"date"`        //游玩日期
	MarketPrice float64 `json:"marketPrice"` //市场价
	Num         int     `json:"num"`         //剩余库存
	SalePrice   float64 `json:"salePrice"`   //零售价
	Seats       []struct {
		EndTime         string  `json:"endTime"`         //结束时间，可能为空
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		Name            string  `json:"name"`            //时段/场次名称
		Num             int     `json:"num"`             //库存
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价格
		StartTime       string  `json:"startTime"`       //开始时间，可能为空
		TimeId          string  `json:"timeId"`          //时段/场次Id，一般自我游自营产品才有
		Value           string  `json:"value"`           //时段/场次值
	} `json:"seats"` //时段/场次信息
	SettlementPrice float64 `json:"settlementPrice"` //结算价格
}
type ZwyProductPriceResp struct {
	InfoId     int64 `json:"infoId"` //产品id
	IsSpec     int   `json:"isSpec"` //是否是多规格, 0：否 1：是
	LinePrices []struct {
		Date         string `json:"date"`       //日期
		MinTeamNum   int    `json:"minTeamNum"` //最小成团人数
		PriceDetails []struct {
			MarketPrice     float64 `json:"marketPrice"`     //市场价
			PriceId         int64   `json:"priceId"`         //规格id
			PriceName       string  `json:"priceName"`       //规格名称
			SalePrice       float64 `json:"salePrice"`       //零售价
			SettlementPrice float64 `json:"settlementPrice"` //结算价
		} `json:"priceDetails"` //价格详细信息
		RemainNum       int     `json:"remainNum"`       //剩余库存
		SingleRoomPrice float64 `json:"singleRoomPrice"` //单房差
		StartDay        int     `json:"startDay"`        //提前天数
		TotalNum        int     `json:"totalNum"`        //总库存
	} `json:"linePrices"` //线路产品价格
	MuskuPrices []struct {
		Date         string `json:"date"` //日期
		Num          int    `json:"num"`  //剩余库存
		PriceDetails []struct {
			MarketPrice     float64 `json:"marketPrice"`     //市场价
			PriceId         int64   `json:"priceId"`         //规格id
			PriceName       string  `json:"priceName"`       //规格名称
			SalePrice       float64 `json:"salePrice"`       //零售价
			SettlementPrice float64 `json:"settlementPrice"` //结算价
		} `json:"priceDetails"` //多规格价格详细信息
	} `json:"muskuPrices"` //门票多规格价格
	SnapupPrices []struct {
		AllNum          int     `json:"allNum"`          //总库存，普通抢购产品才有值
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		Num             int     `json:"num"`             //剩余库存
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价格
		SpecId          string  `json:"specId"`          //规格id，多规格商品才有值
		SpecName        string  `json:"specName"`        //规格名称，多规格商品才有值
	} `json:"snapupPrices"` //抢购产品价格
	SpecProdPrices []struct {
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		Num             int     `json:"num"`             //剩余库存
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价格
		SpecId          string  `json:"specId"`          //规格id
		SpecName        string  `json:"specName"`        //规格名称
	} `json:"specProdPrices"` //多规格商品价格
	Specs []struct {
		SpecName   string   `json:"specName"`   //规格名称
		SpecValues []string `json:"specValues"` //规格值
	} `json:"specs"` //多规格信息
	TicketPrices []ZwyProductPriceTicket `json:"ticketPrices"` //门票产品价格
}

// 增值项目信息，如果产品详情接口isCond为1需要传
type ZwyOrderConds struct {
	CondId int64 `json:"condId"` //增值项目产品id
	Num    int   `json:"num"`    //数量
}

// 订单详细信息，酒店产品必传，其他类目产品可传，传了接口会校验结算价
type ZwyOrderDetails struct {
	Cond       string  `json:"cond"`       //普通门票为游玩日期，酒店为入住日期，多规格商品为规格id，门票多规格为规格id
	Num        int     `json:"num"`        //数量
	PeopleType int     `json:"peopleType"` //人群类型，对应线路价格库存接口的peopleType，线路产品必传
	Price      float64 `json:"price"`      //结算价
}

// 游玩人信息
type ZwyOrderPeoples struct {
	Fields         string `json:"fields"`         //客人自定义信息
	IsSingleRoom   int    `json:"isSingleRoom"`   //单房差，0无 1有，线路产品必传
	LinkAddress    string `json:"linkAddress"`    //游玩人快递地址
	LinkCreditNo   string `json:"linkCreditNo"`   //游玩人证件号
	LinkCreditType int    `json:"linkCreditType"` //游玩人证件类型
	LinkMan        string `json:"linkMan"`        //游玩人姓名
	LinkPhone      string `json:"linkPhone"`      //游玩人手机号
	LinkSex        int    `json:"linkSex"`        //性别, 1 未知，2 男，3 女，线路产品必传
	PeopleType     int    `json:"peopleType"`     //人群类型，对应线路价格库存接口的peopleType，线路产品必传
}

// 打包套餐子产品信息，treeId=1并且isPackage=1才可用，如果子产品游玩日期都会跟母产品一致的话可不传
type ZwyOrderProds struct {
	ProdDate string `json:"prodDate"` //子产品游玩日期 格式yyyy-MM-dd
	ProdId   int64  `json:"prodId"`   //子产品id
}
type ZwyOrderAddReq struct {
	*zwyCommonParams
	ArriveidTime   string            `json:"arriveidTime"`   //到达时间，需要传数字格式如20，代表20点，酒店产品需要传
	Conds          []ZwyOrderConds   `json:"conds"`          //增值项目信息，如果产品详情接口isCond为1需要传
	Details        []ZwyOrderDetails `json:"details"`        //订单详细信息，酒店产品必传，其他类目产品可传，传了接口会校验结算价
	EndTravelDate  string            `json:"endTravelDate"`  //结束游玩日期 格式yyyy-MM-dd
	Fields         string            `json:"fields"`         //客人自定义信息，如果产品详情接口custFieldEx有值，下单需要传自定义信息，例子： {"data":[{"name":"性别","value":"男"}]}
	InfoId         int64             `json:"infoId"`         //产品id
	IsGtpw         string            `json:"isGtpw"`         //中台渠道，内部参数可忽略
	IsSend         string            `json:"isSend"`         //是否需要发送短信，0不需要 1需要，默认需要，只能针对设置到短信内容的产品
	Lang           string            `json:"lang"`           //语种：(简体中文：zh-CN)，(繁體中文：zh-HK)，(日本語：ja-JP)，(英语：en-US)，(韩语：ko-KR)
	LinkAddress    string            `json:"linkAddress"`    //快递地址
	LinkCreditNo   string            `json:"linkCreditNo"`   //联系人证件号
	LinkCreditType int               `json:"linkCreditType"` //联系人证件类型 0 身份证 1 学生证 2 军官证 3 护照 4 户口本(儿童请选择此项) 5 港澳通行证 6 台胞证 7 台湾通行证 8 入台证 9 香港居民往来内地通行证 10 警官证 11 驾驶证 12 海员证 13 外国人在中国永久居留证
	LinkEmail      string            `json:"linkEmail"`      //邮件地址
	LinkMan        string            `json:"linkMan"`        //联系人姓名
	LinkPhone      string            `json:"linkPhone"`      //联系人手机号
	Num            int               `json:"num"`            //预定数量
	OrderMemo      string            `json:"orderMemo"`      //订单备注
	OrderSourceId  string            `json:"orderSourceId"`  //订单流水号，对接方系统的订单流水号（唯一）
	Peoples        []ZwyOrderPeoples `json:"peoples"`        //游玩人信息
	PlanId         string            `json:"planId"`         //价格计划套餐Id，线路产品可传
	Prods          []ZwyOrderProds   `json:"prods"`          //打包套餐子产品信息，treeId=1并且isPackage=1才可用，如果子产品游玩日期都会跟母产品一致的话可不传
	Seat           string            `json:"seat"`           //分时/场次信息，seat接口获取的value值
	SingleRoomNum  int               `json:"singleRoomNum"`  //单房差数量，线路产品可传
	SpecId         string            `json:"specId"`         //规格id，多规格必传
	StartPlaceId   string            `json:"startPlaceId"`   //上车点id，线路产品如果设置到上车点的必传
	TravelDate     string            `json:"travelDate"`     //开始游玩日期 格式yyyy-MM-dd
}

type ZwyOrderAddResp struct {
	MemOrderMoney float64 `json:"memOrderMoney"` //订单零售总金额，单位元
	OrderId       int64   `json:"orderId"`       //订单号
	OrderMoney    float64 `json:"orderMoney"`    //订单结算总金额，单位元
	OrderState    int     `json:"orderState"`    //订单状态，0 表示等待人工确认 1 表示待支付 2 表示已成功 （2 状态是只有景点现付返佣才会出现，可根据产品详情isOnlinepay字段判断）
	PayType       int     `json:"payType"`       //支付方式，0，虚拟支付，1信用支付，3库存支付，4全球货仓抵扣
}

type ZwyOrderAddCheckReq struct {
	ZwyOrderAddReq
}

type ZwyOrderAddCheckResp struct {
	*zwyResponse
}

type ZwyOrderCancelReq struct {
	*zwyCommonParams
	CancelMemo   string `json:"cancelMemo"` //申请取消原因
	CancelMuskus []struct {
		CancelNum int    `json:"cancelNum"` //多规格申请取消数量
		Cond      string `json:"cond"`      //规格id
	} `json:"cancelMuskus"` //门票多规格取消详情，申请部分取消时必传,门票多规格订单才用到
	CancelNum      int      `json:"cancelNum"`      //申请取消数量
	Codes          []string `json:"codes"`          //申请取消的券码,一人一码的情况才需要传
	OrderId        int64    `json:"orderId"`        //自我游订单号
	SourceCancelId string   `json:"sourceCancelId"` //申请取消id
}

type ZwyOrderCancelResp struct {
	CancelId    string  `json:"cancelId"`    //取消id
	CancelMoney float64 `json:"cancelMoney"` //取消金额
	CancelState int     `json:"cancelState"` //取消状态，1取消成功 2审核中
	OrderId     int64   `json:"orderId"`     //自我游订单号
}

type ZwyOrderDetailReq struct {
	*zwyCommonParams
	OrderId       int64  `json:"orderId"`       //自我游订单号，orderId和orderSourceId必须传一个
	OrderSourceId string `json:"orderSourceId"` //第三方订单号，orderId和orderSourceId必须传一个
}

type ZwyOrderDetailResp struct {
	CancelDate string `json:"cancelDate"` //取消订单日期
	Conds      []struct {
		CondId          int64   `json:"condId"`          //增值产品编号
		CondTitle       string  `json:"condTitle"`       //增值产品名称
		MarketPrice     float64 `json:"marketPrice"`     //市场价
		Num             int     `json:"num"`             //数量
		SalePrice       float64 `json:"salePrice"`       //零售价
		SettlementPrice float64 `json:"settlementPrice"` //结算价
		Unit            string  `json:"unit"`
	} `json:"conds"` //增值项目信息
	Details []struct {
		Condition   string  `json:"condition"`   //普通门票，抢购，酒店是入住日期，门票多规格，商品多规格是规格id
		InfoId      int     `json:"infoId"`      //产品id
		InfoTitle   string  `json:"infoTitle"`   //产品标题
		MarketPrice float64 `json:"marketPrice"` //产品窗口价
		Num         int     `json:"num"`         //产品数量
		PeopleType  int     `json:"peopleType"`  //线路人群类型
		Price       float64 `json:"price"`       //产品结算单价
		SalePrice   float64 `json:"salePrice"`   //产品零售单价
		SeqId       int     `json:"seqId"`       //子项排列顺序
	} `json:"details"` //详细订单信息
	EndTravelDate  string               `json:"endTravelDate"`  //结束游玩日期
	FinishNum      int                  `json:"finishNum"`      //总验证数量
	FreeMemo       string               `json:"freeMemo"`       //免费取票说明
	GetPrice       float64              `json:"getPrice"`       //快递费用
	GetType        int                  `json:"getType"`        //快递方式，0 免费 1 需要配送 3 无须配送
	IsConfirm      int                  `json:"isConfirm"`      //确认方式，是否需要确认后才有效 0 不需要确认 1 需要确认
	IsOnlinepay    int                  `json:"isOnlinepay"`    //支付方式，是否必须在线支付 0 不是，1 是
	LinkAddress    string               `json:"linkAddress"`    //联系人地址
	LinkCreditNo   string               `json:"linkCreditNo"`   //联系人证件号码
	LinkCreditType int                  `json:"linkCreditType"` //联系人证件类型
	LinkEmail      string               `json:"linkEmail"`      //联系人email
	LinkMan        string               `json:"linkMan"`        //联系人姓名
	LinkPhone      string               `json:"linkPhone"`      //联系人电话
	MarketPrice    float64              `json:"marketPrice"`    //市场价
	MemOrderMoney  float64              `json:"memOrderMoney"`  //零售总价
	Num            int                  `json:"num"`            //产品预定数量
	OrderDate      string               `json:"orderDate"`      //下单日期
	OrderId        int64                `json:"orderId"`        //自我游订单号
	OrderMemo      string               `json:"orderMemo"`      //订单备注
	OrderMoney     float64              `json:"orderMoney"`     //结算总价
	OrderSourceId  string               `json:"orderSourceId"`  //第三方订单号
	OrderState     ZwyOrderStateEnum    `json:"orderState"`     //订单状态，0新订单（下单成功未确认状态） 1已确认（下单成功已确认状态，已确认状态才能调用支付接口） 2已成功（已支付成功状态） 3已取消 4已完成（已消费状态）
	OrderState2    ZwySubOrderStateEnum `json:"orderState2"`    //订单状态2，可能为空；1已通知（已支付未出票状态） 2已快递（产品需要配送时才有的状态） 3已出票
	PackageOrders  []any                `json:"packageOrders"`  //打包子订单信息
	Peoples        []struct {
		Fields         string `json:"fields"`         //客人自定义信息
		IsSingleRoom   int    `json:"isSingleRoom"`   //单房差，0无 1有，线路产品必传
		LinkAddress    string `json:"linkAddress"`    //游玩人快递地址
		LinkCreditNo   string `json:"linkCreditNo"`   //游玩人证件号
		LinkCreditType int    `json:"linkCreditType"` //游玩人证件类型
		LinkMan        string `json:"linkMan"`        //游玩人姓名
		LinkPhone      string `json:"linkPhone"`      //游玩人手机号
		LinkSex        int    `json:"linkSex"`        //性别, 1 未知，2 男，3 女，线路产品必传
		PeopleType     int    `json:"peopleType"`     //人群类型，对应线路价格库存接口的peopleType，线路产品必传
	} `json:"peoples"` //游玩人信息
	ProductName     string  `json:"productName"`     //产品标题
	ProductNo       int64   `json:"productNo"`       //产品id
	SalePrice       float64 `json:"salePrice"`       //零售价
	SendContent1    string  `json:"sendContent1"`    //产品预定短信
	SendContent2    string  `json:"sendContent2"`    //产品通知单短信
	SendContent3    string  `json:"sendContent3"`    //产品出票短信
	SettlementPrice float64 `json:"settlementPrice"` //结算价
	TravelDate      string  `json:"travelDate"`      //游玩日期
	Vouchers        []struct {
		Code          string `json:"code"`          //凭证码
		CreditNo      string `json:"creditNo"`      //证件号码
		Name          string `json:"name"`          //姓名
		PdfUrl        string `json:"pdfUrl"`        //pdf凭证链接
		QrcodeUrl     string `json:"qrcodeUrl"`     //二维码凭证链接
		Type          int    `json:"type"`          //凭证类型 0凭证码 1二维码链接 2pdf 3普通链接
		VoucherStatus int    `json:"voucherStatus"` //凭证类型 0 正常 1 已核销 2 已取消 3申请取消中 4取消处理中
		VoucherUrl    string `json:"voucherUrl"`    //3普通链接
	} `json:"vouchers"` //凭证信息
}

type ZwyOrderPayReq struct {
	*zwyCommonParams
	OrderId int64 `json:"orderId"` //自我游订单号
}

type ZwyOrderPayResp struct {
	OrderId int64 `json:"orderId"` //订单号
	PayType int   `json:"payType"` //支付方式
}

type ZwyQueryBalanceReq struct {
	*zwyCommonParams
}

type ZwyQueryBalanceResp struct {
	AccountBalance string `json:"accountBalance"` //单位：元
}

type ZwyOrderListReq struct {
	*zwyCommonParams
	EndDate    string `json:"endDate"`    //下单结束时间 yyyy-MM-dd HH:mm:ss
	OrderState string `json:"orderState"` //订单状态 0新订单 1已确认 2已支付 3已取消 4已完成
	Page       int    `json:"page"`       //当前页码：从0开始
	ResultNum  int    `json:"resultNum"`  //每页个数
	StartDate  string `json:"startDate"`  //下单开始时间 yyyy-MM-dd HH:mm:ss
	TravelDate string `json:"travelDate"` //游玩日期 yyyy-MM-dd
	UserId     string `json:"userId"`     //用户id
}
type ZwyOrderListResultItem struct {
	CancelDate      string  `json:"cancelDate"`      //取消订单日期
	EndTravelDate   string  `json:"endTravelDate"`   //结束游玩日期
	FinishNum       int     `json:"finishNum"`       //总验证数量
	IsConfirm       int     `json:"isConfirm"`       //确认方式，是否需要确认后才有效 0 不需要确认 1 需要确认
	IsOnlinepay     int     `json:"isOnlinepay"`     //支付方式，是否必须在线支付 0 不是，1 是
	LinkAddress     string  `json:"linkAddress"`     //联系人地址
	LinkCreditNo    string  `json:"linkCreditNo"`    //联系人证件号码
	LinkCreditType  int     `json:"linkCreditType"`  //联系人证件类型
	LinkEmail       string  `json:"linkEmail"`       //联系人email
	LinkMan         string  `json:"linkMan"`         //联系人姓名
	LinkPhone       string  `json:"linkPhone"`       //联系人电话
	MarketPrice     float64 `json:"marketPrice"`     //市场价
	MemOrderMoney   float64 `json:"memOrderMoney"`   //零售总价
	Num             int     `json:"num"`             //产品预定数量
	OrderDate       string  `json:"orderDate"`       //下单日期
	OrderId         int64   `json:"orderId"`         //自我游订单号
	OrderMemo       string  `json:"orderMemo"`       //订单备注
	OrderMoney      float64 `json:"orderMoney"`      //结算总价
	OrderSourceId   string  `json:"orderSourceId"`   //第三方订单号
	OrderState      int     `json:"orderState"`      //订单状态，0新订单（下单成功未确认状态） 1已确认（下单成功已确认状态，已确认状态才能调用支付接口） 2已成功（已支付成功状态） 3已取消 4已完成（已消费状态）
	OrderState2     string  `json:"orderState2"`     //订单状态2，可能为空；1已通知（已支付未出票状态） 2已快递（产品需要配送时才有的状态） 3已出票
	ProductName     string  `json:"productName"`     //产品标题
	ProductNo       int64   `json:"productNo"`       //产品id
	SalePrice       float64 `json:"salePrice"`       //零售价
	SettlementPrice float64 `json:"settlementPrice"` //结算价
	TravelDate      string  `json:"travelDate"`      //游玩日期
}
type ZwyOrderListResp struct {
	Page       int                      `json:"page"`
	PageCount  int                      `json:"pageCount"`
	ResultNum  int                      `json:"resultNum"`
	Results    []ZwyOrderListResultItem `json:"results"`
	Size       int                      `json:"size"`
	SizeAll    int                      `json:"sizeAll"`
	StartIndex int                      `json:"startIndex"`
}

type ZwyOrderResendReq struct {
	*zwyCommonParams
	Mobile  string `json:"mobile"`
	OrderId int64  `json:"orderId"`
}

type ZwyOrderResendResp struct{}
