package my_wechat

import (
	"context"
	"errors"
	"fmt"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	models2 "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/models"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/payment"
	request3 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/notify/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/request"
	response3 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/response"
	request2 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/refund/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/payment/refund/response"
	"net/http"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

func Payment(method int) *payment.Payment {
	appId := config.Config.Wechat.AppId
	switch method {
	case constmap.PayMethodMpWeixin:
		appId = config.Config.Wechat.AppId
	case constmap.PayMethodWeixin:
		appId = config.Config.WechatOfficial.AppId
	}
	payService, err := payment.NewPayment(&payment.UserConfig{
		AppID:       appId,
		MchID:       config.Config.WechatPay.Mchid,
		MchApiV3Key: config.Config.WechatPay.MchApiV3Key,
		Key:         config.Config.WechatPay.MchApiV2Key,
		SerialNo:    config.Config.WechatPay.SerialNo,
		CertPath:    config.Config.WechatPay.CertPath,
		KeyPath:     config.Config.WechatPay.KeyPath,
		NotifyURL:   config.Config.WechatPay.NotifyUrl,
		HttpDebug:   config.IsDebug(),
		Http: payment.Http{
			Timeout: 30.0,
			BaseURI: "https://api.mch.weixin.qq.com",
		},
		Cache: kernel.NewRedisClient(&kernel.UniversalOptions{
			Addrs: []string{config.Config.Redis.Host},
			DB:    config.Config.Redis.Db,
		}),
	})
	if err != nil {
		panic(fmt.Errorf("payService[%d] error:%s", method, err.Error()))
	}
	return payService
}

func HandlePaidNotify(method int, r *http.Request, closure func(message *request3.RequestNotify, transaction *models2.Transaction, fail func(message string)) interface{}) (*http.Response, error) {
	return Payment(method).HandlePaidNotify(r, closure)
}

func QueryPayByOutTradeNumber(method int, outTradeNumber string) (*response3.ResponseOrder, error) {
	return Payment(method).Order.QueryByOutTradeNumber(context.Background(), outTradeNumber)
}

func CreatePay(method int, req *request.RequestJSAPIPrepay) (*CreatePayResp, error) {
	respUnity, err := Payment(method).Order.JSAPITransaction(context.Background(), req)
	if err != nil {
		return nil, err
	}

	resp := CreatePayResp{
		ResponseUnitfy: *respUnity,
	}

	if payStr, err := buildPayConfig(method, respUnity); err != nil {
		return nil, err
	} else {
		resp.PayStr = payStr
	}

	return &resp, err
}

func DoRefund(outRefundNo, Reason string, RefundMoney int64, orderPayment *models.OrderPayment) (*response.ResponseRefund, error) {
	option := request2.RequestRefund{
		SubMchid:      "",
		TransactionID: "",
		OutTradeNo:    orderPayment.OutTradeNo,
		OutRefundNo:   outRefundNo,
		Reason:        Reason,
		NotifyUrl:     "",
		FundsAccount:  "",
		Amount: &request2.RefundAmount{
			Refund:   int(RefundMoney),
			From:     []*request2.RefundAmountFrom{},
			Total:    int(orderPayment.ActualPayAmount),
			Currency: "CNY",
		},
	}
	resp, err := Payment(orderPayment.PayMethod).Refund.Refund(context.TODO(), &option)
	if err != nil {
		return nil, err
	} else if resp.RefundID == "" {
		err = errors.New(resp.Message)
	}

	return resp, err
}

func QueryRefund(method int, outRefundNo string) (*response.ResponseRefund, error) {
	resp, err := Payment(method).Refund.Query(context.TODO(), outRefundNo)
	if err != nil {
		return nil, err
	} else if resp.RefundID == "" {
		err = errors.New(resp.Message)
	}

	return resp, err
}

func buildPayConfig(method int, resp *response3.ResponseUnitfy) (interface{}, error) {
	if resp.PrepayID == "" {
		return nil, errors.New("prepayId empty")
	}
	return Payment(method).JSSDK.BridgeConfig(resp.PrepayID, true)
}
