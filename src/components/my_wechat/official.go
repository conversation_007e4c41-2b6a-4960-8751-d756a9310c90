package my_wechat

import (
	"context"
	"github.com/ArtisanCloud/PowerSocialite/v3/src/providers"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	officialAccount2 "github.com/ArtisanCloud/PowerWeChat/v3/src/officialAccount"
	"roadtrip-api/src/config"
	"strings"
	"sync"
)

var officialAccount *officialAccount2.OfficialAccount
var onceOfficial sync.Once

func Official() *officialAccount2.OfficialAccount {
	onceOfficial.Do(func() {
		var err error
		officialAccount, err = officialAccount2.NewOfficialAccount(&officialAccount2.UserConfig{
			AppID:     config.Config.WechatOfficial.AppId,
			Secret:    config.Config.WechatOfficial.Secret,
			HttpDebug: config.IsDebug(),
			Debug:     config.IsDebug(),
			Cache: kernel.NewRedisClient(&kernel.UniversalOptions{
				Addrs: []string{
					config.Config.Redis.Host,
				},
				DB: config.Config.Redis.Db,
			}),
		})
		if err != nil {
			panic(err)
		}
	})

	return officialAccount
}

func UserFromCode(code string) (*providers.User, error) {
	return Official().OAuth.UserFromCode(code)
}

func JsSdkConfig() (any, error) {
	jsapiList := []string{
		"updateAppMessageShareData",
		"updateTimelineShareData",
		"chooseImage",
	}
	openTagList := []string{}

	url := config.Config.App.FrontHost
	if strings.LastIndex(url, "/") != len(url)-1 {
		url += "/"
	}

	return Official().JSSDK.BuildConfig(context.Background(),
		jsapiList,
		false,
		false,
		openTagList,
		url,
	)
}
