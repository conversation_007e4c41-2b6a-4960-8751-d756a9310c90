package my_wechat

import (
	"context"
	"errors"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/basicService/subscribeMessage/request"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	response4 "github.com/ArtisanCloud/PowerWeChat/v3/src/kernel/response"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/auth/response"
	response2 "github.com/ArtisanCloud/PowerWeChat/v3/src/miniProgram/phoneNumber/response"
	response3 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/response"
	"roadtrip-api/src/config"
	"roadtrip-api/src/utils"
	"sync"
)

var app *miniProgram.MiniProgram
var once sync.Once

type CreatePayResp struct {
	response3.ResponseUnitfy

	PayStr interface{} `json:"pay_str"`
}

func initApp() {
	var err error

	app, err = miniProgram.NewMiniProgram(&miniProgram.UserConfig{
		AppID:  config.Config.Wechat.AppId,
		Secret: config.Config.Wechat.Secret,
		Cache: kernel.NewRedisClient(&kernel.UniversalOptions{
			Addrs: []string{
				config.Config.Redis.Host,
			},
			DB: config.Config.Redis.Db,
		}),
	})

	if err != nil {
		panic(err)
	}

}

func Mini() *miniProgram.MiniProgram {
	once.Do(initApp)

	return app
}

func GetOpenIdByCode(code string) (openId *response.ResponseCode2Session, err error) {
	if openId, err = Mini().Auth.Session(context.Background(), code); err != nil {
		return nil, err
	} else if openId.ErrCode > 0 {
		return nil, errors.New(openId.ErrMsg)
	}

	return
}

func GetUserPhone(code string) (*response2.ResponseGetUserPhoneNumber, error) {
	return Mini().PhoneNumber.GetUserPhoneNumber(context.Background(), code)
}

// 发送订阅消息
func SendSubscribeMessage(data *request.RequestSubscribeMessageSend) (*response4.ResponseMiniProgram, error) {
	result, err := Mini().SubscribeMessage.Send(context.Background(), data)
	if err != nil {
		return nil, err
	} else if result.ErrCode != 0 {
		return nil, utils.NewErrorStr(result.ErrCode, result.ErrMsg)
	}

	return result, nil
}
