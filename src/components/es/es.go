package es

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"io"
	config2 "roadtrip-api/src/config"
	"strings"
	"sync"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
	"github.com/gin-gonic/gin"
)

var client *elasticsearch.Client
var once sync.Once

type Hit[T any] struct {
	Index  string  `json:"_index"`
	Id     string  `json:"_id"`
	Source T       `json:"_source"`
	Score  float64 `json:"_score"`
}

type SearchResponse[T any] struct {
	Hits struct {
		Total struct {
			Value    int    `json:"value"`
			Relation string `json:"relation"`
		}
		MaxScore float64  `json:"max_score"`
		Hits     []Hit[T] `json:"hits"`
	} `json:"hits"`
}

func initClient() {
	once.Do(func() {
		var err error

		config := elasticsearch.Config{
			Addresses: []string{
				config2.Config.Es.Host,
			},
			Username: config2.Config.Es.Username,
			Password: config2.Config.Es.Password,
		}
		if client, err = elasticsearch.NewClient(config); err != nil {
			panic(err)
		}
	})
}

func Client() *elasticsearch.Client {
	initClient()

	return client
}

func Bulk(data []byte) ([]byte, error) {
	request := esapi.BulkRequest{
		Body:    bytes.NewBuffer(data),
		Refresh: "true",
	}
	res, err := request.Do(context.Background(), Client())
	if err != nil {
		return nil, err
	}

	return readHttpResponse(res)
}

func Index[T any](index string, id string, data T) error {
	body, _ := json.Marshal(data)
	indexRequest := esapi.IndexRequest{
		Index:      index,
		DocumentID: id,
		Body:       bytes.NewBuffer(body),
		Refresh:    "true",
	}
	res, err := indexRequest.Do(context.Background(), Client())
	if err != nil {
		return err
	}

	if _, err := readHttpResponse(res); err != nil {
		return err
	}

	return err
}

func Update[T any](index, documentId string, data T) error {
	buf, _ := json.Marshal(gin.H{
		"doc": data,
	})
	req := esapi.UpdateRequest{
		Index:           index,
		Body:            bytes.NewBuffer(buf),
		DocumentID:      documentId,
		RetryOnConflict: convertor.ToPointer(3),
		Refresh:         "true",
	}
	res, err := req.Do(context.Background(), Client())
	if err != nil {
		return err
	}
	if _, err = readHttpResponse(res); err != nil {
		return err
	}

	return nil
}

func Search[T any](index string, query map[string]any) (*SearchResponse[T], error) {
	buf, err := json.Marshal(query)

	request := esapi.SearchRequest{Index: []string{index},
		Body:   bytes.NewBuffer(buf),
		Pretty: true,
	}
	res, err := request.Do(context.Background(), Client())
	if err != nil {
		return nil, err
	}
	if buf, err = readHttpResponse(res); err != nil {
		return nil, err
	}

	var hits SearchResponse[T]
	if err = json.Unmarshal(buf, &hits); err != nil {
		return nil, err
	}

	return &hits, err
}

// SearchWithAggregations 执行ES查询并将聚合结果解析到指定结构体
// index: 索引名称
// query: 查询条件
// result: 用于接收结果的结构体指针
func SearchWithAggregations(index string, query map[string]any, result any) error {
	buf, err := json.Marshal(query)

	request := esapi.SearchRequest{
		Index:  []string{index},
		Body:   bytes.NewBuffer(buf),
		Pretty: true,
	}

	res, err := request.Do(context.Background(), Client())
	if err != nil {
		return err
	}

	buf, err = readHttpResponse(res)
	if err != nil {
		return err
	}

	return json.Unmarshal(buf, result)
}

// DropIndex 删除索引
func DropIndex(index string) error {
	_, err := Client().Indices.Delete([]string{index})

	return err
}

func CreateIndexType(index, body string) error {
	resp, err := Client().Indices.Create(index,
		func(request *esapi.IndicesCreateRequest) {
			request.Body = strings.NewReader(body)
		},
	)

	if err != nil {
		return err
	}
	_, err = readHttpResponse(resp)

	return err
}

func readHttpResponse(request *esapi.Response) ([]byte, error) {
	defer request.Body.Close()

	buf, err := io.ReadAll(request.Body)
	if request.IsError() {
		return nil, errors.New(string(buf))
	}

	return buf, err
}
