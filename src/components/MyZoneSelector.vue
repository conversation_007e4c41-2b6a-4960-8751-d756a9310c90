<template>
  <el-cascader
      :clearable="clearable"
      v-model="value"
      :options="options"
      :props="selectProps"
  />
</template>

<script setup>
import {computed, onMounted, ref} from 'vue'
import {zones} from '../api/modules'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array]
  },
  multiple: Boolean,
  level: Number,
  clearable: Boolean,
})
const emit = defineEmits(['update:modelValue'])
const options = ref([])
const selectProps = computed(() => {
  return {multiple: props.multiple}
})

const value = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

onMounted(() => {
  const list = []

  zones({level: props.level}).then(res => {
    for (const province of res.data.list) {
      const p = {
        value: province.id,
        label: province.name,
        children: []
      }
      // if (!province.list || province.list.length === 0) {
      //   continue
      // }
      for (const c of province.list) {
        p.children.push({
          value: c.id,
          label: c.name
        })
      }

      list.push(p)
    }

    options.value = list
  })
})

</script>
