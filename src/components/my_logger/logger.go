package my_logger

import (
	"fmt"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"os"
	"roadtrip-api/src/config"
	"time"
)

var logger *zap.Logger

func InitLogger() {
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:  "ts",
		LevelKey: "level",
		NameKey:  "name",
		//CallerKey:      "line",
		MessageKey: "msg",
		//FunctionKey:    "func",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
		EncodeName:     zapcore.FullNameEncoder,
		EncodeTime:     zapcore.TimeEncoderOfLayout(time.DateTime),
	}
	atomicLevel := zap.NewAtomicLevel()
	if config.IsDebug() {
		atomicLevel.SetLevel(zapcore.DebugLevel)
	} else {
		atomicLevel.SetLevel(zapcore.InfoLevel)
	}

	var writers []zapcore.WriteSyncer
	if config.Config.App.Log.LogStdout {
		writers = append(writers, os.Stdout)
	}
	if config.Config.App.Log.LogPath != "" {
		f, err := os.OpenFile(config.Config.App.Log.LogPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			panic(fmt.Errorf("open log file error: %s", err.Error()))
		}
		writers = append(writers, f)
	}
	if len(writers) == 0 {
		writers = append(writers, os.Stdout)
	}

	zapCore := zapcore.NewCore(
		zapcore.NewJSONEncoder(encoderConfig),
		zapcore.NewMultiWriteSyncer(writers...),
		atomicLevel)
	logger = zap.New(zapCore, zap.AddCaller())
}
func Debug(msg string) {
	logger.Debug(msg)
}

func Debugf(format string, arg ...zap.Field) {
	logger.Debug(format, arg...)
}

func Info(msg string) {
	logger.Info(msg)
}

func Infof(format string, arg ...zap.Field) {
	logger.Info(format, arg...)
}

func Errorf(format string, arg ...zap.Field) {
	logger.Error(format, arg...)
}

func Error(msg string) {
	logger.Error(msg)
}
