<template>
  <el-select v-model="value" :clearable="props.clearable">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"/>
  </el-select>
</template>
<script setup>
import {defineEmits, onMounted, ref, watch} from 'vue'
import {otalist} from '../api/modules/index'

const props = defineProps({
  clearable: {type: Boolean, default: true},
})
const emit = defineEmits(['change'])

const options = ref([])

const value = defineModel()

onMounted(() => {
  const list = []

  otalist().then(res => {
    options.value = res.data.list
  })
})

watch(value, (v) => {
  emit('change', options.value.find(item => item.value == v))
})

</script>