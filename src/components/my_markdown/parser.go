package my_markdown

import (
	"github.com/duke-git/lancet/v2/strutil"
	"regexp"
	"roadtrip-api/src/utils/typeset"
	"strings"
)

type NodeType int

const (
	NodeRoot     NodeType = 1 + iota
	NodeSection           //【推荐酒店】
	NodeHeading1          //# 标题
	NodeHeading2          //## 标题
	NodeHeading3          //### 标题
	NodeHeading4          //#### 标题
	NodeHeading5          //##### 标题

	NodeQuote     NodeType = 10 + iota //> 引用文本
	NodeCodeBlock                      //```代码块```
	NodeImage                          //![图片](图片地址)
	NodeLink                           //[链接](链接地址)
	NodeList                           // - 文本
	NodeText                           //文本
)

type MarkdownNode struct {
	parent *MarkdownNode

	NodeType NodeType
	Text     string
	Url      string //图片、链接地址
	Children []*MarkdownNode
}

type Parser struct {
	blockNodes *typeset.TypeSet[NodeType]
	regexps    map[NodeType]*regexp.Regexp
}

func NewParser() *Parser {
	return &Parser{
		regexps: map[NodeType]*regexp.Regexp{
			NodeSection:   regexp.MustCompile("^\\s*【(.+)】\\s*$"),
			NodeHeading1:  regexp.MustCompile("^\\s*#\\s*([^#\\s].*)\\s*$"),
			NodeHeading2:  regexp.MustCompile("^\\s*##\\s*([^#\\s].*)\\s*$"),
			NodeHeading3:  regexp.MustCompile("^\\s*###\\s*([^#\\s].*)\\s*$"),
			NodeHeading4:  regexp.MustCompile("^\\s*####\\s*([^#\\s].*)\\s*$"),
			NodeHeading5:  regexp.MustCompile("^\\s*#####\\s*([^#\\s].*)\\s*$"),
			NodeQuote:     regexp.MustCompile("^\\s*>\\s*(.+)\\s*$"),
			NodeLink:      regexp.MustCompile("^\\s*\\[([^]]+)]\\(([^)]+)\\)\\s*$"),
			NodeImage:     regexp.MustCompile("^\\s*!\\[([^]]+)]\\(([^)]+)\\)\\s*$"),
			NodeList:      regexp.MustCompile("^\\s*-\\s*(.+)\\s*$"),
			NodeCodeBlock: regexp.MustCompile("^\\s*```.*\\s*$"),
		},
		blockNodes: typeset.NewTypeSet(false, NodeCodeBlock),
	}
}

func (o *Parser) Parse(content string) *MarkdownNode {
	lines := strings.Split(content, "\n")
	root := &MarkdownNode{NodeType: NodeRoot}
	o.readLines(root, lines)
	return root
}

func (o *Parser) readLines(parent *MarkdownNode, lines []string) *MarkdownNode {
	for len(lines) > 0 {
		line := lines[0]
		line = strutil.Trim(line)
		if strutil.IsBlank(line) {
			lines = lines[1:]
			continue
		}
		var m *MarkdownNode
		for nodeType, rg := range o.regexps {
			mat := rg.FindStringSubmatch(line)
			if len(mat) > 0 {
				m = &MarkdownNode{
					NodeType: nodeType,
					parent:   parent,
				}
				if len(mat) > 1 {
					m.Text = mat[1]
				}
				if nodeType == NodeImage || nodeType == NodeLink && len(mat) > 2 {
					m.Url = mat[2]
				}
			}
		}
		if m != nil {
			//当前节点小于父节点(即当前节点优先级高于父节点)，向上查到更高级的节点
			for m.NodeType <= parent.NodeType && parent.NodeType > NodeRoot {
				parent = parent.parent
			}
			m.parent = parent
		} else {
			m = &MarkdownNode{
				NodeType: NodeText,
				Text:     line,
				parent:   parent,
			}
		}
		parent.Children = append(parent.Children, m)
		// 多行解析
		if o.blockNodes.Has(m.NodeType) {
			var str strings.Builder
			str.WriteString(line)
			str.WriteByte('\n')
			lines = lines[1:]
			for len(lines) > 0 {
				line := lines[0]
				line = strutil.Trim(line)
				if strutil.IsBlank(line) {
					lines = lines[1:]
					continue
				}
				str.WriteString(line)
				str.WriteByte('\n')
				lines = lines[1:]
				if mat := o.regexps[m.NodeType].FindStringSubmatch(line); len(mat) > 0 {
					break
				}
			}
			m.Text = str.String()
		}
		//非文本节点可以作为父节点
		if m.NodeType < NodeQuote {
			parent = m
		}
		return o.readLines(parent, lines[1:])
	}
	return nil
}
