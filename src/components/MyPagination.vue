<script setup>

const props = defineProps({
  total: {
    type: Number,
    default: 0
  },
  pageSize: {
    type: Number,
    default: 20
  },
  page: {
    type: Number,
    default: 1
  }
})

const emit = defineEmits(['currentChange'])

function onCurrentChange (page) {
  emit('currentChange', page)
}

</script>

<template>
  <div class="mt-4">
    <el-pagination
        :default-current-page="props.page"
        :page-size="props.pageSize"
        :total="props.total"
        background
        layout="prev, pager, next,total"
        size="small"
        @current-change="onCurrentChange"/>
  </div>

</template>

<style lang="scss" scoped>
.mt-4 {
  margin-top: 10px;

  display: flex;
  justify-content: flex-end;
}
</style>
