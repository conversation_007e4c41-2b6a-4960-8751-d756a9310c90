package my_dify

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io"
	"net/http"
	"net/url"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/utils"
)

type DifyChatRequest struct {
	User             string         `json:"user"`
	Query            string         `json:"query"`
	Inputs           map[string]any `json:"inputs"`
	ConversationId   string         `json:"conversation_id"`
	ResponseMode     string         `json:"response_mode"`      //streaming/blocking
	AutoGenerateName bool           `json:"auto_generate_name"` //自动生成标题,默认 true。 若设置为 false，则可通过调用会话重命名接口
}

type DifyWorkResponse struct {
	Data struct {
		Outputs json.RawMessage `json:"outputs"`
	} `json:"data"`
}

type DifyChatResponse struct {
	Answer string `json:"answer"`
}

type DifyResponse struct {
	Request  *http.Request
	Response *http.Response
}

type DifyChatStreamResponse struct {
	DifyResponse
	Reader *bufio.Reader
}

type DifySpiderScene struct {
	Name         string `json:"name"`
	OpeningHours []struct {
		Period        string `json:"period"`
		OpenTime      string `json:"open_time"`
		CloseTime     string `json:"close_time"`
		LastEntryTime string `json:"last_entry_time"`
	} `json:"opening_hours"`
	Tags                  []string              `json:"tags"`
	TicketPrice           beans.ScenicPrices    `json:"ticket_price"`
	RecommendedDuration   beans.ScenicPlayCosts `json:"recommended_duration"`
	BestSeasons           []string              `json:"best_seasons"`
	RecommendedActivities []string              `json:"recommended_activities"`
	AdditionalInfo        struct {
		Website         string `json:"website"`
		BookingRequired bool   `json:"booking_required"`
		WarningNotes    string `json:"warning_notes"`
	} `json:"additional_info"`
}

func doRequest(apiKey string, ctx context.Context, method constmap.HttpMethod, path string, query *url.Values,
	data any, headers http.Header) (*DifyResponse, error) {

	var b = &bytes.Buffer{}
	if data != nil {
		bb, _ := convertor.ToBytes(data)
		_, _ = b.Write(bb)
	}

	var u string
	if query == nil {
		u = fmt.Sprintf("%s%s", config.Config.Dify.Uri, path)
	} else {
		u = fmt.Sprintf("%s%s?%s", config.Config.Dify.Uri, path, query.Encode())
	}
	req, err := http.NewRequestWithContext(ctx, string(method), u, b)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiKey))
	req.Header.Set("Content-Type", "application/json")
	if headers != nil {
		maputil.ForEach(headers, func(key string, value []string) {
			slice.ForEach(value, func(_ int, v string) {
				req.Header.Add(key, v)
			})
		})
	}
	rsp, err := http.DefaultClient.Do(req)
	response := &DifyResponse{
		Request:  req,
		Response: rsp,
	}
	if err != nil {
		return response, err
	}
	if rsp.StatusCode < http.StatusOK || rsp.StatusCode >= http.StatusBadRequest {
		bd, _ := io.ReadAll(rsp.Body)
		return response, fmt.Errorf("[status %d]%s", rsp.StatusCode, bd)
	}
	return response, nil
}

func robotWorkflow[T any](ctx context.Context, key string, inputs *gin.H, out *T) error {
	rsp, err := doRequest(key, ctx, constmap.HttpMethodPost, "/v1/workflows/run", nil, gin.H{
		"inputs":        inputs,
		"response_mode": "blocking", //streaming/blocking
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return err
	}

	var d DifyWorkResponse
	b, _ := io.ReadAll(rsp.Response.Body)
	if err = json.Unmarshal(b, &d); err != nil {
		return err
	}

	my_logger.Debugf("robotWorkflow", zap.ByteString("outputs", d.Data.Outputs))

	if err = json.Unmarshal(d.Data.Outputs, &out); err != nil {
		return errors.Wrap(err, "outputs Unmarshal")
	}
	return nil
}

func robotChat[T any](ctx context.Context, apiKey, prompt string, inputs any, out *T) (*T, error) {
	my_logger.Debugf("robotChat request", zap.String("apiKey", apiKey), zap.String("prompt", prompt), zap.Any("inputs", inputs))
	rsp, err := doRequest(apiKey, ctx, constmap.HttpMethodPost, "/v1/chat-messages", nil, gin.H{
		"inputs":        inputs,
		"query":         prompt,
		"response_mode": "blocking",
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)

	my_logger.Debugf("robotChat", zap.String("apiKey", apiKey), zap.String("body", string(b)))

	resp := new(DifyChatResponse)
	if err = json.Unmarshal(b, resp); err != nil {
		return nil, err
	} else if len(resp.Answer) == 0 {
		return nil, errors.New("no answer")
	}

	resp.Answer = utils.RemoveJsonTail(resp.Answer)

	err = json.Unmarshal([]byte(resp.Answer), out)
	return out, err
}

// 流式对话
func ChatStreaming(ctx context.Context, db *gorm.DB, request *DifyChatRequest) (*DifyChatStreamResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyChatKey)
	if err != nil {
		return nil, err
	}
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/chat-messages", nil, request, nil)
	if err != nil {
		return nil, err
	}
	return &DifyChatStreamResponse{
		DifyResponse: *rsp,
		Reader:       bufio.NewReader(rsp.Response.Body),
	}, nil
}

// 流式对话:推荐酒店
func ChatStreamingHotel(ctx context.Context, db *gorm.DB, request *DifyChatRequest) (*DifyChatStreamResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyHotelChatKey)
	if err != nil {
		return nil, err
	}
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/chat-messages", nil, request, nil)
	if err != nil {
		return nil, err
	}
	return &DifyChatStreamResponse{
		DifyResponse: *rsp,
		Reader:       bufio.NewReader(rsp.Response.Body),
	}, nil
}

// 获取下一条对话建议
func ChatSuggestions(ctx context.Context, db *gorm.DB, user string, messageId string) ([]string, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyChatKey)
	if err != nil {
		return nil, err
	}
	q := &url.Values{}
	q.Set("user", user)
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodGet, fmt.Sprintf("/v1/messages/%s/suggested", messageId), q, nil, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	type bd struct {
		Data []string `json:"data"`
	}
	r := &bd{
		Data: make([]string, 0),
	}
	_ = json.Unmarshal(b, r)
	my_logger.Debugf("ChatSuggestions", zap.String("messageId", messageId), zap.ByteString("body", b))
	return r.Data, nil
}

// 获取景点数据
func SpiderScene(ctx context.Context, db *gorm.DB, scene string) (*DifySpiderScene, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifySpiderSceneKey)
	if err != nil {
		return nil, err
	}
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/workflows/run", nil, gin.H{
		"inputs": gin.H{
			"scene_name": scene,
		},
		"response_mode": "blocking", //streaming/blocking
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return nil, err
	}

	type s struct {
		SceneInfo string `json:"scene_info"`
	}
	var d DifyWorkResponse
	b, _ := io.ReadAll(rsp.Response.Body)
	if err = json.Unmarshal(b, &d); err != nil {
		return nil, err
	}

	my_logger.Debugf("SpiderScene", zap.ByteString("outputs", d.Data.Outputs))

	var dd s
	if err = json.Unmarshal(d.Data.Outputs, &dd); err != nil {
		return nil, errors.Wrap(err, "outputs Unmarshal")
	}
	type ss struct {
		ScenicSpot DifySpiderScene `json:"scenic_spot"`
	}
	sceneInfo := new(ss)
	err = json.Unmarshal([]byte(dd.SceneInfo), sceneInfo)
	if err != nil {
		return nil, errors.Wrap(err, "ScenicSpot unmarshal")
	}
	if strutil.ContainsAny(sceneInfo.ScenicSpot.TicketPrice.Child.Discount, []string{"未知", "暂未"}) {
		sceneInfo.ScenicSpot.TicketPrice.Child.Discount = ""
	}
	if strutil.ContainsAny(sceneInfo.ScenicSpot.TicketPrice.Senior.Discount, []string{"未知", "暂未"}) {
		sceneInfo.ScenicSpot.TicketPrice.Senior.Discount = ""
	}
	return &sceneInfo.ScenicSpot, nil
}

// 生成机器人行程
func MakePlan(ctx context.Context, db *gorm.DB, num int, sysPrompt string) (string, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyRobotPlanKey)
	if err != nil {
		return "", err
	}
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/workflows/run", nil, gin.H{
		"inputs": gin.H{
			"num":        num,
			"sys_prompt": sysPrompt,
		},
		"response_mode": "blocking",
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return "", err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	my_logger.Debugf("MakePlan", zap.String("sysPrompt", sysPrompt), zap.String("body", string(b)))
	resp := &DifyWorkResponse{}
	if err = json.Unmarshal(b, resp); err != nil {
		return "", err
	}
	type s struct {
		TravelInfo string `json:"travel_info"`
	}
	var dd s
	if err = json.Unmarshal(resp.Data.Outputs, &dd); err != nil {
		return "", err
	}

	return dd.TravelInfo, nil
}

// 生成心愿单行程
func MakeWishPlan(ctx context.Context, db *gorm.DB, sysPrompt string) (string, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyMakeWishPlanKey)
	if err != nil {
		return "", err
	}
	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/workflows/run", nil, gin.H{
		"inputs": gin.H{
			"sys_prompt": sysPrompt,
		},
		"response_mode": "blocking",
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return "", err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	my_logger.Debugf("MakePlan", zap.String("sysPrompt", sysPrompt), zap.String("body", string(b)))
	resp := &DifyWorkResponse{}
	if err = json.Unmarshal(b, resp); err != nil {
		return "", err
	}
	type s struct {
		TravelInfo string `json:"travel_info"`
	}
	var dd s
	if err = json.Unmarshal(resp.Data.Outputs, &dd); err != nil {
		return "", err
	}

	return dd.TravelInfo, nil
}

// 获取首页推荐语
func RecPrompt(ctx context.Context, db *gorm.DB, city string, num int) ([]string, error) {

	my_logger.Debugf("RecPrompt request", zap.String("city", city), zap.Int("num", num))

	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyRecPromptsKey)
	if err != nil {
		return nil, err
	}

	rsp, err := doRequest(key.(string), ctx, constmap.HttpMethodPost, "/v1/workflows/run", nil, gin.H{
		"inputs": gin.H{
			"num":  num,
			"city": city,
		},
		"response_mode": "blocking",
		"user":          convertor.ToString(constmap.RobotUserId),
	}, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)

	my_logger.Debugf("RecPrompt", zap.String("body", string(b)))

	resp := &DifyWorkResponse{}
	if err = json.Unmarshal(b, resp); err != nil {
		return nil, err
	}
	type s struct {
		Topics string `json:"topics"`
	}
	var dd s
	if err = json.Unmarshal(resp.Data.Outputs, &dd); err != nil {
		return nil, err
	}
	return strutil.SplitAndTrim(dd.Topics, "\n"), nil
}

type PlanPdfPrimeZoneResponse struct {
	PrimeZone  beans.PlanPdfPrimeZone `json:"prime_zone"` //主要城市信息
	Attentions []string               `json:"attentions"` //出行注意事项
}

func PlanPdfPrimeZone(ctx context.Context, db *gorm.DB, prompt string, jsonInput any) (*PlanPdfPrimeZoneResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanPdfKey)
	if err != nil {
		return nil, err
	}
	if jsonInput != nil {
		jsonInput = gin.H{
			"json_input": convertor.ToString(jsonInput),
		}
	}
	out := &PlanPdfPrimeZoneResponse{}
	return robotChat[PlanPdfPrimeZoneResponse](ctx, key.(*beans.PlanPdfDifyKeys).PrimeZone, prompt, gin.H{"json_input": jsonInput}, out)
}

type PlanPdfPrimeScenesResponse struct {
	Scenes []beans.PlanPdfPrimeScene `json:"scenes"`
}

func PlanPdfPrimeScenes(ctx context.Context, db *gorm.DB, prompt string, jsonInput any) (*PlanPdfPrimeScenesResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanPdfKey)
	if err != nil {
		return nil, err
	}
	if jsonInput != nil {
		jsonInput = gin.H{
			"json_input": convertor.ToString(map[string]any{
				"scenes": jsonInput,
			}),
		}
	}
	out := &PlanPdfPrimeScenesResponse{}
	return robotChat[PlanPdfPrimeScenesResponse](ctx, key.(*beans.PlanPdfDifyKeys).PrimeScenes, prompt, jsonInput, out)
}

type PlanPdfSectionResponse struct {
	Section beans.PlanPdfSection `json:"section"`
}

func PlanPdfSection(ctx context.Context, db *gorm.DB, prompt string, jsonInput any) (*PlanPdfSectionResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanPdfKey)
	if err != nil {
		return nil, err
	}
	if jsonInput != nil {
		jsonInput = gin.H{
			"json_input": convertor.ToString(map[string]any{
				"section": jsonInput,
			}),
		}
	}
	out := &PlanPdfSectionResponse{}
	return robotChat[PlanPdfSectionResponse](ctx, key.(*beans.PlanPdfDifyKeys).Section, prompt, jsonInput, out)
}

type PlanPdfZoneResponse struct {
	Zone beans.PlanPdfZone `json:"zone"`
}

// 城市民俗文化、美食、特产
func PlanPdfZone(ctx context.Context, db *gorm.DB, prompt string, jsonInput any) (*PlanPdfZoneResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanPdfKey)
	if err != nil {
		return nil, err
	}
	if jsonInput != nil {
		jsonInput = gin.H{
			"json_input": convertor.ToString(map[string]any{
				"zone": jsonInput,
			}),
		}
	}
	out := &PlanPdfZoneResponse{}
	return robotChat[PlanPdfZoneResponse](ctx, key.(*beans.PlanPdfDifyKeys).Zone, prompt, jsonInput, out)
}

type ZoneSpecialityResponse struct {
	Zone beans.ZoneSpecialityData `json:"zone"`
}

// 城市民俗文化、美食、特产
func ZoneSpeciality(ctx context.Context, db *gorm.DB, prompt string) (*ZoneSpecialityResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyZoneSpecialityKey)
	if err != nil {
		return nil, err
	}

	out := &ZoneSpecialityResponse{}
	return robotChat[ZoneSpecialityResponse](ctx, key.(string), prompt, nil, out)
}

type HotelRecReasonResponse struct {
	RecommendReason string `json:"recommend_reason"`
}

// 酒店推荐理由
func HotelRecommendReason(ctx context.Context, db *gorm.DB, prompt string) (*HotelRecReasonResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyHotelRecReasonKey)
	if err != nil {
		return nil, err
	}
	var out = new(HotelRecReasonResponse)
	err = robotWorkflow(ctx, key.(string), &gin.H{
		"hotel_info": prompt,
	}, out)
	return out, err
}

type PlanHotTagsResponse struct {
	CityTags []string `json:"city_tags"`
}

// 当月热门行程标签
func PlanHotTags(ctx context.Context, db *gorm.DB, city, date string) (*PlanHotTagsResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanHotTagsKey)
	if err != nil {
		return nil, err
	}
	var mp = struct {
		CityTags string `json:"city_tags"`
	}{}
	err = robotWorkflow(ctx, key.(string), &gin.H{
		"city": city,
		"date": date,
	}, &mp)
	if err != nil {
		return nil, err
	}
	var out = &PlanHotTagsResponse{
		CityTags: make([]string, 0),
	}
	mp.CityTags = utils.RemoveJsonTail(mp.CityTags)
	err = json.Unmarshal([]byte(mp.CityTags), &out.CityTags)
	return out, err
}

type PlanTagsResponse struct {
	MainTags   []string `json:"main_tags"`
	DeputyTags []string `json:"deputy_tags"`
}

// 获取行程标签
func PlanTags(ctx context.Context, db *gorm.DB, prompt string) (*PlanTagsResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyPlanTagsKey)
	if err != nil {
		return nil, err
	}

	type outT struct {
		TravelTags string `json:"travel_tags"`
	}
	out := &outT{}

	err = robotWorkflow[outT](ctx, key.(string), &gin.H{
		"travel_info": prompt,
	}, out)
	if err != nil {
		return nil, err
	}
	rsp := &PlanTagsResponse{}
	if strutil.IsBlank(out.TravelTags) {
		return nil, fmt.Errorf("PlanTags empty response")
	} else if err = json.Unmarshal([]byte(utils.RemoveJsonTail(out.TravelTags)), &rsp); err != nil {
		return nil, err
	} else {
		return rsp, nil
	}
}

type HotZonesResponse struct {
	Cities []string `json:"cities"`
}

// 热门城市
func HotZones(ctx context.Context, db *gorm.DB) (*HotZonesResponse, error) {

	my_logger.Debugf("HotZones request")

	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyHotZonesKey)
	if err != nil {
		return nil, err
	}

	type outT struct {
		Output string `json:"output"`
	}
	var out = &outT{}

	if err = robotWorkflow[outT](ctx, key.(string), &gin.H{}, out); err != nil {
		return nil, err
	}

	rsp := &HotZonesResponse{}
	err = json.Unmarshal([]byte(utils.RemoveJsonTail(out.Output)), rsp)
	return rsp, err
}

type WishTodosResponse struct {
	WantToDo []string `json:"want_to_do"`
}

// 心愿单想做的事
func WishTodos(ctx context.Context, db *gorm.DB, prompt string) (*WishTodosResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyWishTodosKey)
	if err != nil {
		return nil, err
	}

	type outT struct {
		Output string `json:"output"`
	}
	var out outT

	err = robotWorkflow[outT](ctx, key.(string), &gin.H{
		"request_input": prompt,
	}, &out)
	if err != nil {
		return nil, err
	}
	rsp := &WishTodosResponse{}
	err = json.Unmarshal([]byte(utils.RemoveJsonTail(out.Output)), rsp)
	return rsp, err
}

type WishSubjectResponse struct {
	Subject string `json:"subject"`
}

// 心愿单主题
func WishSubject(ctx context.Context, db *gorm.DB, prompt string) (*WishSubjectResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyWishSubjectKey)
	if err != nil {
		return nil, err
	}

	type outT struct {
		Output string `json:"output"`
	}
	var out outT

	err = robotWorkflow[outT](ctx, key.(string), &gin.H{
		"wish_info": prompt,
	}, &out)
	if err != nil {
		return nil, err
	}
	return &WishSubjectResponse{out.Output}, nil
}

type WishDescResponse struct {
	Desc string `json:"desc"`
}

// 心愿单主题
func WishDesc(ctx context.Context, db *gorm.DB, prompt string) (*WishDescResponse, error) {
	key, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyWishDescKey)
	if err != nil {
		return nil, err
	}

	type outT struct {
		Output string `json:"output"`
	}
	var out outT

	err = robotWorkflow[outT](ctx, key.(string), &gin.H{
		"wish_info": prompt,
	}, &out)
	if err != nil {
		return nil, err
	}
	return &WishDescResponse{out.Output}, nil
}
