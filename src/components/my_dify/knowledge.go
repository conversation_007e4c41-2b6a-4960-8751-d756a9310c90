package my_dify

import (
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"io"
	"net/url"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"

	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type KnowledgeBase struct {
	db *gorm.DB
}

func NewKnowledgeBase(db *gorm.DB) *KnowledgeBase {
	return &KnowledgeBase{
		db: db,
	}
}

type CreateDocumentResponse struct {
	Document struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	} `json:"document"`
}

func (o *KnowledgeBase) CreateDocumentByText(ctx context.Context, apiKey, datasetId, docName, text string) (*CreateDocumentResponse, error) {
	rsp, err := doRequest(apiKey, ctx, constmap.HttpMethodPost, fmt.Sprintf("/v1/datasets/%s/document/create-by-text", datasetId), nil, gin.H{
		"text":               text,
		"name":               docName,
		"indexing_technique": "high_quality",
		"doc_form":           "hierarchical_model",
		"process_rule": gin.H{
			"mode": "hierarchical",
			"rules": gin.H{
				"pre_processing_rules": []gin.H{
					{"id": "remove_extra_spaces", "enabled": true},
				},
				"segmentation": gin.H{
					"separator":  "---",
					"max_tokens": 4000,
				},
				"parent_mode": "paragraph",
				"subchunk_segmentation": gin.H{
					"separator":  "\n",
					"max_tokens": 2048,
				},
			},
		},
	}, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	resp := &CreateDocumentResponse{}
	err = json.Unmarshal(b, resp)
	return resp, err
}

type KnowledgeSegment struct {
	Id         string `json:"id"`
	DocumentId string `json:"document_id"`
	Content    string `json:"content"`
}
type QuerySegmentsResponse struct {
	Data    []KnowledgeSegment `json:"data"`
	HasMore bool               `json:"has_more"`
}

func (o *KnowledgeBase) QuerySegments(ctx context.Context, key beans.DifyKnowledgeKey, keyword string, page int) (*QuerySegmentsResponse, error) {
	rsp, err := doRequest(key.ApiKey, ctx, constmap.HttpMethodGet, fmt.Sprintf("/v1/datasets/%s/documents/%s/segments", key.StoreId, key.DocId), &url.Values{
		"keyword": []string{keyword},
		"page":    []string{convertor.ToString(page)},
	}, nil, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	resp := &QuerySegmentsResponse{}
	if err := json.Unmarshal(b, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

type CreateSegmentsResponse struct {
	Data []KnowledgeSegment `json:"data"`
}

func (o *KnowledgeBase) CreateSegments(ctx context.Context, key beans.DifyKnowledgeKey, segments []any) (*CreateSegmentsResponse, error) {
	my_logger.Debugf("CreateSegments", zap.String("storeId", key.StoreId), zap.String("docId", key.DocId), zap.Any("segments", segments))
	rsp, err := doRequest(key.ApiKey, ctx, constmap.HttpMethodPost, fmt.Sprintf("/v1/datasets/%s/documents/%s/segments", key.StoreId, key.DocId), nil, gin.H{
		"segments": slice.Map(segments, func(_ int, item any) gin.H {
			return gin.H{"content": convertor.ToString(item)}
		}),
	}, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	resp := &CreateSegmentsResponse{}
	if err := json.Unmarshal(b, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

type UpdateSegmentResponse struct {
	Data KnowledgeSegment `json:"data"`
}

func (o *KnowledgeBase) UpdateSegment(ctx context.Context, key beans.DifyKnowledgeKey, segId string, segment any) (*UpdateSegmentResponse, error) {
	my_logger.Debugf("UpdateSegment", zap.String("storeId", key.StoreId), zap.String("docId", key.DocId), zap.String("segId", segId), zap.Any("segment", segment))
	rsp, err := doRequest(key.ApiKey, ctx, constmap.HttpMethodPost, fmt.Sprintf("/v1/datasets/%s/documents/%s/segments/%s", key.StoreId, key.DocId, segId), nil, gin.H{
		"segment": gin.H{"content": convertor.ToString(segment)},
	}, nil)
	if err != nil {
		return nil, err
	}
	b, _ := io.ReadAll(rsp.Response.Body)
	resp := &UpdateSegmentResponse{}
	if err := json.Unmarshal(b, resp); err != nil {
		return nil, err
	}
	return resp, nil
}

func (o *KnowledgeBase) DeleteSegment(ctx context.Context, key beans.DifyKnowledgeKey, segId string) error {
	_, err := doRequest(key.ApiKey, ctx, constmap.HttpMethodPost, fmt.Sprintf("/v1/datasets/%s/documents/%s/segments/%s", key.StoreId, key.DocId, segId), nil, nil, nil)
	return err
}
