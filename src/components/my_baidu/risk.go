package my_baidu

import (
	"encoding/json"
	"github.com/duke-git/lancet/v2/strutil"

	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"

	"go.uber.org/zap"

	"github.com/Baidu-AIP/golang-sdk/aip/censor"
)

type RiskClient struct {
	client *censor.ContentCensorClient
}

func NewRiskClient() *RiskClient {
	return &RiskClient{
		client: censor.NewClient(config.Config.BaiduRisk.ApiKey, config.Config.BaiduRisk.ApiSecret),
	}
}

type ConclusionType int

const (
	ConclusionOk        ConclusionType = 1
	ConclusionNotOk     ConclusionType = 2
	ConclusionSuspect   ConclusionType = 3
	ConclusionAuditFail ConclusionType = 4
)

// ImageCensorResponse 百度云图像审核响应结构体
type ImageCensorResponse struct {
	LogID          int64             `json:"log_id"`         // 请求唯一id，用于问题排查
	ErrorCode      int64             `json:"error_code"`     // 错误提示码，失败才返回，成功不返回
	ErrorMsg       string            `json:"error_msg"`      // 错误提示信息，失败才返回，成功不返回
	Conclusion     string            `json:"conclusion"`     // 审核结果，可取值：合规、不合规、疑似、审核失败
	ConclusionType ConclusionType    `json:"conclusionType"` // 审核结果类型，1：合规，2：不合规，3：疑似，4：审核失败
	Data           []ImageCensorData `json:"data"`           // 不合规/疑似/命中白名单项详细信息
	IsHitMd5       bool              `json:"isHitMd5"`       // 是否命中人审违规数据
	PhoneRisk      *PhoneRiskInfo    `json:"phoneRisk"`      // 是否命中手机号风险检测
}

// ImageCensorData 审核详细数据结构
type ImageCensorData struct {
	ErrorCode      int64          `json:"error_code"`     // 内层错误提示码
	ErrorMsg       string         `json:"error_msg"`      // 内层错误提示信息
	Type           int            `json:"type"`           // 结果具体命中的模型类型
	SubType        int            `json:"subType"`        // 审核子类型
	Conclusion     string         `json:"conclusion"`     // 审核结果
	ConclusionType int            `json:"conclusionType"` // 审核结果类型
	Msg            string         `json:"msg"`            // 不合规项描述信息
	Probability    float64        `json:"probability"`    // 不合规项置信度
	DatasetName    string         `json:"datasetName"`    // 命中所属自定义数据集名称
	Stars          []StarInfo     `json:"stars"`          // 命中人脸相关信息
	Hits           []HitInfo      `json:"hits"`           // 图文审核命中信息
	Codes          []string       `json:"codes"`          // 二维码或条形码识别结果
	Location       []LocationInfo `json:"location"`       // 命中内容的位置相关信息
	Completeness   float64        `json:"completeness"`   // 完整度（用于人脸占比等）
}

// StarInfo 人脸相关信息
type StarInfo struct {
	Name        string  `json:"name"`        // 人名
	Probability float64 `json:"probability"` // 人脸相似度
	DatasetName string  `json:"datasetName"` // 人脸所属自定义数据集名称
	Area        string  `json:"area"`        // 涉政人物所属地区（高级版专属）
	Information string  `json:"information"` // 涉政人物的职务等详细信息（高级版专属）
}

// HitInfo 图文审核命中信息
type HitInfo struct {
	Words             []string `json:"words"`             // 检文本命中词库的关键词
	Probability       float64  `json:"probability"`       // 不合规项置信度
	DatasetName       string   `json:"datasetName"`       // 违规项目所属自定义名称
	ModelHitPositions any      `json:"modelHitPositions"` // 送检文本命中模型的详细信息
	WordHitPositions  any      `json:"wordHitPositions"`  // 送检文本命中词库的详细信息
	ModelName         string   `json:"modelName"`         // 命中自定义模型名称（EasyDL）
	Score             float64  `json:"score"`             // 命中自定义模型置信度（EasyDL）
	ModelID           any      `json:"modelId"`           // 命中自定义模型ID（EasyDL）
	Label             string   `json:"label"`             // 命中自定义模型标签名称（EasyDL）
}

// LocationInfo 位置信息
type LocationInfo struct {
	Top      float64 `json:"top"`      // 命中内容与上边界的距离
	Left     float64 `json:"left"`     // 命中内容与左边界的距离
	Width    float64 `json:"width"`    // 命中内容的宽度
	Height   float64 `json:"height"`   // 命中内容的高度
	Rotation float64 `json:"rotation"` // 相对于竖直方向的顺时针旋转角度
}

// PhoneRiskInfo 手机号风险信息
type PhoneRiskInfo struct {
	PhoneRisk []RiskLabel `json:"phoneRisk"` // 手机号风险标签
	UserRisk  []RiskLabel `json:"userRisk"`  // 画像风险标签
	PhoneHash float64     `json:"phoneHash"` // sha256加密的手机号
	DeviceID  float64     `json:"deviceId"`  // 设备ID
}

// RiskLabel 风险标签
type RiskLabel struct {
	FirstLabelZH string `json:"firstLabelZH"` // 风险标签（中文）
	FirstLabelEN string `json:"firstLabelEN"` // 风险标签（英文）
	Level        int    `json:"level"`        // 风险等级，0:高风险 1:中风险 2:低风险 3:正常 4:检测失败
}

// 图片审核
func (o *RiskClient) ImageCensor(imgBase64, imgUrl string) (*ImageCensorResponse, error) {
	var rspStr string
	if !strutil.IsBlank(imgBase64) {
		rspStr = o.client.ImgCensor(imgBase64, nil)
	} else {
		rspStr = o.client.ImgCensorUrl(imgUrl, nil)
	}
	var rsp = new(ImageCensorResponse)
	err := json.Unmarshal([]byte(rspStr), rsp)
	my_logger.Infof("ImageCensor", zap.Error(err), zap.String("resp", rspStr))
	return rsp, err
}

// VideoCensorResponse 百度云视频审核响应结构体
type VideoCensorResponse struct {
	LogID                    int64                     `json:"log_id"`                   // 调用唯一ID
	ErrorCode                int64                     `json:"error_code"`               // 服务调用错误码，失败才返回，成功不返回
	ErrorMsg                 string                    `json:"error_msg"`                // 服务调用提示信息，失败才返回，成功不返回
	Conclusion               string                    `json:"conclusion"`               // 审核结果描述，可取值：合规、不合规、疑似
	ConclusionType           ConclusionType            `json:"conclusionType"`           // 审核结果，可取值：1 合规，2 不合规，3 疑似， 4 审核失败
	IsHitMd5                 bool                      `json:"isHitMd5"`                 // 是否命中视频黑库MD5提示
	Msg                      string                    `json:"msg"`                      // 命中MD5提示
	Frames                   []VideoFrame              `json:"frames"`                   // 帧审核明细
	ConclusionTypeGroupInfos []ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos"` // 审核结论汇总
}

// VideoFrame 视频帧信息
type VideoFrame struct {
	FrameTimeStamp    int64             `json:"frameTimeStamp"`    // 帧时间戳
	Conclusion        string            `json:"conclusion"`        // 帧审核结果描述，可取值：合规、不合规、疑似
	ConclusionType    ConclusionType    `json:"conclusionType"`    // 帧审核结果，可取值：1 合规，2 不合规，3 疑似， 4 审核失败
	FrameUrl          string            `json:"frameUrl"`          // 帧url地址
	FrameThumbnailUrl string            `json:"frameThumbnailUrl"` // 帧缩略图url地址
	Data              []ImageCensorData `json:"data"`              // 各维度明细审核结果
}

// ConclusionTypeGroupInfo 审核结论汇总
type ConclusionTypeGroupInfo struct {
	Msg             string        `json:"msg"`             // 一级违规类型描述信息
	TypeInfo        TypeInfo      `json:"typeInfo"`        // type：一级违规类型描述
	SubTypeInfoList []SubTypeInfo `json:"subTypeInfoList"` // 二级违规列表
}

// TypeInfo 一级违规类型
type TypeInfo struct {
	Type string `json:"type"` // 一级违规类型描述
}

// SubTypeInfo 二级违规信息
type SubTypeInfo struct {
	SubType   string `json:"subType"`   // 二级违规类型描述
	Timestamp int64  `json:"timestamp"` // 帧时间戳
}

// 视频审核
func (o *RiskClient) VideoCensor(videoUrl string) (*VideoCensorResponse, error) {
	rspStr := o.client.VideoCensor("", videoUrl, "", nil)
	var rsp = new(VideoCensorResponse)
	err := json.Unmarshal([]byte(rspStr), rsp)
	my_logger.Infof("VideoCensor", zap.String("url", videoUrl), zap.Error(err), zap.String("resp", rspStr))
	return rsp, err
}

// TextCensorResponse 百度云文本审核响应结构体
type TextCensorResponse struct {
	LogID          int64            `json:"log_id"`         // 请求唯一id，用于问题排查
	ErrorCode      int64            `json:"error_code"`     // 错误提示码，失败才返回，成功不返回
	ErrorMsg       string           `json:"error_msg"`      // 错误提示信息，失败才返回，成功不返回
	Conclusion     string           `json:"conclusion"`     // 审核结果，可取值：合规、不合规、疑似、审核失败
	ConclusionType ConclusionType   `json:"conclusionType"` // 审核结果类型，1：合规，2：不合规，3：疑似，4：审核失败
	Data           []TextCensorData `json:"data"`           // 不合规/疑似/命中白名单项详细信息
	PhoneRisk      *PhoneRiskInfo   `json:"phoneRisk"`      // 是否命中手机号风险检测
}

// TextCensorData 文本审核详细数据结构
type TextCensorData struct {
	ErrorCode      int64     `json:"error_code"`     // 内层错误提示码
	ErrorMsg       string    `json:"error_msg"`      // 内层错误提示信息
	Type           int       `json:"type"`           // 审核主类型，11：百度官方违禁词库、12：文本反作弊、13:自定义文本黑名单、14:自定义文本白名单
	SubType        int       `json:"subType"`        // 审核子类型
	Conclusion     string    `json:"conclusion"`     // 审核结果
	ConclusionType int       `json:"conclusionType"` // 审核结果类型
	Msg            string    `json:"msg"`            // 不合规项描述信息
	Hits           []HitInfo `json:"hits"`           // 送检文本违规原因的详细信息
}

// 文本审核
func (o *RiskClient) TextCensor(text string) (*TextCensorResponse, error) {
	rspStr := o.client.TextCensor(text)
	var rsp = new(TextCensorResponse)
	err := json.Unmarshal([]byte(rspStr), rsp)
	my_logger.Infof("TextCensor", zap.String("text", text), zap.Error(err), zap.String("resp", rspStr))
	return rsp, err
}
