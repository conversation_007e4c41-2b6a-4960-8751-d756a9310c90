<script setup>

import {computed, onMounted, ref, toRaw, watch} from "vue";
import MyProductSelector from "./MyProductSelector.vue";
import {productDetail} from "../api/modules/product"

const props = defineProps({
  gift: <PERSON><PERSON><PERSON>,
})
const emit = defineEmits(['close'])
const show = ref(true)
const title = computed(() => props.gift ? '选择赠品和数量' : '选择商品和数量')
const form = ref({
  product_id: '',
  quantity: 10,
  name: '',
  gift: false,
  price: 10,
})

function handleOk() {
  if (form.value.product_id) {
    productDetail(form.value.product_id).then(res => {
      const {data} = res
      form.value = Object.assign({}, form.value, {
        name: data.name,
        price: data.price
      })

      show.value = false
    })
  } else {
    show.value = false
  }
}

onMounted(() => {
  form.value.gift = props.gift
})

watch(show, () => {
  if (form.value.product_id) {
    const data = toRaw(form.value)

    emit('close', data)
  } else {
    emit('close')
  }
})

</script>

<template>
  <el-dialog v-model="show" :title="title" append-to-body>
    <el-form label-width="80">
      <el-form-item label="类型">
        <el-radio-group v-model="form.gift">
          <el-radio-button :label="false">商品</el-radio-button>
          <el-radio-button :label="true">赠品</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="选择商品">
        <MyProductSelector v-model="form.product_id" :gift="form.gift"/>
      </el-form-item>
      <el-form-item label="数量">
        <el-input-number v-model="form.quantity"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="show = false">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>

</style>
