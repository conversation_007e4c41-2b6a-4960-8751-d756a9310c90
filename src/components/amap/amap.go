package amap

import (
	"fmt"
	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/slice"
	"gorm.io/gorm"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"sync"
)

var mySdk *go_amap.AmapClient
var once sync.Once

func initSdk() {
	mySdk = go_amap.NewAmapClient(config.Config.Amap.Key, config.Config.Amap.Secret)
}

func Sdk() *go_amap.AmapClient {
	once.Do(initSdk)

	return mySdk
}

func Weather(adCode string) (*go_amap.WeatherResponse, error) {
	return Sdk().Weather.Info(adCode, "")
}

func IpLocation(ip string) (*go_amap.IpResponse, error) {
	return Sdk().Location.IpLocation(ip)
}

func Geo(address, city string) (*go_amap.GeoResponse, error) {
	return Sdk().Location.Geo(address, city)
}

// 经纬度转地址
func ReGeo(lng, lat float64) (*go_amap.ReGeoResponse, error) {
	request := go_amap.ReGeoRequest{
		Location: fmt.Sprintf("%f,%f", lng, lat),
	}
	resp, err := Sdk().Location.ReGeo(&request)
	if err != nil {
		return nil, err
	}
	if resp.Status != "1" {
		return nil, utils.NewErrorStr(constmap.ErrorSystem, resp.Response.Info)
	}

	cities := []string{
		"021",
		"022",
		"010",
		"023",
	}
	if slice.Contain(cities, resp.ReGeoCode.AddressComponent.Citycode) {
		resp.ReGeoCode.AddressComponent.City = resp.ReGeoCode.AddressComponent.Province
	}

	return resp, err
}

// 搜索机场、车站
func SearchStation(db *gorm.DB, in SearchStationRequest) (*go_amap.PlaceInputTipsResponse, error) {
	req := &go_amap.PlaceInputTipsRequest{
		Keywords: in.Keyword,
	}

	if in.ZoneId > 0 {
		var zone models.Zone
		db.Select("ad_code").Take(&zone, in.ZoneId)
		req.City = zone.AdCode
		req.CityLimit = true
	}

	switch in.Type {
	case constmap.JTimelinePlane:
		req.Type = "150104"
	case constmap.JTimelineTrain:
		req.Type = "150200"
	case constmap.JTimelineBus:
		req.Type = "150400"
	}

	rsp, err := InputTips(req)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func InputTips(req *go_amap.PlaceInputTipsRequest) (*go_amap.PlaceInputTipsResponse, error) {
	return Sdk().Place.InputTips(req)
}

// 搜索POI 2.0 https://lbs.amap.com/api/webservice/guide/api-advanced/newpoisearch
func Search(req *go_amap.PlaceRequest) (*go_amap.PlaceResponse, error) {
	return Sdk().Place.Search(req)
}

// 路径规划2.0 https://lbs.amap.com/api/webservice/guide/api/newroute
func Driving(from, to string, waypoints []string, showFields string) (*go_amap.DrivingResponse, error) {

	request := &go_amap.DrivingRequest{
		Origin:      from,
		Destination: to,
		Waypoints:   waypoints,
		ShowFields:  showFields,
		Strategy:    32,
	}

	return Sdk().Direction.Driving(request)
}

// 返回图片字节内容
func StaticMap(req *go_amap.StaticMapRequest) ([]byte, error) {
	return Sdk().StaticMap.Do(req)
}
