import axios from 'axios'
import {ElMessage} from 'element-plus'
import {useUserStore} from '../store/modules/users'
import qs from 'qs'
import Nprogress from 'nprogress'

const service = axios.create({
    withCredentials: true,
    baseURL: import.meta.env.VITE_BASE_API
})

service.interceptors.request.use((config) => {
    const s = useUserStore()

    if (s.token !== '') {
        config.headers.admin_token = s.token
    }

    if (config.method.toUpperCase() === 'POST') {
        if (config.data instanceof FormData) {
            config.headers['content-type'] = 'multipart/form-data'
        } else {
            config.headers['content-type'] = 'application/x-www-form-urlencoded'
            config.data = qs.stringify(config.data)
        }
    }

    Nprogress.start()

    return config
})

// Response interceptors
service.interceptors.response.use(
    (response) => {
        Nprogress.done()

        if (response.status !== 200) {
            ElMessage({
                type: 'error',
                message: '服务器忙,请稍后再试~'
            })
            return
        }

        const {data} = response
        if (data.code !== 0) {
            ElMessage({
                type: 'error',
                message: data.msg
            })
            if (data.code === 900000) {
                setTimeout(() => {
                    window.location.hash = '/login'
                }, 2000)
            } else {
                return Promise.reject(data)
            }
        } else {
            return data
        }
    },
    (error) => {
        Nprogress.done()
        return Promise.reject(error)
    }
)

export default service
