import service from '../request.js'

export function activityList(params = {}) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/activity/list',
    params,
  })
}

export function activitySave(data = {}) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/activity/save',
    data,
  })
}


export function activityDetail(activity_id) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/activity/detail',
    params: { activity_id },
  })
}

export function activityEnable(data = {}) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/activity/enable',
    data,
  })
}