import request from '../request'

/**
 * 获取心愿列表
 * @param {Object} params 查询参数
 * @returns {Promise} 心愿列表数据
 */
export const getWishList = (params) => {
  return request({
    url: '/api/v1/admin/wish/list',
    method: 'get',
    params
  })
}

/**
 * 获取心愿详情
 * @param {Number} wish_id 心愿ID
 * @returns {Promise} 心愿详情数据
 */
export const getWishDetail = (wish_id) => {
  return request({
    url: '/api/v1/admin/wish/detail',
    method: 'get',
    params: {wish_id}
  })
}

/**
 * 关闭心愿
 * @param {Object} data 关闭数据 {wish_id: Number, reason: String}
 * @returns {Promise} 操作结果
 */
export const closeWish = (data) => {
  return request({
    url: '/api/v1/admin/wish/close',
    method: 'post',
    data
  })
}


/**
 * 标记心愿完成
 * @param {Object} data 完成数据 {wish_id: Number}
 * @returns {Promise} 操作结果
 */
export const finishWish = (data) => {
  return request({
    url: '/api/v1/admin/wish/finish',
    method: 'post',
    data
  })
}

/**
 * 审核同行人
 * @param {Object} data 审核数据 {member_id: Number, state: Number}
 * @returns {Promise} 操作结果
 */
export const reviewMember = (data) => {
  return request({
    url: '/api/v1/admin/wish/member_review',
    method: 'post',
    data
  })
}

/**
 * 更新同行人信息
 * @param {Object} data 更新数据
 * @returns {Promise} 操作结果
 */
export const updateMember = (data) => {
  return request({
    url: '/api/v1/admin/wish/member_update',
    method: 'post',
    data
  })
}

/**
 * 审核通过心愿单
 * @param {Object} data 审核数据 {wish_id: Number}
 * @returns {Promise} 操作结果
 */
export const approveWish = (data) => {
  return request({
    url: '/api/v1/admin/wish/approve',
    method: 'post',
    data
  })
}
