import request from '../request'

/**
 * 获取工作流列表
 * @param params
 * @returns {Promise<*>}
 */
export function workflows(params) {
  return request({
    url: '/workflows',
    method: 'get',
    params
  })
}

/**
 * 获取工作流详情
 * @param id
 * @returns {Promise<*>}
 */
export function workflowDetail(id) {
  return request({
    url: `/workflows/${id}`,
    method: 'get'
  })
}

/**
 * 创建或更新工作流
 * @param data
 * @returns {Promise<*>}
 */
export function workflowSave(data) {
  const url = data.id ? `/workflows/${data.id}` : '/workflows'
  const method = data.id ? 'put' : 'post'
  
  return request({
    url,
    method,
    data
  })
}
