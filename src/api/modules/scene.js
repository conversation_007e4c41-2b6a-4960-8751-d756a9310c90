import service from '../request.js'

export function scenes(params = {}) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/scenic/list',
        params,
    })
}

export function sceneEnable(id, state) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/scenic/enable',
        data: {id, state},
    })
}

export function sceneTaglist(params={}) {
    return service({
        method:"GET",
        url:"/api/v1/admin/scenic/taglist",
        params,
    })
}

export function sceneDetail(params={}) {
    return service({
        method:"GET",
        url:"/api/v1/admin/scenic/detail",
        params,
    })
}

export function sceneSave(data={}){
    return service({
        method:"POST",
        url:"/api/v1/admin/scenic/save",
        data,
    })
}

export function otaList(params={}){
    return service({
        method:"GET",
        url:"/api/v1/admin/scenic/otalist",
        params,
    })
}

export function otaBindSave(data={}){
    return service({
        method:"POST",
        url:"/api/v1/admin/scenic/otabindsave",
        data,
    })
}