import service from "../request.js";

export function zoneUpsert(data) {
  return service({
    method: "POST",
    url: "/api/v1/admin/zone/upsert",
    data,
  });
}

export function zoneDetail(id) {
  return service({
    method: "GET",
    url: "/api/v1/admin/zone/detail",
    params: { id },
  });
}

export function bannerDetail(id) {
  return service({
    method: "GET",
    url: "/api/v1/admin/banner/detail",
    params: { id },
  });
}

export function bannerSave(data) {
  return service({
    method: "POST",
    url: "/api/v1/admin/banner/save",
    data,
  });
}

export function banners(params = {}) {
  return service({
    method: "GET",
    url: "/api/v1/admin/banner/list",
    params,
  });
}

import {
  searchMockConfigs,
  getMockConfigDetail,
  // saveMockConfig 未使用，已注释
} from "../mock/config";

// 是否使用模拟数据（开发环境下使用模拟数据）
// 目前未使用，保留以供未来使用
// const useMockData = false;

// 获取系统配置项列表
export function getConfigList(params = {}) {
  return service({
    method: "GET",
    url: "/api/v1/admin/setting/list",
    params,
  });
}

// 获取系统配置项详情
export function getConfigDetail(key) {
  return service({
    method: "GET",
    url: "/api/v1/admin/setting/list",
    params: { keys: key },
  }).then((res) => {
    // 处理返回数据，将列表第一项转换为详情格式
    if (
      res.code === 0 &&
      res.data &&
      res.data.list &&
      res.data.list.length > 0
    ) {
      return {
        code: 0,
        data: res.data.list[0],
        msg: "success",
      };
    }
    return {
      code: 404,
      data: null,
      msg: "配置项不存在",
    };
  });
}

// 保存系统配置项
export function saveConfig(data) {
  return service({
    method: "POST",
    url: "/api/v1/admin/setting/save",
    data: {
      key: data.key,
      value: data.value,
      desc: data.desc,
      value_type: data.value_type,
    },
  });
}
