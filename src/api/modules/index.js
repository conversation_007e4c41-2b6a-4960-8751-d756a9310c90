import service from '../request.js'

export function upload(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/upload',
        data
    })
}

export function categories(params) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/common/catelist',
        params
    })
}

export function otalist(params) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/common/otalist',
        params
    })
}

export function geo(address, city = '') {
    return service({
        method: 'GET',
        url: '/api/v1/admin/zone/geo',
        params: {address, city}
    })
}

export function zones() {
    return service({
        method: 'GET',
        url: '/api/v1/admin/zones',
    })
}

export function resetpwd(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/my/resetpwd',
        data,
    })
}

export function loginInfo(query) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/login',
        data: query
    })
}

export function logout() {
    return service({
        method: 'POST',
        url: '/api/v1/admin/logout',
    })
}

export function version() {
    return service({
        method: 'GET',
        url: '/api/v1/admin/version',
    })
}
