import service from '../request.js'

export function signTasks(params = {}) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/task/signtasks',
    params,
  })
}

export function taskList(params = {}) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/task/list',
    params,
  })
}

export function taskSave(data = {}) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/task/save',
    data,
  })
}


export function taskDetail(task_id) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/task/detail',
    params: {task_id},
  })
}

export function taskEnable(data = {}) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/task/enable',
    data,
  })
}