import service from '../request.js'

export function flowcardImport(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/wifi/import',
        data,
    })
}

export function flowcardRollback(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/wifi/rollback',
        data,
    })
}

export function flowcardEnable(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/flowcard/enable',
        data,
    })
}

export function flowcardPurchase(data = {}) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/flowcard/purchase',
        data
    })
}

export function flowcards(params = {}) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/wifi/flowcards',
        params
    })
}

export function flowcardGetRecords(params = {}) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/wifi/getrecords',
        params,
    })
}

export function flowcardCodes(params = {}) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/wifi/codes',
        params,
    })
}