import request from '../request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @returns {Promise} 用户列表数据
 */
export const getUserList = (params) => {
  return request({
    url: '/api/v1/admin/user/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param {String} user_id 用户ID
 * @returns {Promise} 用户详情数据
 */
export const getUserDetail = (user_id) => {
  return request({
    url: '/api/v1/admin/user/detail',
    method: 'get',
    params: { user_id }
  })
}

/**
 * 操作用户账户
 * @param {Object} data 操作数据 {amount: Number, remark: String}
 * @returns {Promise} 操作结果
 */
export const accountOperate = (data) => {
  return request({
    url: '/api/v1/admin/user/account_operate',
    method: 'post',
    data
  })
}