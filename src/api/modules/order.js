import service from '../request.js'

export function sendReward(data) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/order/send_reward',
    data,
  })
}

export function refundRetry(data) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/order/refundretry',
    data,
  })
}

export function refundReview(data) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/order/refundreview',
    data,
  })
}

export function refunds(params) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/order/refunds',
    params,
  })
}

export function applyRefund(data) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/order/applyrefund',
    data,
  })
}

export function payment(pay_id) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/order/paymentinfo',
    params: {pay_id},
  })
}

export function orders(params) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/order/list',
    params,
  })
}

export function orderDetail(params) {
  return service({
    method: 'GET',
    url: '/api/v1/admin/order/detail',
    params,
  })
}

export function orderPay(data) {
  return service({
    method: 'POST',
    url: '/api/v1/admin/order/pay',
    data,
  })
}
