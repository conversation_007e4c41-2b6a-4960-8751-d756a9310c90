import service from '../request.js'

export function tuanSkuDetail(id) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/tuan/skudetail',
        params: { sku_id: id }
    })
}

export function productSaveDate(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/tuan/savedate',
        data,
    })
}

export function productSaveSkuDate(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/tuan/saveskudate',
        data,
    })
}

export function productSaveTripNode(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/tuan/savetripnode',
        data,
    })
}

export function productDetail(id) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/tuan/detail',
        params: { id }
    })
}

export function productSave(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/tuan/save',
        data
    })
}

export function products(params = {}) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/tuan/list',
        params
    })
}

export function productPrices(id, month) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/tuan/prices',
        params: { id, month }
    })
}

export function productEnable(id, state) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/tuan/enable',
        data: { id, state }
    })
}
