import request from '../request'

/**
 * 获取套餐列表
 * @param params
 * @returns {Promise<*>}
 */
export function packages (params) {
  return request({
    url: '/api/v1/admin/pkg/list',
    method: 'get',
    params
  })
}

/**
 * 启用或禁用套餐
 * @param id
 * @param state
 * @returns {Promise<*>}
 */
export function packageEnable (id, state) {
  return request({
    url: '/api/v1/admin/pkg/enable',
    method: 'post',
    data: {
      id,
      state
    }
  })
}

/**
 * 获取套餐详情
 * @param id
 * @returns {Promise<*>}
 */
export function packageDetail (id) {
  return request({
    url: '/api/v1/admin/pkg/detail',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * 创建或更新套餐
 * @param data
 * @returns {Promise<*>}
 */
export function packageSave (data) {
  return request({
    url: '/api/v1/admin/pkg/save',
    method: 'post',
    data
  })
}
