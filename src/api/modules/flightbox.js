import service from '../request.js'

export function versionDetail(id) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/lightbox/versiondetail',
        params: {id},
    })
}

export function saveVersion(data) {
    return service({
        method: 'POST',
        url: '/api/v1/admin/lightbox/saveversion',
        data,
    })
}

export function versions(params) {
    return service({
        method: 'GET',
        url: '/api/v1/admin/lightbox/versions',
        params,
    })
}