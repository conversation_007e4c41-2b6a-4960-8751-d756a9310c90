/**
 * 系统配置项模拟数据
 */

// 模拟配置项列表数据
export const mockConfigList = [
  {
    key: 'system_name',
    value: '路程旅行管理系统',
    type: 1, // TEXT
    description: '系统名称，显示在浏览器标题和登录页',
    created_at: '2024-12-01 10:00:00',
    updated_at: '2024-12-01 10:00:00'
  },
  {
    key: 'system_version',
    value: '1.0.0',
    type: 1, // TEXT
    description: '系统版本号',
    created_at: '2024-12-01 10:00:00',
    updated_at: '2024-12-15 14:30:00'
  },
  {
    key: 'max_upload_size',
    value: 10,
    type: 2, // NUMBER
    description: '最大上传文件大小(MB)',
    created_at: '2024-12-02 09:30:00',
    updated_at: '2024-12-02 09:30:00'
  },
  {
    key: 'enable_registration',
    value: false,
    type: 4, // BOOL
    description: '是否开启用户注册功能',
    created_at: '2024-12-02 11:20:00',
    updated_at: '2024-12-10 16:45:00'
  },
  {
    key: 'maintenance_mode',
    value: false,
    type: 4, // BOOL
    description: '系统维护模式开关',
    created_at: '2024-12-03 08:15:00',
    updated_at: '2024-12-03 08:15:00'
  },
  {
    key: 'homepage_banners_count',
    value: 5,
    type: 2, // NUMBER
    description: '首页Banner展示数量',
    created_at: '2024-12-03 14:00:00',
    updated_at: '2024-12-20 11:30:00'
  },
  {
    key: 'api_rate_limit',
    value: 100,
    type: 2, // NUMBER
    description: 'API请求速率限制(每分钟)',
    created_at: '2024-12-04 10:25:00',
    updated_at: '2024-12-04 10:25:00'
  },
  {
    key: 'notification_settings',
    value: JSON.stringify({
      email: true,
      sms: false,
      wechat: true,
      push: true
    }),
    type: 3, // JSON
    description: '系统通知设置',
    created_at: '2024-12-05 09:10:00',
    updated_at: '2024-12-18 15:40:00'
  },
  {
    key: 'sms_provider_config',
    value: JSON.stringify({
      provider: 'aliyun',
      accessKey: 'mock_access_key',
      secretKey: 'mock_secret_key',
      signName: '路程旅行',
      templates: {
        register: 'SMS_123456789',
        login: 'SMS_987654321',
        reset: 'SMS_567891234'
      }
    }),
    type: 3, // JSON
    description: '短信服务提供商配置',
    created_at: '2024-12-06 13:30:00',
    updated_at: '2024-12-16 17:20:00'
  },
  {
    key: 'default_package_settings',
    value: JSON.stringify({
      duration: 30,
      price: 99.99,
      features: ['基础功能', '高级报表', '数据分析', '客户支持'],
      autoRenew: false
    }),
    type: 3, // JSON
    description: '默认套餐设置',
    created_at: '2024-12-07 15:45:00',
    updated_at: '2024-12-21 10:15:00'
  },
  {
    key: 'system_announcement',
    value: '欢迎使用路程旅行管理系统，如有问题请联系客服。',
    type: 1, // TEXT
    description: '系统公告，显示在登录后的首页',
    created_at: '2024-12-08 09:00:00',
    updated_at: '2024-12-22 08:30:00'
  },
  {
    key: 'workflow_states',
    value: JSON.stringify([
      { id: 1, name: '待处理', color: '#ff9900' },
      { id: 2, name: '处理中', color: '#0099ff' },
      { id: 3, name: '已完成', color: '#00cc66' },
      { id: 4, name: '已取消', color: '#cc0000' }
    ]),
    type: 3, // JSON
    description: '工作流状态配置',
    created_at: '2024-12-09 11:20:00',
    updated_at: '2024-12-19 14:10:00'
  }
];

// 根据关键词搜索配置项
export function searchMockConfigs(keyword = '', page = 1, limit = 20) {
  let result = [...mockConfigList];
  
  if (keyword) {
    const lowerKeyword = keyword.toLowerCase();
    result = result.filter(item => 
      item.key.toLowerCase().includes(lowerKeyword) || 
      (item.description && item.description.toLowerCase().includes(lowerKeyword))
    );
  }
  
  // 计算分页
  const total = result.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const list = result.slice(startIndex, endIndex);
  
  return {
    code: 0,
    data: {
      list,
      total,
      page,
      limit
    },
    msg: 'success'
  };
}

// 根据key获取配置项详情
export function getMockConfigDetail(key) {
  const config = mockConfigList.find(item => item.key === key);
  
  if (!config) {
    return {
      code: 404,
      data: null,
      msg: '配置项不存在'
    };
  }
  
  return {
    code: 0,
    data: config,
    msg: 'success'
  };
}

// 保存配置项
export function saveMockConfig(data) {
  const index = mockConfigList.findIndex(item => item.key === data.key);
  const now = new Date().toISOString().replace('T', ' ').substring(0, 19);
  
  if (index === -1) {
    // 新增配置项
    const newConfig = {
      ...data,
      created_at: now,
      updated_at: now
    };
    mockConfigList.push(newConfig);
  } else {
    // 更新配置项
    mockConfigList[index] = {
      ...mockConfigList[index],
      ...data,
      updated_at: now
    };
  }
  
  return {
    code: 0,
    data: null,
    msg: 'success'
  };
}
