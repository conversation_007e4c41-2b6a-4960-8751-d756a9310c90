package routers

import (
	"roadtrip-api/src/components/business"
	"roadtrip-api/src/constmap"
	admin2 "roadtrip-api/src/handles/v1/admin"
	"roadtrip-api/src/handles/v1/admin/activity"
	"roadtrip-api/src/handles/v1/admin/advertiser"
	"roadtrip-api/src/handles/v1/admin/banner"
	"roadtrip-api/src/handles/v1/admin/common"
	"roadtrip-api/src/handles/v1/admin/flowcard_purchase"
	"roadtrip-api/src/handles/v1/admin/lightbox"
	"roadtrip-api/src/handles/v1/admin/my"
	order2 "roadtrip-api/src/handles/v1/admin/order"
	"roadtrip-api/src/handles/v1/admin/pkg"
	"roadtrip-api/src/handles/v1/admin/plan"
	"roadtrip-api/src/handles/v1/admin/scenic"
	"roadtrip-api/src/handles/v1/admin/setting"
	"roadtrip-api/src/handles/v1/admin/task"
	"roadtrip-api/src/handles/v1/admin/tuan"
	"roadtrip-api/src/handles/v1/admin/user"
	"roadtrip-api/src/handles/v1/admin/wifi"
	"roadtrip-api/src/handles/v1/admin/wish"
	zone2 "roadtrip-api/src/handles/v1/admin/zone"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"

	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

func admin() {
	a := engine.Group("api/v1/admin")
	a.Use(getAdminUser())
	{
		a.GET("zones", handle(admin2.Zones))
		a.POST("upload", handle(admin2.Upload))
		a.POST("logout", adminAuth(), handle(admin2.Logout))
		a.POST("login", handle(admin2.Login))

		settings := a.Group("setting")
		settings.Use(adminAuth())
		{
			settings.GET("list", handle(setting.List))
			settings.POST("save", handle(setting.Save))
		}

		zone := a.Group("zone")
		zone.Use(adminAuth())
		{
			zone.GET("geo", handle(zone2.Geo))
			zone.GET("detail", handle(zone2.Detail))
			zone.POST("upsert", handle(zone2.Upsert))
		}
		b := a.Group("banner")
		b.Use(adminAuth())
		{
			b.POST("save", handle(banner.Save))
			b.GET("list", handle(banner.List))
			b.GET("detail", handle(banner.Detail))
		}

		light := a.Group("lightbox")
		light.Use(adminAuth())
		{
			light.GET("versiondetail", handle(lightbox.VersionDetail))
			light.GET("versions", handle(lightbox.Versions))
			light.POST("saveversion", handle(lightbox.SaveVersion))
		}

		adv := a.Group("advertiser")
		adv.Use(adminAuth())
		{
			adv.GET("list", handle(advertiser.List))
			adv.POST("save", handle(advertiser.Save))
		}

		pl := a.Group("plan")
		pl.Use(adminAuth())
		{
			pl.GET("list", handle(plan.List))
		}

		m := a.Group("my")
		m.Use(adminAuth())
		{
			m.POST("resetpwd", handle(my.ResetPwd))
		}

		f := a.Group("flowcard")
		f.Use(adminAuth())
		{
			f.POST("enable", handle(flowcard_purchase.Enable))
			f.POST("purchase", handle(flowcard_purchase.Purchase))
		}

		w := a.Group("wifi")
		w.Use(adminAuth())
		{
			w.GET("flowcards", handle(wifi.FlowCards))
			w.GET("getrecords", handle(wifi.GetRecords))
			w.GET("cardimage", handle(wifi.CardImage))
			w.GET("codes", handle(wifi.Codes))
			w.POST("rollback", handle(wifi.Rollback))
			w.GET("export", handle(wifi.Export))
			w.POST("import", handle(wifi.Import))
		}

		p := a.Group("tuan")
		p.Use(adminAuth())
		{
			p.GET("detail", handle(tuan.Detail))
			p.GET("list", handle(tuan.List))
			p.POST("save", handle(tuan.Save))
			p.POST("enable", handle(tuan.Enable))
			p.GET("prices", handle(tuan.Prices))
			p.POST("savedate", handle(tuan.SaveDate))
			p.POST("saveskudate", handle(tuan.SaveSkuDate))
			p.POST("savetripnode", handle(tuan.SaveTripNode))
			p.GET("skudetail", handle(tuan.SkuDetail))
		}
		o := a.Group("order")
		o.Use(adminAuth())
		{
			o.POST("applyrefund", handle(order2.ApplyRefund))
			o.POST("refundreview", handle(order2.RefundReview))
			o.POST("refundretry", handle(order2.RefundRetry))
			o.GET("paymentinfo", handle(order2.Payment))
			o.POST("pay", handle(order2.Pay))
			o.GET("detail", handle(order2.Detail))
			o.GET("list", handle(order2.List))
			o.GET("refunds", handle(order2.RefundList))
			o.POST("send_reward", handle(order2.SendReward))
		}
		{
			g := a.Group("scenic")
			g.Use(adminAuth())
			g.GET("list", handle(scenic.List))
			g.GET("detail", handle(scenic.Detail))
			g.GET("taglist", handle(scenic.TagList))
			g.POST("save", handle(scenic.Save))
			g.POST("enable", handle(scenic.Enable))
			g.GET("otalist", handle(scenic.OtaList))
			g.POST("otabindsave", handle(scenic.OtaBindSave))
		}
		{
			g := a.Group("common")
			g.Use(adminAuth())
			g.GET("catelist", handle(common.CateList))
			g.GET("otalist", handle(common.OtaList))
		}
		{
			g := a.Group("activity")
			g.Use(adminAuth())
			g.GET("list", handle(activity.List))
			g.GET("detail", handle(activity.Detail))
			g.POST("enable", handle(activity.Enable))
			g.POST("save", handle(activity.Save))
		}
		{
			g := a.Group("task")
			g.GET("options", handle(task.Options))
			g.Use(adminAuth())
			g.GET("list", handle(task.List))
			g.GET("detail", handle(task.Detail))
			g.POST("enable", handle(task.Enable))
			g.POST("save", handle(task.Save))
			g.GET("signtasks", handle(task.SignTasks))
		}
		{
			pg := a.Group("pkg")
			pg.Use(adminAuth())
			pg.GET("list", handle(pkg.List))
			pg.GET("detail", handle(pkg.Detail))
			pg.POST("save", handle(pkg.Save))
			pg.POST("enable", handle(pkg.Enable))
		}
		{
			u := a.Group("user")
			u.Use(adminAuth())
			u.GET("list", handle(user.List))
			u.GET("detail", handle(user.Detail))
			u.POST("account_operate", handle(user.AccountOperate))
		}
		{
			wis := a.Group("wish")
			wis.Use(adminAuth())
			wis.GET("list", handle(wish.List))                   // 心愿单列表
			wis.GET("detail", handle(wish.Detail))               // 心愿单详情
			wis.POST("close", handle(wish.Close))                // 关闭心愿单
			wis.POST("follow", handle(wish.UpdateFollowState))   // 更新跟进状态
			wis.POST("finish", handle(wish.Finish))              // 标记心愿单已完成
			wis.POST("approve", handle(wish.Approve))            // 审核通过心愿单
			wis.POST("member_review", handle(wish.MemberReview)) // 审核同行人
			wis.POST("member_update", handle(wish.MemberUpdate)) // 更新同行人信息
		}
	}
}

func adminAuth() gin.HandlerFunc {
	return func(context *gin.Context) {
		start := time.Now()
		if session, err := business.GetAdminLoginUser(context); err != nil {
			response(start, context, nil, utils.NewErrorStr(constmap.ErrorNotLogin, constmap.ErrorMsgNotLogin))
			context.Abort()
		} else {
			if time.Now().Sub(session.UpdatedAt) > time.Minute*10 {
				db := utils.GetDB(context)

				updates := models.AdminSession{
					UserId: session.UserId,
				}
				db.Model(&session).Omit(clause.Associations).Updates(updates)
			}

			context.Next()
		}
	}

}

func getAdminUser() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		token := ctx.GetHeader("admin_token")
		if strutil.IsBlank(token) {
			token, _ = ctx.Cookie("admin_token")
		}
		if strutil.IsBlank(token) {
			token = ctx.Query("admin_token")
		}

		if !strutil.IsBlank(token) {
			var session models.AdminSession
			db := utils.GetDB(ctx)

			if db.Where("token=?", token).Joins("User").
				Take(&session).Error != nil {
				return
			}

			ctx.Set(constmap.ContextUser, &session)
		}

	}
}
