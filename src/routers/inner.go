package routers

import (
	"github.com/gin-gonic/gin"
	"roadtrip-api/src/config"
	"roadtrip-api/src/handles/v1/inner/hotel"
	"roadtrip-api/src/handles/v1/inner/web"
	"roadtrip-api/src/utils"
)

func inner() {
	a := engine.Group("api/v1/inner")
	a.Use(verifyInner())
	{
		b := a.Group("hotel")
		b.GET("search", handle(hotel.Search))

		w := a.Group("web")
		w.GET("fetch", handle(web.Fetch))
	}
}

func verifyInner() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		token := ctx.Query("token")

		yjKey := config.Config.Dify.YjKey
		if utils.Md5(yjKey) != token {
			ctx.AbortWithStatusJSON(401, gin.H{
				"code": 401,
				"msg":  "token验证失败",
			})
			return
		}
		ctx.Next()
	}
}
