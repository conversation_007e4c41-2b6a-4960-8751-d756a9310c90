package routers

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
	"roadtrip-api/src/components/cron"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"sync"
	"time"
)

var engine *gin.Engine
var once sync.Once

func initRoute() {
	if config.IsDebug() {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	engine = gin.New()
	engine.Use(gin.Recovery(), cors(), initDb())

	admin()
	front()
	outer()
	inner()

	cron.Start()
}

type HandleFunc func(ctx *gin.Context) (any, error)

func GetGin() *gin.Engine {
	once.Do(initRoute)

	return engine
}

func initDb() gin.HandlerFunc {
	return func(context *gin.Context) {
		db := models.New()
		context.Set("db", db)

		context.Next()
	}
}

func cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin") //请求头部
		if origin != "" {
			//接收客户端发送的origin （重要！）
			c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		}
		//服务器支持的所有跨域请求的方法
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE,UPDATE")
		//允许跨域设置可以返回其他子段，可以自定义字段

		requestHeaders := c.Request.Header.Get("Access-Control-Request-Headers")
		if requestHeaders != "" {
			c.Writer.Header().Set("Access-Control-Allow-Headers", requestHeaders)
		} else {
			c.Writer.Header().Set("Access-Control-Allow-Headers", "*")
		}

		// 允许浏览器（客户端）可以解析的头部 （重要）
		//c.Header("Access-Control-Expose-Headers", "Preload-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers")
		//设置缓存时间
		c.Writer.Header().Set("Access-Control-Max-Age", "172800")
		//允许客户端传递校验信息比如 cookie (重要)
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")

		//允许类型校验
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		c.Next()
	}
}

func response(start time.Time, ctx *gin.Context, data interface{}, err error) {
	code := constmap.RequestOk
	msg := "success"

	if err != nil {
		code = constmap.ErrorSystem
		msg = err.Error()
		if appError, ok := err.(constmap.AppError); ok {
			code = appError.Code
		}
	}

	if _, ok := ctx.Get(constmap.ContextHttpRaw); ok {
		return
	}

	//上传文件单独处理
	if ctx.Request.URL.Path == "/admin/files/upload" {
		if err != nil {
			ctx.AbortWithError(500, err)
			return
		}
	}

	response := gin.H{
		"code": code,
		"data": data,
		"msg":  msg,
	}

	var params string
	switch ctx.Request.Method {
	case "GET":
		params = ctx.Request.URL.RawQuery
	case "POST":
		params = ctx.Request.PostForm.Encode()
	}
	my_logger.Infof("request", zap.String("method", ctx.Request.Method),
		zap.String("url", ctx.Request.URL.Path),
		zap.String("data", params),
		zap.Any("response", gin.H{
			"code": code,
			"msg":  msg,
		}),
		zap.Duration("cost", time.Since(start)),
	)

	ctx.JSON(200, response)
}

func handle(handleFunc HandleFunc) gin.HandlerFunc {
	return func(context *gin.Context) {
		start := time.Now()
		r, err := handleFunc(context)

		response(start, context, r, err)
	}
}
