package cmd

import (
	"github.com/spf13/cobra"
	"roadtrip-api/src/cmd/sub"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/config"
)

var rootCmd *cobra.Command
var configFile string

func init() {
	rootCmd = &cobra.Command{
		Use: "roadtrip-api",
	}

	rootCmd.PersistentFlags().StringVarP(&configFile, "conf", "c", "app.toml", "配置文件路径")
	rootCmd.PersistentPreRun = func(cmd *cobra.Command, args []string) {
		config.InitConfig(configFile)
		my_logger.InitLogger()
	}
}

func Execute() {
	rootCmd.AddCommand(Web(), IndexCmd(), Ota(), Order(), Once(), Cmd(), sub.Qiniu())
	_ = rootCmd.Execute()
}
