package sub

import (
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/wish_biz/wish_asm"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

func GenWishIndex() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "wish",
		Short: "心愿单数据",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}

	cmd.AddCommand(createWishIndex(), importWishData())

	return cmd
}

func createWishIndex() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "创建心愿单索引",
		Run: func(cmd *cobra.Command, args []string) {

			// 创建索引请求体
			createIndexBody := `
{
	"settings": {
		"analysis": {
			"analyzer": {
				"ik_smart_analyzer": {
					"type": "custom",
					"tokenizer": "ik_smart"
				}
			}
		}
	},
	"mappings": {
		"dynamic": "true",
		"properties": {
			"title": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"tags": {"type": "keyword"},
			"day_tags": {"type": "keyword"},
			"created_at": {"type":"date", "format":"yyyy-MM-dd HH:mm:ss"}
		}
	}
}
`
			if err := es.CreateIndexType(constmap.EsIndexWish, createIndexBody); err != nil {
				panic(err)
			}
		},
	}
}

func importWishData() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sync",
		Short: "同步数据到es中",
		Run: func(cmd *cobra.Command, args []string) {

			var err error
			db := models.New()
			err = wish_asm.SyncAll(db)

			cmd.Printf("task done.err:%v", err)
		},
	}
	return cmd
}
