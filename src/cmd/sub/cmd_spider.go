package sub

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/components/cron/cron_biz"
	"roadtrip-api/src/components/my_cache"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/components/my_queue"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"roadtrip-api/src/utils/typeset"
	"strings"
	"time"
)

func Spider() *cobra.Command {
	helpShort := `爬虫脚本(scene景点|plan推荐行程|rec_prompts推荐语|zone_speciality城市民俗文化、美食、特产)，默认为长三角地区`

	var cmd = &cobra.Command{
		Use:   "spider",
		Short: helpShort,
		Long:  helpShort,
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			var err error
			c := cmd.Flag("continue").Value.String() == "true"
			zone_ids := cmd.Flag("zone_ids").Value.String()
			scene_ds := cmd.Flag("scene_ids").Value.String()

			maxPlan, _ := convertor.ToInt(cmd.Flag("max_plan").Value.String())

			var (
				zoneIds  []uint
				sceneIds []uint
			)
			if zone_ids != "" {
				ids, _ := utils.ToArray[uint](zone_ids, ",")
				zoneIds = ids
			}
			if scene_ds != "" {
				ids, _ := utils.ToArray[uint](scene_ds, ",")
				sceneIds = ids
			}

			switch args[0] {
			default:
				_ = cmd.Help()
			case "scene":
				err = spiderScene(db, c, zoneIds, sceneIds)
			case "plan":
				err = cron_biz.SpiderPlan(db, c, int(maxPlan), zoneIds)
			case "rec_prompts":
				err = cron_biz.SpiderRecPrompt(db, c, zoneIds)
			case "zone_speciality":
				err = cron_biz.SpiderZoneSpeciality(db, c, zoneIds)
			}
			cmd.Printf("taskDone[%s]. error:%v", time.Now().Format(constmap.DateFmtLongFull), err)
		},
	}
	flags := cmd.Flags()
	flags.StringP("zone_ids", "z", "", "城市id列表")
	flags.StringP("scene_ids", "s", "", "景点id列表")
	flags.Int("max_plan", 10, "每个城市生成行程数")
	flags.Bool("continue", true, "是否继续上次进度")
	return cmd
}

func spiderScene(db *gorm.DB, doContinue bool, zoneIds, ids []uint) error {

	//  默认爬取长三角地区的城市
	if len(ids) == 0 && len(zoneIds) == 0 {
		if ids, err := utils.ToArray[uint](constmap.SpiderDefaultZoneIds, ","); err == nil {
			zoneIds = ids
		}
	}

	do := func(zoneId uint, where *strings.Builder, whereArgs []any) error {
		var nextId uint
		var list []models.Scenic
		for {
			db.Preload("Ext").
				Where("llm=?", constmap.Enable).
				Where(where.String(), whereArgs...).
				Where("id>?", nextId).
				Order("id asc").Limit(100).
				Find(&list)
			if len(list) == 0 {
				return nil
			}
			nextId = list[len(list)-1].ID
			for _, item := range list {
				if zoneId > 0 {
					_ = my_cache.Set(fmt.Sprintf(constmap.RKSpider, "scene", zoneId), item.ID, constmap.TimeDur30d)
				}
				if err := spiderSceneItem(db, item); err != nil {
					return err
				}
			}
		}
	}

	where := &strings.Builder{}
	whereArgs := make([]any, 0)

	if len(zoneIds) > 0 {
		for _, id := range zoneIds {
			where.Reset()
			whereArgs = whereArgs[:0]
			where.WriteString("zone_id=?")
			whereArgs = append(whereArgs, id)
			var nextId uint
			if doContinue && my_cache.Get(fmt.Sprintf(constmap.RKSpider, "scene", id), &nextId) {
				where.WriteString(" and id>=?")
				whereArgs = append(whereArgs, nextId)
			}
			if err := do(id, where, whereArgs); err != nil {
				return err
			}
		}
	} else {
		where.WriteString("1=1")
		if len(ids) > 0 {
			where.WriteString(" AND id IN ?")
			whereArgs = append(whereArgs, ids)
		}
		if err := do(0, where, whereArgs); err != nil {
			return err
		}
	}
	return nil
}

func spiderSceneItem(db *gorm.DB, scene models.Scenic) error {
	zoneMap := zone_biz.NewZoneBiz().GetZonesToTop(db, []uint{scene.ZoneId}, 0)
	sceneName := scene.Name
	if z, ok := zoneMap[scene.ZoneId]; ok {
		sceneName = z.Name + " " + sceneName
		for z.Parent != nil {
			sceneName = z.Parent.Name + " " + sceneName
			z = z.Parent
		}
	}
	ctx, unlocker := context.WithCancel(context.Background())
	defer unlocker()

	my_logger.Infof("spiderScene", zap.String("sceneName", sceneName))

	sceneInfo, err := my_dify.SpiderScene(ctx, db, sceneName)
	if err != nil {
		return err
	}
	my_cache.RedisLock(ctx, fmt.Sprintf(constmap.RKSpin, "spiderScene", ""), constmap.TimeDur1m)
	tagSet := typeset.NewTypeSet[string](false)
	if len(sceneInfo.Tags) > 0 {
		tagSet.Add(sceneInfo.Tags...)
		var tags []models.ScenicTag
		db.Where("value in ?", sceneInfo.Tags).Find(&tags)
		slice.ForEach(tags, func(index int, item models.ScenicTag) {
			tagSet.Del(item.Value)
		})
	}
	var openTime []string
	for _, v := range sceneInfo.OpeningHours {
		openTime = append(openTime, fmt.Sprintf("%s %s-%s %s停止检票", v.Period, v.OpenTime, v.CloseTime, v.LastEntryTime))
	}
	b, _ := json.Marshal(sceneInfo.TicketPrice)
	scene.ScenicPrices = string(b)
	scene.CostText = utils.If(!strutil.IsBlank(scene.CostText), scene.CostText, fmt.Sprintf("%.2f元", sceneInfo.TicketPrice.Adult.MonthlyPriceAvg))
	b, _ = json.Marshal(sceneInfo.RecommendedDuration)
	scene.PlayCosts = string(b)
	var months []int64
	slice.ForEach(sceneInfo.BestSeasons, func(index int, item string) {
		var str strings.Builder
		for _, v := range []rune(item) {
			if v > '9' || v < '0' {
				continue
			}
			str.WriteRune(v)
		}
		if i, err := convertor.ToInt(str.String()); err == nil {
			months = append(months, i)
		}
	})
	scene.BestPlayMonths = utils.Join(months, ",")
	scene.OpenTime = strings.Join(openTime, ";")
	scene.IsFree = utils.If(sceneInfo.TicketPrice.IsFree, constmap.Enable, constmap.Disable)
	scene.Tags = strings.Join(tagSet.Values(), ",")
	scene.IsBooking = utils.If(sceneInfo.AdditionalInfo.BookingRequired, constmap.Enable, constmap.Disable)
	scene.Ext.ScenicId = scene.ID
	scene.Ext.PlayTutorial = strings.Join(sceneInfo.RecommendedActivities, "\n\n")
	scene.Ext.WarningNotes = sceneInfo.AdditionalInfo.WarningNotes
	err = db.Transaction(func(tx *gorm.DB) error {
		if tagSet.Len() > 0 {
			if err = tx.Create(slice.Map(tagSet.Values(), func(index int, item string) models.ScenicTag {
				return models.ScenicTag{
					Value: item,
				}
			})).Error; err != nil {
				return err
			}
		}
		if tx.Model(&scene).Omit(clause.Associations, "ID", "CreatedAt").Select("*").Updates(scene).RowsAffected == 0 {
			return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
		}
		if scene.Ext.ID > 0 {
			if tx.Model(&scene.Ext).Omit("ID", "CreatedAt").Updates(scene.Ext).RowsAffected == 0 {
				return utils.NewErrorStr(constmap.ErrorSystem, "更新失败")
			}
		} else {
			if err = tx.Create(&scene.Ext).Error; err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	_ = my_queue.Light(constmap.EventScenicUpdate, gin.H{
		"ids": fmt.Sprintf("%d", scene.ID),
	})
	return nil
}
