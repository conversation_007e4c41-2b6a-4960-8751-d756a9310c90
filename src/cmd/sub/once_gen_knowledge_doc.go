package sub

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/spf13/cobra"
	"roadtrip-api/src/beans"
	"roadtrip-api/src/components/business/sys_config_biz"
	"roadtrip-api/src/components/my_dify"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
)

// 生成知识库文档
func GenerateKnowledgeBaseDoc() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "gen_knowledge_doc",
		Short: "生成酒店知识库文档",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()

			cfg := new(models.SysConfig)
			db.Where(models.SysConfig{Key: constmap.SysConfigDifyKnowledgeHotelKey}).Take(&cfg)

			if cfg.ID == 0 {
				cmd.PrintErrln("配置不存在")
				return
			}

			keys := new(beans.DifyKnowledgeHotelKey)
			if err := json.Unmarshal([]byte(cfg.Value), keys); err != nil {
				cmd.PrintErr(err)
				return
			} else if keys.ApiKey == "" || keys.StoreId == "" {
				cmd.PrintErrln("ApiKey/知识库id不能为空")
				return
			}
			if len(keys.DocZoneMap) == 0 {
				keys.DocZoneMap = make(map[uint]string)
			}

			ctx := context.Background()

			kdg := my_dify.NewKnowledgeBase(db)

			list := make([]*models.Zone, 0)
			size := 100
			nextId := uint(0)
			for {
				query := db.Where("id>?", nextId)
				query.Where(models.Zone{State: constmap.Enable, Level: constmap.ZoneLevelCity})
				query.Order("id asc").Limit(size).Find(&list)
				if len(list) == 0 {
					break
				}
				nextId = list[len(list)-1].ID
				for _, v := range list {
					cmd.Printf("CreateDocumentByText:%s\n", v.Name)
					doc, err := kdg.CreateDocumentByText(ctx, keys.ApiKey, keys.StoreId, fmt.Sprintf("%s酒店数据.txt", v.Name), "")
					if err != nil {
						cmd.PrintErr(err)
						return
					}
					keys.DocZoneMap[v.ID] = doc.Document.Id
					cfg.Value = convertor.ToString(keys)
					if db.Model(&cfg).Updates(models.SysConfig{Value: cfg.Value}).RowsAffected == 0 {
						cmd.PrintErrln("更新配置失败")
						return
					}
				}
			}
			sys_config_biz.ClearCache(constmap.SysConfigDifyKnowledgeHotelKey)
			if _, err := sys_config_biz.GetConfig(db, constmap.SysConfigDifyKnowledgeHotelKey); err != nil {
				cmd.PrintErr(err)
			}

			cmd.Println("task done.")
		},
	}

	return cmd
}
