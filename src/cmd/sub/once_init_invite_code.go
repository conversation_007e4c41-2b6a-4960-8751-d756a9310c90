package sub

import (
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/user_biz/user_asm"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func InitInviteCode() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "init_invite_code",
		Short: "生成邀请码并初始化积分",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()

			str := cmd.Flag("user_ids").Value.String()
			var userIds []uint

			if strutil.IsNotBlank(str) {
				userIds, _ = utils.ToArray[uint](str, ",")
			}

			list := make([]*models.User, 0)
			size := 100
			nextId := uint(0)
			for {
				query := db.Where("id>?", nextId)
				if len(userIds) > 0 {
					query.Where("id in ?", userIds)
				}
				query.Order("id asc").Limit(size).Find(&list)
				if len(list) == 0 {
					break
				}
				nextId = list[len(list)-1].ID
				for _, v := range list {
					if v.InviteCode == "" {
						cmd.Printf("GenerateInviteCode %d\n", v.ID)
						if err := user_asm.GenerateInviteCode(db, v); err != nil {
							cmd.Printf("GenerateInviteCode %d error: %v\n", v.ID, err)
						}
					}
				}
			}
			cmd.Println("task done.")
		},
	}

	flags := cmd.Flags()
	flags.StringP("user_ids", "u", "", "用户id1,id2,...")

	return cmd
}
