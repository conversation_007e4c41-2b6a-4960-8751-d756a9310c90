package sub

import (
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/ota_asm"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
)

func GenZwyCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "zwy",
		Short: "自我游",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}
	cmd.AddCommand(zwySyncScenic())
	return cmd
}

func zwySyncScenic() *cobra.Command {
	return &cobra.Command{
		Use:   "sync_scenic",
		Short: "同步自我游景点",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			ota_asm.Zwy.SyncScenic(db)
		},
	}
}

func GenCeekeeCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "ceekee",
		Short: "思客",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}
	cmd.AddCommand(ceekeeSyncHotel(), ceekeeRepairHotel())
	return cmd
}

func ceekeeRepairHotel() *cobra.Command {
	return &cobra.Command{
		Use:   "repair_hotel",
		Short: "修复酒店城市id为0的数据",
		Run: func(cmd *cobra.Command, args []string) {
			ota_asm.Ceekee.WithConcurrencyNum(1).RepairHotel(models.New())
		},
	}
}

func ceekeeSyncHotel() *cobra.Command {
	var c int
	var line int

	cmd := &cobra.Command{
		Use:   "sync_hotel",
		Short: "同步思客酒店",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			file := cmd.Flag("file").Value.String()
			if file != "" {
				ota_asm.Ceekee.WithConcurrencyNum(c).SyncHotelByCsv(db, file, line)
				return
			}
			str := cmd.Flag("zone_id").Value.String()
			var zoneIds []uint
			var err error
			if str != "" {
				if zoneIds, err = utils.ToArray[uint](str, ","); err != nil {
					cmd.PrintErrln(err)
					return
				}
			}
			ota_asm.Ceekee.WithConcurrencyNum(c).SyncHotel(db, zoneIds...)
		},
	}
	flags := cmd.Flags()
	flags.StringP("zone_id", "z", "", "zoneID1,zoneID2,... 指定更新的城市id")
	flags.IntVarP(&c, "concurrent_num", "n", 1, "并发线程数")
	flags.StringP("file", "f", "", "思客_携程csv文件路径")
	flags.IntVarP(&line, "fline", "l", 1, "指定从CSV文件第几行开始处理")
	return cmd
}
