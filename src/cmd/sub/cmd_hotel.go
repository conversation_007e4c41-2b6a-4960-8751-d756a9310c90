package sub

import (
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/cron/cron_biz/cron_asm"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"roadtrip-api/src/utils"
	"time"
)

func Hotel() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "hotel",
		Short: "(price计算指定月均价|knowledge_sync同步知识库)",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			var err error
			c := cmd.Flag("continue").Value.String() == "true"
			zone_ids := cmd.Flag("zone_ids").Value.String()
			scene_ds := cmd.Flag("scene_ids").Value.String()

			var (
				zoneIds  []uint
				sceneIds []uint
			)
			if zone_ids != "" {
				ids, _ := utils.ToArray[uint](zone_ids, ",")
				zoneIds = ids
			}
			if scene_ds != "" {
				ids, _ := utils.ToArray[uint](scene_ds, ",")
				sceneIds = ids
			}

			ctx := cmd.Context()
			switch args[0] {
			default:
				_ = cmd.Help()
			case "price":
				err = cron_asm.HotelPriceSync(db, zoneIds, c)
			case "knowledge_sync":
				err = cron_asm.HotelKnowledgeSync(ctx, db, zoneIds, sceneIds, c)
			}
			cmd.Printf("taskDone[%s]. error:%v", time.Now().Format(constmap.DateFmtLongFull), err)
		},
	}
	flags := cmd.Flags()
	flags.StringP("zone_ids", "z", "", "城市id列表")
	flags.String("scene_ids", "", "景点id")
	flags.String("month", "", "指定月份(2025-03-04)，默认下月")
	flags.Bool("continue", true, "是否继续上次进度")
	return cmd
}
