package sub

import (
	"github.com/mozillazg/go-pinyin"
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/zone_biz"
	"roadtrip-api/src/models"
	"strings"
)

func ZonePinyin() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "zone_pinyin",
		Short: "刷入城市拼音",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			a := pinyin.NewArgs()

			list := make([]models.Zone, 0)
			size := 100
			nextId := uint(0)
			for {
				db.Where("id>?", nextId).Order("id asc").Limit(size).Find(&list)
				if len(list) == 0 {
					break
				}
				nextId = list[len(list)-1].ID
				for _, v := range list {
					p := pinyin.Pinyin(v.Name, a)
					py := make([]string, 0)
					for _, vv := range p {
						for _, vvv := range vv {
							py = append(py, vvv)
						}
					}
					db.Model(&v).Updates(models.Zone{
						Pinyin: strings.Join(py, " "),
					})
				}
			}
			cmd.Println("task done.")
		},
	}

	return cmd
}

func ZoneHot() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "zone_hot",
		Short: "刷入热门城市",
		Run: func(cmd *cobra.Command, args []string) {
			zone_biz.NewZoneBiz().MakeHotZones(models.New())
			cmd.Println("task done.")
		},
	}

	return cmd
}
