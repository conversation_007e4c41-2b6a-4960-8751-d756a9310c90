package sub

import (
	"fmt"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/orders"
	"roadtrip-api/src/models"
)

func OrderRefund() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "refund 【订单id】",
		Short: "订单退款",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			orderId, err := convertor.ToInt(args[0])
			if err != nil {
				cmd.PrintErrln(fmt.Errorf("订单id必须为数字:%v", err))
				return
			}
			db := models.New()
			if err = orders.ResetOrderRefundPayment(db, uint(orderId)); err != nil {
				cmd.PrintErrln(err)
				return
			} else {
				cmd.Println("退款单已重置为【通过审核】")
			}
		},
	}

	return cmd
}
