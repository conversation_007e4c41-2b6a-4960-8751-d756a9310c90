package sub

import (
	"errors"
	"fmt"
	go_amap "gitee.com/yjsoft-sh/go-amap"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"log"
	"roadtrip-api/src/components/amap"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/my_logger"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"strings"
	"time"
)

func GenAmapCmd() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "amap",
		Short: "高德",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}
	cmd.AddCommand(amapSyncScenic(), amapSyncHotel(), amapSyncZone())

	return cmd
}

func amapSyncZone() *cobra.Command {
	return &cobra.Command{
		Use:   "sync_zone",
		Short: "同步高德城市",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			res, err := amap.Sdk().Location.ChinaDistricts()
			my_logger.Debugf("amap Location ChinaDistricts", zap.Any("res", res), zap.Error(err))
			if err != nil {
				log.Panicf("amap ChinaDistricts error:%v", err)
				return
			}
			do := func(v *go_amap.District, parent models.Zone) models.Zone {
				var err error
				var zone models.Zone
				db.Model(&zone).Where(models.Zone{AdCode: v.Adcode}).Take(&zone)
				if zone.ID > 0 {
					return zone
				}

				geo := strings.Split(v.Center, ",")
				zone.Lng, err = convertor.ToFloat(geo[0])
				if err != nil {
					log.Panicf("convertor ToFloat error:%v", err)
					return zone
				}
				zone.Lat, err = convertor.ToFloat(geo[1])
				if err != nil {
					log.Panicf("convertor ToFloat error:%v", err)
					return zone
				}
				switch v.Level {
				case "province":
					zone.Level = constmap.ZoneLevelProvince
				case "city":
					zone.Level = constmap.ZoneLevelCity
				case "district":
					zone.Level = constmap.ZoneLevelDistrict
				default: //不保留street级别
					return zone
				}
				zone = models.Zone{
					Name:     v.Name,
					Level:    zone.Level,
					AdCode:   v.Adcode,
					ParentId: parent.ID,
					State:    constmap.Enable,
					Lng:      zone.Lng,
					Lat:      zone.Lat,
					Pic:      zone.Pic,
				}
				err = db.Create(&zone).Error
				if err != nil {
					log.Panicf("db create error:%v", err)
					return zone
				}
				return zone
			}
			var loop func(*go_amap.District, models.Zone)
			loop = func(v *go_amap.District, parent models.Zone) {
				zone := do(v, parent)
				for _, vv := range v.Districts {
					loop(vv, zone)
				}
			}
			for _, v := range res {
				loop(v, models.Zone{})
			}
		},
	}
}

func amapSyncScenic() *cobra.Command {
	return &cobra.Command{
		Use:   "sync_scenic [zone_ids]",
		Short: "同步高德的景点",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()

			var zones = getZone(db, strings.Split(args[0], ","))
			if len(zones) == 0 {
				panic(errors.New("请指定地区"))
			}

			types := "110000|110200|110201|110202|110203|110204|110205|110206|110207|110208|110209|110210|140100|140600|140700|141201"

			var total int

			for _, zone := range zones {
				page := 1
				for {
					request := &go_amap.PlaceRequest{
						Types:      types,
						Region:     zone.AdCode,
						CityLimit:  true,
						PageNum:    page,
						ShowFields: "business,photos",
					}

					if strings.Contains(zone.Name, "重庆") {
						request.Region = "重庆"
					} else if strings.Contains(zone.Name, "天津") {
						request.Region = "天津"
					}

					resp, err := amap.Sdk().Place.Search(request)
					if err != nil {
						my_logger.Errorf("import scenic error", zap.Error(err), zap.String("city", zone.Name))
						break
					} else if resp.Count == 0 {
						break
					}

					for _, pois := range resp.Pois {
						var c int64
						if db.Model(&models.ScenicOta{}).Where("ota_code=? and ota_id=?", constmap.OtaCodeAmap, pois.Id).Count(&c); c > 0 {
							continue
						}

						err = db.Transaction(func(tx *gorm.DB) error {
							scenicOta := models.ScenicOta{OtaId: pois.Id, OtaCode: constmap.OtaCodeAmap}
							if err = tx.Create(&scenicOta).Error; err != nil {
								return err
							}
							location := slice.Map(strings.Split(pois.Location, ","), func(index int, item string) float64 {
								f, _ := convertor.ToFloat(item)
								return f
							})
							if len(location) != 2 {
								return errors.New(fmt.Sprintf("%s => location error: %s", pois.Name, pois.Location))
							}

							scenic := models.Scenic{
								ZoneId:   zone.ID,
								Name:     pois.Name,
								Lng:      location[0],
								Lat:      location[1],
								Address:  pois.Address,
								Tel:      pois.Business.Tel,
								CostText: pois.Business.Cost,
							}
							if err = tx.Create(&scenic).Error; err != nil {
								return err
							}

							if tx.Model(&scenicOta).Updates(models.ScenicOta{ScenicId: scenic.ID}).RowsAffected == 0 {
								return errors.New(fmt.Sprintf("update scenic_ota error: %s", scenic.Name))
							}

							var scenicTypes []models.ScenicType
							for _, ty := range strings.Split(pois.Typecode, "|") {
								scenicTypes = append(scenicTypes, models.ScenicType{
									ScenicId: scenic.ID,
									TypeCode: ty,
								})
							}
							if len(scenicTypes) > 0 {
								if err = tx.Create(&scenicTypes).Error; err != nil {
									return err
								}
							}
							var scenicPics []models.ScenicPic
							for _, i := range pois.Photos {
								scenicPics = append(scenicPics, models.ScenicPic{
									ScenicId: scenic.ID,
									Url:      i.Url,
									Title:    i.Title,
								})
							}
							if len(scenicPics) > 0 {
								if err = tx.Create(&scenicPics).Error; err != nil {
									return err
								}

								update := models.Scenic{
									Pic: scenicPics[0].Url,
									Desc: slice.Join(slice.Map(scenicPics, func(index int, item models.ScenicPic) string {
										return "<img src=\"" + item.Url + "\"/>"
									}), ""),
								}
								if err = tx.Model(&scenic).Updates(update).Error; err != nil {
									return err
								}
							}

							return nil
						})
						if err != nil {
							my_logger.Errorf("import scenic error", zap.Error(err))
						} else {
							total += 1
						}
					}
					page += 1

					time.Sleep(time.Second)
				}
			}

			if total == 0 {
				return
			}
			now := time.Now()
			start := datetime.AddHour(now, -2)
			poi_biz.EsSyncBiz.FetchScenicSpots(db, start, false)
		},
	}
}

func amapSyncHotel() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "sync_hotel [zone_ids]",
		Short: "同步高德的酒店",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()

			var zones = getZone(db, strings.Split(args[0], ","))
			if len(zones) == 0 {
				panic(errors.New("请指定地区"))
			}
			for _, zone := range zones {
				page := 1

				for {
					request := &go_amap.PlaceRequest{
						Types:      "100000|100100|100101|100102|100103|100104|100105|100200|100201",
						Region:     zone.Name,
						CityLimit:  true,
						PageNum:    page,
						ShowFields: "business,photos",
					}
					resp, err := amap.Sdk().Place.Search(request)
					if err != nil {
						my_logger.Errorf("import hotel error", zap.Error(err))
						break
					} else if resp.Count == 0 {
						break
					}

					for _, pois := range resp.Pois {
						var c int64
						if db.Model(&models.HotelOta{}).Where("ota_code=? and ota_id=?", constmap.OtaCodeAmap, pois.Id).Count(&c); c > 0 {
							continue
						}

						err = db.Transaction(func(tx *gorm.DB) error {
							hotelOta := models.HotelOta{OtaId: pois.Id, OtaCode: constmap.OtaCodeAmap}
							if err = tx.Create(&hotelOta).Error; err != nil {
								return err
							}
							location := slice.Map(strings.Split(pois.Location, ","), func(index int, item string) float64 {
								f, _ := convertor.ToFloat(item)
								return f
							})
							if len(location) != 2 {
								return errors.New(fmt.Sprintf("%s => location error: %s", pois.Name, pois.Location))
							}

							hotel := models.Hotel{
								ZoneId:   zone.ID,
								Name:     pois.Name,
								Lng:      location[0],
								Lat:      location[1],
								Address:  pois.Address,
								Tel:      pois.Business.Tel,
								CostText: pois.Business.Cost,
							}
							if err = tx.Create(&hotel).Error; err != nil {
								return err
							}

							if tx.Model(&hotelOta).Updates(models.HotelOta{HotelId: hotel.ID}).RowsAffected == 0 {
								return errors.New(fmt.Sprintf("update scenic_ota error: %s", hotel.Name))
							}

							var hotelTypes []models.HotelType
							for _, ty := range strings.Split(pois.Typecode, "|") {
								hotelTypes = append(hotelTypes, models.HotelType{
									HotelId:  hotel.ID,
									TypeCode: ty,
								})
							}
							if len(hotelTypes) > 0 {
								if err = tx.Create(&hotelTypes).Error; err != nil {
									return err
								}
							}
							var hotelPics []models.HotelPic
							for _, i := range pois.Photos {
								hotelPics = append(hotelPics, models.HotelPic{
									HotelId: hotel.ID,
									Url:     i.Url,
									Title:   i.Title,
								})
							}
							if len(hotelPics) > 0 {
								if err = tx.Create(&hotelPics).Error; err != nil {
									return err
								}
							}

							return nil
						})
						if err != nil {
							my_logger.Errorf("import scenic error", zap.Error(err))
						}
					}
					page += 1
				}
			}

			now := time.Now()
			start := datetime.AddHour(now, -2)
			poi_biz.EsSyncBiz.FetchHotel(db, start, false)
		},
	}

	return cmd
}

func getZone(db *gorm.DB, zoneIds []string) []models.Zone {
	var zones []models.Zone
	db.Where("level=? and id in ?", constmap.ZoneLevelCity, zoneIds).Find(&zones)

	return zones
}
