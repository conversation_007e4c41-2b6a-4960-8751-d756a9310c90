package sub

import (
	"errors"
	"github.com/duke-git/lancet/v2/convertor"
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/poi_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/config"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	utils2 "roadtrip-api/src/utils"
	"time"
)

func GenPoiIndex() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "poi",
		Short: "poi数据",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}

	cmd.AddCommand(createPoiIndex(), importPoiData())

	return cmd
}

func importPoiData() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sync [all|zone|scenic|hotel]",
		Short: "同步数据到es中",
		Args:  cobra.ExactArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			act := args[0]
			force := "true" == cmd.Flag("force").Value.String()
			db := models.New()
			start := datetime.AddHour(time.Now(), -2)

			switch act {
			case "zone":
				poi_biz.EsSyncBiz.FetchCityZone(db, start, force)
			case "scenic":
				poi_biz.EsSyncBiz.FetchScenicSpots(db, start, force)
			case "hotel":
				poi_biz.EsSyncBiz.FetchHotel(db, start, force)
			case "all":
				poi_biz.EsSyncBiz.FetchCityZone(db, start, force)
				poi_biz.EsSyncBiz.FetchScenicSpots(db, start, force)
				poi_biz.EsSyncBiz.FetchHotel(db, start, force)
			default:
				panic(errors.New("未知的操作"))
			}
			cmd.Println("task done.")
		},
	}
	cmd.Flags().BoolP("force", "f", false, "同步全量数据")
	return cmd
}

func createPoiIndex() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "创建地点索引",
		Run: func(cmd *cobra.Command, args []string) {

			lines, _ := utils2.ReadLines(config.Config.Es.StopwordsPath)
			// 创建索引请求体
			createIndexBody := `
{
	"settings": {
		"analysis": {
			"filter": {
        		"my_stop": {
          			"type": "stop",
          			"stopwords": ` + convertor.ToString(lines) + `
				}
			},
			"analyzer": {
				"ik_smart_analyzer": {
					"type": "custom",
					"tokenizer": "ik_max_word",
					"filter": ["my_stop"]
				}
			}
		}
	},
	"mappings": {
		"dynamic": "true",
		"properties": {
			"name": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"keywords": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"location": {
				"type":  "geo_point"
			},
			"city_name": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"tags": {"type": "keyword"},
			"cate": {"type": "keyword"},
			"score": {"type": "float"},
			"ota": {"type": "keyword"},
			"ota_id": {"type": "keyword"},
			"brand_name":{
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			}
		}
	}
}`
			if err := es.CreateIndexType(constmap.EsIndexPoi, createIndexBody); err != nil {
				panic(err)
			}
		},
	}
}
