package sub

import (
	"io/fs"
	"path/filepath"
	"roadtrip-api/src/components/business/qiniu_biz"
	"roadtrip-api/src/components/my_logger"

	"github.com/qiniu/go-sdk/v7/storagev2/objects"
	"github.com/spf13/cobra"
	"go.uber.org/zap"
)

func Qiniu() *cobra.Command {
	c := &cobra.Command{
		Use:   "qiniu",
		Short: "七牛操作",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}

	c.AddCommand(uploadQiniu(), RmDir())

	return c
}

func RmDir() *cobra.Command {
	return &cobra.Command{
		Use:   "rmdir [dir]",
		Short: "删除目录",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			prefix := args[0]
			var marker string
			_ = qiniu_biz.List(prefix, func() string {
				return marker
			}, func(details *objects.ObjectDetails) error {
				if err := qiniu_biz.Delete(details.Name); err != nil {
					my_logger.Errorf("delete qiniu file error", zap.Error(err))
				}
				marker = details.Name

				return nil
			})
		},
	}
}

func uploadQiniu() *cobra.Command {
	return &cobra.Command{
		Use:   "upload [dir] [prefix]",
		Short: "将老文件迁移到七牛",
		Args:  cobra.MinimumNArgs(2),
		Run: func(cmd *cobra.Command, args []string) {
			dir := args[0]
			prefix := args[1]

			_ = filepath.WalkDir(dir, func(path string, d fs.DirEntry, err error) error {
				if err != nil {
					return err
				}
				if d.IsDir() {
					return nil
				}

				relPath, _ := filepath.Rel(dir, path)
				relPath = filepath.Join(prefix, relPath)

				if err := qiniu_biz.UploadFile(path, relPath); err != nil {
					my_logger.Errorf("upload file to qiniu error", zap.Error(err))
				} else {
					my_logger.Infof("upload file to qiniu success", zap.String("relPath", relPath))
				}

				return nil
			})
		},
	}
}
