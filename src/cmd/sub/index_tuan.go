package sub

import (
	"github.com/duke-git/lancet/v2/datetime"
	"github.com/spf13/cobra"
	"roadtrip-api/src/components/business/tuan_biz"
	"roadtrip-api/src/components/es"
	"roadtrip-api/src/constmap"
	"roadtrip-api/src/models"
	"time"
)

func GenTuanIndex() *cobra.Command {
	var cmd = &cobra.Command{
		Use:   "tuan",
		Short: "团游数据",
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}

	cmd.AddCommand(createTuanIndex(), importTuanData())

	return cmd
}

func createTuanIndex() *cobra.Command {
	return &cobra.Command{
		Use:   "create",
		Short: "创建团游索引",
		Run: func(cmd *cobra.Command, args []string) {

			// 创建索引请求体
			createIndexBody := `
{
	"settings": {
		"analysis": {
			"analyzer": {
				"ik_smart_analyzer": {
					"type": "custom",
					"tokenizer": "ik_smart"
				}
			}
		}
	},
	"mappings": {
		"dynamic": "true",
		"properties": {
			"name": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"city_name": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			},
			"cate": {
				"type": "keyword"
			},
			"keycnt": {
				"type": "text",
				"analyzer": "ik_smart_analyzer"
			}
		}
	}
}`
			if err := es.CreateIndexType(constmap.EsIndexTuan, createIndexBody); err != nil {
				panic(err)
			}
		},
	}
}

func importTuanData() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sync",
		Short: "同步数据到es中",
		Run: func(cmd *cobra.Command, args []string) {
			db := models.New()
			start := datetime.AddHour(time.Now(), -2)
			force := "true" == cmd.Flag("force").Value.String()
			tuan_biz.EsSyncBiz.FetchTuan(db, start, force)
		},
	}
	cmd.Flags().BoolP("force", "f", false, "同步全量数据")
	return cmd
}
