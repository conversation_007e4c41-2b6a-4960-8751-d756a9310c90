package cmd

import (
	"fmt"
	"github.com/spf13/cobra"
	"roadtrip-api/src/cmd/sub"
	"roadtrip-api/src/components/es"
)

func IndexCmd() *cobra.Command {
	var pCmd = &cobra.Command{
		Use:              "index",
		Short:            "es索引数据",
		TraverseChildren: true,
		Run: func(cmd *cobra.Command, args []string) {
			fmt.Println("hello")
		},
	}
	pCmd.AddCommand(sub.GenPoiIndex(), dropIndexCmd())
	pCmd.AddCommand(sub.GenTuanIndex())
	pCmd.AddCommand(sub.GenWishIndex())

	return pCmd
}

func dropIndexCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "drop [index]",
		Short: "删除es索引",
		Args:  cobra.MinimumNArgs(1),
		Run: func(cmd *cobra.Command, args []string) {
			if err := es.DropIndex(args[0]); err != nil {
				panic(err)
			}
		},
	}
}
