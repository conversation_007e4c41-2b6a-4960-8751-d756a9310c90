<template>
  <router-view v-if="isRouterAlive"></router-view>
</template>

<script setup>
import {nextTick, onMounted, provide, ref} from 'vue'
// 局部组件刷新
const isRouterAlive = ref(true)
const reload = () => {
  isRouterAlive.value = false
  nextTick(() => {
    isRouterAlive.value = true
  })
}

provide('reload', reload)
onMounted(() => {
})
</script>

<style lang="scss">
.el-select {
  min-width: 200px;
}
</style>
