import {loginInfo, logout as logoutApi} from '../../api/modules'
import {ElMessage} from 'element-plus'
import {defineStore} from 'pinia'
import {computed, reactive, ref} from 'vue'
import {useRouter} from "vue-router";

export const useUserStore = defineStore('user', () => {
    const token = ref(localStorage.getItem('token'))
    const userInfo = ref(Object.assign({}, JSON.parse(localStorage.getItem('userInfo'))))
    const isCollapse = ref(false)
    const themeConfig = reactive({
        primary: '#4060c7',
        tabColor: '#FFFFFF',
        footColor: '#606266',
        backgroundColor: '#FFFFFF',
        textColor: '#00000099',
        istags: true
    })
    const actionPermission = computed(() => {
        if (userInfo.value.permissions) {
            return userInfo.value.permissions.filter(item => item.type === 2)
        }

        return []
    })
    const pagePermission = computed(() => {
        if (userInfo.value.permissions) {
            return userInfo.value.permissions.filter(item => item.type === 1)
        }

        return []
    })
    const router = useRouter()

    function changeIsCollapse(value) {
        isCollapse.value = value
    }

    function setToken(t) {
        localStorage.setItem('token', t)
        token.value = t
    }

    function setUserInfo(data) {
        localStorage.setItem('userInfo', JSON.stringify(data))
        userInfo.value = data
    }

    function logout() {
        logoutApi().then(() => {
            localStorage.removeItem('token')
            token.value = ''
            router.push({path: '/login'})
        })
    }

    function login(form) {
        return new Promise((resolve, reject) => {
            loginInfo(form).then(res => {
                const {data} = res

                setToken(data.token)
                setUserInfo(data)

                ElMessage.success('登录成功')
                router.push({name: 'Home'})

                resolve()
            }).catch(() => {
                reject()
            })
        })
    }

    function checkActionPermission(action) {
        if (userInfo.value.is_admin) {
            return true
        }
        const index = actionPermission.value.findIndex(item => {
            return item.page === action
        })
        return index >= 0
    }

    function checkRoutePermission(to) {
        if (userInfo.value.is_admin || to.meta.constant) {
            return true
        }

        const index = pagePermission.value.findIndex(item => {
            return item.page === to.name
        })
        return index >= 0
    }

    return {
        token,
        setToken,
        login,
        logout,
        userInfo,
        themeConfig,
        isCollapse,
        changeIsCollapse,
        checkRoutePermission,
        checkActionPermission,
    }
})

export default {
    namespaced: true,
    state: {
        UserInfo: {},
        token: sessionStorage.getItem('token') || '',
        isCollapse: true
    },
    mutations: {
        setToken(state, token) {
            state.token = token
        },
        setUserInfo(state, userinfo) {
            state.UserInfo = userinfo
        },
    },

    actions: {}
}
