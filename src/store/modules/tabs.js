import {defineStore} from 'pinia'
import {ref} from 'vue'
import {useRouter} from "vue-router";

export const useTabStore = defineStore('tabs', () => {
    const tabsMenuList = ref([])
    const router = useRouter()

    function addTabs(obj) {
        let found = false
        for (let index = 0; index < tabsMenuList.value.length; index++) {
            const element = tabsMenuList.value[index]

            tabsMenuList.value[index].checked = false
            if (element.title === obj.title) {
                tabsMenuList.value[index].checked = true
                tabsMenuList.value[index].query = obj.query
                found = true
            }
        }

        if (!found) {
            tabsMenuList.value.forEach(item => {
                item.checked = false
            })

            if (!obj.close) {
                tabsMenuList.value.splice(0, 0, obj)
            } else {
                tabsMenuList.value.push(obj)
            }
        }
    }

    function deleteTag(obj) {
        if (tabsMenuList.value.length === 1) {
            return
        }

        for (let i = 0; i < tabsMenuList.value.length; i++) {
            const ele = tabsMenuList.value[i]
            if (obj === ele) {
                let last = tabsMenuList.value[i - 1]
                if (!last) {
                    last = tabsMenuList.value[i + 1]
                }

                last.checked = true
                tabsMenuList.value.splice(i, 1)

                router.push({
                    path: last.url,
                    query: last.query
                })
            }
        }
    }

    return {tabsMenuList, addTabs, deleteTag}
})
