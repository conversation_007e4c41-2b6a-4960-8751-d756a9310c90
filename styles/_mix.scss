@mixin center($direction: row) {
  display: flex;
  flex-direction: $direction;
  align-items: center;
  justify-content: center;
}

@mixin h3 {
  font-size: $h3-v2;
  font-weight: 500;
}

@mixin h1 {
  font-size: $h1-v2;
  font-weight: bold;
}

@mixin linear-gradient {
  background: linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1)) !important;
}

@mixin ellipse($line: 1) {
  overflow: hidden;
  text-overflow: ellipsis;

  @if $line > 1 {
    display: flex;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
    flex-direction: column;
    justify-content: center;
    line-clamp: $line;
  } @else {
    white-space: nowrap;
  }
}

@mixin tag-bg-colors {
  &:nth-child(4n+1) {
    background-color: #E7F4FF;
    color: #1890FF;
  }
  &:nth-child(4n+2) {
    background-color: #FFF2C1;
    color: #694209;
  }
  &:nth-child(4n+3) {
    background-color: #E8FFDF;
    color: #466909;
  }
  &:nth-child(4n+4) {
    background-color: #F5DFFF;
    color: #690969;
  }
}
