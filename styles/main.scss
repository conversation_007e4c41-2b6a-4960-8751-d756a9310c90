@import '_define.scss';
@import '_mix.scss';

page {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Source Han Sans SC', 'Noto Sans CJK SC', 'Helvetica Neue', Arial, sans-serif;
  background: white;
  color: #333;
  font-size: $fontsize;
}

.hide {
  display: none !important;
}

image {
  max-width: 100%;
  max-height: 100%;
}

.img-bg {
  background-repeat: no-repeat;
  background-size: contain;
}

view {
  box-sizing: border-box;
}

.link {
  color: $primary-color;
}

checkbox {
  transform: scale(0.7);
}

.descriptions {
  .description-item {
    @include center();
    justify-content: flex-start;
    margin-bottom: $padding;

    &:last-child {
      margin-bottom: 0;
    }

    > view {
      &:first-child {
        color: $info-color;
        width: 150rpx;
      }
    }
  }
}

//订单状态
.order-state-12,
.order-state-11 {
  color: $font-color-gray;
}

.order-state-1 {
  color: #ff4d4f;
}

.tag-list {
  gap: 16rpx;
  display: flex;

  text {
    padding: 4rpx 12rpx;
    font-size: 20rpx;

    &:nth-child(4n+1) {
      background: #C0FAFF;
      color: #1890FF;
    }

    &:nth-child(4n+2) {
      background: #FFF2C1;
      color: #694209;
    }

    &:nth-child(4n+3) {
      background: #F5DFFF;
      color: #690969;
    }

    &:nth-child(4n+4) {
      background: #B5F496;
      color: #29670A;
    }
  }
}

.tag {
  color: #1890ff;
  background: #e7f4ff;
  border-radius: $padding-small;
  padding: $padding-mini $padding-small;
  display: inline-block;
  margin-right: $padding-small;

  &:last-child {
    margin-right: 0;
  }

  &.cancel {
    color: $font-color-gray;
    background: $gray-color-v2;
  }

  &.success {
    background: $plan-color-2;
    color: white;
  }
}

rich-text {
  img {
    max-width: 100%;
    height: auto;
  }
}

.btn-more {
  width: 40rpx;
  height: 36rpx;
}

.btn {
  font-size: 28rpx;
  border-radius: 9rpx;
  padding: 15rpx 10rpx;
  margin-right: 10rpx;
  text-align: center;
  border: 1px solid $gray-color;

  &:last-child {
    margin-right: 0;
  }

  &.primary {
    border-color: $primary-color;
    background: linear-gradient(133deg, #18b8c4 1%, #008e98 96%);
    box-shadow: 2rpx 3rpx 8rpx 0rpx rgba(0, 111, 119, 0.6);
    color: #ffffff;
  }

  &.cancel {
    background: $gray-color;
    color: white;
  }

  &.primary-v2,
  &.danger {
    border: none;
    color: white;
  }

  &.primary-v2 {
    padding: $padding-v2;
    background: $primary-color-v2;
    border-radius: $border-radius-v2;
  }

  &.danger {
    background: $plan-color-1;
  }
}
