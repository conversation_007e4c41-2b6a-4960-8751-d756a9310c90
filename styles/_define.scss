$static_res_host: "https://rp.yjsoft.com.cn/yiban/static/";

$primary-color: #008e98;

$border-color: #d7d7d7;

$gray-color: #929292;

$price-color: #cc3333;
$info-color: #909399;
$lightgray-color: #edf0f7;
$vip-color: #ead7b9;
$warning-color: #e3563b;


$box-shadow: 0rpx 0rpx 7rpx 0rpx rgba(0, 0, 0, 0.1);
$border-radius: 16rpx;
$radius: 20rpx;

$h1: 42rpx;
$h2: 36rpx;
$h3: 30rpx;

$padding: 16rpx;


$normal-size: 28rpx;

// ----------- v2
$border-radius-v2: 10rpx;
$border-radius-middle: $border-radius-v2 * 2;
$border-radius-max: $border-radius-v2 * 3;

//颜色
$primary-color-v2: #FA6226;
$border-color-v2: #DFDFDF;
$font-color-gray: #909090;
$gray-color-v2: #E2E2E2;
$page-bg-color: #F7F7F9;
$black-color: #333333;
$black-color-v2: #666666;
$black-color-v3: #999999;

//时间线颜色
$plan-color-1: #FF4D4F;
/* 橙红色 */
$plan-color-2: #52C41A;
/* 绿色 */
$plan-color-3: #1890FF;
/* 蓝色 */
$plan-color-4: #FAAD14;
/* 粑紫色 */

$padding-page: 28rpx;
$padding-v2: 20rpx;
$padding-middle: 40rpx;
$padding-small: 10rpx;
$padding-mini: 6rpx;
$padding-big: $padding-v2 + 30rpx;

$fontsize: 28rpx;
$fontsize-small: 24rpx;
$fontsize-mini: 17rpx;
$h1-v1: 48rpx;
$h2-v2: 44rpx;
$h3-v2: 36rpx;
