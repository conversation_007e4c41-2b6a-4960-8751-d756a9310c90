# Roadtrip API

## 系统依赖

### 必需组件

1. **Go 环境**

   - Go 1.20
   - 包管理工具: Go Modules

2. **数据库**

   - MariaDB 10.11.13
   - Redis 6.0+
   - Elasticsearch 8.17.0 (需安装 ik 分词器)

3. **存储服务**
   - 七牛云对象存储

### 外部服务依赖

1. **微信服务**

   - 微信公众号
   - 微信支付
   - 企业微信

2. **地图服务**

   - 高德地图 API

3. **AI 服务**

   - Dify API

4. **第三方 OTA**
   - Ceekee
   - 同程
   - ZWY

### 开发工具

1. **PDF 生成**

   - Chrome/Chromium (用于 PDF 生成服务，需安装中文字体和表情符字体，包括微软雅黑字体及其粗体版本，PingFangSC)

2. **构建工具**
   - shell 环境
3. **视频处理**
   - ffmpeg(用于视频抽封面)

## 环境要求

- 操作系统: Linux/macOS
- 内存: 至少 4GB RAM
- 存储: 至少 20GB 可用空间
- 网络: 稳定的互联网连接
