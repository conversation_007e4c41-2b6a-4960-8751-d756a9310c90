# 义伴运营平台 (roadtrip-admin)

## 项目介绍

义伴运营平台是一个基于Vue 3 + Vite构建的前端管理系统，用于管理和运营义伴出行相关业务。该平台提供了用户管理、订单处理、内容管理等多种功能，并采用现代化的UI设计，提供流畅的用户体验。

## 技术栈

- **前端框架**: Vue 3
- **构建工具**: Vite 4
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **CSS预处理器**: SCSS
- **编辑器**: Wang Editor
- **数据可视化**: ECharts

## 项目结构

```plaintext
/src
├── api/            # API请求模块
├── assets/         # 静态资源
├── components/     # 公共组件
├── layout/         # 布局组件
├── router/         # 路由配置
├── store/          # Pinia状态管理
├── styles/         # 全局样式
├── utils/          # 工具函数
├── views/          # 页面视图组件
├── App.vue         # 根组件
└── main.js         # 入口文件
```

## 环境要求

- Node.js 16+
- PNPM 7+

## 安装与运行

### 安装依赖

```bash
pnpm install
```

### 开发环境运行

```bash
pnpm dev
```

应用将在 [http://localhost:3000](http://localhost:3000) 启动

### 构建生产版本

```bash
pnpm build
```

### 构建测试环境版本

```bash
pnpm build:test
```

### 预览构建版本

```bash
pnpm preview
```

## 环境变量配置

项目使用以下环境变量文件：

- `.env` - 基础环境变量
- `.env.development` - 开发环境变量
- `.env.test` - 测试环境变量

主要环境变量：

- `VITE_BASE_API` - API基础URL
- `VITE_H5_HOST` - H5站点URL

## VSCode调试配置

项目提供了VSCode的调试配置（`.vscode/launch.json`），支持以下调试模式：

1. **启动Chrome并打开应用** - 启动新的Chrome实例并导航到应用
2. **附加到Chrome** - 附加到已运行的Chrome实例
3. **启动Vite开发服务器** - 仅启动开发服务器
4. **全流程调试** - 启动服务器并在Chrome中调试

## 开发规范

项目使用ESLint进行代码风格检查，配置文件为`.eslintrc.js`。请确保提交代码前运行lint检查，保持代码风格一致。

## 更多信息

如需更多信息或帮助，请联系项目维护者。
