{"name": "roadtrip-admin", "version": "0.0.0", "private": true, "scripts": {"build": "vite build", "build:test": "vite build --mode test", "dev": "vite --host 0.0.0.0 --port 3000", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.2", "dayjs": "^1.11.11", "echarts": "^5.4.3", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.9.4", "nprogress": "^0.2.0", "pinia": "^2.1.6", "qs": "^6.12.1", "screenfull": "^6.0.1", "vue": "^3.4.27", "vue-qrcode": "^2.2.2", "vue-router": "^4.3.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "eslint": "^8.49.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-n": "^16.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.17.0", "sass": "^1.77.4", "vite": "^4.5.14"}}