<script setup>
import {onLaunch} from '@dcloudio/uni-app'

import {useGlobalStore} from './store/global.js'
import {init} from './api/index.js';
import {useUserStore} from "@/store/user";
import {trackSetOpenid, trackSetUserid} from "@/utils/tracker";

const globalStore = useGlobalStore()
const userStore = useUserStore()

function checkUpdate() {
  const updateManager = uni.getUpdateManager()
  updateManager.onCheckForUpdate(function (res) {

  })

  updateManager.onUpdateReady(function (res) {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate()
        }
      }
    })
  })
}

onLaunch(() => {

  // #ifdef MP
  checkUpdate()
  initNavBar()
  // #endif
  checkSource()

  init().then(res => {
    const {
      data
    } = res

    globalStore.serviceEmail = data.service_email
    globalStore.serviceWx = data.service_wx
    globalStore.shareMiniTask = data.share_mini_task
    globalStore.bindInviteGive = data.bind_invite_give
    globalStore.planPdfCostAmount = data.plan_pdf_cost_amount
    globalStore.serviceUrl = data.package_buy_service
    globalStore.adConfig = data.ad_config || {}

    if (data.zone_id > 0 && !globalStore.location.zone_id) {
      globalStore.setLocation({
        lat: data.lat,
        lng: data.lng,
        city: data.zone_name,
        zone_id: data.zone_id,
        name: data.zone_name,
      })
    }
  })
})

// #ifdef MP
function initNavBar() {
  uni.getSystemInfo({
    success(res) {
      const menuButton = uni.getMenuButtonBoundingClientRect()
      globalStore.navbar.titleHeight = menuButton.height
      globalStore.navbar.height = globalStore.navbar.titleHeight + menuButton.top
      globalStore.navbar.marginRight = res.screenWidth - menuButton.right
    }
  })

}

// #endif

function checkSource() {
  // #ifdef H5
  const userAgent = window.navigator.userAgent.toLowerCase()
  if (userAgent.indexOf('micromessenger') !== -1) {
    globalStore.channel = 'weixin-h5'
  }
  // #endif

  // #ifdef MP
  globalStore.channel = 'mp-weixin'
  if (userStore.isLogin) {
    trackSetOpenid(userStore.userInfo.open_id)
    trackSetUserid(userStore.userInfo.user_id)
  }
  // #endif
}
</script>

<style>
@import 'static/font/iconfont.css';
</style>

<style lang="scss">
@import './styles/main.scss';
</style>
