{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "义伴出行",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/details/expense",
      "style": {
        "navigationBarTitleText": "行程费用明细",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/details/details",
      "style": {
        "navigationBarTitleText": "行程详情",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/details/pics",
      "style": {
        "navigationBarTitleText": "图集",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/hotel/hotel",
      "style": {
        "navigationBarTitleText": "酒店详情"
      }
    },
    {
      "path": "pages/scenic/scenic",
      "style": {
        "navigationBarTitleText": "景点详情"
      }
    },
    {
      "path": "pages/option/scene/scene",
      "style": {
        "navigationBarTitleText": "景点搜索"
      }
    },
    {
      "path": "pages/option/station",
      "style": {
        "navigationBarTitleText": "交通方式"
      }
    },
    {
      "path": "pages/option/hotel/hotel",
      "style": {
        "navigationBarTitleText": "酒店搜索"
      }
    },
    {
      "path": "pages/search/search",
      "style": {
        "disableScroll": true,
        "navigationStyle": "custom",
        "navigationBarTitleText": "搜索"
      }
    },
    {
      "path": "pages/search/poi",
      "style": {
        "navigationBarTitleText": "搜索"
      }
    },
    {
      "path": "pages/tuan/detail",
      "style": {
        "navigationBarTitleText": "旅游商品详情"
      }
    },
    {
      "path": "pages/option/cities/cities",
      "style": {
        "disableScroll": true,
        "navigationStyle": "custom",
        "navigationBarTitleText": "选择城市"
      }
    },
    {
      "path": "pages/orderpreview/orderpreview",
      "style": {
        "navigationBarTitleText": "填写订单信息"
      }
    },
    {
      "path": "pages/pay/pay",
      "style": {
        "navigationBarTitleText": "订单支付"
      }
    },
    {
      "path": "pages/orders/orderdetail",
      "style": {
        "navigationBarTitleText": "订单详情"
      }
    },
    {
      "path": "pages/orders/refund",
      "style": {
        "navigationBarTitleText": "退款详情"
      }
    },
    {
      "path": "pages/card/card",
      "style": {
        "navigationBarTitleText": "机上WIFI上网码"
      }
    },
    {
      "path": "pages/card/got/got",
      "style": {
        "navigationBarTitleText": "机上WIFI上网码"
      }
    },
    {
      "path": "pages/mine/plans/plans",
      "style": {
        "navigationBarTitleText": "我的行程规划"
      }
    },
    {
      "path": "pages/orders/orders",
      "style": {
        "navigationBarTitleText": "我的订单"
      }
    },
    {
      "path": "pages/mine/invitecode",
      "style": {
        "navigationBarTitleText": "邀请码",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/discover/discover",
      "style": {
        "navigationBarTitleText": "发现"
      }
    },
    {
      "path": "pages/tuan/apply/apply",
      "style": {
        "navigationBarTitleText": "确认订单",
        //#ifdef H5
        "navigationStyle": "custom"
        //#endif
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mine/wifis/wifis",
      "style": {
        "navigationBarTitleText": "我的机上Wi-Fi"
      }
    },
    {
      "path": "pages/points/points",
      "style": {
        "navigationBarTitleText": "积分中心",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/peoples/peoples",
      "style": {
        "navigationBarTitleText": "出行人管理"
      }
    },
    {
      "path": "pages/chat/chat",
      "style": {
        "disableScroll": true,
        "navigationBarTitleText": "对话",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/activity/activity",
      "style": {
        "navigationBarTitleText": "活动",
        "navigationStyle": "custom",
        "disableSwipeBack": true
      }
    },
    {
      "path": "pages/plans/plans",
      "style": {
        "navigationBarTitleText": "推荐行程",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/activity/shj/shj_activity",
      "style": {
        "navigationBarTitleText": "探秘上古神话",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/h5/h5",
      "style": {
        "navigationBarTitleText": "H5",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/edittraveldetails/edittraveldetails",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/mine/myactivities/myactivities",
      "style": {
        "navigationBarTitleText": "我的活动"
      }
    },
    {
      "path": "pages/publish-wish/publish-wish",
      "style": {
        "navigationBarTitleText": "发布心愿",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/my-wish-list/my-wish-list",
      "style": {
        "navigationBarTitleText": "我的心愿单",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/wish-square/wish-square",
      "style": {
        "navigationBarTitleText": "心愿广场",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/wish-detail/wish-detail",
      "style": {
        "navigationBarTitleText": "心愿详情",
        "navigationStyle": "custom"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "义伴",
    "navigationBarBackgroundColor": "#fff",
    "backgroundTextStyle": "light"
  },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "test",
        "path": "pages/h5/h5",
        "query": "url=http%3A%2F%2F192.168.10.9%3A3030%2F%23%2Factivity%2Fposter"
      }
    ]
  },
  "uniIdRouter": {}
}
