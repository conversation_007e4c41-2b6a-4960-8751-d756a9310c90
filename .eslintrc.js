module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    'standard',
    'plugin:vue/vue3-essential'
  ],
  overrides: [
    {
      env: {
        node: true
      },
      files: [
        '.eslintrc.{js,cjs}'
      ],
      parserOptions: {
        sourceType: 'script'
      }
    }
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'vue'
  ],
  rules: {
    quotes: ['error', 'single', { allowTemplateLiterals: true, avoidEscape: true }],
    semi: ['error', 'never'],  // 要求使用分号
    'array-bracket-spacing': ['error', 'never'], // 数组括号内不需要空格
    'object-curly-spacing': 'off', // 关闭对象大括号空格检查
    'no-multi-spaces': 'off', // 关闭多余空格警告
    'comma-dangle': 'off' // 关闭末尾逗号警告
  }
}
