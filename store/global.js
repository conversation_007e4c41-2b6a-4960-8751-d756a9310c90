import {ref} from 'vue'
import {defineStore} from 'pinia'
import {geo} from '../api'
import {cacheGet, cacheSet} from "@/utils/cache";
import {deepToRaw} from "@/utils";

export const useGlobalStore = defineStore('store', () => {
  let location = ref({
    lng: '',
    lat: '',
    address: '',
    city: '',
    zone_id: '',
  })
  let tmp = cacheGet('location_v2')
  if (tmp) {
    location.value = tmp
  }
  const shareMiniTask = ref(null)
  const shareUid = ref('')
  const bindInviteGive = ref(0)
  const planPdfCostAmount = ref(0)
  const adConfig = ref({})
  
  const navbar = ref({
    height: 0,
    titleHeight: 32,
    marginRight: 93,
    contentWidth: 0,
  })
  
  const editPlan = ref({
    sections: [],
    prompt_options: {}
  })
  const viewedTips = ref(uni.getStorageSync('viewedTips') ?? [])
  
  const histories = ref(uni.getStorageSync('histories') ?? [])
  const channel = ref('h5')
  const serviceWx = ref('')
  const serviceEmail = ref('')
  const scene = ref({})
  const serviceUrl = ref('')
  const events = {
    pageTap: [],
  }
  const data = ref({})
  const city = ref({}) //城市选择
  const context_id = ref('') // 聊天上下文ID
  
  if (!(viewedTips.value instanceof Array)) {
    viewedTips.value = []
  }
  
  if (histories.value.length === 0) {
    histories.value = []
  }
  
  function viewTip(type) {
    if (viewedTips.value.includes(type)) {
      return
    }
    
    viewedTips.value.push(type)
    
    uni.setStorageSync('viewedTips', viewedTips.value)
  }
  
  function setLocation(l) {
    Object.assign(location.value, l)
    
    cacheSet('location_v2', deepToRaw(location.value), 60 * 20)
  }
  
  function getLocation(force = false) {
    return new Promise((resolve, reject) => {
      const loc = cacheGet('location_v2')
      
      if (!loc || force) {
        uni.getLocation({
          isHighAccuracy: true,
          success: async (res) => {
            
            const params = {
              query_lng: res.longitude,
              query_lat: res.latitude,
            }
            const {data} = await geo(params)
            Object.assign(data, {
              lat: res.latitude,
              lng: res.longitude,
            })
            setLocation(data)
            resolve(data)
          },
          fail(res) {
            resolve(location.value)
          }
        })
        
      } else {
        resolve(location.value)
      }
    })
    
  }
  
  function pageTap() {
    Object.entries(events).forEach(([name, fns]) => {
      fns.forEach(fn => fn())
    })
  }
  
  function registerEvent(name, fn) {
    events[name]?.push(fn)
  }
  
  function setData(type, d) {
    data.value = {
      type,
      data: d
    }
  }
  
  function clearData() {
    data.value = {}
  }
  
  // 设置聊天上下文ID
  function setContextId(id) {
    context_id.value = id
    // 使用cacheSet保存context_id，设置过期时间为7天
    cacheSet('chat_context_id', id, 7 * 24 * 60 * 60)
  }
  
  // 获取聊天上下文ID
  function getContextId() {
    // 如果context_id.value为空，则尝试从缓存中获取
    if (!context_id.value) {
      const cachedContextId = cacheGet('chat_context_id')
      if (cachedContextId) {
        context_id.value = cachedContextId
      }
    }
    return context_id.value
  }
  
  return {
    data,
    registerEvent,
    pageTap,
    viewedTips,
    viewTip,
    channel,
    setLocation,
    getLocation,
    histories,
    serviceEmail,
    serviceWx,
    location,
    scene,
    navbar,
    editPlan,
    city,
    setData,
    clearData,
    setContextId,
    getContextId,
    context_id,
    shareMiniTask,
    shareUid,
    bindInviteGive,
    planPdfCostAmount,
    serviceUrl,
    adConfig,
  }
})
