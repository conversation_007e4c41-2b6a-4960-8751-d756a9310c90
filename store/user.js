import {computed, ref} from 'vue'
import {defineStore} from 'pinia'
import {login as loginX} from '../api/index.js'
import {trackSetOpenid, trackSetUserid} from "@/utils/tracker";


function isObject(object) {
  return object !== null && typeof object === 'object'
}

export const useUserStore = defineStore('user', () => {
  const userInfo = ref({})
  
  const tmp = uni.getStorageSync('userinfo')
  if (tmp) {
    if (isObject(tmp)) {
      Object.assign(userInfo.value, tmp)
    } else {
      Object.assign(userInfo.value, JSON.parse(tmp))
    }
  }
  
  const token = computed(() => {
    return userInfo.value?.token || ''
  })
  const isLogin = computed(() => token.value !== '')
  
  function login(data) {
    return new Promise((resolve, reject) => {
      loginX(data).then((res) => {
        const {
          data
        } = res
        Object.assign(userInfo.value, data)
        uni.setStorageSync('userinfo', data)
        
        trackSetOpenid(data.open_id)
        trackSetUserid(data.user_id)
        
        
        resolve()
        
      }).catch(res => {
        reject(res)
      })
    })
    
  }
  
  return {
    token,
    login,
    userInfo,
    isLogin,
  }
})