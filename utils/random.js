// 字符集常量定义
const CHARSET = {
  NUMBERS: '0123456789',
  LOWERCASE: 'abcdefghijklmnopqrstuvwxyz',
  UPPERCASE: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
  SPECIAL: '!@#$%^&*()_+-=[]{}|;:,.<>?'
}

// 预定义的字符集组合
const PRESET_CHARSETS = {
  // 仅数字
  numeric: CHARSET.NUMBERS,
  // 仅小写字母
  lowercase: CHARSET.LOWERCASE,
  // 仅大写字母
  uppercase: CHARSET.UPPERCASE,
  // 字母（大小写）
  alpha: CHARSET.LOWERCASE + CHARSET.UPPERCASE,
  // 字母数字（默认）
  alphanumeric: CHARSET.NUMBERS + CHARSET.LOWERCASE + CHARSET.UPPERCASE,
  // 包含特殊字符的完整字符集
  full: CHARSET.NUMBERS + CHARSET.LOWERCASE + CHARSET.UPPERCASE + CHARSET.SPECIAL
}

// 易混淆字符映射
const CONFUSING_CHARS = {
  '0': 'O',
  '1': 'lI',
  '2': 'Z',
  '5': 'S',
  '6': 'G',
  '8': 'B',
  '9': 'g'
}

/**
 * 生成指定长度的随机字符串
 * @param {number} length - 字符串长度，必须为正整数
 * @param {object} options - 配置选项
 * @param {string} options.charset - 字符集类型，可选值：'numeric', 'lowercase', 'uppercase', 'alpha', 'alphanumeric', 'full'，默认为'alphanumeric'
 * @param {string} options.customCharset - 自定义字符集，如果提供则忽略charset参数
 * @param {boolean} options.excludeConfusing - 是否排除易混淆字符，默认为false
 * @param {string[]} options.excludeChars - 要排除的特定字符数组
 * @returns {string} 生成的随机字符串
 * @throws {Error} 当参数无效时抛出错误
 *
 * @example
 * // 生成8位字母数字字符串
 * generateRandomString(8)
 *
 * // 生成6位纯数字字符串
 * generateRandomString(6, { charset: 'numeric' })
 *
 * // 生成10位字符串，排除易混淆字符
 * generateRandomString(10, { excludeConfusing: true })
 *
 * // 使用自定义字符集
 * generateRandomString(5, { customCharset: 'ABCDEF0123456789' })
 */
export function generateRandomString(length, options = {}) {
  // 参数验证
  if (!Number.isInteger(length) || length <= 0) {
    throw new Error('长度必须为正整数')
  }

  // 解构配置选项，设置默认值
  const {
    charset = 'alphanumeric',
    customCharset = '',
    excludeConfusing = false,
    excludeChars = []
  } = options

  // 确定使用的字符集
  let finalCharset = ''

  if (customCharset) {
    // 使用自定义字符集
    finalCharset = customCharset
  } else {
    // 使用预设字符集
    if (!PRESET_CHARSETS[charset]) {
      throw new Error(`不支持的字符集类型: ${charset}。支持的类型: ${Object.keys(PRESET_CHARSETS).join(', ')}`)
    }
    finalCharset = PRESET_CHARSETS[charset]
  }

  // 处理排除易混淆字符
  if (excludeConfusing) {
    const confusingChars = Object.keys(CONFUSING_CHARS).join('') + Object.values(CONFUSING_CHARS).join('')
    finalCharset = finalCharset.split('').filter(char => !confusingChars.includes(char)).join('')
  }

  // 处理排除特定字符
  if (excludeChars.length > 0) {
    finalCharset = finalCharset.split('').filter(char => !excludeChars.includes(char)).join('')
  }

  // 验证最终字符集是否为空
  if (finalCharset.length === 0) {
    throw new Error('字符集为空，无法生成随机字符串')
  }

  // 生成随机字符串
  let result = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * finalCharset.length)
    result += finalCharset[randomIndex]
  }

  return result
}