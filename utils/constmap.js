export const UserTypeNormal = 10 //普通用户
export const UserTypeVip = 20 //黄金会员

export const IdTypeIdCard = 1 //证件类型（身份证）

export const Disable = 2
export const Enable = 1

export const PayTypePackage = 30 //订单类型（套餐）
export const PayTypeOrder = 10 //订单类型（订单）

export const PayStateNotPay = 1 //支付状态（未支付）
export const PayStatePayed = 2 //支付状态（已支付）
export const PayStatePartPay = 3 //支付状态（部分支付）

export const OrderTypeHotel = 'hotel'
export const OrderTypeTicket = 'ticket'
export const OrderTypeTravel = 'travel'
export const OrderTypeTuan = 'tuan'

export const OrderSubTypeTicket = 2 //子订单类型（门票）
export const OrderSubTypeTuan = 1 //子订单类型（团游）
export const OrderSubTypeHotel = 3 //子订单类型（酒店）

export const VipBenefitTypeDiscount = 1 //会员权益类型（折扣）
export const VipBenefitTypeTimes = 2 //会员权益类型（次数）

export const HotelGuestTypeMainland = 1
export const HotelGuestTypeOverseas = 2
export const HotelGuestTypeAll = 3

export const HotelGuestTypeMap = {
  [HotelGuestTypeMainland]: '仅大陆旅客',
  [HotelGuestTypeOverseas]: '仅大陆和港澳台',
  [HotelGuestTypeAll]: '大陆、港澳台及外国人'
}

export const TimelineTypeScene = 'scene'
export const TimelineTypeBus = 'bus'
export const TimelineTypeText = 'text'
export const TimelineTypeHotel = 'hotel'
export const TimelineTypePlane = 'plane'
export const TimelineTypeTrain = 'train'

export const ChatRoleUser = 'user' //聊天角色（用户）
export const ChatRoleSystem = 'system' //聊天角色（系统）

export const conflictTips = ['index-retry', 'index-edit', 'index-delete']

export const GlobalTypePeople = 'people' //全局数据类型（出行人）
export const GlobalTypeTravel = 'travel' //全局数据类型（旅行）

export const CustomerTypeAdult = 1 //成人
export const CustomerTypeChild = 2 //儿童

export const DateFmtMonth = 'YYYY-MM'

export const WishMemberState = { //ished状态
  WaitReview: 1, //等待审核
  Approved: 2, //审核通过
  Rejected: 3, //审核拒绝
}

export const LikeObjType = { //点赞对象类型
  Wish: 1,
}

export const BudgetType = {
  Single: 1, //单
  Team: 2 //团队
}

export const WishMediaType = { //媒体类型
  Image: 1,
  Video: 2
}

export const PoiType = { //POI类型
  City: 3,
  Scene: 1,
  Hotel: 2,
}

export const WishOpenScope = { //开放范围
  Public: 1,
  Private: 2
}
