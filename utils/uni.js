export function uniGetProvider(service) {
  return new Promise((resolve, reject) => {
    uni.getProvider({
      service,
      success: resolve,
      fail: reject,
    })
  })
}

export function uniLogin(params) {
  return new Promise((resolve, reject) => {
    uni.login({
      ...params,
      success: resolve,
      fail: reject,
    })
  })
}


export function uniPay(params) {
  return new Promise((resolve, reject) => {
    uni.requestPayment({
      ...params,
      success: resolve,
      fail: reject,
    })
  })
}

export function uniGetSystemInfo() {
  return new Promise((resolve, reject) => {
    uni.getSystemInfo({
      success(d) {
        resolve(d)
      },
      fail(err) {
        reject(err)
      },
    })
  })
}

export function uniGetMenuButtonBounding() {
  return new Promise((resolve, reject) => {
    // #ifdef MP
    resolve(uni.getMenuButtonBoundingClientRect())
    // #endif
    // #ifndef MP
    resolve({
      left: 0,
      top: 0,
      height: 0,
      width: 0,
    })
    // #endif
  })
}

/**
 * 选择文件（图片或视频）
 * @param {Object} options - 配置项
 * @param {string} options.sourceType - 来源：'album'（相册）或 'camera'（相机），默认 ['album', 'camera']
 * @returns {Promise<File>} - 返回 File 对象
 */
export function uniChooseMedia(options = {}) {
  return new Promise((resolve, reject) => {
    const {sourceType = ['album', 'camera'], size} = options;
    
    // 小程序端使用 chooseMedia
    // #ifdef MP
    uni.chooseMedia({
      count: 1,
      mediaType: ['image', 'video'],
      sourceType,
      success: async (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        const file = await new Promise((resolve) => {
          uni.getFileInfo({
            filePath: tempFilePath,
            success: (info) => {
              if (size && info.size > size) {
                reject(new Error('文件大小超过限制'));
                return;
              }
              resolve(tempFilePath);
            },
          });
        });
        resolve(file);
      },
    });
    // #endif
    
    // H5 端使用 uni.chooseFile
    // #ifdef H5
    uni.chooseFile({
      count: 1,
      extension: ['jpg', 'jpeg', 'png', 'gif', 'mp4'],
      success: (res) => {
        if (size && res.tempFiles[0].size > size) {
          reject(new Error('文件大小超过限制'));
          return;
        }
        resolve(res.tempFiles[0]);
      }
    });
    // #endif
  });
}
