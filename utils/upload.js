import {hideLoading, showLoading, showToast} from "@/utils/index";
import {uploadToken} from "@/api";

export function imageUpload(file) {
  const url = import.meta.env.VITE_API_BASE_URL + '/api/v1/front/upload';
  
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url,
      filePath: file,
      name: 'file',
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code !== 0) {
          showToast(`上传失败: ${data.msg}`)
          reject(data.msg)
          return
        }
        resolve(data.data)
      },
      fail: (err) => {
        showToast(`上传失败: ${err.toString()}`)
      }
    })
  })
}

function generateKeyWithDate(prefix, file) {
  // 格式化当前日期为YYYYMMDD
  const currentDate = new Date();
  const formattedDate = currentDate.toISOString().slice(0, 10).replace(/-/g, '');
  
  // 提取文件扩展名
  let fileExtension = '';
  if (typeof file === 'string') {
    fileExtension = file.includes('.') ? '.' + file.split('.').pop() : '';
  } else {
    fileExtension = file.name.includes('.') ? '.' + file.name.split('.').pop() : '';
  }
  
  // 生成随机文件名并组合路径
  const randomString = Math.random().toString(36).substr(2);
  return `${prefix}/${formattedDate}/${Date.now()}_${randomString}${fileExtension}`;
}

export function qiniuUpload(file, expire = 0) {
  return new Promise((resolve, reject) => {
    showLoading('上传中...');
    uploadToken({exp_days: expire}).then(({data: {bucket,domain, token, prefix}}) => {
      const uploadUrl = 'https://up.qiniup.com';
      const formData = {
        token,
        key: generateKeyWithDate(prefix, file),
      };
      
      const data = {
        formData,
        name: 'file'
      }
      
      const {uniPlatform} =uni.getSystemInfoSync()
      if (uniPlatform === 'web') {
        data.file = file
      } else {
        data.filePath = file
      }
      const options = {
        url: uploadUrl,
        ...data,
        success: (res) => {
          hideLoading();
          resolve(`${domain}/${formData.key}`)
        },
        fail: (err) => {
          hideLoading();
          showToast(`上传失败: ${err.errMsg}`)
          reject(new Error(err.errMsg));
        },
      }
      console.log(options)
      
      uni.uploadFile(options)
      
    }).catch(err => {
      hideLoading();
      showToast(err.toString());
      reject(err);
    })
  });
}
