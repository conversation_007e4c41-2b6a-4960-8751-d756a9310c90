import request from "../utils/request.js";

export function chatList() {
  return request({
    url: "/api/v1/front/chat/list",
    method: "get",
  });
}

export function chatDetail(data) {
  return request({
    url: "/api/v1/front/chat/detail",
    method: "get",
    data,
  });
}

export function startNewChat(data) {
  return request({
    url: "/api/v1/front/chat/new_stream_chat",
    method: "post",
    data,
  });
}

export function chatIndex() {
  return request({
    url: "/api/v1/front/chat/index",
    method: "get"
  });
}
