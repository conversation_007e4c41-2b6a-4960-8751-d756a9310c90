import request from '../utils/request.js'

export function tuanOption() {
	return request({
		url: '/api/v1/front/tuan/option',
		method: 'get',
	})
}

export function tuans(data) {
	return request({
		url: '/api/v1/front/tuan/list',
		method: 'get',
		data,
	})
}

export function productDates(data) {
	return request({
		url: '/api/v1/front/tuan/dates',
		method: 'get',
		data,
	})
}

export function productSkudates(data) {
	return request({
		url: '/api/v1/front/tuan/skudates',
		method: 'get',
		data,
	})
}

export function productDetail(id) {
	return request({
		url: '/api/v1/front/tuan/detail',
		method: 'get',
		data: {
			id
		}
	})
}