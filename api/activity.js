import request from "../utils/request.js";

export function activityPoster(data) {
  return request({
    method: "get",
    url: "/api/v1/front/activity/poster",
    data: {...data, return_url: 1},
  });
}

export function taskLogs(id) {
  return request({
    url: "/api/v1/front/activity/tasklogs",
    method: "get",
    data: {task_id: id},
  });
}

export function activityDetail(data) {
  return request({
    url: "/api/v1/front/activity/detail",
    method: "get",
    data,
  });
}

export function activityJoin(data) {
  return request({
    url: "/api/v1/front/activity/joinact",
    method: "post",
    data,
  });
}

export function activityGetReward(uuid, task_id) {
  return request({
    url: "/api/v1/front/activity/getreward",
    method: "post",
    data: {uuid, task_id},
  });
}

export function shjActivityGetTaskInfo(data) {
  return request({
    url: "/api/v1/front/tuan/ordertaskinfo",
    method: "get",
    data,
  });
}

export function clockInActivity(data) {
  return request({
    url: "/api/v1/front/tuan/dotask",
    method: "post",
    data,
  });
}

export function getTaskReward(data) {
  return request({
    url: "/api/v1/front/tuan/gettaskreward",
    method: "post",
    data,
  });
}

export function doActivityTask(data) {
  return request({
    url: '/api/v1/front/activity/dotask',
    method: 'post',
    data,
  })
}