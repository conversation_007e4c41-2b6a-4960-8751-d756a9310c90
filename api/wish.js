import request from '../utils/request.js'

//心愿的ai建议
export function wishAiSuggest(data) {
  return request({
    url: '/api/v1/front/wish/ai_suggest',
    method: 'get',
    data,
  })
}

//申请同行
export function applyJoinWish(data) {
  return request({
    url: '/api/v1/front/wish/member_apply',
    method: 'post',
    data,
  })
}

//心愿详情
export function wishDetail(id) {
  return request({
    url: '/api/v1/front/wish/detail',
    method: 'get',
    data: {wish_id: id},
  })
}

//心愿广场列表
export function wishSquareList(data) {
  return request({
    url: '/api/v1/front/wish/squares',
    method: 'get',
    data,
  })
}

//心愿广场首页初始化
export function wishIndex() {
  return request({
    url: '/api/v1/front/wish/square_index',
    method: 'get',
  })
}

export function wishSubmit(data) {
  return request({
    url: '/api/v1/front/wish/submit',
    method: 'post',
    data,
  })
}
