import request from '../utils/request.js'

export function scenicDetail(id, loading = true) {
  return request({
    url: '/api/v1/front/scenic/detail',
    method: 'get',
    data: {
      id
    }
  }, loading)
}

export function sceneTickets(scenic_id, loading = true) {
  return request({
    method: 'get',
    url: '/api/v1/front/scenic/tickets',
    data: {
      scenic_id
    },
  }, loading)
}

export function sceneSearch(data, loading = true) {
  return request({
    method: 'get',
    url: '/api/v1/front/scenic/search',
    data,
  }, loading)
}