import request from '../utils/request.js'

export function userWifis(data) {
	return request({
		url: '/api/v1/front/user/wificards',
		method: 'get',
		data,
	})
}

export function cardGot(data) {
	return request({
		url: '/api/v1/front/card/got',
		method: 'get',
		data,
	})
}

export function cardGet(data) {
	return request({
		url: '/api/v1/front/card/get',
		method: 'post',
		data,
	})
}

export function cardInfo(code) {
	return request({
		url: '/api/v1/front/card/info',
		method: 'get',
		data: {
			code
		}
	})
}