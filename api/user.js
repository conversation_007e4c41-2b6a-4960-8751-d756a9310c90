import request from '../utils/request.js'

export function bindInvite(code) {
	return request({
		method: 'post',
		url: '/api/v1/front/user/bind_invite_code',
		data: {
			invite_code: code
		},
	})
}

export function peopleRemove(id) {
	return request({
		method: 'post',
		url: '/api/v1/front/user/people/del',
		data: {
			people_id: id
		},
	})
}

export function peopleList(data) {
	return request({
		url: '/api/v1/front/user/people/list',
		method: 'get',
		data
	})
}

export function peopleSave(data) {
	return request({
		url: '/api/v1/front/user/people/save',
		method: 'post',
		data
	})
}

export function peopleOptions() {
	return request({
		url: '/api/v1/front/user/people/options',
		method: 'get',
	})
}

export function planRemove(id) {
	return request({
		method: 'post',
		url: '/api/v1/front/user/planremove',
		data: { plan_id: id },
	})
}

export function userVipPay(data = {}) {
	return request({
		url: '/api/v1/front/user/vippay',
		method: 'post',
		data
	})
}

export function userVipBenefit() {
	return request({
		url: '/api/v1/front/user/vipbenefit',
		method: 'get',
	})
}

export function userPlans(data) {
	return request({
		url: '/api/v1/front/user/planlist',
		method: 'get',
		data
	})
}

export function userWishList(data) {
	return request({
		url: '/api/v1/front/user/wish/list',
		method: 'get',
		data
	})
}

//关闭心愿单
export function wishClose(data) {
	return request({
		url: '/api/v1/front/user/wish/close',
		method: 'post',
		data,
	})
}

export function updateProfile(data) {
	return request({
		url: '/api/v1/front/user/upprofile',
		method: 'post',
		data
	})
}
