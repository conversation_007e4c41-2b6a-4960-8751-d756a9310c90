import request from '../utils/request.js'

/**
 * 广告观看权限检查
 * @param {Object} data - 请求参数
 * @param {Boolean} loading
 * @returns {Promise} - 返回请求结果
 */
export function adViewGet(data = {}, loading = true) {
  return request({
    url: '/api/v1/front/user/ad_view_get',
    method: 'get',
    data
  }, loading)
}

/**
 * 广告观看完成后增加用户积分
 * @param {Object} data - 增加的积分数量，默认为30
 * @returns {Promise} - 返回请求结果
 */
export function adViewPost(data) {
  return request({
    method: 'post',
    url: '/api/v1/front/user/ad_view_post',
    data
  });
}
