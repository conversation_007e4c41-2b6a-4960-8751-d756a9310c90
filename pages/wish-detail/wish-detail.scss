@use "../../styles/mix";
@use "../../styles/define";

.wish-detail {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  @include mix.linear-gradient();
  padding: define.$padding-page define.$padding-page 150rpx;
  position: relative;
}

.section {
  border-top: 2rpx solid #EDEEF0;

  .section-title {
    display: block;
    margin-top: 32rpx;
    font-weight: bold;
  }

  .section-body {
    padding: 20rpx 20rpx 30rpx 20rpx;
  }
}

.todo-list {
  .list {
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .todo-item {
      display: flex;
      gap: 4rpx;
      align-items: center;
    }
  }
}

.tags {
  text {
    @include mix.tag-bg-colors();
  }
}

.title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.info-section {
  margin-bottom: 30rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;

  image {
    width: 40rpx;
    height: 40rpx;
  }

  > view {
    @include mix.center();
    gap: 8rpx;
  }
}

.swiper {
  height: 390rpx;
  margin-bottom: 30rpx;

  image, video {
    width: 100%;
    height: 100%;
    border-radius: 20rpx;
  }
}

.header {
  @include mix.center();
  justify-content: space-between;
  margin-bottom: 40rpx;

  .status {
    color: #1890FF;
  }

  .user-info {
    @include mix.center();
    align-items: stretch;
    gap: 20rpx;

    .avatar {
      width: 84rpx;
      height: 84rpx;
      border-radius: 50%;
    }

    .right {
      display: flex;
      flex-direction: column;
      flex: 1;
      justify-content: space-between;
    }

    .nickname {
      font-size: 32rpx;
      font-weight: bold;
    }

    .publish-time {
      font-size: 20rpx;
      color: #999;
    }
  }
}

.join-box {
  padding: 40rpx 48rpx;

  .join-header {
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    &:after {
      height: 4rpx;
      content: ' ';
      background: linear-gradient(90deg, #A0FFC7 0%, #E0F6A1 48%, #FED531 100%);;
    }

    > view {
      &:first-child {
        font-weight: bold;
      }

      &:last-child {
        font-size: 24rpx;
      }
    }
  }

  .form-card {
    margin-top: 40rpx;
    gap: 20rpx;

    &, .form-item {
      display: flex;
      flex-direction: column;
    }

    .form-item {
      gap: 10rpx;

      .label {
        font-weight: bold;
      }
    }

    :deep(.my-button) {
      margin-top: 20rpx;
    }

    :deep(.myinput2 input), :deep(.myinput2) input {
      background: #EDEEF0;
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  background: white;
  padding: 30rpx define.$padding-page;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .like-section, .progress-section {
    display: flex;
    align-items: center;
    gap: 4rpx;

    .like {
      &.active {
        color: red;
      }
    }
  }
}
