<template>
  <YjNavBar :custom-style="{
    background: scrollY > 60 ? 'white' : 'transport'
  }" title="心愿详情" @load="(({height, paddingBottom}) => pageStyle.paddingTop=`${height+paddingBottom}px`)"
            @not-found="navTo('pages/index/index')"/>

  <view :style="pageStyle" class="wish-detail">
    <!-- 顶部区域 -->
    <view class="header">
      <view class="user-info">
        <image :src="wishInfo.avatar" class="avatar" mode="aspectFill"></image>
        <view class="right">
          <text class="nickname">{{ wishInfo.nickname }}</text>
          <text class="publish-time">{{ formatTimeAgo(wishInfo.created_at) }}</text>
        </view>

      </view>
      <view class="status">
        <text class="iconfont icon-wanchengdu"/>
        {{ wishInfo.state_text }}
      </view>
    </view>

    <!-- 中间区域 -->
    <view class="content">
      <!-- 轮播图 -->
      <swiper indicator-dots autoplay circular class="swiper">
        <swiper-item v-for="(item, index) in wishInfo.medias" :key="index">
          <image v-if="item.media_type === WishMediaType.Image " :src="imageResize(item.res_url, 800)"
                 mode="scaleToFill"></image>
          <video v-else :src="item.res_url"></video>
        </swiper-item>
      </swiper>

      <view class="title">{{ wishInfo.title }}</view>

      <!-- 标题等信息 -->
      <view class="info-section">
        <view class="time">
          <image src="/static/wish/calendar.png" mode="widthFix"/>
          {{ wishInfo.date_str }}
        </view>
        <view class="people-count">
          <image src="/static/wish/peoples.png" mode="widthFix"/>
          {{ `最多${wishInfo.total_people}人` }}
        </view>
        <view class="budget">
          <image src="/static/wish/budget.png" mode="widthFix"/>
          {{ wishInfo.budget }}/{{ wishInfo.budget_type === BudgetType.Single ? '人' : '整体' }}
        </view>
        <view class="destination">
          <image src="/static/wish/to.png" mode="widthFix"/>
          {{ wishInfo.to }}
        </view>
      </view>

      <!-- 想做的事情列表 -->
      <view class="section todo-list" v-if="wishInfo.todos.length">
        <text class="section-title">想做的事情</text>
        <view class="section-body">
          <view class="list">
            <view v-for="(item, index) in wishInfo.todos" :key="index" class="todo-item">
              <YjCheckbox :checked="item.is_must === Enable"/>
              <text>{{ item.todo }}</text>
            </view>
          </view>
        </view>

      </view>

      <!-- 同行人要求 -->
      <view class="section requirements" v-if="wishInfo.member_desc">
        <text class="section-title">同行人要求</text>
        <view class="section-body">
          {{ wishInfo.member_desc }}
        </view>

      </view>

      <!-- 心愿描述 -->
      <view class="section description" v-if="wishInfo.wish_desc">
        <text class="section-title">心愿描述</text>
        <view class="section-body">{{ wishInfo.wish_desc }}</view>
      </view>

      <!-- 标签 -->
      <view class="tags" v-if="wishInfo.tags.length">
        <text v-for="(tag, index) in wishInfo.tags" :key="index" class="tag">{{ tag }}</text>
      </view>
    </view>

    <!-- 底部区域 -->
    <view class="footer">
      <view @tap="onLike" class="like-section">
        <text :class="wishInfo.is_like ? 'active' : ''" class="like iconfont icon-a-shoucang-weishoucang"/>
        <text>{{ wishInfo.likes }}</text>
      </view>
      <view class="progress-section">
        <text class="iconfont icon-wanchengdu"/>
        <text>完成度{{ `${members.length}/${wishInfo.total_people}` }}</text>
      </view>
      <MyButton v-if="wishInfo.can_apply_member" type="primary" class="join-button" @tap="showJoinPopup = true">愿意同行
      </MyButton>
    </view>
  </view>

  <MyInfoPopup2 v-if="showJoinPopup" @close="showJoinPopup = false">
    <view class="join-box">
      <view class="join-header">
        <view>请填写以下信息</view>
        <view>*为保护隐私，您的个人信息在行前不会透露给发布者</view>
      </view>
      <view class="form-card">
        <view class="form-item">
          <view class="label">真实姓名</view>
          <MyInput2 v-model="joinForm.real_name" placeholder="请输入真实姓名"/>
        </view>
        <view class="form-item">
          <view class="label">手机号码</view>
          <MyInput2 v-model="joinForm.phone" placeholder="请输入手机号码"/>
        </view>
        <MyButton @tap="onJoinWish" type="primary">确认同行</MyButton>
      </view>
    </view>
  </MyInfoPopup2>

</template>

<script setup>
import {computed, ref} from 'vue';
import {imageResize, navTo, showToast} from "@/utils";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import {onLoad, onPageScroll, onShareAppMessage} from "@dcloudio/uni-app";
import {applyJoinWish, wishDetail} from "@/api/wish";
import {BudgetType, Enable, LikeObjType, WishMediaType, WishMemberState} from "@/utils/constmap";
import {isMobileFunc} from "@/utils/validators";
import {like} from "@/api";

const pageStyle = ref({})
const scrollY = ref(0)
const showJoinPopup = ref(false);
const wishInfo = ref({
  title: '',
  medias: [],
  todos: [],
  tags: [],
  members: [],
  can_apply_member: false,
});
const joinForm = ref({
  phone: '',
  real_name: '',
})
const wishId = ref(0);
const members = computed(() => wishInfo.value.members.filter(item => item.state === WishMemberState.Approved))

function onLike() {
  const data = {res_id: wishId.value, type: LikeObjType.Wish}

  like(data).then(({data: {is_like}}) => {
    showToast(`${is_like ? '点赞成功' : '取消点赞成功'}`).then(() => {
      getDetail()
    })
  })
}

const formatTimeAgo = (timestamp) => {
  const now = new Date();
  // 确保时间戳是数字类型（如果是字符串，转换为数字），并转换为毫秒
  const parsedTimestamp = typeof timestamp === 'string' ? parseInt(timestamp, 10) * 1000 : timestamp * 1000;
  const date = new Date(parsedTimestamp);
  const diffInSeconds = Math.floor((now - date) / 1000);
  const diffInMinutes = Math.floor(diffInSeconds / 60);
  const diffInHours = Math.floor(diffInMinutes / 60);
  const diffInDays = Math.floor(diffInHours / 24);

  if (diffInDays > 0) {
    return `${diffInDays}天前`;
  } else if (diffInHours > 0) {
    return `${diffInHours}小时前`;
  } else if (diffInMinutes > 0) {
    return `${diffInMinutes}分钟前`;
  } else {
    return "刚刚";
  }
};

const onJoinWish = () => {
  if (!wishInfo.value.can_apply_member) {
    return;
  }

  const trimmedForm = {
    real_name: joinForm.value.real_name?.trim() || '',
    phone: joinForm.value.phone?.trim() || ''
  };

  if (!trimmedForm.real_name || !trimmedForm.phone) {
    showToast("请填写完整的信息");
    return;
  } else if (!isMobileFunc(trimmedForm.phone)) {
    showToast("请输入正确的手机号码");
    return;
  }

  const params = {
    ...trimmedForm,
    wish_id: wishInfo.value.wish_id
  };

  applyJoinWish(params).then(() => {
    showToast('加入成功').then(() => {
      showJoinPopup.value = false;
      getDetail();
    });
  });
};

function getDetail() {
  wishDetail(wishId.value).then(({data}) => {
    Object.assign(wishInfo.value, data)
  })
}

onShareAppMessage(() => {
  return {
    title: wishInfo.value.title,
    path: `pages/wish-detail/wish-detail?id=${wishId.value}`
  }
})

onLoad(query => {
  wishId.value = query.id
  getDetail()
})

onPageScroll((e) => {
  scrollY.value = e.scrollTop
})

</script>

<style lang="scss">
@use "wish-detail";
</style>
