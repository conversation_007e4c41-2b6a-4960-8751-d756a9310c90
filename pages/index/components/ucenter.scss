@import "../../../styles/define";
@import "../../../styles/mix";

@mixin fullWidth() {
  width: 100%;
}

@mixin autoRight() {
  margin-left: auto;
}

$bg-gray: #FFFFFF;

.wrap {
  z-index: 9999;
}

.space {
  @include fullWidth();
  height: 20rpx;
}

.accordion {
  @include fullWidth();
  @include center(column);
  align-items: flex-start;
  gap: 20rpx;
  padding: 30rpx 0;

  .accordion-item {
    @include fullWidth();
    @include center();
    justify-content: flex-start;
    gap: 16rpx;

    .item-icon {
      width: 48rpx;
      height: 48rpx;
    }

    .item-text {
      color: $black-color;
      font-size: 28rpx;
      font-weight: bold;
    }

    .item-right {
      @include autoRight();
      font-size: 32rpx;
      font-weight: bold;
      color: $black-color;
    }
  }

  &.light {
    padding: 24rpx 28rpx 20rpx 36rpx;
    gap: 24rpx;

    .accordion-item {
      .item-icon {
        font-size: 36rpx;
      }

      .item-text {
        font-weight: normal;
      }
    }
  }
}


.ucenter {
  @include center(column);
  justify-content: flex-start;
  height: 100vh;
  padding: 96rpx $padding-page 30rpx;

  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%),
  linear-gradient(90deg, rgba(165, 234, 249, 0.5) 0%, rgba(238, 249, 233, 0.5) 100%),
  linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 250, 228, 0.68) 100%);

  .gradient-section {
    @include fullWidth();
  }

  .utop {
    @include fullWidth();
    padding: 0 0 30rpx;
    @include center();
    gap: 20rpx;
    justify-content: flex-start;

    .icon {
      image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
      }
    }

    .nickname {
      padding: 14rpx 0 18rpx;
      gap: 6rpx;
      flex: 1;
      display: flex;
      flex-direction: column;

      .nickname-text {
        display: flex;
        justify-content: space-between;
      }

      view {
        color: $black-color;

        &:last-child {
          font-size: 20rpx;
        }

        &:first-child {
          font-size: 32rpx;
          font-weight: bold;
        }
      }

      .id-line {
        display: flex;
        align-items: center;
        font-size: inherit;
        gap: 8rpx;

        .icon-copy {
          font-size: 24rpx;
          color: rgba(0, 0, 0, 0.4);

          &:active {
            color: rgba(0, 0, 0, 0.6);
          }
        }
      }
    }
  }

  .order-list {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 26rpx;

    .accordion-item {
      @include center(column);
      gap: 8rpx;
      font-weight: bold;

      &:before {
        content: ' ';
        width: 48rpx;
        height: 48rpx;
        background-size: contain;
        background-repeat: no-repeat;
      }

      &:first-child {
        &:before {
          background-image: url(https://rp.yjsoft.com.cn/yiban/static/mine/icon-all-order.png);
        }
      }

      &:nth-child(2) {
        &:before {
          background-image: url(https://rp.yjsoft.com.cn/yiban/static/mine/icon-wait-go.png);
        }
      }

      &:nth-child(3) {
        &:before {
          background-image: url(https://rp.yjsoft.com.cn/yiban/static/mine/icon-wait-pay.png);
        }
      }

      &:last-child {
        &:before {
          background-image: url(https://rp.yjsoft.com.cn/yiban/static/mine/icon-wait-shouhou.png);
        }
      }
    }
  }

  .chat-list {
    @include fullWidth();
    @include center(column);
    align-items: flex-start;
    gap: 20rpx;
    padding: 30rpx 28rpx 30rpx 0;

    .label {
      color: $black-color;
      font-weight: bold;
      font-size: 28rpx;
    }

    .chat-item {
      @include ellipse();
      @include fullWidth;
    }

    .empty {
      color: $black-color-v2;
      margin: auto;
      font-size: 26rpx;
    }
  }


  .card-left {
    height: 144rpx;
    background-image: url(#{$static_res_host}/home/<USER>/ucenter-package.png?v=2);
    background-repeat: no-repeat;
    background-size: cover;
    width: 592rpx;
    padding: 30rpx 22rpx 28rpx 158rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-right-radius: 30rpx;
    border-bottom-right-radius: 30rpx;

    .left, .right {
      display: flex;
      flex-direction: column;
      gap: 12rpx;
    }

    .package-title {
      font-weight: bold;
    }

    .left {

      text {
        &:last-child {
          font-size: 24rpx;
        }
      }
    }

    .right {
      font-size: 24rpx;
      justify-content: flex-end;

      .points-text {
        gap: 8rpx;
        @include center();
        font-size: 28rpx;

        .iconfont {
          color: $plan-color-3;
        }
      }

      .renew {
        border-radius: 22rpx;
        padding: 6rpx 28rpx;
        background: white;
        font-weight: bold;
      }
    }


  }

}
