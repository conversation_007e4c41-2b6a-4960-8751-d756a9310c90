@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.container {
  min-height: 100vh;
  position: relative;
  @include linear-gradient();

  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-bottom: 146rpx;
}

.banner_img {
  width: 694rpx;
  height: auto;
  margin: 26rpx 28rpx 0;
  border-radius: 20rpx;
}

.plan-tabs, .recommend {
  margin: 30rpx $padding-page 0 $padding-page;
}

.plan-tabs {
  scroll-view {
    white-space: nowrap;
    overflow: auto;
    display: flex;
  }

  .tab-item {
    display: inline-block;
    border-radius: 32rpx;
    padding: 12rpx 34rpx;
    background: #FFF7D4;
    color: #E6CC86;
    margin-right: 20rpx;

    &.active {
      color: white;
    }
  }
}

.navbar-center {
  @include center();
  gap: 8rpx;
  flex: 1;

  text {
    &:first-child {
      color: #1890FF;
    }
  }
}

.recommend {
  margin-top: 50rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  background: white;
  padding: 0 20rpx 30rpx 20rpx;
  position: relative;

  image {
    position: absolute;
    width: 152rpx;
    left: 0;
    top: -32rpx;
  }

  .header {
    padding: 32rpx 0 18rpx 100rpx;
    @include center();
    justify-content: space-between;


    view {
      &:first-child {
        font-weight: 600;
        background-image: url("https://rp.yjsoft.com.cn/yiban/static/reco-bg.png");
        background-size: cover;
        background-position: top center;

        text {
          color: #1890FF;
        }
      }

      .iconfont {
        color: black;
      }
    }
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}

.keywords {
  margin-top: 24rpx;
  padding: 0 $padding-page;

  .list {
    margin-top: 20rpx;
    display: flex;
    gap: 20rpx;
    font-size: 24rpx;
    flex-wrap: wrap;

    view {
      background: white;
      border-radius: 8rpx;
      padding: 8rpx 20rpx;
      box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
      @include center();
      justify-content: flex-start;
      gap: 5rpx;
    }
  }
}

.footer {
  @include center();
  gap: 20rpx;
  position: fixed;
  bottom: 46rpx;
  left: 0;
  right: 0;

  .ai, .wish {
    width: 336rpx;
    background: #FFFFFF;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 20rpx;
    padding: 20rpx 0;
    @include center();
    font-weight: bold;
    gap: 8rpx;

    &:before {
      content: ' ';
      display: block;
      width: 48rpx;
      height: 48rpx;
      background-size: contain;
    }
  }

  .ai {
    &:before {
      background-image: url("/static/index/btn-ai-bg.png");
    }
  }

  .wish {
    &:before {
      background-image: url("/static/index/btn-wish-bg.png");
    }
  }
}

.activity-content {
  image {
    width: 732rpx;
  }
}

.wish-menus {
  display: flex;
  flex-direction: column;
  padding: 48rpx;
  gap: 40rpx;
  background: white;

  > view {
    border-radius: 48rpx;
    background: #F7F7F9;
    padding: 12rpx 36rpx;
    @include center();
    justify-content: flex-start;
    gap: 22rpx;
    font-weight: bold;

    &:before {
      content: ' ';
      width: 72rpx;
      height: 72rpx;
      background-size: contain;
      display: block;
    }

    &.wish-guangchang {
      &:before {
        background-image: url("/static/index/wish-guanchang.png");
      }
    }

    &.wish-publish {
      &:before {
        background-image: url("/static/index/wish-publish.png");
      }
    }
  }
}
