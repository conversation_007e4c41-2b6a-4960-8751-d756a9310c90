@import '../../styles/_mix.scss';
@import '../../styles/_define.scss';

.container {
  background: $page-bg-color;
  min-height: 100vh;
  padding: $padding-page $padding-page 200rpx;
}

.header {

  .state,
  .payed {
    @include center();
  }

  .state {
    font-size: $h3-v2;
    font-weight: 600;
    justify-content: flex-start;
    gap: $padding-small;

    .payed {
      font-size: $fontsize-small;
      background: $plan-color-3;
      color: white;
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
    }
  }
}

.tel,
.ren {
  @include center();
  justify-content: flex-start;
  gap: $padding-mini;
}

.body {
  display: flex;
  flex-direction: column;
  gap: $padding-v2;
  margin-top: $padding-small;

  .summary {
    > view {
      margin-bottom: $padding-v2;
      @include center();
      justify-content: space-between;

      &:last-child {
        margin-bottom: 0;
        color: $font-color-gray;
      }

      &:first-child {
        font-weight: bold;
        font-size: $h3-v2;
      }
    }
  }

  .contacts {
    @include center();
    justify-content: space-between;
  }

  .peoples {
    color: $font-color-gray;
    @include center();
    justify-content: flex-start;
    gap: $padding-mini;
    font-size: $fontsize-mini;

    view {
      color: #333;
      font-size: $fontsize;
    }
  }

  .detail {
    > view {
      border-bottom: 1rpx solid $border-color-v2;
      padding: $padding-v2 0;
      color: $font-color-gray;

      &:last-child {
        border-bottom: none;
      }

      &:first-child {
        display: flex;
        gap: $padding-v2;

        .pic {
          width: 140rpx;
          height: 140rpx;
          border-radius: $border-radius-v2;
        }

        .right {
          display: flex;
          flex-direction: column;
          gap: $padding-small;
          font-size: $fontsize-small;
          max-width: 480rpx;

          .name {
            color: #333;
            font-size: $fontsize;
            font-weight: 500;
          }
        }
      }
    }

    &.hotel {
      .room {
        @include center(column);
        gap: $padding-small;
        align-items: flex-start;

        > view {
          &:first-child {
            color: #333;
          }

          &:last-child {
            gap: $padding-mini;
            @include center();
            flex-wrap: wrap;
            justify-content: flex-start;
          }
        }

        .attrs {
          display: flex;
          gap: $padding-mini;
        }
      }

      :deep(.checkin-box) {
        .value {
          color: #333;
        }
      }
    }
  }

  .task-info {
    border: 2rpx solid #EDEEF0;
    background: #FFFFFF;
    border-radius: 20rpx;

    .reward-delivery-info {
      @include center();
      justify-content: flex-start;
      gap: 10rpx;
    }

    .cover {
      width: 100%;
      //height: 296rpx;
      border-top-right-radius: 20rpx;
      border-top-left-radius: 20rpx;
      margin-bottom: 20rpx;
    }

    .task-info-data {
      display: flex;
      align-items: flex-end;
      justify-content: space-between;
      padding: 0 30rpx 30rpx 30rpx;

      .task-item {
        margin-bottom: 10rpx;
        @include center();
        justify-content: flex-start;
        gap: 8rpx;

        .user-task-done {
          color: #FF4D4F;
          @include center();
          justify-content: flex-start;
          font-size: 20rpx;
          gap: 4rpx;

          &:before {
            content: '';
            height: 20rpx;
            width: 20rpx;
            background: #FF4D4F;
            display: block;
            border-radius: 50%;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        .name {
          font-weight: bold;
        }

        image {
          width: 40rpx;
          height: 40rpx;
        }

        text {
          &:last-child {
            color: #333;
          }
        }
      }
    }

    .task-reward {
      padding: 18rpx 30rpx;
      border-top: 2rpx solid #EDEEF0;

      text {
        font-weight: 600;
      }
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  @include center();
  justify-content: flex-end;
  background: #FFFFFF;
  padding: 36rpx 28rpx;
  gap: 16rpx;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  border-radius: 20rpx 20rpx 0rpx 0rpx;

  :deep(.contact) {
    background: #E7F4FF;
    border: 2rpx solid #1890FF;
    color: #1890FF;
  }
}