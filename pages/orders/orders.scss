@import '../../styles/_define.scss';
@import '../../styles/_mix.scss';

.order-container {
  min-height: 100vh;
  background: $page-bg-color;
}

:deep(.tabs) {
  padding: 0 $padding-page;
  background: white;
}

.list {
  padding: $padding-page;
  display: flex;
  flex-direction: column;
  gap: $padding-v2;

  .order-item {
    box-shadow: $box-shadow;
    background: white;
    border-radius: 20rpx;
    padding: $padding-page;
    border: 1rpx solid $border-color-v2;

    .header {

      &,
      .left,
      .right {
        @include center();
        justify-content: space-between;
      }

      .left {
        gap: $padding-small;

        .iconfont {
          width: 36rpx;
          height: 36rpx;
          background: $plan-color-3;
          color: white;
          display: inline-block;
          border-radius: 50%;
          font-size: $fontsize-small;
          @include center();

          &.icon-jingdian {
            background: $plan-color-2;
          }

          &.icon-huowudui {
            background: #faca14;
          }
        }
      }

      .right {
        gap: 8rpx;

        text {
          &:first-child {
            padding-right: 8rpx;
            border-right: 2rpx solid $border-color-v2;
            color: $font-color-gray;
          }
        }
      }
    }

    .details {
      margin: $padding-v2 0;

      .order-title {
        @include center();
        justify-content: flex-start;
      }

      .detail {
        display: flex;
        justify-content: flex-start;
        gap: $padding-small;
        padding: 16rpx 0;
        border-bottom: 2rpx solid #EDEEF0;

        &:first-child {
          border-top: 2rpx solid #EDEEF0;
        }

        &:last-child {
          border-bottom: none;
        }

        .left {
          width: 140rpx;
          height: 140rpx;
          border-radius: $border-radius-v2;
        }

        .right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          flex: 1;

          > view {
            &:first-child {
              @include center(column);
              align-items: flex-start;
              gap: 4rpx;
              font-size: $fontsize-small;
              color: $font-color-gray;

              .name {
                max-width: 450rpx;
                font-weight: 500;
                @include ellipse();
                font-size: $fontsize;
                color: #333;
              }

              .sku_name {
                max-width: 480rpx;
                @include ellipse();
              }
            }

            &:last-child {
              text-align: right;
              font-weight: 600;
            }
          }
        }
      }
    }

    .footer {
      display: flex;
      justify-content: flex-end;

      .btn {
        background: white;
      }

      .pay {
        color: $plan-color-1;
        border: 1rpx solid $plan-color-1;
      }

      .cancel {
        color: #333;
        border: 1rpx solid #333;
      }
    }
  }
}