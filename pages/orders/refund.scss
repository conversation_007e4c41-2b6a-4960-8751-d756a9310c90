@use "../../styles/mix";
@use "../../styles/define";

.container {
  padding: 20rpx 28rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  background: define.$page-bg-color;
  min-height: 100vh;
}

.state-text {
  color: #FF4D4F;
  font-size: 34rpx;
  font-weight: bold;
}

.title {
  font-weight: bold;
}

.summary {
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  > view {
    @include mix.center();
    justify-content: space-between;
  }

  // 申请金额部分的图标样式
  .iconfont.icon-gengduo {
    font-size: 40rpx; // 设置图标大小
    margin-left: 10rpx; // 设置左侧间距
    vertical-align: middle; // 垂直居中对齐
  }
}

.list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .refund-item {
    .item-info {
      display: flex;
      padding-bottom: 20rpx;
      border-bottom: 2rpx solid #EDEEF0;
      margin-bottom: 18rpx;
      position: relative; // 添加相对定位，以便于绝对定位子元素

      image {
        width: 160rpx;
        height: 160rpx;
        border-radius: 20rpx;
      }

      .quantity {
        margin-top: 4rpx;
        position: absolute; // 绝对定位
        right: 0; // 位于右侧
        bottom: 20rpx; // 与图片底部对齐，考虑到padding-bottom: 20rpx
        font-size: 28rpx; // 与其他文本大小一致
        color: define.$black-color; // 与其他次要文本颜色一致
      }

      .right {
        margin-left: 20rpx;
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 10rpx;
        color: define.$black-color-v3;

        .name {
          font-weight: bold;
          font-size: 28rpx;
          color: define.$black-color;
        }

        .sku-name {
          font-size: 24rpx;
          color: define.$black-color-v3;
        }

        .ticket-day{
          font-size: 24rpx;
          color: define.$black-color-v3;
        }

        .day-quantity {
          display: flex;
          justify-content: space-between;
          align-items: flex-end;

          .hotel-day {
            display: flex;
            gap: 10rpx;
            .weekday {
              font-size: 20rpx;
              margin-bottom: 4rpx;
            }
            > view {
              display: flex;
              flex-direction: column;
              gap: 4rpx;
              font-size: 24rpx;
            }
          }
        }

      }
    }



    .service-process {
      display: flex;
      flex-direction: column;
      gap: 20rpx;

      .line {
        //margin: 20rpx 0;
        @include mix.center();
        justify-content: space-between;
        gap: 10rpx;
        font-size: 20rpx;
        color: #B7B9BD;

        .refund-state {
          padding: 6rpx 16rpx;
          background: #EDEEF0;
          border-radius: 8rpx;
        }

        // 服务进度部分的图标样式
        .iconfont.icon-gengduo {
          font-size: 40rpx; // 设置图标大小
          margin: 0 10rpx; // 设置水平间距
          vertical-align: middle; // 垂直居中对齐
        }

        .do {
          color: #1890FF;
          background: #E7F4FF;
        }

        .icon_do {
          color: #1890FF;
        }
      }

      .reason {
        font-size: 24rpx;
      }

      .complete {
        @include mix.center();
        justify-content: flex-start;
        gap: 10rpx;

        .refund-amount {
          color: #FF4D4F;
        }
      }

      .refund-tips {
        display: flex;
        flex-direction: column;
        gap: 10rpx;

        > view {
          @include mix.center();
          justify-content: space-between;
          width: 278rpx;
          font-weight: normal;

          &:last-child {
            border-top: 2rpx solid #EDEEF0;
          }
        }
      }

      // 退款明细信息框
      .refund-tips-box {
        background: white;
        border: 2rpx solid #1890FF;
        border-radius: 20rpx;
        padding: 17rpx 35rpx;
        display: flex;
        flex-direction: column;
        gap: 10rpx;

        > view {
          @include mix.center();
          justify-content: space-between;
          width: 278rpx;
          font-weight: normal;
          font-size: 24rpx;
          color: #333333;

          &:last-child {
            border-top: 2rpx solid #EDEEF0;
            padding-top: 10rpx;
          }
        }
      }

      // 退款明细信息框
      .tooltip-container {
        position: relative;
        display: inline-block;
      }

      .refund-detail-box {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-25%); // 将偏移量从 -50% 改为 -25%，使信息框向右偏移
        margin-bottom: 10rpx;
        z-index: 10;

        .refund-tips-box {
          background: white;
          border: 2rpx solid #1890FF;
          border-radius: 20rpx;
          padding: 17rpx 35rpx;
          display: flex;
          flex-direction: column;
          gap: 10rpx;

          > view {
            @include mix.center();
            justify-content: space-between;
            width: 278rpx;
            font-weight: normal;
            font-size: 24rpx;
            color: #333333;

            &:last-child {
              border-top: 2rpx solid #EDEEF0;
              padding-top: 10rpx;
            }
          }
        }

        .refund-tips-arrow {
          position: absolute;
          left: 25%; // 将箭头的位置从 50% 改为 25%，使其与按钮的中心对齐
          bottom: -16rpx;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 16rpx solid transparent;
          border-right: 16rpx solid transparent;
          border-top: 16rpx solid #1890FF;
        }
      }
    }

  }
}