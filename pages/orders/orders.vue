<template>
  <view class="order-container">
    <YjTabs v-model="activeTab" :data="tabData" active-color="#1890FF" custom-class="tabs"
            text-color="#999999"></YjTabs>

    <!-- 售后标签显示退款订单列表 -->
    <RefundOrderList v-if="activeTab === 'refund'"/>

    <!-- 其他标签显示普通订单列表 -->
    <template v-else>
      <view class="list">
        <view v-for="item in list" :key="item.order_id" class="order-item">
          <view class="header">
            <view class="left">
              <text :class="{
                'icon-chuang': item.order_type === OrderTypeHotel,
                'icon-jingdian': item.order_type == OrderTypeTicket,
                'icon-huowudui': [OrderTypeTravel, OrderTypeTuan].includes(item.order_type)
              }" class="iconfont"></text>
              {{ item.order_type_text }}
            </view>
            <view class="right">
              <text>{{ formatTime(item.created_at, 'YYYY.M.D H:mm') }}</text>
              <text :class="`order-state-${item.state}`">{{ item.state_text }}</text>
            </view>
          </view>

          <view class="details">
            <view v-if="item.title.length && item.order_type === OrderTypeTravel" class="order-title"
                  @tap="onTapPlanDetail(item)">{{
                item.title
              }}
              <text class="iconfont icon-gengduo"></text>
            </view>
            <view v-for="(detail, dIndex) in item.items" :key="dIndex" class="detail" @tap="handleOrderItemClick(item)">
              <image :src="detail.pic" class="left" mode="scaleToFill"></image>
              <view class="right">
                <view v-if="detail.order_sub_type == OrderSubTypeHotel">
                  <view class="name">{{ detail.product_name }}</view>
                  <view class="address">{{ detail.address }}</view>
                  <view>
                    {{
                      `${formatTime(detail.date_start)}至${formatTime(detail.date_end)}，${detail.quantity}间`
                    }}
                  </view>
                </view>
                <view v-if="[OrderSubTypeTicket, OrderSubTypeTuan].includes(detail.order_sub_type)">
                  <view class="name">{{ detail.product_name }}</view>
                  <view class="sku_name">{{ `${detail.sku_name} x ${detail.quantity}` }}</view>
                  <view v-if="detail.order_sub_type === OrderSubTypeTicket">{{ formatTime(detail.date_start) }} 有效
                  </view>
                  <view v-else>团期：{{ formatTime(item.date) }}</view>
                </view>

                <view class="price">{{ formatMoney(detail.amount) }}</view>
              </view>
            </view>
          </view>
          <view class="footer">
            <view v-if="item.can_cancel" class="btn cancel" @tap="onCancel(item.order_id)">取消订单</view>
            <view v-if="item.can_pay" class="btn danger pay" @tap="handlePay(item.order_id)">立即支付</view>
          </view>
        </view>
      </view>

      <uni-load-more :status="loadMoreStatus"></uni-load-more>
    </template>
  </view>
</template>

<script setup>
import {onLoad, onReachBottom} from '@dcloudio/uni-app'
import {computed, ref, watch} from 'vue'
import {orderCancel, orderList} from '../../api/order.js'
import {formatMoney, formatTime, navTo, showConfirm, showToast} from '../../utils/index.js'
import {
  OrderSubTypeHotel,
  OrderSubTypeTicket,
  OrderSubTypeTuan,
  OrderTypeHotel,
  OrderTypeTicket,
  OrderTypeTravel,
  OrderTypeTuan
} from '@/utils/constmap.js'
import YjTabs from '@/components/YjTabs/YjTabs.vue'
import RefundOrderList from './components/refundOrderList.vue'

const tabData = [
  {
    label: '全部订单',
    value: 'all'
  },
  {
    label: '待付款',
    value: 'not'
  },
  {
    label: '待出行',
    value: 'pay'
  },
  {
    label: '售后',
    value: 'refund'
  }
]
const activeTab = ref('all')

const list = ref([])
const total = ref(0)
let page = 1
const pageSize = 20
const loading = ref(false)

const hasMore = computed(() => Math.ceil(total.value / pageSize) > total.value)

const loadMoreStatus = computed(() => {
  if (loading.value) {
    return 'loading'
  }

  return hasMore.value ? 'more' : 'noMore'
})

watch(activeTab, () => {
  // 只有非售后标签才需要重置和获取数据
  if (activeTab.value !== 'refund') {
    page = 1
    list.value = []
    getList()
  }
})

function onTapPlanDetail(item) {
  navTo('pages/details/details', {plan_id: item.plan_id})
}

function handlePay(id) {
  uni.navigateTo({
    url: `/pages/pay/pay?order_id=${id}`
  })
}

function onCancel(id) {
  showConfirm('取消订单', '您确定要取消该订单吗？').then(() => {
    orderCancel(id).then(() => {
      showToast('取消成功').then(() => {
        const index = list.value.findIndex((item) => item.order_id == id)
        if (index > -1) {
          list.value.splice(index, 1)
        }
      })
    })
  })
}

// 处理订单项点击
function handleOrderItemClick(item) {
  // 如果是售后订单，跳转到退款详情页
  if (activeTab.value === 'refund') {
    navTo('pages/orders/refund', {id: item.order_id})
  } else {
    // 其他类型订单跳转到普通订单详情页
    navTo('pages/orders/orderdetail', {id: item.order_id})
  }
}

function getList() {
  // 如果正在加载或当前是售后标签，则不获取普通订单数据
  if (loading.value || activeTab.value === 'refund') {
    return
  }

  const params = {
    page,
    type: activeTab.value,
    page_size: pageSize
  }
  loading.value = true

  orderList(params)
      .then((res) => {
        const {data} = res
        total.value = data.total
        list.value.push(...data.list)
      })
      .finally(() => (loading.value = false))
}

onReachBottom(() => {
  // 如果当前是售后标签，则不触发普通订单的加载更多
  if (activeTab.value === 'refund') {
    return
  }

  if (loading.value || !hasMore.value) {
    return
  }
  page++
  getList()
})

onLoad((query) => {
  activeTab.value = query.type ?? 'all'

  // 只有非售后标签才需要获取普通订单数据
  if (activeTab.value !== 'refund') {
    getList()
  }
})
</script>

<style lang="scss" scoped>
@import "orders";
</style>
