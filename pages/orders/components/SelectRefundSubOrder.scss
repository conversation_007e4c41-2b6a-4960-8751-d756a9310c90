@use "../../../styles/mix";
@use "../../../styles/define";

/* 自定义圆形checkbox样式 */
.custom-checkbox {
  flex-shrink: 0; /* 防止被压缩 */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
  margin: 0 4rpx;
  box-sizing: content-box;
}

.checkbox-inner {
  flex-shrink: 0; /* 防止被压缩 */
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #CCCCCC;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* 确保内容不溢出圆形 */
  position: relative; /* 为子元素定位提供参考 */
}

.checkbox-inner.checked {
  background-color: #1890FF;
  border-color: #1890FF;
}

.checkbox-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #FFFFFF;
  font-size: 20rpx;
  line-height: 1;
}

.sub-orders {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  max-height: 720rpx;
  overflow-y: auto;
  /* 尝试使用多种方法来防止滚动传递，以确保跨平台兼容性 */
  overscroll-behavior: contain; /* 现代浏览器 */
  -webkit-overflow-scrolling: touch; /* iOS平台 */
  touch-action: pan-y; /* 限制只能垂直滚动 */

  .sub-order-item {
    @include mix.center();
    margin-bottom: 20rpx;

    &.selected {
      .right {
        border-color: #1890FF;

        .select-quantity-box {
          border-top: 2rpx solid #1890FF; /* 分界线颜色设置为 #1890FF */
          background: #E1F3FE; /* 背景色设置为 #E1F3FE */
        }
      }
    }

    .right {
      border-radius: 20rpx;
      border: 2rpx solid #EDEEF0;
      background: #FFFFFF;
      flex: 1;
      display: flex;
      flex-direction: column;

      .item-pic {
        width: 160rpx;
        height: 160rpx;
        border-radius: 8rpx;
        object-fit: cover; /* 保证图片裁切居中 */
        flex-shrink: 0; /* 防止压缩 */
        margin-right: 20rpx; /* 与右侧文字的间距 */
      }

      .info {
        padding: 30rpx;
        display: flex;
        gap: 8rpx;

        .icon {
          width: 36rpx;
          height: 36rpx;
          background: define.$plan-color-3;
          color: white;
          display: inline-block;
          border-radius: 50%;
          font-size: define.$fontsize-small;
          @include mix.center();

          &.icon-jingdian {
            background: define.$plan-color-2;
          }

          &.icon-huowudui {
            background: #faca14;
          }
        }

        .you {
          flex: 1;
          display: flex;
          flex-direction: column;

          .item-name {


            .name {
              color: define.$black-color;
              font-size: 28rpx;
              font-weight: bold;
            }
          }

          .item-details {
            flex: 1;
            color: #999999;
            font-size: 25rpx;

            .attrs {
              margin-top: 10rpx;
              color: define.$black-color-v3;
              font-size: 20rpx;
            }

            .refund-rules {
              position: relative;
              display: flex;
              margin-top: 10rpx;
              padding-right: 30rpx;

              .rules {
                @include mix.center(column);
                align-items: flex-start;
                background: #FACA14;
                padding: 4rpx 8rpx;
                color: #694209;
                font-size: 20rpx;

                .rule {
                  flex: 0;
                }
              }

              .expander {
                position: absolute;
                top: 0;
                right: 0;

                &.expand {
                  transform: rotate(180deg);
                }
              }
            }

            .refund-amount {
              margin-top: 14rpx;
              @include mix.center();
              justify-content: space-between;

              .amount {
                color: define.$black-color;
                font-size: 20rpx;

                .yen {
                  color: #1890FF;
                  font-weight: bold;
                }
              }

              .quantity {
                color: define.$black-color;
                font-size: 20rpx;

                .b {
                  font-size: 28rpx;
                  font-weight: bold;
                }
              }
            }
          }

          .name {
            color: #333333;
            font-size: define.$fontsize;
            font-weight: 600;
            @include mix.ellipse();
            max-width: 420rpx;
            margin-bottom: 10rpx;
          }

          .attr-name {
            margin-bottom: 10rpx;
          }

          .hotel-day {
            display: flex;
            gap: 10rpx;

            > view {
              display: flex;
              flex-direction: column;
              gap: 4rpx;
            }
          }
        }

      }

      .select-quantity-box {
        border-bottom-left-radius: 20rpx;
        border-bottom-right-radius: 20rpx;
        padding: 18rpx 30rpx; /* 调整上下内边距 */
        @include mix.center();
        justify-content: space-between;

        .quantity-label {
          font-size: 28rpx;
          color: #333333;
          font-weight: normal;
        }

        .new_view {
          width: 194rpx;

          &.noselect {
            background: transparent;
          }
        }
      }
    }
  }
}

.footer-container {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
  padding: 30rpx 28rpx;

  .summary {
    display: flex;
    flex-direction: column;
    gap: 10rpx;

    .infos {
      @include mix.center();
      justify-content: space-between;
      font-size: 20rpx;
      color: define.$black-color;

      .num, .amount {
        color: #1890FF;
      }

      .amount {
        font-weight: bold;
      }
    }

  }

  .bottom {
    @include mix.center();
    justify-content: space-between;

    .left {
      flex: 1;
      @include mix.center();
      justify-content: space-between;

      .total {
        @include mix.center(column);
        align-items: flex-start;
        font-size: 28rpx;
        color: define.$black-color;

        .amount {
          color: #1890FF;
        }
      }

      :deep(.yj-checkbox .label) {
        font-weight: 600;
      }
    }

    .right {
      display: flex;
      gap: 16rpx;


      :deep(.my-button) {
        padding: 0;
        width: 200rpx;
        height: 72rpx;
        @include mix.center();
      }

      :deep(.contact-btn) {
        background: #E7F4FF;
        border: 2rpx solid #1890FF;
        color: #1890FF;
      }
    }
  }
}