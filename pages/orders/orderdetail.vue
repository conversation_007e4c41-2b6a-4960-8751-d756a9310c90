<template>
  <view class="container">
    <view class="header">
      <view :class="`order-state-${orderInfo.state}`" class="state">
        <text v-if="orderInfo.pay_state == PayStatePayed" class="iconfont icon-duigoux payed"></text>
        {{ orderInfo.state_text }}
      </view>

      <text v-if="showPayExpire && orderInfo.pay_time_expire">请在{{ orderInfo.pay_time_expire }}前完成订单的支付</text>
    </view>

    <view class="body">
      <Card>
        <view class="summary">
          <view>
            <view>订单金额</view>
            <view>{{ formatMoney(orderInfo.total_amount) }}</view>
          </view>
          <view>
            <view>订单时间</view>
            <view>{{ formatTime(orderInfo.created_at, 'YYYY.M.D HH:mm') }}</view>
          </view>
        </view>
      </Card>

      <Card v-for="(detail, index) in orderInfo.details" :key="index">
        <view v-if="detail.order_sub_type === OrderSubTypeTicket" class="detail ticket">
          <view @tap="navTo('pages/scenic/scenic', { id: detail.scenic.id })">
            <image :src="detail.pic" class="pic" mode="scaleToFill" />
            <view class="right">
              <view class="name">{{ detail.scenic.name }}</view>
              <view class="address">{{ `${detail.sku_name} x ${detail.quantity}` }}</view>
              <view>预订日期：{{ formatTime(detail.date_start) }}</view>
            </view>
          </view>
        </view>
        <view v-if="detail.order_sub_type === OrderSubTypeTuan" class="detail">
          <view @tap="navTo('pages/tuan/detail', { id: detail.product_id })">
            <image :src="detail.pic" class="pic" mode="scaleToFill" />
            <view class="right">
              <view class="name">{{ orderInfo.title }}</view>
              <view>{{ `${detail.sku_name}x${detail.quantity}` }}</view>
              <view>预订日期：{{ orderInfo.date }}</view>
            </view>
          </view>
        </view>

        <view v-if="detail.order_sub_type == OrderSubTypeHotel" class="detail hotel">
          <view @tap="navTo('pages/hotel/hotel', { id: detail.hotel.id })">
            <image :src="detail.pic" class="pic" mode="scaleToFill" />
            <view class="right">

              <view class="name">{{ detail.hotel.name }}</view>
              <view class="address">{{ detail.hotel.addr }}</view>
              <view class="tel">
                <text class="iconfont icon-dianhua1"></text>
                {{ detail.hotel.tel }}
              </view>
            </view>
          </view>
          <view class="room">
            <view>{{ detail.sku_name }},{{ detail.quantity }}间</view>
            <view class="attrs">
              <text v-for="(attr, aI) in detail.hotel.room.attrs" :key="aI">{{ attr }}</text>
            </view>
            <view>
              <view class="tag">{{ detail.hotel.room.policy[0].name }}</view>
              <view class="tag cancel">{{ detail.hotel.room.policy[0].cancel_desc }}</view>
            </view>
          </view>
          <view>
            <MyHotelCheckin :checkin="dayjs.unix(detail.hotel.start).toDate()"
              :checkout="dayjs.unix(detail.hotel.end).toDate()" />
          </view>
        </view>

        <view v-if="detail.peoples?.length" class="peoples">
          旅客信息
          <view v-if="detail.peoples?.length">
            {{detail.peoples.map(item => item.name).join(',')}}
          </view>
          <view v-if="orderInfo.peoples?.length">
            {{orderInfo.peoples.map(item => item.name).join(',')}}
          </view>
        </view>
      </Card>
      <Card title="联系人">
        <view class="contacts">
          <view class="ren">
            <text class="iconfont icon-chengren"></text>
            {{ orderInfo.contacts_name }}
          </view>
          <view class="tel">
            <text class="iconfont icon-dianhua1 "></text>
            {{ orderInfo.contacts_tel }}
          </view>
        </view>
      </Card>

      <view v-if="orderInfo.has_task" class="task-info">
        <image :src="taskInfo.task.cover" class="cover" mode="widthFix" />
        <view class="task-info-data">
          <view class="left">
            <view v-for="(item, index) in taskInfo.task.children" :key="index" class="task-item">
              <text>秘境{{ index + 1 }}：</text>
              <text class="name">{{ item.name }}</text>
              <text class="iconfont icon-gengduo"></text>
              <image :src="getCdnUrl('/static/map-location.png')"
                @tap="navTo('pages/scenic/scenic', { id: item.scene.id })" />
              <view v-if="item.user_task_state === UserTaskStatusDone" class="user-task-done">已打卡</view>
            </view>
          </view>
          <MyButton :disable="taskInfo.task.user_task_state === UserTaskStatusDone"
            :type="taskInfo.task.user_task_state === UserTaskStatusWaitReward ? 'danger' : 'primary'" size="small"
            @tap="onDoTask">
            {{ taskStateText }}
          </MyButton>
        </view>
        <template v-if="taskInfo.reward">
          <view v-if="taskInfo.reward.reward_state === 2" class="task-reward">已线下领取</view>
          <view v-else-if="taskInfo.reward.delivery_no" class="reward-delivery-info">
            物流：
            <text>{{ taskInfo.reward.delivery_name }} {{ taskInfo.reward.delivery_no }}</text>
            <text class="iconfont icon-changyongxinxi" @tap="copyText(taskInfo.reward.delivery_no)"></text>
          </view>
        </template>

      </view>

    </view>

    <view class="footer">
      <MyButton custom-class="contact" open-type="contact">联系客服</MyButton>
      <MyButton v-if="orderInfo.can_refund" type="primary" @tap="openRefund">申请退款</MyButton>
    </view>

    <SelectRefundSubOrders v-if="showRefund" :order-id="orderId" @close="closeRefund" />

    <MyHotelRoomPriceDaily v-if="showHotelFee" :num="hotelDetailQuantity" :policy="hotelDetailPolicy"
      :total-price="hotelDetailTotalAmount" @close="showHotelFee = false"></MyHotelRoomPriceDaily>
  </view>
</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app';
import { computed, ref } from 'vue';
import { orderDetail } from '../../api/order';
import { copyText, formatMoney, formatTime, navTo, showToast, getCdnUrl } from '../../utils/index.js';

import dayjs from 'dayjs';

import {
  OrderSubTypeHotel,
  OrderSubTypeTicket,
  OrderSubTypeTuan,
  PayStateNotPay,
  PayStatePayed
} from '../../utils/constmap';
import Card from '@/components/Card/Card.vue';
import MyHotelCheckin from '@/components/MyHotelCheckin/MyHotelCheckin.vue';
import MyButton from "@/components/MyButton/MyButton.vue";
import SelectRefundSubOrders from "@/pages/orders/components/SelectRefundSubOrders.vue";
import { shjActivityGetTaskInfo } from "@/api/activity";
import { UserTaskStatusDone, UserTaskStatusProcessing, UserTaskStatusWaitReward } from "@/utils/constmap_activity";

const orderId = ref('');
const orderInfo = ref({
  id: '',
  details: [],
  peoples: [],
  has_task: false,
});
const taskInfo = ref({
  reward: null,
  task: {}
});
const taskStateText = computed(() => {
  switch (taskInfo.value.task.user_task_state) {
    case UserTaskStatusProcessing:
      return '去打卡'
    case UserTaskStatusDone:
      return '已领奖'
    default:
      return '去领奖'
  }
})

const showHotelFee = ref(false);
const hotelDetailTotalAmount = ref(0);
const hotelDetailPolicy = ref({});
const hotelDetailQuantity = ref(0);
const showPayExpire = computed(() => {
  return orderInfo.value.pay_state == PayStateNotPay;
});
const showRefund = ref(false)

function onDoTask() {
  if (taskInfo.value.task.user_task_state === UserTaskStatusProcessing) {
    //#ifdef MP
    uni.scanCode({
      onlyFromCamera: true,
      fail(res) {
        console.log(res);
      },
      success({ result, path }) {
        if (path) {
          uni.navigateTo({
            url: '/' + path,
          })
        } else if (result?.indexOf('?') > 0) {
          uni.navigateTo({
            url: `/pages/activity/shj/shj_activity${result.slice(result.indexOf('?'))}&order_id=${orderId.value}`
          })
        } else {
          showToast('二维码内容无效')
        }
      }
    })
    //#endif
  } else if (taskInfo.value.task.user_task_state === UserTaskStatusWaitReward) {
    navTo('pages/activity/shj/shj_activity', {
      tuan_id: orderInfo.value.product_id,
      order_id: orderInfo.value.id
    })
  }
}

// 打开退款弹窗
function openRefund() {
  // #ifdef MP-WEIXIN || APP-PLUS
  // 微信小程序或App中，打开前禁止页面滚动
  uni.pageScrollTo({
    scrollTop: 0,
    duration: 0
  });
  // #endif

  showRefund.value = true
}

// 关闭退款弹窗
function closeRefund() {
  showRefund.value = false
}

function getDetail() {
  orderDetail(orderId.value).then(async (res) => {
    Object.assign(orderInfo.value, res.data);

    uni.setNavigationBarTitle({
      title: orderInfo.value.title
    });

    if (orderInfo.value.has_task) {
      const params = {
        tuan_id: orderInfo.value.product_id,
        order_id: orderInfo.value.id
      }
      const { data } = await shjActivityGetTaskInfo(params)
      Object.assign(taskInfo.value, data)
    }

  });
}

onLoad((query) => {
  orderId.value = query.id;
});

onShow(() => {
  getDetail();
})
</script>

<style lang="scss" scoped>
@import 'orderdetail.scss';
</style>
