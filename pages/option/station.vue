<script setup>
import { stations } from '@/api';
import MyButton from '@/components/MyButton/MyButton.vue';
import YjTabs from '@/components/YjTabs/YjTabs.vue';
import { useGlobalStore } from '@/store/global';
import UniSearchBar from '@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue';
import { navBack, throttle } from '@/utils';
import { TimelineTypeBus, TimelineTypePlane, TimelineTypeTrain } from '@/utils/constmap';
import { onLoad } from '@dcloudio/uni-app';
import { computed, ref, watch } from 'vue';

const globalStore = useGlobalStore()

const tabData = [
  {
    label: '火车',
    value: TimelineTypeTrain,
  },
  {
    label: '飞机',
    value: TimelineTypePlane,
  },
  {
    label: '大巴',
    value: TimelineTypeBus,
  }
]
const activeTab = ref(TimelineTypeTrain)
const formData = ref({
  from: '',
  to: '',
})
const fromPlaceHolder = computed(() => {
  if (activeTab.value === TimelineTypeTrain) {
    return '从哪出发（城市火车站）'
  } else if (activeTab.value === TimelineTypePlane) {
    return '从哪出发（机场）'
  } else if (activeTab.value === TimelineTypeBus) {
    return '从哪出发（汽车站）'
  }
})
const toPlaceHolder = computed(() => {
  if (activeTab.value === TimelineTypeTrain) {
    return '到哪去（城市火车站）'
  } else if (activeTab.value === TimelineTypePlane) {
    return '到哪去（机场）'
  } else if (activeTab.value === TimelineTypeBus) {
    return '到哪去（汽车站）'
  }
})

const fromList = ref([])
const toList = ref([])
const isFill = ref(false)
const from = ref({})
const to = ref({})
const query = ref({
  action: 'replace',
  day: 0,
  index: 0,
})

watch(() => activeTab.value, () => {
  fromList.value = []
  toList.value = []
})

function onSubmit() {
  if (!from.value.id || !to.value.id) {
    uni.showToast({
      title: '请填写出发和到达地',
      icon: 'none'
    })
    return
  }

  const timeline = {
    type: activeTab.value,
    pics: [],
    transport: {
      from: packItem(from.value),
      to: packItem(to.value),
    }
  }

  if (query.value.action === 'replace') {
    globalStore.editPlan.sections[query.value.day].timeline[query.value.index] = timeline
  } else {
    globalStore.editPlan.sections[query.value.day].timeline.push(timeline)
  }

  navBack()
}

function packItem(item) {
  return {
    zone_name: item.zone_name,
    station: item.name,
    zone_id: item.zone_id,
    lat: item.lat,
    lng: item.lng,
  }
}

function onSwitch() {
  [from.value, to.value] = [to.value, from.value]
  [formData.value.from, formData.value.to] = [formData.value.to, formData.value.from]
}


function getList(keyword, isFrom) {
  const params = { type: activeTab.value, keyword, page_size: 5 }

  stations(params).then(({ data }) => {
    if (!isFrom) {
      toList.value = data.list
    } else {
      fromList.value = data.list
    }

  })
}

const onFromInput = throttle(e => {
  if (!e) {
    fromList.value = []
    from.value = {}
    return
  } else if (isFill.value) {
    isFill.value = false
    return
  }
  getList(e, true)
}, 300)


const onToInput = throttle(e => {
  if (!e) {
    toList.value = []
    to.value = {}
    return
  } else if (isFill.value) {
    isFill.value = false
    return
  }

  getList(e, false)
}, 300)

function onFill(item, isFrom) {
  if (isFrom) {
    from.value = item
    formData.value.from = item.name
    fromList.value = []
  } else {
    formData.value.to = item.name
    to.value = item
    toList.value = []
  }
  isFill.value = true
}

onLoad(q => {
  Object.assign(query.value, q)
})

</script>

<template>

  <view class="container">
    <YjTabs v-model="activeTab" :data="tabData" active-color="#1890FF"></YjTabs>

    <view class="box">
      <view>
        <UniSearchBar @input="onFromInput" v-model="formData.from" :placeholder="fromPlaceHolder" />

        <view v-if="fromList.length > 0" class="list">
          <view @tap="onFill(item, true)" v-for="(item, index) in fromList">{{ item.name }}</view>
        </view>

        <UniSearchBar :placeholder="toPlaceHolder" v-model="formData.to" @input="onToInput" />
        <view v-if="toList.length > 0" class="list">
          <view @tap="onFill(item, false)" v-for="(item, index) in toList">{{ item.name }}</view>
        </view>
      </view>
      <text @tap="onSwitch" class="iconfont icon-24gl-swapVertical" />
    </view>

    <MyButton @tap="onSubmit" class="add" type="primary">{{ query.action == 'add' ? '添加' : '替换' }}</MyButton>

  </view>

</template>

<style lang="scss" scoped>
@import '@/styles/_mix.scss';
@import '@/styles/_define.scss';

.container {
  background: $page-bg-color;
}

:deep(.tabs) {
  padding: 0 $padding-page;
}

.add {
  margin: $padding-page;
}

.box {
  @include center();
  padding: $padding-page;
  gap: $padding-mini;

  >view {
    &:first-child {
      flex: 1;
    }
  }

  text {
    width: 56rpx;
    height: 56rpx;
    background: $plan-color-3;
    @include center();
    color: white;
    border-radius: 50%;
  }
}

.list {
  background: white;
  padding: $padding-v2;
  display: flex;
  flex-direction: column;
  gap: $padding-small;
  border-bottom-left-radius: $border-radius-v2;
  border-bottom-right-radius: $border-radius-v2;

  >view {

    @include ellipse();

  }
}
</style>