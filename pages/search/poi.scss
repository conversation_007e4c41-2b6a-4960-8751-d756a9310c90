@use "../../styles/define";
@use "../../styles/mix";

.poi-search-container {
  padding: define.$padding-page;

  .search-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    position: relative;
    border: 2rpx solid #C1F7FF;
    border-radius: 32rpx;

    .search-input {
      flex: 1;
      padding: 12rpx 26rpx;
      //border: 1px solid #ddd;
      //border-radius: 40rpx;
      font-size: 28rpx;
    }

    .clear-button {
      position: absolute;
      right: 120rpx;
      width: 40rpx;
      height: 40rpx;
      line-height: 40rpx;
      text-align: center;
      background: #999999;
      color: white;
      border-radius: 50%;
      z-index: 1;
      @include mix.center(column);

      text {
        font-size: 20rpx;
      }
    }

    .search-button {
      //margin-left: 14rpx;
      padding: 12rpx 26rpx;

      background: linear-gradient(270deg, #35C3FF 0%, #1890FF 100%);
      color: #fff;
      border-radius: 28rpx;
    }
  }

  .search-results {
    .no-results {
      text-align: center;
      padding: 40rpx;
      color: #999;
    }

    .list {
      display: flex;
      gap: 30rpx;
      flex-direction: column;

      .result-item {
        display: flex;
        gap: 12rpx;

        .short {
          font-size: 20rpx;
          color: #999999;
        }

        &.type-3 {
          .left {
            background-image: url("/static/search/poi-icon-citiy.png");
          }

          .right {
            .name {
              margin-right: 12rpx;
            }
          }
        }

        &.type-1 {
          .left {
            background-image: url("/static/search/poi-icon-scene.png");
          }

          .item-desc {
            display: flex;
            flex-direction: column;
            gap: 10rpx;

            > view {
              display: flex;
              gap: 8rpx;
              font-size: 20rpx;

              &:last-child {
                text {
                  &:first-child {
                    color: #FF4D4F;
                    font-weight: bold;
                  }
                }
              }
            }
          }
        }

        .left {
          width: 40rpx;
          height: 40rpx;
          background-size: contain;
        }

        .right {
          .name {
            color: define.$plan-color-3;
            font-weight: bold;
            font-size: 28rpx;
          }
        }
      }
    }
  }
}
