<template>
  <view class="poi-search-container">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="可以搜索目的地/景点"
          @confirm="handleSearch"
          @input="handleInputChange"
      />
      <view
          v-if="showClearButton"
          class="clear-button"
          @click="handleClear"
      >
        <text class="iconfont icon-guanbi"/>
      </view>
      <view class="search-button" @click="handleSearch">搜索</view>
    </view>

    <!-- 搜索结果 -->
    <view class="search-results">
      <view v-if="!searchResults || searchResults.length === 0" class="no-results">
        暂无搜索结果
      </view>

      <view v-else class="list">
        <view
            v-for="(item, index) in searchResults"
            :key="index"
            :class="`type-${item.type}`"
            class="result-item"
            @click="handleItemClick(item)"
        >
          <view class="left"></view>
          <view class="right">
            <view v-if="item.type === PoiType.City" class="item-desc">
              <text class="name">{{ item.name }}</text>
              <text class="short">{{ `${item.province}-${item.zone_name}` }}</text>
            </view>
            <view v-if="item.type === PoiType.Scene" class="item-desc">
              <view>
                <text class="name">{{ item.name }}</text>
                <text class="type-name">・景点</text>
                <text class="short">{{ `${item.province}-${item.zone_name}` }}</text>
              </view>
              <view>
                <text v-if="item.level > 0">{{ item.level }}A</text>
                <text v-if="item.level > 0">|</text>
                <text class="short">{{ item.address }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, ref} from 'vue';
import {deepToRaw, navBack, showToast} from "@/utils";
import {searchPoi} from "@/api";
import {PoiType} from "@/utils/constmap";
import {useGlobalStore} from "@/store/global";

const globalStore = useGlobalStore()
const searchKeyword = ref('');
const searchResults = ref([]);

// 是否显示清除按钮
const showClearButton = computed(() => {
  return searchKeyword.value.length > 0;
});

// 输入框内容变化时触发
const handleInputChange = () => {
  if (searchKeyword.value === '') {
    searchResults.value = [];
  }
};

function getList() {
  searchPoi({q: searchKeyword.value.trim()}).then(({data}) => {
    searchResults.value = data.list
  })
}

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    showToast('请输入搜索关键字')
    return;
  }

  getList()
};

// 清除搜索
const handleClear = () => {
  searchKeyword.value = '';
  searchResults.value = [];
};

// 处理点击搜索结果
const handleItemClick = (item) => {
  globalStore.setData('poi', deepToRaw(item))
  navBack()
};
</script>

<style lang="scss" scoped>
@use "poi";
</style>
