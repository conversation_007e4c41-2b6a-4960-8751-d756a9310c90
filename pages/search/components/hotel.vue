<script setup>
import { useGlobalStore } from '@/store/global'
import { computed, getCurrentInstance, nextTick, onBeforeMount, onMounted, ref, watch, watchEffect } from 'vue';
import { hotelSearch } from '@/api/hotel'
import { navTo } from '@/utils';
import {
  HotelGuestTypeMainland,
  HotelGuestTypeMap
} from '@/utils/constmap';
import SearchBar from './searchBar.vue';

const props = defineProps({
  active: Boolean,
  query: null,
})
let page = 1
const globalStore = useGlobalStore()
const city = computed(() => globalStore.location)
const params = ref({
  keyword: '',
  order: 'desc',
})
const list = ref([])
const hasMore = ref(true)

watch(city.value, () => {
  if (city.value.zone_id) {
    Object.assign(params.value, {
      zone_ids: city.value.zone_id,
    })
    doLoad(true)
  }
})

const doLoad = async (reset = false) => {
  if (reset) {
    page = 1
    list.value = []
  }
  const { data } = await hotelSearch({
    ...params.value,
    page,
  })


  list.value = list.value.concat(data.list.map(v => {
    v.tags = []
    if (v.open_up_time) {
      v.tags.push(v.open_up_time + '年开业')
    }
    if (v.guest_type.length > 0) {
      v.tags = v.tags.concat(v.guest_type.map(vv => {
        const t = HotelGuestTypeMap[vv]

        return t ?? HotelGuestTypeMap[HotelGuestTypeMainland]
      }))
    }
    v.stars = []
    if (v.star > 0) {
      v.stars = Array(v.star).fill(0)
    }
    return v
  }))
  hasMore.value = data.list.length > 0
}

const doLoadNext = () => {
  if (!hasMore.value) {
    return
  }
  page++
  doLoad()
}

const doSort = (sort) => {
  Object.assign(params.value, {
    sort: sort,
  })
  doLoad(true)
}

onMounted(async () => {
  Object.assign(params.value, props.query)
  if (!params.value.zone_ids) {
    let loc = await globalStore.getLocation()
    params.value.zone_ids = loc.zone_id
  }
  doLoad()
})


</script>

<template>
  <view class="body">
    <SearchBar v-model="params.keyword" :city="city" placeholder="酒店" @confirm="doLoad(true)"></SearchBar>
    <view class="sorter">
      <view :class="{ active: params.sort == 'score' }" class="sitem" @tap="doSort('score')">
        <text>好评</text>
        <text class="iconfont icon-xialajiantou"></text>
      </view>
      <view :class="{ active: params.sort == 'star' }" class="sitem" @tap="doSort('star')">
        <text>星级排序</text>
        <text class="iconfont icon-xialajiantou"></text>
      </view>
    </view>
    <scroll-view class="content" scroll-y @scrolltolower="doLoadNext" enhanced :show-scrollbar="false">
      <view class="main">
        <view v-for="item in list" :key="item.id" class="list-item" @tap="navTo('pages/hotel/hotel', { id: item.id })">
          <view class="pic">
            <image :src="item.pic" mode="aspectFill" />
            <view v-if="item.score" class="score">{{ item.score }}</view>
          </view>
          <view class="right">
            <view class="name">
              <view>{{ item.name }}</view>
              <view class="star-cost">
                <text v-for="(_, i) in item.stars" :key="i" class="iconfont icon-vip-diamond-fill star-text" />
              </view>
            </view>
            <view class="tag-content">
              <view v-for="(tag, i) in item.tags" :key="i" class="tag1">{{ tag }}</view>
            </view>
            <view v-if="item.good_rate > 0">
              <text class="iconfont icon-huanjingzhan" />
              好评率：{{ item.good_rate }}%
            </view>
            <view>
              <view class="address ltxt">
                <text class="iconfont icon-ditu" />
                {{ item.address }}
              </view>
              <view v-if="item.cost_text" class="cost_text">均价：￥{{ item.cost_text }}</view>
            </view>

          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<style lang="scss" scoped>
@import "@/styles/_define.scss";
@import "@/styles/_mix.scss";

.body {
  background: white;
  height: 100%;
  overflow-y: hidden;
  display: flex;
  flex-direction: column;

  .sorter {
    display: flex;
    gap: 30rpx;
    margin: 20rpx 0 20rpx 28rpx;

    .sitem {
      @include center();
      gap: 20rpx;
      color: #333333;
      font-size: 24rpx;

      & .iconfont {
        font-size: 20rpx;
      }

      &.active {
        color: #1890FF;
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;

    .main {
      display: flex;
      flex-direction: column;

      .list-item {
        @include center();
        justify-content: flex-start;
        align-items: stretch;
        margin: 0 $padding-page 30rpx;
        gap: 16rpx;
        border-radius: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .pic {
          width: 160rpx;
          height: 160rpx;
          border-radius: $border-radius-v2;
          position: relative;

          .score {
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 10;
            height: 48rpx;
            width: 48rpx;
            @include center();
            border-radius: 50%;
            font-size: 24rpx;
            background: linear-gradient(180deg, #FDE72D 0%, #FACA14 100%);
          }

          image {
            border-radius: $border-radius-v2;
          }
        }

        .right {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 6rpx;
          font-size: 24rpx;

          .name {
            @include center();
            justify-content: flex-start;
            font-weight: bold;
            gap: 10rpx;

            >view {
              &:first-child {
                max-width: 340rpx;
                font-size: 28rpx;
                @include ellipse();
              }
            }

            .star-cost {
              @include center();
              justify-content: flex-start;
              gap: 8rpx;

              .star-text {
                color: #FACA14;
                font-size: 24rpx;
              }
            }
          }

          .address {
            max-width: 520rpx;
            @include ellipse();
          }

          .tag-content {
            display: flex;
            gap: 10rpx;
          }

          >view {
            &:last-child {
              @include center();
              justify-content: space-between;
            }

            .cost_text {
              font-size: 20rpx;
              font-weight: bold;
              color: #52C41A;
            }
          }
        }
      }
    }
  }
}
</style>
