<script setup>
import YjTabs from '@/components/YjTabs/YjTabs.vue';
import {onLoad} from '@dcloudio/uni-app';
import {onMounted, ref} from 'vue';
import Scenic from './components/scenic.vue';
import Hotel from './components/hotel.vue';
import {useGlobalStore} from '@/store/global'
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import {navTo} from "@/utils";

const globalStore = useGlobalStore()
const tabs = [
  {
    label: '景点', value: 'scenic',
  },
  {
    label: '酒店', value: 'hotel',
  }]
const activeTab = ref('hotel')
const navWidth = ref('100%')
const height = ref(0)
const query = ref({
  hotel: {},
  scenic: {},
})
const contentStyle = ref({
  paddingTop: '',
})

onLoad((q = {}) => {
  if (['hotel', 'scenic'].includes(q.tab)) {
    activeTab.value = q.tab
    delete q.tab
    for (let i in q) {
      q[i] = decodeURIComponent(q[i])
    }
    Object.assign(query.value[activeTab.value], q)
  }
})

function navbarLoad({height, paddingBottom}) {
  contentStyle.value = {
    paddingTop: `${height + paddingBottom}px`,
  }
}

onMounted(() => {
  uni.getSystemInfo({
    success({screenHeight, screenWidth}) {
      //#ifdef MP
      const menuButton = uni.getMenuButtonBoundingClientRect()
      navWidth.value = (menuButton.right - menuButton.width) + 'px'
      //#endif
      height.value = screenHeight - globalStore.navbar.height
    },
  })

})
</script>

<template>
  <YjNavBar custom-class="nav" @load="navbarLoad" @not-found="navTo('pages/index/index')">
    <template #title>
      <view :style="{ width: navWidth }">
        <YjTabs v-model="activeTab" :data="tabs" active-color="black" custom-class="tabs" text-color="#999999">
          <template #default="{item}">
            <view class="nav-item">
              <view>
                <text :class="{
                'icon-chuangtouka': item.value === 'hotel',
                'icon-ancient-gate-fill': item.value === 'scenic',
              }" class="iconfont"/>
                {{ item.label }}
              </view>

            </view>
          </template>
        </YjTabs>
      </view>
    </template>
  </YjNavBar>
  <view :style="contentStyle" class="container">
    <Scenic v-if="activeTab == 'scenic'" :active="activeTab == 'scenic'" :height="height" :query="query.scenic">
    </Scenic>
    <Hotel v-else :active="activeTab == 'hotel'" :height="height" :query="query.hotel">
    </Hotel>
  </view>
</template>

<style lang="scss" scoped>
@import "../../styles/_define.scss";
@import "../../styles/_mix.scss";

.container {
  position: relative;
  height: 100vh;
}

:deep(.tabs.yj-tabs) {
  justify-content: center;
  gap: 40rpx;

  .iconfont {
    font-size: 28rpx;
  }

  .tabs-item {
    &:first-child.active {
      .iconfont {
        color: #1890FF;
      }
    }

    &:last-child.active {
      .iconfont {
        color: #52C41A;
      }
    }
  }

  .active {
    font-weight: bold;

    .nav-item {
      display: flex;
      flex-direction: column;
      position: relative;

      &:after {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        z-index: -10;
        background: #FFE47C;
        border-radius: 10rpx;
        height: 20rpx;
        content: " ";
      }
    }
  }
}
</style>
