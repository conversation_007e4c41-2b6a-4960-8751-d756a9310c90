@use "../../styles/mix";
@use "../../styles/define";

.publish-wish {
  min-height: 100vh;
  @include mix.linear-gradient();
  position: relative;
  padding-bottom: define.$padding-page;
  padding-left: define.$padding-page;
  padding-right: define.$padding-page;
}

.tips {
  color: #999999;
  font-size: 20rpx;
  font-weight: normal;
}

.section {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .section-title {
    font-weight: bold;
    @include mix.center();
    justify-content: space-between;

    view {
      &:last-child {
        @include mix.center();
        gap: 4rpx;
      }
    }

    image {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .section-body {
    background: white;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 20rpx;
    padding: 30rpx;
  }

  textarea {
    padding: 30rpx 20rpx;
    width: 100%;
    box-sizing: border-box;
  }
}

.nobody {
  padding: 0 !important;
  border-radius: 0 !important;;
  background: transparent !important;;
  box-shadow: none !important;;
}

.dest-budget-day {
  .from-to {
    display: flex;
    gap: 4rpx;

    .left {
      @include mix.center(column);
      justify-content: space-between;
      gap: 4rpx;
      padding: 30rpx 0;

      text:first-child {
        display: block;
        height: 24rpx;
        width: 24rpx;
        background: #D8D8D8;
        border-radius: 4rpx;
      }

      text:last-child {
        color: #1890FF;
      }

      view {
        flex: 1;
        border: 2rpx dashed #979797;
      }
    }

    .dest {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30rpx;

      .to {
        border-radius: 16rpx;
        border: 2rpx solid #EDEEF0;
        padding: 18rpx 20rpx;
      }
    }
  }

  .switch {
    display: flex;
    gap: 10rpx;

    view {
      color: #999999;
      @include mix.center();
      gap: 6rpx;
      padding: 12rpx 0;
      flex: 1;
      background: #F7F7F9;
      border-radius: 20rpx;
    }

    .active {
      color: white;
      background: black;
    }
  }

  .budget {
    margin-top: 36rpx;
    margin-bottom: 30rpx;

    .tips {
      margin: 16rpx 0;
      @include mix.center();
      justify-content: flex-start;
      color: define.$plan-color-3;
    }

    .budget-options {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 16rpx;

      .budget-item {
        height: 56rpx;
        line-height: 56rpx;
        border-radius: 8rpx;
        border: 2rpx solid #EDEEF0;
        text-align: center;
        font-size: 20rpx;

        &.active {
          background: define.$plan-color-3;
          color: white;
          border-color: define.$plan-color-3;
        }
      }
    }
  }

  .date {
    .date-text {
      margin-top: 20rpx;
      border-radius: 8rpx;
      border: 2rpx solid #EDEEF0;
      padding: 14rpx 20rpx;
    }
  }
}

.todos {
  .todo-item, .btn-add {
    padding: 22rpx 20rpx;
    border-radius: 16rpx;
    border: 2rpx solid #EDEEF0;
  }

  .section-title {

  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-bottom: 20rpx;

    .todo-item {
      @include mix.center();
      justify-content: space-between;
      gap: 10rpx;

      .right {
        @include mix.center();
        gap: 20rpx;

        .iconfont {
          color: #B7B9BD;
        }
      }
    }
  }

  .btn-add {
    margin-top: 20rpx;
    @include mix.center();
    gap: 4rpx;
    color: define.$plan-color-3;

  }
}

.with-peoples {
  .peoples {
    margin-bottom: 20rpx;
    padding: 28rpx 20rpx;
    border-radius: 16rpx;
    border: 2rpx solid #EDEEF0;
    @include mix.center();
    justify-content: space-between;

    .left {
      text {

      }
    }

    .right {
      width: 130rpx;

      :deep(.input-number .input) {
        min-width: auto;
      }
    }
  }

  textarea {
    border-radius: 16rpx;
    border: 2rpx solid #EDEEF0;
  }
}

.wish-subject {
}


.tags {
  .list {
    display: flex;
    position: relative;
    flex-wrap: wrap;
    gap: 10rpx;

    .list-item {
      border-radius: 36rpx;
      padding: 16rpx 22rpx;
      background: define.$plan-color-3;
      color: white;
    }
  }

  .btn-add-tag {
    display: inline-block;
    cursor: pointer;

    > view {
      @include mix.center();
      gap: 4rpx;
    }

    border-radius: 36rpx;
    border: 2rpx solid #E7F4FF;
    color: define.$plan-color-3;
    padding: 16rpx 22rpx;
  }
}

.video {
  .section-title {
    //.tips {
    //  color: #999999;
    //  font-size: 20rpx;
    //  font-weight: normal;
    //}
  }

  .media-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .btn-upload, .media-item {
      width: 136rpx;
      height: 136rpx;
    }

    .btn-upload {
      @include mix.center(column);
      color: define.$plan-color-3;
      gap: 4rpx;
      border: 2rpx solid #E7F4FF;
      border-radius: 16rpx;
    }

    video, image {
      width: 100%;
      height: 100%;
      border-radius: 20rpx;
    }
  }
}

.tel {
  .note {
    font-size: 20rpx;
    @include mix.center();
    justify-content: flex-start;
    flex-wrap: wrap;
    color: #999999;

    .iconfont {
      color: define.$plan-color-4;
    }
  }

  .section-body {
    @include mix.center();
    justify-content: flex-start;

    text {
      color: #999;
    }
  }
}

.open_scope {
  label {
    background: white;
    margin-bottom: 20rpx;
    display: block;
    padding: 30rpx 40rpx;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 20rpx;

    text {
      color: #999999;
      font-size: 20rpx;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
