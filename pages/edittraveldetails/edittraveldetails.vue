<template>
  <!-- 微信小程序使用page-meta元素 -->
  <!-- #ifdef MP-WEIXIN -->
  <page-meta :page-style="'overflow:' + (showPopup ? 'hidden' : 'visible')"></page-meta>
  <!-- #endif -->
  <view class="container">
    <YjNavBar :custom-style="{
            background: 'transparent',
            position: 'absolute',
            top: '0',
            left: '0',
            width: '100%',
            zIndex: 20
        }" @not-found="navTo('pages/index/index')"/>

    <!-- 地图区域 -->
    <view class="map-container">
      <map id="myMap" :include-points="markers" :latitude="latitude" :longitude="longitude" :markers="markers"
           :polyline="rainbowPolyline" enable-3D @markertap="onMarkerTap" @touchend="handleMapTouchEnd"
           @touchmove="handleMapDrag" @touchstart="handleMapTouchStart"></map>
      <view class="map-mask"></view>

    </view>

    <!-- 详情页面区域 -->
    <view :class="{ 'detail-maximized': isDetailMaximized, 'detail-minimized': !isDetailMaximized }"
          :style="detailStyles" class="detail-container">
      <!-- 拖拽手柄 -->
      <view class="drag-handle" @touchstart.stop.prevent="handleDragStart"
            @touchmove.stop.prevent="handleDragMove" @touchend.stop.prevent="handleDragEnd"
            @touchcancel.stop.prevent="handleDragEnd">
        <view class="drag-indicator"></view>
      </view>

      <scroll-view id="detailContent" ref="detailContent" :class="{ 'scroll-active': isScrollEnabled }"
                   :scroll-top="scrollTop" :scroll-y="isScrollEnabled" enhanced :show-scrollbar="false"
                   :style="{ 'overflow-y': isScrollEnabled ? 'auto' : 'hidden' }" class="detail-content"
                   @scroll="handleContentScroll" @tap="handleDetailClick" @touchmove="handleContentTouchMove"
                   @touchstart="handleContentTouchStart">
        <view class="top-container">
          <view class="left-container">
            <view class="title-container">
              <text class="title">{{ detail.subject }}</text>
              <image class="edit-icon" mode="aspectFit"
                     :src="getCdnUrl('/static/edittraveldetails/edit.png')" @tap="onEditMainTitle"></image>
            </view>
            <text class="date">{{ dateRange }}</text>
          </view>
          <view class="right-container">
            <text class="price">{{ formatMoney(totalPrice, 1) }}</text>
            <text class="price-desc">预计费用</text>
            <text class="price-desc">（酒店+景点）</text>
          </view>
        </view>
        <view class="middle-container">
          <view class="first-container">
            <view class="m-left-container">
              <image class="image" mode="aspectFit"
                     :src="getCdnUrl('/static/edittraveldetails/xcts.png')"></image>
              <view class="title">
                <text class="title-text">行程天数</text>
              </view>
            </view>

            <view class="right-container">
              <text class="text">{{ detail.sections?.length || 0 }}天</text>
            </view>
          </view>
          <view class="first-container">
            <view class="m-left-container">
              <image class="image" mode="aspectFit"
                     :src="getCdnUrl('/static/edittraveldetails/jdsl.png')"></image>
              <view class="title">
                <text class="title-text">景点数量</text>
              </view>
            </view>
            <view class="right-container">
              <text class="text">{{ sceneCount }}个</text>
            </view>
          </view>
        </view>
        <view class="last-container">
          <view class="l-text">出发日期</view>
          <view class="r-view" @tap="onShowCalendar">
            <text class="r-text">{{ departureDate }}</text>
            <image class="r-icon" mode="aspectFit"
                   :src="getCdnUrl('/static/edittraveldetails/more-time.png')"></image>
          </view>
        </view>
        <view class="list-container">
          <!-- 天数tab栏 -->
          <view ref="tabContainer" class="days-tab-container">
            <view class="days-tab-wrapper">
              <view class="days-tab-scroll">
                <view v-for="(section, index) in detail.sections" :key="index"
                      :class="{ 'active': currentActiveDay === index }" class="day-tab"
                      @tap="onTabClick(index)">
                  <view class="day-tab-content">
                    <text class="day-text">{{ dayDates[index] || `第${index + 1}天` }}</text>
                  </view>
                  <view v-if="currentActiveDay === index" class="day-tab-indicator"></view>
                </view>
                <view class="add-day-tab" @tap="onAddDay">
                  <image mode="aspectFit" :src="getCdnUrl('/static/edittraveldetails/add_more.png')">
                  </image>
                </view>
              </view>
            </view>
          </view>

          <view ref="dataContainer" class="data">
            <view ref="sectionList" class="section-list">
              <view v-for="(section, index) in detail.sections" :key="index"
                    :class="[getDayClass(index), { 'menu-active': editSectionIndex === index }]"
                    class="section-item">
                <!-- 天数标题区域 -->
                <view class="section-header">
                  <view class="day-indicator">
                    <view :style="{ backgroundColor: getDayColor(index) }" class="day-circle">
                    </view>
                    <view class="day-title">{{ section.section_name }} {{ section.subject }}</view>
                  </view>
                  <view :class="{ 'active': editSectionIndex === index }" class="day-menu-btn"
                        @tap="onEditSection(index)">
                  </view>
                </view>

                <!-- 天数操作菜单 -->
                <view v-if="editSectionIndex == index" class="day-menu-popup">
                  <view v-if="index < detail.sections.length - 1" class="menu-item"
                        @tap="onMoveSection(index, 'backward')">
                    <image class="image_top" mode="aspectFit"
                           :src="getCdnUrl('/static/edittraveldetails/move_down.png')"></image>
                    <text>下移</text>
                  </view>
                  <view v-if="index > 0" class="menu-item" @tap="onMoveSection(index, 'forward')">
                    <image class="image_top" mode="aspectFit"
                           :src="getCdnUrl('/static/edittraveldetails/move_up.png')"></image>
                    <text>上移</text>
                  </view>
                  <view class="menu-item more_text"
                        @tap="onEditSectionSubject(index, 'subject')">
                    <image class="image_top" mode="aspectFit"
                           :src="getCdnUrl('/static/edittraveldetails/edit_title.png')"></image>
                    <text>编辑标题</text>
                  </view>
                  <view class="menu-item add_more_day" @tap="onAddSection(index)">
                    <image class="image_top" mode="aspectFit"
                           :src="getCdnUrl('/static/edittraveldetails/add_more_day.png')"></image>
                    <text>此后加一天</text>
                  </view>
                  <view class="menu-item more_text" @tap="onRemoveSection(index)">
                    <image class="image_top" mode="aspectFit"
                           :src="getCdnUrl('/static/edittraveldetails/delete.png')"></image>
                    <text>删除整天</text>
                  </view>
                </view>

                <!-- 时间线内容区域 -->
                <view class="timeline-container">

                  <template v-for="(line, li) in section.timeline" :key="li">
                    <view :class="`type-${line.type}`" class="timeline-item-card">
                      <!-- 时间线圆点 -->
                      <view :class="`dot-day-${index}`" class="timeline-dot"></view>

                      <!-- 类型图标 -->
                      <view class="type-icon">
                        <image :src="getTypeIcon(line)" mode="aspectFit"></image>
                      </view>

                      <!-- 内容区域 -->
                      <view class="item-content">
                        <view
                            v-if="line.type === 'plane' || line.type === 'train' || line.type === 'bus'"
                            class="transport-content">
                          <view class="transport-from">
                            <text class="zone-name">{{
                                line.transport.from.zone_name
                              }}
                            </text>
                            <text class="station">{{ line.transport.from.station }}</text>
                          </view>
                          <image class="transport-arrow" mode="widthFix"
                                 :src="getCdnUrl('/static/fly-to.png')"/>
                          <view class="transport-to">
                            <text class="zone-name">{{ line.transport.to.zone_name }}</text>
                            <text class="station">{{ line.transport.to.station }}</text>
                          </view>
                        </view>
                        <template v-else>
                          <view class="item-header">
                            <view v-if="line.pics && line.pics.length > 0" class="item-pic">
                              <image :src="line.pics[0]" mode="aspectFill"></image>
                            </view>
                            <view class="item-info">
                              <view class="item-title">{{ line.title }}</view>
                              <view v-if="line.type === 'hotel'" class="price-info">
                                <text class="price-label">均价：</text>
                                <text class="price">{{
                                    line.avg_price ? formatMoney(line.avg_price, 1) :
                                        '暂无均价'
                                  }}
                                </text>
                              </view>
                              <view v-else-if="line.type === 'scene'" class="item-desc">
                                门票：{{
                                  line.scene.is_free ? '免费' :
                                      line.avg_price ?
                                          formatMoney(line.avg_price, 1) :
                                          '暂无门票价格'
                                }}
                              </view>
                              <view v-else class="item-desc">
                                <text>{{ line.desc }}</text>
                              </view>
                              <view v-if="line.tags && line.tags.length > 0"
                                    class="item-tags">
                                <view v-for="(tag, tagIndex) in line.tags"
                                      :key="tagIndex" :class="getTagClass(tagIndex)"
                                      class="tag">
                                  {{ tag }}
                                </view>
                              </view>
                            </view>
                          </view>

                        </template>
                      </view>

                      <!-- 操作按钮 -->
                      <view :class="{ 'active': editTimeItemIndex === `${index}-${li}` }"
                            class="item-action-btn" @tap="onEditTimeItem(index, li)">
                      </view>
                      <!-- 操作菜单 -->
                      <view v-if="editTimeItemIndex === `${index}-${li}`" class="item-menu-popup">
                        <view class="menu-item" @tap="onMoveTimeTime(index, li, 'backward')">
                          <image class="image_top" mode="aspectFit"
                                 :src="getCdnUrl('/static/edittraveldetails/move_down.png')">
                          </image>
                          <text>下移</text>
                        </view>
                        <view class="menu-item" @tap="onMoveTimeTime(index, li, 'forward')">
                          <image class="image_top" mode="aspectFit"
                                 :src="getCdnUrl('/static/edittraveldetails/move_up.png')">
                          </image>
                          <text>上移</text>
                        </view>
                        <view class="menu-item add_more_day"
                              @tap="onAddScenicItem(index, line)">
                          <image class="image_top" mode="aspectFit"
                                 :src="getCdnUrl('/static/edittraveldetails/replace_scene.png')">
                          </image>
                          <text>替换景点</text>
                        </view>
                        <view class="menu-item add_more_day" @tap="onAddHotelItem(index, line)">
                          <image class="image_top" mode="aspectFit"
                                 :src="getCdnUrl('/static/edittraveldetails/replace_hotel.png')">
                          </image>
                          <text>替换酒店</text>
                        </view>
                        <view class="menu-item" @tap="onRemoveTimeItem(index, li)">
                          <image class="image_top" mode="aspectFit"
                                 :src="getCdnUrl('/static/edittraveldetails/delete.png')">
                          </image>
                          <text>删除</text>
                        </view>
                      </view>
                    </view>
                  </template>

                  <!-- 空状态时的添加按钮 -->
                  <view v-if="section.timeline.length == 0" class="empty-actions">
                    <view class="add-item-card" @tap="onShowAddItemPopup(index)">
                      <text class="iconfont icon-tianjia"></text>
                      <text>添加景点或酒店</text>
                    </view>
                  </view>

                  <!-- 时间线底部添加按钮 -->
                  <view v-if="section.timeline.length > 0"
                        :style="{ borderColor: getDayColor(index), color: getDayColor(index) }"
                        class="timeline-add-button" @tap="onShowAddItemPopup(index)">
                    <text class="iconfont icon-jia2"/>
                    <text class="add-text"> 添加景点或酒店</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar">
      <view class="action-btn ai-optimize-btn" @tap="onAIOptimize">
        <image class="btn-icon" mode="aspectFit" :src="getCdnUrl('/static/edittraveldetails/replan.png')">
        </image>
        <text class="btn-text">AI智能优化</text>
      </view>
      <view class="action-btn save-btn" @tap="onSaveTrip">
        <text class="btn-text">保存行程</text>
      </view>
    </view>

    <!-- 添加项目弹窗 -->
    <MyInfoPopup2 v-if="showAddItemPopup" class="add-item-popup" title="选择添加类型" @close="onCloseAddItemPopup">
      <view class="add-item-popup-content">
        <view class="add-item-option" @tap="onAddScenic">
          <view class="option-icon">
            <text class="iconfont icon-jindian"></text>
          </view>
          <view class="option-content">
            <text class="option-title">添加景点</text>
            <text class="option-desc">添加旅游景点到行程中</text>
          </view>
          <view class="option-arrow">
            <text class="iconfont icon-gengduo"></text>
          </view>
        </view>
        <view class="add-item-option" @tap="onAddHotel">
          <view class="option-icon">
            <text class="iconfont icon-jiudian"></text>
          </view>
          <view class="option-content">
            <text class="option-title">添加酒店</text>
            <text class="option-desc">添加住宿酒店到行程中</text>
          </view>
          <view class="option-arrow">
            <text class="iconfont icon-gengduo"></text>
          </view>
        </view>
      </view>
    </MyInfoPopup2>

    <!-- 日历组件 -->
    <WuCalendar ref="calendar" :date="departureDate2" :insert="false" :lunar="true" :monthShowCurrentMonth="true"
                color="#1890FF" mode="single" @confirm="calendarConfirm"></WuCalendar>

    <MyInfoPopup2 v-if="showChat" custom-class="min-chat" @close="showChat = false">
      <view style="height: 70vh">
        <MyChat :can-open-menu="false" :model-value="myChatData" :show-hello="false"
                :show-hotel-replace-btn="true" :show-view-more-hotel="false" chat-type="replan"
                @replan="onSelectPlan" @hotel-click="onSelectPlan"/>
      </view>

    </MyInfoPopup2>
  </view>
</template>
<script setup>

import {onLoad, onReady} from '@dcloudio/uni-app'
import {computed, nextTick, ref, watch, onUnmounted} from 'vue'
import {planDetail, planPrompt, planUpdate} from '../../api/plan'
import {useGlobalStore} from '../../store/global'
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import MyInfoPopup2 from "@/components/MyInfoPopup2/MyInfoPopup2.vue";
import WuCalendar from '@/uni_modules/wu-calendar/components/wu-calendar/wu-calendar.vue'
import {
  deepClone,
  moveElement,
  navBack,
  navTo,
  scrollToBottom,
  showConfirm,
  showPrompt,
  showToast,
  formatMoney,
  getCdnUrl
} from '../../utils'
import {getRainbowColors} from "@/utils/colors";
import MyChat from "@/components/MyChat/MyChat.vue";
import {trackEvent} from "@/utils/tracker";

const doTaskData = ref({})
const rpx2px = uni.rpx2px
const detail = computed(() => globalStore.editPlan)
const showChat = ref(false)
const myChatData = ref({})

// 计算彩虹线数据
const rainbowPolyline = computed(() => {
  // 按天分段的路线
  const polylines = []
  let last = null
  detail.value?.sections?.forEach((section, sectionIndex) => {
    // 收集当天景点和酒店的坐标点
    const points = section.timeline
        .filter(item => (item.type === 'scene' || item.type === 'hotel') && item.poi?.lng && item.poi?.lat)
        .map(item => {
          return {
            longitude: item.poi.lng,
            latitude: item.poi.lat
          }
        })
    // 当天有足够点才添加路线
    if (points.length > 0) {
      if (last) {
        points.unshift(last)
      }

      polylines.push({
        points,
        width: 8,
        arrowLine: true,
        color: getRainbowColors(sectionIndex),
      })
      last = points[points.length - 1]
    }
  })

  return polylines
})

// 计算日期范围字符串
const dateRange = computed(() => {
  const promptOptions = detail.value?.prompt_options
  const days = detail.value?.sections?.length

  if (!promptOptions?.start_date || !days) {
    return '2025年2月1日-2月3日' // 默认显示
  }

  const startTimestamp = promptOptions.start_date

  // 计算开始日期
  const startDate = new Date(startTimestamp * 1000)
  const startYear = startDate.getFullYear()
  const startMonth = startDate.getMonth() + 1
  const startDay = startDate.getDate()

  // 计算结束日期（开始日期 + 天数 - 1）
  const endDate = new Date(startTimestamp * 1000)
  endDate.setDate(endDate.getDate() + days - 1)
  const endMonth = endDate.getMonth() + 1
  const endDay = endDate.getDate()

  // 格式化为中文日期格式：年月日-月日
  const startDateStr = `${startYear}年${startMonth}月${startDay}日`
  const endDateStr = `${endMonth}月${endDay}日`

  return `${startDateStr}-${endDateStr}`
})

// 计算出发日期字符串
const departureDate = computed(() => {
  const promptOptions = detail.value?.prompt_options

  if (!promptOptions?.start_date) {
    return '2025年2月1日' // 默认显示
  }

  const startTimestamp = promptOptions.start_date
  const startDate = new Date(startTimestamp * 1000)
  const startYear = startDate.getFullYear()
  const startMonth = startDate.getMonth() + 1
  const startDay = startDate.getDate()

  return `${startYear}年${startMonth}月${startDay}日`
})
// 计算出发日期字符串
const departureDate2 = computed(() => {
  const promptOptions = detail.value?.prompt_options

  if (!promptOptions?.start_date) {
    return '2025-2-1' // 默认显示
  }

  const startTimestamp = promptOptions.start_date
  const startDate = new Date(startTimestamp * 1000)
  const startYear = startDate.getFullYear()
  const startMonth = startDate.getMonth() + 1
  const startDay = startDate.getDate()

  return `${startYear}-${startMonth}-${startDay}`
})

// 计算每天的日期数组
const dayDates = computed(() => {
  const promptOptions = detail.value?.prompt_options
  const sections = detail.value?.sections

  if (!promptOptions?.start_date || !sections) {
    return [] // 返回空数组，模板中会回退到默认显示
  }

  const startTimestamp = promptOptions.start_date
  const dates = []

  for (let i = 0; i < sections.length; i++) {
    const currentDate = new Date(startTimestamp * 1000)
    currentDate.setDate(currentDate.getDate() + i)

    const month = currentDate.getMonth() + 1
    const day = currentDate.getDate()

    dates.push(`${month}月${day}日`)
  }

  return dates
})

// 计算景点数量
const sceneCount = computed(() => {
  const sections = detail.value?.sections
  if (!sections) return 0

  return sections.reduce((total, section) => {
    const sceneItems = section.timeline?.filter(item => item.type === 'scene') || []
    return total + sceneItems.length
  }, 0)
})

// 计算总价格（景点+酒店）
const totalPrice = computed(() => {
  const sections = detail.value?.sections
  if (!sections) return 0

  return sections.reduce((total, section) => {
    const timeline = section.timeline || []
    return total + timeline.reduce((sectionTotal, item) => {
      // 只计算景点和酒店的价格
      if (item.type === 'scene' || item.type === 'hotel') {
        const price = item.avg_price
        // 确保价格是有效数字（以分为单位）
        if (price && !isNaN(price) && price > 0) {
          return sectionTotal + price // 保持以分为单位
        }
      }
      return sectionTotal
    }, 0)
  }, 0)
})
const globalStore = useGlobalStore()
const polyline = ref([])
const latitude = ref('')
const longitude = ref('')
const query = ref({})
const editSectionIndex = ref(-1)
const editTimeItemIndex = ref('')
const isDetailMaximized = ref(true) // 详情区域是否最大化
const currentDetailPosition = ref(0) // 详情区域当前位置
const isTransitioning = ref(false) // 添加过渡状态标记
const isScrollEnabled = computed(() => isDetailMaximized.value && currentDetailPosition.value === 0) // 是否允许内容滚动

// 天数tab相关响应式数据
const currentActiveDay = ref(0) // 当前激活的天数索引
const isTabSticky = ref(false) // tab是否处于吸顶状态
const tabContainer = ref(null) // tab容器ref
const scrollTop = ref(0) // scroll-view的滚动位置
const dataContainer = ref(null) // 数据容器ref
const sectionList = ref(null) // section列表ref
const detailContent = ref(null) // 详情内容容器ref
const sectionRefs = ref([]) // 各个section的ref数组

// 添加项目弹窗相关状态
const showAddItemPopup = ref(false) // 控制添加项目弹窗显示
const currentAddSectionIndex = ref(-1) // 当前要添加项目的section索引

// 日历组件引用
const calendar = ref(null)

// 获取屏幕高度和屏幕中点位置
const screenHeight = ref(0)
const screenMidPoint = ref(0)
const detailMaxHeight = ref(0) // 详情区域最大高度（像素）
const detailDefaultHeight = ref(0) // 详情区域默认高度（像素）

// 计算markers数据
const markers = computed(() => {
  const markerList = []
  let markerIndex = 10

  detail.value?.sections?.forEach((section) => {
    section.timeline.forEach((item) => {
      if ((item.type === 'scene' || item.type === 'hotel') && item.poi?.lng && item.poi?.lat) {
        markerList.push({
          id: ++markerIndex,
          longitude: item.poi.lng,
          latitude: item.poi.lat,
          title: item.title || item.poi.name,
          ...getMarkerIcon(item.type)
        })
      }
    })
  })

  return markerList
})

//在行程对话框中选择了一个方案
function onSelectPlan(ai_reqid) {
  planDetail({ai_reqid}, true).then(({data}) => {
    Object.assign(detail.value, data)
  }).finally(() => showChat.value = false)
}

onReady(() => {
  uni.getSystemInfo({
    success: (res) => {
      screenHeight.value = res.windowHeight
      screenMidPoint.value = res.windowHeight / 2
      // 详情区域最大高度为屏幕85%高度
      detailMaxHeight.value = res.windowHeight * 0.85
      // 详情区域默认高度为屏幕70%高度
      detailDefaultHeight.value = res.windowHeight * 0.7

      // 使用nextTick确保DOM更新后设置初始位置
      nextTick(() => {
        // 初始化详情区域位置，默认显示70%
        currentDetailPosition.value = detailMaxHeight.value - detailDefaultHeight.value

        // 标记为最大化状态(虽然只显示70%)
        isDetailMaximized.value = true
      })
    }
  })
})

const isDragging = ref(false) // 是否正在拖动
const dragStartY = ref(0) // 拖动开始时的Y坐标
const lastDragY = ref(0) // 上次拖动的Y坐标
const startPosition = ref(0) // 开始拖动时的详情区域位置

// 添加变量控制过渡效果
const useTransition = ref(false)

// 计算详情区域的样式，包括transform和transition
const detailStyles = computed(() => {
  const styles = {
    transform: `translateY(${currentDetailPosition.value}px)`
  }

  // 当拖动结束时应用过渡效果
  if (useTransition.value) {
    styles.transition = 'transform 0.3s ease'
  }

  return styles
})

// 拖拽开始 - 手柄事件
const handleDragStart = (e) => {
  // 确保获取正确的触摸点
  if (!e.touches || !e.touches[0]) return

  // 移除过渡效果，确保拖动过程中无延迟
  useTransition.value = false

  dragStartY.value = e.touches[0].clientY
  lastDragY.value = e.touches[0].clientY
  isDragging.value = true
  startPosition.value = currentDetailPosition.value
}

// 拖拽移动 - 手柄事件
const handleDragMove = (e) => {
  if (!isDragging.value) return

  // 获取当前触摸位置
  const currentY = e.touches[0].clientY

  // 计算移动距离
  const deltaY = currentY - dragStartY.value

  // 计算新位置，但限制在合理范围内
  // 最小值为0（完全展开），最大值为最大高度减去可见部分高度（几乎完全隐藏，但保留把手可见）
  const maxTranslate = detailMaxHeight.value - 100 // 保留100px高度的把手区域可见
  let newPosition = startPosition.value + deltaY

  if (newPosition < 0) newPosition = 0
  if (newPosition > maxTranslate) newPosition = maxTranslate

  // 更新位置
  currentDetailPosition.value = newPosition

  // 更新上次位置
  lastDragY.value = currentY
}

// 拖拽结束 - 手柄事件
const handleDragEnd = () => {
  if (!isDragging.value) return

  // 添加过渡效果，使状态切换平滑
  useTransition.value = true

  // 根据释放位置决定最终状态
  // 三种状态：最大化(85%)、默认(70%)和最小化
  if (currentDetailPosition.value < detailMaxHeight.value * 0.1) {
    // 接近顶部，最大化到85%
    currentDetailPosition.value = 0
    isDetailMaximized.value = true
  } else if (currentDetailPosition.value < detailMaxHeight.value * 0.5) {
    // 中间状态，恢复到默认高度70%
    currentDetailPosition.value = detailMaxHeight.value - detailDefaultHeight.value
    isDetailMaximized.value = true // 仍然视为最大化状态
  } else {
    // 接近底部，最小化
    currentDetailPosition.value = detailMaxHeight.value - 200
    isDetailMaximized.value = false
  }

  // 重置拖动状态
  isDragging.value = false

  // 一段时间后移除过渡效果，为下次拖动做准备
  setTimeout(() => {
    useTransition.value = false
  }, 300)
}

onLoad((q) => {
  Object.assign(query.value, q)

  planDetail(planQueryParams.value).then((res) => {
    const {data} = res

    Object.assign(globalStore.editPlan, data)

    // 处理时间线数据
    // nextTick(() => {
    // processTimelineData()
    // })
    getRoute(Object.assign({
      ai_reqid: detail.value.ai_reqid,
    }, planQueryParams.value))
  })
})

const planQueryParams = computed(() => {
  const p = {}

  if (query.value.plan_id) {
    p.plan_id = query.value.plan_id
  } else if (query.value.ai_reqid) {
    p.ai_reqid = query.value.ai_reqid
  }

  return p
})

function getRoute(params = {}) {
  return

}

function getMarkerIcon(type = 'scene') {
  return {
    iconPath: getCdnUrl(`/static/plan/marker-${type}.png`),
    width: 48,
    height: 48
  }
}

// 显示添加项目弹窗
function onShowAddItemPopup(sectionIndex) {
  currentAddSectionIndex.value = sectionIndex
  showAddItemPopup.value = true
}

// 关闭添加项目弹窗
function onCloseAddItemPopup() {
  showAddItemPopup.value = false
  currentAddSectionIndex.value = -1
}

// 添加景点
function onAddScenic() {
  if (currentAddSectionIndex.value >= 0) {
    const params = {
      day: currentAddSectionIndex.value,
      action: 'add'
    }
    navTo('pages/option/scene/scene', params)
    onCloseAddItemPopup()
  }
}

// 添加酒店
function onAddHotel() {
  if (currentAddSectionIndex.value >= 0) {
    const params = {
      day: currentAddSectionIndex.value,
      action: 'add'
    }
    navTo('pages/option/hotel/hotel', params)
    onCloseAddItemPopup()
  }
}

// 显示日历
function onShowCalendar() {
  if (calendar.value) {
    calendar.value.open()
  }
}

function onSelectStartDate(date) {
  console.log('选择的日期:', date)
}

// 日历确认选择
function calendarConfirm(date) {
  console.log('选择的日期:', date)

  // 验证日期数据
  if (!date || !date.fulldate) {
    console.error('日期数据无效:', date)
    showToast('日期选择失败，请重试')
    return
  }

  try {
    // 获取选择的日期，格式为 "2025-06-26"
    const selectedDateStr = date.fulldate
    console.log('选择的日期字符串:', selectedDateStr)

    // 将日期字符串转换为Date对象
    const selectedDate = new Date(selectedDateStr)

    // 验证日期是否有效
    if (isNaN(selectedDate.getTime())) {
      console.error('无效的日期格式')
    }

    // 转换为秒级时间戳
    const selectedTimestamp = Math.floor(selectedDate.getTime() / 1000)
    console.log('转换后的时间戳:', selectedTimestamp)

    // 更新全局状态中的出发日期
    if (globalStore.editPlan && globalStore.editPlan.prompt_options) {
      globalStore.editPlan.prompt_options.start_date = selectedTimestamp
      console.log('出发日期已更新:', selectedTimestamp)
    } else {
      console.error('无法访问计划数据')
    }

  } catch (error) {
    console.error('更新出发日期失败:', error)
    showToast('日期更新失败，请重试')
  }
}

function onAddScenicItem(sectionIndex, item, action = 'replace') {
  const params = {
    day: sectionIndex,
    action
  }
  if (action == 'replace') {
    params.id = item.item_id
  }

  navTo('pages/option/scene/scene', params)
}

function onAddStationItem(sectionIndex, timeIndex, action = 'replace') {
  const params = {
    day: sectionIndex,
    action
  }
  if (action == 'replace') {
    params.index = timeIndex
  }

  navTo('pages/option/station', params)
}

function onAddHotelItem(sectionIndex, item, action = 'replace') {
  const params = {
    day: sectionIndex,
    action
  }
  if (action == 'replace') {
    params.id = item.item_id
  }

  navTo('pages/option/hotel/hotel', params)
}

function onEditTimeItem(sectionIndex, timeIndex) {
  const index = `${sectionIndex}-${timeIndex}`
  if (editTimeItemIndex.value == index) {
    editTimeItemIndex.value = ''
  } else {
    editTimeItemIndex.value = index
  }
}

// 编辑主标题
function onEditMainTitle() {
  showPrompt('行程标题', detail.value.subject).then((content) => {
    if (content) {
      globalStore.editPlan.subject = content
    }
  })
}

function onEditSectionSubject(index, type) {
  const item = globalStore.editPlan.sections[index]
  showPrompt(type == 'section_name' ? '标题' : '备注', item[type]).then((content) => {
    if (content) {
      item[type] = content
    }
  })
}

function onEditSection(index) {
  if (editSectionIndex.value == index) {
    editSectionIndex.value = -1
  } else {
    editSectionIndex.value = index
  }
}

function onMoveTimeTime(index, tIndex, direction) {
  if (moveElement(globalStore.editPlan.sections[index].timeline, tIndex, direction)) {
    onEditTimeItem(index, direction == 'forward' ? tIndex - 1 : tIndex + 1)
  }
}

function onMoveSection(index, direction) {
  if (moveElement(globalStore.editPlan.sections, index, direction)) {
    editSectionIndex.value = direction == 'forward' ? index - 1 : index + 1
  }
}

function onRemoveTimeItem(sectionIndex, timeIndex) {
  showConfirm('确认删除', '确定要删除这个行程项目吗？').then(() => {
    globalStore.editPlan.sections[sectionIndex].timeline.splice(timeIndex, 1)
    editTimeItemIndex.value = ''
    // showToast('已删除')
  })
}

function onRemoveSection(index) {
  if (globalStore.editPlan.sections.length <= 1) {
    showToast('至少需要保留一天的行程')
    return
  }

  showConfirm('确认删除', '确定要删除这一天的行程吗？').then(() => {
    globalStore.editPlan.sections.splice(index, 1)
    editSectionIndex.value = -1

    // 重新调整天数名称
    globalStore.editPlan.sections.forEach((section, i) => {
      if (!section.section_name || section.section_name.includes('第') && section.section_name.includes('天')) {
        section.section_name = `第${i + 1}天`
      }
    })

    // 调整当前激活的天数
    if (currentActiveDay.value >= globalStore.editPlan.sections.length) {
      currentActiveDay.value = Math.max(0, globalStore.editPlan.sections.length - 1)
    }

    // showToast('已删除')
  })
}

function onAddSection(index) {
  const newSection = {
    section_name: `第${globalStore.editPlan.sections.length + 1}天`,
    subject: '',
    timeline: []
  }
  globalStore.editPlan.sections.splice(index + 1, 0, newSection)

  // 重新调整天数名称
  globalStore.editPlan.sections.forEach((section, i) => {
    if (!section.section_name || section.section_name.includes('第') && section.section_name.includes('天')) {
      section.section_name = `第${i + 1}天`
    }
  })

  editSectionIndex.value = -1

  nextTick(() => {
    onTabClick(index + 1)
    // showToast('已添加新的一天')
  })
}

// 监听详情区域滚动
const onDetailScroll = () => {
  // 仅用于监听，不需要特殊处理
}

// 地图拖动相关变量
const mapTouchStartY = ref(0)
const isMapDragging = ref(false)
const mapDragThreshold = 5 // 最小拖动距离阈值，避免误触（单位：像素）

// 地图触摸开始
const handleMapTouchStart = (e) => {
  if (!e.touches || !e.touches[0]) return

  mapTouchStartY.value = e.touches[0].pageY
  isMapDragging.value = false
}

// 地图拖动处理
const handleMapDrag = (e) => {
  if (!e.touches || !e.touches[0]) return

  const currentY = e.touches[0].pageY
  const deltaY = currentY - mapTouchStartY.value

  // 如果拖动距离超过阈值，认为用户正在拖动地图
  if (Math.abs(deltaY) > mapDragThreshold) {
    isMapDragging.value = true

    // 如果详情区域当前不是最小化状态，直接最小化
    if (isDetailMaximized.value) {
      // 添加过渡效果
      useTransition.value = true

      // 最小化详情区域
      currentDetailPosition.value = detailMaxHeight.value - 200
      isDetailMaximized.value = false

      // 一段时间后移除过渡效果
      setTimeout(() => {
        useTransition.value = false
      }, 300)
    }
  }
}

// 地图标记点点击处理
const onMarkerTap = (e) => {
  // 可以在这里处理地图标记点击事件
  console.log('标记点击', e)
}

// 获取类型图标路径
function getTypeIcon(line) {
  switch (line.type) {
    case 'scene':
      return getCdnUrl('/static/edittraveldetails/scene_mini.png')
    case 'hotel':
      return getCdnUrl('/static/edittraveldetails/hotel_mini.png')
    case 'text':
    default:
      return getCdnUrl('/static/edittraveldetails/pic_mini.png')
  }
}

// 获取标签样式类名
function getTagClass(index) {
  const classes = ['tag-blue', 'tag-orange', 'tag-purple']
  return classes[index % classes.length] || 'tag-blue'
}

// 获取天数对应的颜色
function getDayColor(dayIndex) {
  const colors = [
    '#1890ff', // 蓝色
    '#52c41a', // 绿色
    '#fa8c16', // 橙色
    '#722ed1', // 紫色
    '#eb2f96', // 粉色
    '#13c2c2', // 青色
    '#f5222d', // 红色
    '#faad14'  // 黄色
  ]
  return colors[dayIndex % colors.length]
}

// 获取天数对应的CSS类名
function getDayClass(dayIndex) {
  const classes = [
    'day-0', 'day-1', 'day-2', 'day-3',
    'day-4', 'day-5', 'day-6', 'day-7'
  ]
  return classes[dayIndex % classes.length]
}

// 地图touchend事件处理
const handleMapTouchEnd = () => {
  // 重置地图拖动状态
  isMapDragging.value = false
}

// 内容区域触摸相关变量
const contentTouchStartY = ref(0)

// 内容区域触摸开始
const handleContentTouchStart = (e) => {
  if (!e.touches || !e.touches[0]) return

  contentTouchStartY.value = e.touches[0].pageY
}

// 内容区域触摸移动
const handleContentTouchMove = (e) => {
  if (!e.touches || !e.touches[0]) return

  const currentY = e.touches[0].pageY
  const deltaY = currentY - contentTouchStartY.value

  // 如果正在过渡中，阻止所有滑动操作
  if (isTransitioning.value) {
    e.preventDefault()
    e.stopPropagation()
    return
  }

  // 如果详情不是最大化状态，并且用户试图向上滚动（deltaY < 0表示向上滚动）
  if (!isScrollEnabled.value && deltaY < -5) { // 添加5像素阈值避免误触
    // 阻止默认滚动行为
    e.preventDefault()
    e.stopPropagation()

    // 标记正在过渡
    isTransitioning.value = true

    // 将详情区域最大化
    useTransition.value = true
    currentDetailPosition.value = 0
    isDetailMaximized.value = true

    // 过渡完成后，移除过渡效果并结束过渡状态
    setTimeout(() => {
      useTransition.value = false
      isTransitioning.value = false
    }, 300)
  }
}

// 详情区域点击处理
const handleDetailClick = (e) => {
  // 如果详情区域未完全最大化（包括70%状态和最小化状态），点击任何地方都最大化
  if (!isScrollEnabled.value) {
    // 阻止事件冒泡，避免触发其他点击事件
    e.stopPropagation()

    // 添加过渡效果
    useTransition.value = true
    isTransitioning.value = true

    // 最大化详情区域到100%
    currentDetailPosition.value = 0
    isDetailMaximized.value = true

    // 过渡完成后，移除过渡效果
    setTimeout(() => {
      useTransition.value = false
      isTransitioning.value = false
    }, 300)
  }
}

// 节流函数
let scrollTimer = null
const throttle = (func, delay) => {
  return (...args) => {
    if (!scrollTimer) {
      scrollTimer = setTimeout(() => {
        func.apply(this, args)
        scrollTimer = null
      }, delay)
    }
  }
}

// 详情内容滚动处理（节流版本）
const handleContentScroll = throttle(() => {
  if (isScrollEnabled.value) {
    handleDetailScrollForTab()
  }
}, 50) // 减少延迟，提高快速滑动时的响应速度

// 天数tab相关方法
function onTabClick(dayIndex) {
  currentActiveDay.value = dayIndex
  scrollToSection(dayIndex)
}

function onAddDay() {
  // 限制最大天数（可根据需要调整）
  const maxDays = 15
  if (detail.value.sections.length >= maxDays) {
    showToast(`最多支持${maxDays}天的行程规划`)
    return
  }

  // 添加新的一天
  const newSection = {
    section_name: `第${detail.value.sections.length + 1}天`,
    subject: '',
    timeline: []
  }
  detail.value.sections.push(newSection)

  // 切换到新添加的天数
  nextTick(() => {
    const newDayIndex = detail.value.sections.length - 1
    onTabClick(newDayIndex)
    showToast('已添加新的一天')
  })
}

function scrollToSection(dayIndex) {
  nextTick(() => {
    const query = uni.createSelectorQuery()

    // 查询scroll-view的当前滚动位置和所有相关元素
    query.select('#detailContent').scrollOffset()
    query.selectAll('.section-item').boundingClientRect()
    query.select('.days-tab-container').boundingClientRect()
    query.select('#detailContent').boundingClientRect()
    query.exec((res) => {
      const scrollInfo = res[0]
      const sectionElements = res[1]
      const tabContainer = res[2]
      const detailContentElement = res[3]

      if (scrollInfo && sectionElements && sectionElements[dayIndex] && tabContainer && detailContentElement) {
        const targetSection = sectionElements[dayIndex]
        const tabHeight = tabContainer.height || 0
        const currentScrollTop = scrollInfo.scrollTop

        // 计算section相对于scroll-view内容区域的真实固定位置
        const sectionRelativeTop = (targetSection.top - detailContentElement.top) + currentScrollTop
        const targetScrollTop = sectionRelativeTop - tabHeight - 20

        // console.log('dayIndex:', dayIndex, 'currentScrollTop:', currentScrollTop, 'sectionRelativeTop:', sectionRelativeTop, 'targetScrollTop:', targetScrollTop)

        // 使用 scroll-view 的 scroll-top 属性进行滚动
        scrollTop.value = Math.max(0, targetScrollTop)
      }
    })
  })
}

// 监听滚动事件，动态切换活跃的天数
function handleDetailScrollForTab() {
  const query = uni.createSelectorQuery()

  // 查询滚动容器和所有section项目
  query.select('#detailContent').scrollOffset()
  query.selectAll('.section-item').boundingClientRect()
  query.select('.days-tab-container').boundingClientRect()
  query.select('#detailContent').boundingClientRect()
  query.exec((res) => {
    const scrollInfo = res[0]
    const sectionElements = res[1]
    const tabContainer = res[2]
    const detailContentElement = res[3]

    if (!scrollInfo || !sectionElements || !tabContainer || !detailContentElement) return

    const scrollTop = scrollInfo.scrollTop

    // 使用简单可靠的2/3位置判断策略
    // 当滚动位置超过某个section的2/3位置时才切换tab，避免过早切换
    for (let i = 0; i < sectionElements.length; i++) {
      const section = sectionElements[i]
      const sectionTop = section.top - detailContentElement.top
      const sectionBottom = sectionTop + section.height

      // 计算section的2/3位置作为切换点
      const switchPoint = sectionTop + (section.height * 2 / 3)

      // 当滚动位置超过section的2/3位置且在section范围内时切换tab
      if (scrollTop >= switchPoint && scrollTop < sectionBottom) {
        if (currentActiveDay.value !== i) {
          currentActiveDay.value = i
        }
        break
      }
    }
  })
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
    scrollTimer = null
  }
})

// 监听数据变化，确保当前激活天数不超出范围
watch(() => detail.value.sections?.length, (newLength) => {
  if (newLength && currentActiveDay.value >= newLength) {
    currentActiveDay.value = Math.max(0, newLength - 1)
  }
}, {immediate: true})

// 底部操作栏事件处理
function onAIOptimize() {
  myChatData.value = {
    plan_id: detail.value.plan_id
  }
  showChat.value = true

  trackEvent('ai_optimize_click')
}

function onSaveTrip() {
  const sections = detail.value.sections
  sections.forEach((section, sectionIndex) => {
    section.timeline.forEach((line, lineIndex) => {
      line.item_id = Number.parseInt(line.item_id || 0)
    })
  })
  const start_date = detail.value.prompt_options.start_date
  const main_title = detail.value.subject

  const data =
      {
        plan_id: detail.value.plan_id,
        start_date: start_date,
        subject: main_title,
        sections: JSON.stringify(sections)
      }

  planUpdate(data).then(() => {
    showToast('保存成功').then(() => navBack())
  })
}

</script>
<style lang="scss" scoped>
@import 'edittraveldetails.scss';
</style>
