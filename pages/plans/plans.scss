@use "../../styles/mix";
@use "../../styles/define";

.container, :deep(.navbar) {
  background-image: url("https://rp.yjsoft.com.cn/yiban/static/home/<USER>/plans/bg.png");
  background-repeat: no-repeat;
  background-size: contain;
}

:deep(.navbar) {
  background-size: cover;
}

.container {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.recommend {
  flex: 1;
  margin: 100rpx 0 0 0;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 20rpx;
  background: white;
  padding: 0 28rpx 30rpx 28rpx;
  position: relative;

  image {
    position: absolute;
    width: 152rpx;
    left: 0;
    top: -32rpx;
  }

  .header {
    padding: 32rpx 0 18rpx 100rpx;
    @include mix.center();
    justify-content: space-between;


    view {
      &:first-child {
        font-weight: 600;
        background-image: url("https://rp.yjsoft.com.cn/yiban/static/reco-bg.png");
        background-size: cover;
        background-position: top center;

        text {
          color: #1890FF;
        }
      }

      .iconfont {
        color: black;
      }
    }
  }

  .list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}
