<template>
  <YjNavBar custom-class="navbar" @load="({height,paddingBottom}) => bodyStyle.paddingTop=`${height+paddingBottom}px`"
            @not-found="navTo('pages/index/index')"/>
  <view :style="bodyStyle" class="container">
    <view class="recommend">
      <image mode="widthFix" src="https://rp.yjsoft.com.cn/yiban/static/home/<USER>/index/list-item-ren.png"/>
      <view class="header">
        <view>{{ location.city }}·
          <text :style='`color:${list.color}`'>{{ list.title }}</text>
        </view>
      </view>
      <view class="list">
        <MyRecommendPlanItem v-for="(item,index) in list.list" :key="index" :itemIndex="index" :plan="item"/>
      </view>
      <uni-load-more :content-text="{}" status="noMore"/>
    </view>
  </view>
</template>
<script setup>
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import {ref} from "vue";
import {navTo} from "@/utils";
import MyRecommendPlanItem from "@/components/MyRecommendPlanItem/MyRecommendPlanItem.vue";
import {onLoad} from "@dcloudio/uni-app";
import {plans} from "@/api";
import UniLoadMore from "@/uni_modules/uni-load-more/components/uni-load-more/uni-load-more.vue";
import {useGlobalStore} from "@/store/global";

const globalStore = useGlobalStore()
const bodyStyle = ref({})
const list = ref({
  title: '夏季精选',
  color: '',
  list: []
})
const location = ref({})

onLoad(({tag = ''}) => {
  plans({hot_tag: decodeURIComponent(tag)}).then(({data}) => {
    Object.assign(list.value, data)
  })
  globalStore.getLocation().then(res => {
    Object.assign(location.value, res)
  })
})

</script>

<style lang="scss" scoped>
@import "plans";
</style>
