<script setup>
import {ref, onMounted} from "vue";
import {usercenter} from "@/api";
import {packageList} from "@/api/point";
import {formatMoney, showToast} from "@/utils";

// 定义事件
const emit = defineEmits(['selectPackage'])

// 响应式数据
const userInfo = ref({
  package_info: {
    package_id: 0,
    package_name: '',
    expire_at: ''
  }
})
const packageListData = ref([])
const selectedPackageId = ref(null)
const loading = ref(false)

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    const {data} = await usercenter()
    console.log("usercenter", data)
    userInfo.value = data
  } catch (e) {
    console.error('获取用户信息失败', e)
  }
}

// 获取套餐列表
const fetchPackageList = async () => {
  try {
    loading.value = true
    const {data} = await packageList()
    console.log("packageList", data)
    packageListData.value = data.list || []
  } catch (e) {
    console.error('获取套餐列表失败', e)
    showToast('获取套餐列表失败')
  } finally {
    loading.value = false
  }
}

// 选择套餐
const selectPackage = (pkg) => {
  // 判断套餐是否可购买
  if (pkg.can_buy === false) {
    showToast('您当前不可购买此套餐')
    return
  }

  selectedPackageId.value = pkg.id
  // 发送选择套餐事件到父组件
  emit('selectPackage', pkg)
}

// 购买套餐
const onPurchase = (pkg) => {
  uni.showToast({
    title: `购买 ${pkg.name}`,
    icon: 'none'
  })
}

// 格式化有效期
const formatExpireDate = (expireAt) => {
  if (!expireAt) return ''
  const date = new Date(expireAt * 1000) // 将秒级时间戳转换为毫秒级
  if (isNaN(date.getTime())) return ''
  return `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`
}

// 计算剩余天数
const calculateRemainingDays = (expireAt) => {
  if (!expireAt) return 0

  const now = new Date()
  const expireDate = new Date(expireAt * 1000)

  if (isNaN(expireDate.getTime()) || expireDate < now) return 0

  // 计算两个日期之间的毫秒差值
  const diffTime = Math.abs(expireDate - now)
  // 转换为天数并向下取整
  return Math.floor(diffTime / (1000 * 60 * 60 * 24))
}

onMounted(async () => {
  await Promise.all([
    fetchUserInfo(),
    fetchPackageList()
  ])
})
</script>

<template>
  <view class="purchase-container">
    <view class="current-package">
      <view class="package-title">
        当前套餐：{{
          (userInfo.package_info && userInfo.package_info.package_id > 0) ? userInfo.package_info.package_name :
              '暂无'
        }}
        <text v-if="userInfo.package_info && userInfo.package_info.package_id > 0 && userInfo.package_info.expire_at"
              class="remaining-days">
          还剩{{ calculateRemainingDays(userInfo.package_info.expire_at) }}天到期
        </text>
      </view>
      <view class="package-expire">
        服务到期时间：{{
          (userInfo.package_info && userInfo.package_info.package_id > 0 && userInfo.package_info.expire_at) ?
              formatExpireDate(userInfo.package_info.expire_at) : '暂无'
        }}
      </view>
    </view>

    <view v-if="!loading" class="packages-list">
      <view v-for="pkg in packageListData" :key="pkg.id" :class="{ selected: selectedPackageId === pkg.id }"
            class="package-item" @tap="selectPackage(pkg)">


        <view class="package-content">
          <!-- 积分信息 -->
          <view>
            <view class="left">
              <text class="points-number">{{ pkg.amount }}</text>
              <text class="iconfont icon-pinpai points-icon"/>
            </view>
            <!-- 标签区域 -->
            <view class="tags">
              <view v-for="tag in [].concat(...pkg.tags, pkg.name)" :key="tag" :class="{ popular: tag === '最受欢迎' }"
                    class="tag">
                {{ tag }}
              </view>
            </view>
          </view>

          <!-- 价格信息 -->
          <view v-if="!pkg.can_use_new" class="price-info">
            <text class="price">{{ formatMoney(pkg.price) }}/{{
                pkg.exp_num > 1 ? pkg.exp_num : ''
              }}{{ pkg.exp_unit_text }}
            </text>
          </view>
          <view v-else class="price-info" style="align-items: flex-end">
            <text class="new-price">{{ formatMoney(pkg.new_real_price) }}/{{
                pkg.exp_num > 1 ? pkg.exp_num : ''
              }}{{ pkg.exp_unit_text }}
            </text>
            <text class="original-price">{{ formatMoney(pkg.price) }}/{{
                pkg.exp_num > 1 ? pkg.exp_num : ''
              }}{{ pkg.exp_unit_text }}
            </text>
          </view>

          <!-- 描述信息 -->
          <view class="description">{{ pkg.desc }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
@import "./points_purchase.scss";
</style>
