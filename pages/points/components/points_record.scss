@import "../../../styles/_define.scss";
@import "../../../styles/_mix.scss";

/* 隐藏全局滚动条 */
:deep(page) {
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

.record-page {
  width: 100%;
  min-height: 100vh; /* 改为 min-height，让内容自然撑开 */
  display: flex;
  flex-direction: column;
  background-color: transparent;
}

.fixed-header {
  margin: 30rpx 0rpx;
  padding-left: 28rpx;
  padding-right: 28rpx;
}

.time-filter-card {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;

  .filter-content {
    background-color: #F7F7F9;
    border-radius: 16rpx;
    padding: 10rpx 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .selected-option {
      font-size: 28rpx;
      color: #333333;
    }

    .dropdown-icon {
      width: 24rpx;
      height: 24rpx;
    }
  }

  .dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    margin-top: 4rpx;
    overflow: hidden;

    .option-item {
      padding: 24rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      border-top: 1rpx solid #F0F0F0;

      &.active {
        color: #1890FF;
        background-color: #F6F9FF;
      }

      &:active {
        background-color: #F6F6F6;
      }
    }
  }
}

/* .record-container 样式已移除，避免 scroll-view 宽度超出屏幕 */
/* 高度通过动态计算在模板中设置 */

.records-list {
  padding: 0 28rpx; /* 在内部容器设置边距 */

  .record-item {
    display: flex;
    align-items: flex-start;
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
  }
}

.record-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  margin-right: 20rpx;
  
  image {
    width: 48rpx;
    height: 48rpx;
  }
}

.record-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.record-info {
  flex: 1;

  .record-title {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 8rpx;
  }

  .record-date {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 6rpx;
  }
  
  .record-expire {
    font-size: 20rpx;
    color: #1890FF;
    margin-bottom: 6rpx;
  }
}

.record-amount {
  font-size: 32rpx;
  font-weight: bold;

  &.income {
    color: #52c41a;
  }

  &.expense {
    color: #ff4d4f;
  }
}

.load-more {
  text-align: center;
  padding: 20rpx 0;
  
  text {
    font-size: 26rpx;
    color: #999;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
  
  text {
    font-size: 28rpx;
    color: #999;
  }
}
