@import "../../../styles/_define.scss";
@import "../../../styles/_mix.scss";

/* 隐藏全局滚动条 */
:deep(page) {
  &::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }

  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

.purchase-container {
  height: 100%;
  width: 100%;
  padding-left: 28rpx;
  padding-right: 28rpx;
  background-color: transparent;
  padding-bottom: env(safe-area-inset-bottom);
  flex: 1;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */

  /* 隐藏滚动条 - WebKit浏览器（Chrome、Safari） */
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    background: transparent;
  }

  /* 隐藏滚动条 - Firefox */
  scrollbar-width: none;

  /* 隐藏滚动条 - IE/Edge */
  -ms-overflow-style: none;
}

.current-package {
  background: #F6F6F6;
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 30rpx 0;

  .package-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 12rpx;

    .remaining-days {
      color: #1890FF;
      font-size: 20rpx;
      font-weight: normal;
    }
  }

  .package-expire {
    font-size: 24rpx;
    color: #666666;
  }
}

.packages-list {
  .package-item {
    background: #F0FDFF;
    border: 2rpx solid #E7F4FF;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 30rpx;
    position: relative;

    &.selected {
      background: linear-gradient(to bottom, #35C3FF, #1890FF);
      border: 0 transparent;

      .points-number,
      .points-text,
      .description,
      .price, .points-icon,
      .new-price, .original-price {
        color: white !important;
      }

      .tag {
        background: #E7F4FF !important;
        color: #1890FF !important;

        &.popular {
          background: #E7F4FF !important;
          color: #1890FF !important;
        }
      }
    }
  }
}

.package-content {

  > view {
    &:first-child {
      display: flex;
      justify-content: space-between;

      .left {
        @include center();
        gap: 4rpx;

        .points-icon {
          color: $plan-color-3;
        }
      }
    }
  }

  .points-info {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;

    .points-number {
      font-size: 34rpx;
      font-weight: bold;
      color: #333333;
    }
  }

  .tags {
    display: flex;
    gap: 8rpx;
    max-width: 40%;
    justify-content: flex-end;

    .tag {
      background: #E7F4FF;
      color: #1890FF;
      padding: 6rpx 14rpx;
      border-radius: 8rpx;
      font-size: 20rpx;
      white-space: nowrap;

      &.popular {
        background: #FACA14;
        color: white;
      }
    }
  }

  .price-info {
    margin-bottom: 10rpx;
    display: flex;
    align-items: center;
    gap: 10rpx;

    .price {
      font-size: 20rpx;
      color: #333333;
    }

    .original-price {
      font-size: 20rpx;
      color: #999;
      text-decoration: line-through;
    }

    .new-price {
      font-size: 34rpx;
      font-weight: bold;
    }
  }

  .description {
    font-size: 20rpx;
    color: #999999;
  }
}
