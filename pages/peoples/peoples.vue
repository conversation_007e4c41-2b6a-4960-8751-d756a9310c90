<template>
  <view class="container">
    <YjTabs v-model="activeTab" :data="tabData" active-color="#1890FF" />

    <view class="buttons">
      <view class="d">
        <view @tap="() => {
          currentPeople = {}
          showPeopleForm = true
        }
        ">
          <text class="iconfont icon-jia"></text>
          <text>手动添加</text>
        </view>
        <view @tap="onTakePhoto">
          <text class="iconfont icon-zhaoxiangji"></text>
          <text>识别证件添加</text>
        </view>
      </view>
    </view>

    <view class="list">
      <view v-for="(item, index) in list" :key="index" class="list-item">
        <view class="name" @tap="onEdit(item)">{{ item.name }}</view>
        <view class="info">
          <view class="left">
            <radio-group v-if="choose" @change="onChoosePeople">
              <radio :value="index" />
            </radio-group>

            <view>
              <view>
                <text class="label">{{ item.id_type_text }}</text>
                {{ item.id_no }}
              </view>
              <view>
                <text class="label">手机号码</text>
                {{ item.phone }}
              </view>
            </view>

          </view>
          <view>
            <text class="iconfont icon-gengduo" @tap="onEdit(item)"></text>
          </view>
        </view>
      </view>
    </view>

    <uni-load-more :status="loadMoreStatus" />

    <PeopleForm v-if="showPeopleForm" :data="currentPeople" @close="() => {
      getList()
      showPeopleForm = false
    }
    " @remove="e => {
      const index = list.findIndex(item => item.id === e)
      if (index > -1) {
        list.splice(index, 1)
      }

      showPeopleForm = false

    }" />
  </view>
</template>

<script setup>

import { onLoad } from '@dcloudio/uni-app'
import PeopleForm from './components/PeopleForm.vue'
import { computed, ref } from 'vue'
import { peopleList } from '@/api/user';
import { navBack, showToast } from '@/utils';
import { GlobalTypePeople, IdTypeIdCard } from '@/utils/constmap';
import YjTabs from '@/components/YjTabs/YjTabs.vue';
import { useGlobalStore } from "@/store/global";

const tabData = [
  {
    label: '出行人',
    value: 'people'
  },
  {
    label: '地址',
    value: 'address'
  },
  {
    label: '报销凭证',
    value: 'c'
  }
]

const globalStore = useGlobalStore()
const choose = ref(false)
const required = ref([])
const activeTab = ref('people')
const showPeopleForm = ref(false)
const currentPeople = ref({})
const page = ref(1)
const page_size = 20
const list = ref([])
const isLoading = ref(false)
const total = ref(0)
const loadMoreStatus = computed(() => {
  if (isLoading.value) {
    return 'loading'
  }
  return Math.ceil(total.value / page_size) > page.value ? 'more' : 'noMore'
})

function onEdit(row) {
  currentPeople.value = row
  showPeopleForm.value = true
}

function onChoosePeople(e) {
  const people = list.value[e.detail.value]
  if (!people) {
    return
  }

  const mapping = {
    phone: '手机号码',
    id_no: '证件号',
  }

  const field = required.value.find(it => {
    return !people[it]
  })
  if (field) {
    showToast(`请选择${mapping[field]}不为空的出行人`)
    return;
  }

  globalStore.setData(GlobalTypePeople, [people])

  navBack()
}

function getList() {
  isLoading.value = true
  const params = {
    page: page.value,
    page_size
  }

  peopleList(params).then(({ data }) => {
    total.value = data.total
    list.value.push(...data.list)

    list.value = list.value.reduce((acc, current) => {
      const x = acc.find((item) => item.id === current.id)
      if (!x) {
        acc.push(current)
      } else {
        Object.assign(x, current)
      }
      return acc
    }, [])

  }).finally(() => (isLoading.value = false))
}

function onTakePhoto() {
  uni.chooseMedia({
    count: 1,
    mediaType: ['image'],
    success: async (res) => {
      // 检查用户是否选择了文件
      if (!res.tempFiles || res.tempFiles.length === 0) {
        showToast('请选择文件')
        return;
      }

      const tempFilePath = res.tempFiles[0].tempFilePath;

      try {
        uni.showLoading({
          title: '识别中...'
        })
        const invokeRes = await wx.serviceMarket.invokeService({
          service: 'wx79ac3de8be320b71',
          api: 'OcrAllInOne',
          data: {
            // 用 CDN 方法标记要上传并转换成 HTTP URL 的文件
            img_url: new wx.serviceMarket.CDN({
              type: 'filePath',
              filePath: tempFilePath,
            }),
            data_type: 3,
            ocr_type: 1
          },
        })
        if (invokeRes.data.idcard_res.type !== 0) {
          showToast('请选择正面身份证')
          return
        }
        const { data: { idcard_res } } = invokeRes
        Object.assign(currentPeople.value, {
          name: idcard_res.name.text,
          id_no: idcard_res.id.text,
          id_type: IdTypeIdCard
        })
        showPeopleForm.value = true

      } catch (err) {
        showToast('识别失败')
      } finally {
        uni.hideLoading()
      }
    }
  })
}

onLoad((query) => {
  activeTab.value = query.tab ?? 'people'
  choose.value = query.choose ?? false
  required.value = query.required ? query.required.split(',') : []

  getList()
})
</script>

<style lang="scss" scoped>
@import 'peoples.scss';
</style>
