@use "../../styles/mix";
@use "../../styles/define";

.wish-square {
  position: relative;
  min-height: 100vh;
  padding: 0 define.$padding-page;
  @include mix.linear-gradient();

  .banner {
    height: 140rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .search-section {
    margin-bottom: 30rpx;
    margin-top: 30rpx;

    .search-group {
      margin-bottom: 20rpx;

      > view {
        font-weight: bold;
        margin-bottom: 10rpx;
      }
    }

    .scroll-view {
      white-space: nowrap;

      .filter-item {
        display: inline-block;
        padding: 8rpx 18rpx;
        margin-right: 12rpx;
        background: #EDEEF0;
        border-radius: 30rpx;

        &.active {
          background: define.$plan-color-3;
          color: white;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .sort-options {
    display: flex;
    margin: 30rpx 0 20rpx 0;
    gap: 20rpx;
    color: #999999;
    font-weight: bold;

    .sort-item {

      &.active {
        color: define.$plan-color-3;

      }
    }
  }
}

.wish-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;

  .wish-item {
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;

    .cover {
      width: 100%;
      height: 300rpx;
      border-top-left-radius: 16rpx;
      border-top-right-radius: 16rpx;
    }

    .wish-info {
      padding: 20rpx;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .title {
      font-weight: bold;
      margin: 0 20rpx 10rpx 0;
    }

    .desc {
      font-size: 20rpx;
      color: #999;
      @include mix.ellipse(2);
    }

    .tags {
      margin-top: 10rpx;
      margin-bottom: 20rpx;
      display: flex;
      flex-wrap: wrap;

      text {
        padding: 8rpx 16rpx;
        border-radius: 30rpx;
        font-size: 20rpx;
        @include mix.tag-bg-colors();
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .left {
        display: flex;
        align-items: center;
      }

      .avatar {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        margin-right: 8rpx;
      }
    }
  }
}
