<template>
  <YjNavBar :custom-style="{
    background: navBg
  }" title="心愿广场" @load="(({ height, paddingBottom }) => pageStyle.paddingTop = `${height + paddingBottom}px`)"
            @not-found="navTo('pages/index/index')"/>
  <view :style="pageStyle" class="wish-square">
    <!-- 顶部banner -->
    <view v-for="(item, index) in banners" class="banner" @click="navTo(item.link)">
      <image mode="widthFix" :src="item.pic"/>
    </view>

    <!-- 搜索区 -->
    <view class="search-section">
      <view class="search-group">
        <view>热门城市</view>
        <scroll-view class="scroll-view" scroll-x>
          <view @tap="currentCity = ''" class="filter-item" :class="{
            active: currentCity === '',
          }">全部
          </view>
          <view :class="{
            active: currentCity === city.id
          }" @tap="currentCity = city.id" v-for="(city, index) in cities" :key="index" class="filter-item">
            {{ city.name }}
          </view>
        </scroll-view>
      </view>

      <view class="search-group">
        <view>主题标签</view>
        <scroll-view class="scroll-view" scroll-x>
          <view @tap="currentTag = []" :class="{
            active: currentTag.length === 0
          }" class="filter-item">全部
          </view>
          <view @tap="onTapTag(tag)" :class="{
            active: currentTag.includes(tag.tag)
          }" v-for="(tag, index) in tags" :key="index" class="filter-item">{{ tag.tag }}
          </view>
        </scroll-view>
      </view>

      <view class="search-group">
        <view>出行时间</view>
        <scroll-view class="scroll-view" scroll-x>
          <view @tap="currentTime = []" :class="{
          active: currentTime.length === 0
        }" class="filter-item">全部
          </view>
          <view @tap="onTapTime(time.name)" :class="{
            active: currentTime.includes(time.name)
          }" v-for="(time, index) in times" :key="index" class="filter-item">{{ time.name }}
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 排序选项 -->
    <view class="sort-options">
      <view class="sort-item" :class="{ active: sortType === 'hot' }" @click="sortType = 'hot'">热度优先</view>
      <view class="sort-item" :class="{ active: sortType === 'ctime' }" @click="sortType = 'ctime'">最新发布</view>
    </view>

    <!-- 心愿列表 -->
    <view class="wish-list">
      <view v-for="(item, index) in wishList" :key="index" class="wish-item" @tap="onTapItem(item)">
        <image :src="imageResize(item.cover, 332)" class="cover" mode="aspectFill"/>
        <view class="wish-info">
          <view>
            <view class="title">{{ item.title }}</view>
            <view class="desc">{{ item.wish_desc }}</view>
            <view class="tags">
              <text v-for="(tag, i) in item.tags.slice(0, 3)" :key="i">{{ tag }}</text>
            </view>
          </view>

          <view class="user-info">
            <view class="left">
              <image :src="item.avatar" class="avatar"/>
              <text class="nickname">{{ item.nickname }}</text>
            </view>

            <view class="likes">
              <text class="iconfont icon-a-shoucang-weishoucang"/>
              {{ item.likes }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import {computed, ref, watch} from 'vue'
import {onLoad, onPageScroll, onReachBottom} from "@dcloudio/uni-app";
import {wishIndex, wishSquareList} from "@/api/wish";
import {imageResize, navTo} from "@/utils";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";

const pageStyle = ref({})

// 筛选数据
const cities = ref([])
const tags = ref([])
const times = ref([])
const banners = ref([])

let page = 1
let total = 0
const page_size = 20
const hasMore = computed(() => total.value > page_size * page)
const currentCity = ref('')
const currentTag = ref([])
const currentTime = ref([])
const pageScroll = ref(0)
const navBg = computed(() => {
  const threshold = 50 // 滚动距离阈值
  if (pageScroll.value >= threshold) {
    return 'rgba(255, 255, 255, 1)' // 白色背景
  }
  return 'rgba(255, 255, 255, 0)' // 透明背景
})

// 心愿列表数据
const sortType = ref('hot')
const wishList = ref([])

watch([sortType, currentCity, () => currentTag.value.length, () => currentTime.value.length], () => {
  page = 1
  wishList.value = []
  getList()
})

const onTapTag = (tag) => {
  if (currentTag.value.includes(tag.tag)) {
    currentTag.value = currentTag.value.filter(item => item !== tag.tag)
  } else {
    currentTag.value.push(tag.tag)
  }
}

const onTapTime = (time) => {
  if (currentTime.value.includes(time)) {
    currentTime.value = currentTime.value.filter(item => item !== time)
  } else {
    currentTime.value.push(time)
  }
}

const getList = () => {
  const params = {
    page,
    page_size,
    sort: sortType.value,
    to_zone_id: currentCity.value,
    tags: currentTag.value.join(','),
    day_tags: currentTime.value.join(','),
  }

  wishSquareList(params).then(({data}) => {
    wishList.value.push(...data.list)
  })
}

function onTapItem(item) {
  navTo('pages/wish-detail/wish-detail', {id: item.wish_id})
}

// 监听页面滚动到底部事件
onReachBottom(() => {
  if (hasMore.value) {
    page++
    getList()
  }
})

onPageScroll(({scrollTop}) => {
  pageScroll.value = scrollTop
})

onLoad(query => {
  sortType.value = query.sort || 'hot'

  wishIndex().then(({data}) => {
    cities.value = data.hot_zones
    tags.value = data.hot_tags
    times.value = data.hot_day_tags
    banners.value = data.banners

    getList()
  })
})

</script>

<style lang="scss" scoped>
@use "wish-square";
</style>
