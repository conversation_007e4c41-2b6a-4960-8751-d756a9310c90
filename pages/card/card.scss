@import '../../styles/_mix.scss';
@import '../../styles/_wifi.scss';

.app-container {
  min-height: 100vh;
  background: #F7F7F9;
}

.app-container, .tabs, .card-container {
  display: flex;
  flex-direction: column;
}

.card-container {
  margin-top: 44rpx;
  flex: 1;

  .tabs {
    flex: 1;

    .tabs-header {
      display: flex;
      justify-content: center;

      .item {
        flex: 1;
        @include center(column);
        gap: 20rpx;

        image {
          width: 68rpx;
          height: 68rpx;
        }
      }
    }

    .get {
      @include center(column);
      gap: 6rpx;
      margin: 44rpx 0 16rpx 0;

      :deep(.my-button) {
        padding-left: 144rpx;
        padding-right: 144rpx;
      }

      text {
        font-size: 20rpx;
      }

      .expire {
        color: #999999;
      }
    }

    .tabs-content, .tabs-content :deep(.desc-box) {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
}