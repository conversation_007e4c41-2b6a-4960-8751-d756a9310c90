<template>
  <div class="app-container">
    <my-wifi-head :name="info.advertiser_name"/>

    <view class="card-container">
      <view class="tabs">
        <radio-group @change="onSwitchTab">
          <view class="tabs-header">
            <view v-for="item in info.airlines" :key="item.code" :class="{active: active === item.code}"
                  class="item">
              <image :src=" item.logo"></image>
              {{ item.name }}
              <radio :checked="active === item.code" :value="item.code"/>
            </view>
          </view>
        </radio-group>
        <view class="get">
          <my-button :disable="!canGet" type="warning" @tap="onGet">点击领取</my-button>
          <text class="stock">剩余库存：({{ current.stock }})</text>
          <text class="expire">有效期至{{ formatTime(info.end_time) }}</text>
        </view>

        <view class="tabs-content">
          <my-wifi-desc :desc="current.desc" :scope="current.scope" class="desc-box"/>
        </view>
      </view>
    </view>
  </div>
</template>

<script setup>
import {onLoad} from '@dcloudio/uni-app'
import {computed, ref} from 'vue';
import {formatTime, navTo, showToast} from '../../utils';
import {cardGet, cardInfo} from '../../api/wifi';
import MyButton from "@/components/MyButton/MyButton.vue";
import MyWifiHead from "@/pages/card/components/MyWifiHead.vue";
import MyWifiDesc from "@/pages/card/components/MyWifiDesc.vue";
import dayjs from "dayjs";
import {trackEvent} from "@/utils/tracker";

const code = ref('')
const info = ref({
  advertiser_name: '',
  airlines: [],
  get_by_my: false,
})
const active = ref('')
const canGet = computed(() => {
  const now = new Date().getTime()
  const start = info.value.start_time * 1000
  const end = info.value.end_time * 1000

  if (current.stock <= 0) {
    return false
  } else if (info.value.state !== 1 || info.value.got_state !== 1) {
    return false
  } else if (start > now || end < now) {
    return false
  }

  return true
})

const current = computed(() => {
  if (!active.value) {
    return {
      desc: [],
      scope: '',
      stock: 0,
      end_time: 0,
    }
  }
  const item = {
    ...info.value.airlines.find(item => item.code == active.value)
  }

  item.desc = item.desc.split("\n")

  return item
})

function onSwitchTab({detail}) {
  active.value = detail.value
}

function onGet() {
  const now = dayjs()
  const start = dayjs.unix(info.value.start_time)
  const end = dayjs.unix(info.value.end_time)

  if (now.isBefore(start) || now.isAfter(end)) {
    showToast(`请在${start.format('YYYY-MM-DD HH:mm')}至${end.format('YYYY-MM-DD HH:mm')}领取`)
    return
  } else if (!canGet.value) {
    return
  }

  const data = {
    airline: active.value,
    code: code.value
  }

  trackEvent('get_wifi_code', data)

  cardGet(data).then((res) => {
    const rel_id = res.data.rel_id

    showToast('领取成功', 'success').then(() => {
      navTo('pages/card/got/got', {rel_id: rel_id}, true)
    })
  })
}

onLoad(query => {
  if (query.q) {
    const q = decodeURIComponent(query.q)

    const matches = q.match(/code=(.+)/)
    if (matches && matches.length >= 2) {
      code.value = matches[1]
    }
  } else if (query.code) {
    code.value = query.code
  }

  getCardInfo()
})

function getCardInfo() {
  cardInfo(code.value).then(res => {
    const {
      data
    } = res
    Object.assign(info.value, data)

    if (info.value.airlines.length > 0) {
      active.value = info.value.airlines[0].code
    }

    if (info.value.get_by_my) {
      navTo('pages/card/got/got', {rel_id: info.value.rel_id})
    }
  })
}
</script>

<style lang="scss" scoped>
@import 'card.scss';
</style>