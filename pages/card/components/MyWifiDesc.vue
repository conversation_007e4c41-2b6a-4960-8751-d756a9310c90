<script setup>

const props = defineProps({
  scope: {
    type: String,
    default: ''
  },
  desc: {
    type: Array,
    default: () => []
  }
})

</script>

<template>
  <view class="desc-container">
    <view class="scope">{{ scope }}</view>
    <view class="data">
      <view class="desc">
        <view class="title">使用说明：</view>
        <view class="c">
          <view v-for="(item, index) in desc" :key="index">{{ item }}</view>
        </view>
      </view>
      <view class="right">
        <image mode="widthFix" show-menu-by-longpress
          src="https://rp.yjsoft.com.cn/yiban/static/resources/h5/gongzhonghao-qr.png" />
        <view>
          <text>长按识别二维码</text>
          <text>查看详细使用流程</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@use "../../../styles/mix";

.desc-container {
  flex: 1;
  background: linear-gradient(#FBD35A 0%, #F6F9DC 100%);
  padding-top: 28rpx;
}

.scope {
  padding: 16rpx 0;
  font-size: 20rpx;
  background: #F6F9DC;

  @include mix.center();
}

.data {
  display: flex;
  padding: 60rpx 44rpx 0 44rpx;
  justify-content: space-between;
  gap: 12rpx;

  .right {
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    image {
      width: 200rpx;
    }

    view {
      font-size: 20rpx;
      display: flex;
      flex-direction: column;
    }
  }

  .desc {
    flex: 1;
    font-size: 19rpx;
    background: url('https://rp.yjsoft.com.cn/yiban/static/wifi/feiji.png') no-repeat right;
    background-size: contain;
    padding-left: 38rpx;
    color: #022BAB;

    .title {
      margin-bottom: 10rpx;
    }

    .c {
      display: flex;
      gap: 10rpx;
      flex-direction: column;

      >view {
        @include mix.center();
        justify-content: flex-start;
        gap: 10rpx;

        &:before {
          display: inline-block;
          content: ' ';
          width: 10rpx;
          height: 10rpx;
          background: black;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
