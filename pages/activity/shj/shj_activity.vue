<template>
  <YjNavBar :custom-style="{
    background: 'transparent'
  }" arrow-color="#fff" @not-found="navTo('pages/index/index')" />
  <view class="container">
    <!-- 背景图片区域 -->
    <view class="background-area">
      <!-- 顶部背景图片 -->
      <image class="top-image" mode="widthFix"
        src="https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/top.jpeg">
      </image>

      <!-- 中间渐变背景区域 -->
      <view class="gradient-area">
      </view>

      <!-- 右下角贴边视图 -->
      <view class="bottom-right-view-wrap">
        <FloatButton :buttons="[{ key: 'self', text: '自提', angle: 200 }, { key: 'delivery', text: '配送', angle: 270 }]"
          :show="floatButtonShow">
          <template #btn="{ item }">
            <view class="fbtn" @tap="onDelivery(item)">
              <text>{{ item.text }}</text>
            </view>
          </template>
          <view :class="{ 'can-claim': activityStatus === 'can-claim', 'claimed': activityStatus === 'claimed' }"
            class="bottom-right-view" @tap="handleBottomRightTap">
            <!-- 宝箱图片 -->
            <image
              :src="activityStatus === 'claimed' ? 'https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/baohe-linqu.png' : 'https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/baohe-jindu.png'"
              class="treasure-box" mode="aspectFit"></image>

            <!-- 宝箱上层的进度描述 -->
            <view class="status-description">
              <view v-if="activityStatus === 'in-progress'" class="status-text">
                {{ flippedCount }}/{{ cards.length }}
              </view>
              <view v-else-if="activityStatus === 'can-claim'" class="status-text claim-text">
                点击领取
              </view>
              <view v-else-if="activityStatus === 'claimed'" class="status-text claimed-text">
                已领取
              </view>
            </view>

            <!-- 宝箱底部描述 -->
            <view v-if="activityStatus !== 'claimed'" class="bottom-description">
              <view v-if="activityStatus === 'in-progress'" class="bottom-text">
                再点亮{{ cards.length - flippedCount }}个秘境就可以打开宝箱
              </view>
              <view v-else-if="activityStatus === 'can-claim'" class="bottom-text">
                您已点亮所有秘境可点击领取宝物
              </view>
            </view>
          </view>
        </FloatButton>
      </view>

      <!-- 底部背景图片 -->
      <image class="bottom-image" mode="widthFix"
        src="https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/bg-1.jpg">
      </image>

    </view>

    <!-- 新增的视图层 -->
    <view class="overlay-layer">
      <!-- 卡片区域 -->
      <view class="cards-container">
        <view class="cards-row">
          <view v-for="(card, index) in cards" :key="index" :class="{ 'active': currentIndex === index }"
            class="card-wrapper" @tap="handleCardTap(index)">

            <FlipCard v-model="cardFlipStates[index]" :clickable="false" :customStyle="{
              width: currentIndex === index ? '200rpx' : '165rpx',
              transition: 'all 0.3s ease'
            }" :duration="500" :height="currentIndex === index ? '317rpx' : '272rpx'" animationType="simple">
              <!-- 未翻开的卡片正面 -->
              <template #front>
                <view class="card-front">
                  <image class="card-bg" mode="aspectFit"
                    src="https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/mj_card__unturn_bg.png">
                  </image>
                  <image :src="`https://rp.yjsoft.com.cn/yiban/static/shjactivity/mj_${index + 1}.png`" class="card-num"
                    mode="aspectFit"></image>
                </view>
              </template>
              <!-- 翻开后的卡片背面 -->
              <template #back>
                <view class="card-back">
                  <image :src="`${card.cover}`" class="card-content" mode="aspectFit">
                  </image>
                </view>
              </template>
            </FlipCard>

            <!-- 选中卡片下方的箭头指示器 -->
            <image v-if="currentIndex === index" class="arrow-indicator" mode="aspectFit"
              src="https://rp.yjsoft.com.cn/yiban/static/shjactivity/bottom_jt.png">
            </image>
          </view>
        </view>
      </view>

      <!-- 卡片描述区域 -->
      <view v-if="cards.length > 0" class="card-description-area">
        <view class="card-title">秘境{{ currentIndex + 1 }}：{{
          cards[currentIndex].user_task_state === 3 ? (cards[currentIndex]?.name
            || '?') : "?"
        }}
        </view>
        <view class="card-description">
          <view v-if="!cardFlipStates[currentIndex]">请到秘境门口扫码点亮秘境。</view>
          <view v-else>{{ cards[currentIndex]?.short_desc || '您已成功点亮此秘境！' }}</view>
        </view>

        <text v-if="!cardFlipStates[currentIndex]" class="action-text">如你想了解秘境请点击</text>
        <view class="card-actions" @tap="tapSearchScene(cards[currentIndex].scene.id)">
          <view class="action-button">
            <view class="action-icon">
              <image mode="aspectFit" src="https://rp.yjsoft.com.cn/yiban/static/shjactivity/icon-search.png"></image>
            </view>
            <text>探索秘境</text>
          </view>
        </view>
        <text v-if="!cardFlipStates[currentIndex]" class="action-text_margin">如你想导航过去请点击</text>
        <view v-if="!cardFlipStates[currentIndex]" class="card-actions" @tap="tapNation(cards[currentIndex])">
          <view class="action-button">
            <view class="action-icon">
              <image mode="aspectFit" src="https://rp.yjsoft.com.cn/yiban/static/shjactivity/icon-navigation.png">
              </image>
            </view>
            <text>秘境导航</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 打卡成功动画遮罩层 -->
    <view v-if="showSuccessAnimation" class="success-animation-mask">
      <view class="success-animation-container">
        <!-- 成功提示文字 -->
        <view class="success-text">打卡成功</view>

        <!-- 中央的FlipCard组件 -->
        <view class="success-card-wrapper">
          <FlipCard v-model="animationCardFlipped" :clickable="false" :customStyle="{
            width: '350rpx',
            transition: 'all 0.3s ease'
          }" :duration="1000" :rotations="2" :scratchDuration="1500" animationType="spin" enableScratchEffect
            height="560rpx" @scratchComplete="onAnimationComplete">
            <!-- 未翻开的卡片正面 -->
            <template #front>
              <view class="card-front">
                <image class="card-bg" mode="aspectFit"
                  src="https://rp.yjsoft.com.cn/yiban/static/activity/shanhaijing/mj_card__unturn_bg.png">
                </image>
                <image :src="`https://rp.yjsoft.com.cn/yiban/static/shjactivity/mj_${successCardIndex + 1}.png`"
                  class="card-num" mode="aspectFit">
                </image>
              </view>
            </template>
            <!-- 翻开后的卡片背面 -->
            <template #back>
              <view class="card-back">
                <image :src="cards[successCardIndex]?.cover" class="card-content" mode="aspectFit">
                </image>
              </view>
            </template>
          </FlipCard>
        </view>

        <!-- 添加关闭按钮 -->
        <!-- <view class="close-button" @tap="closeSuccessAnimation">
          <text>关闭</text>
        </view> -->
      </view>
    </view>
  </view>

  <MyInfoPopup2 v-if="showDeliveryPickup" @close="showDeliveryPickup = false">
    <view class="select-delivery">
      <template v-if="pickerForm.self_recv === Enable">
        <view class="header">
          <text>自提礼品请到：</text>
          <MyButton icon="icon-qiehuan1" size="small" type="primary" @tap="pickerForm.self_recv = Disable">
            快递到家
          </MyButton>
        </view>
        <view class="content">
          <view class="address-name">笃合书局</view>
          <view class="address-location">{{ rewardPickupAddr }}</view>
          <view class="delivery-footer">
            <MyButton type="primary" @tap="onSubmitGetReward">提交</MyButton>
          </view>

        </view>
      </template>
      <template v-else>
        <view class="header">
          <text>快递到家：</text>
          <MyButton icon="icon-qiehuan1" size="small" type="primary" @tap="pickerForm.self_recv = Enable">
            自提
          </MyButton>
        </view>
        <view class="content">
          <uni-forms ref="pickerFormRef" :label-width="90" :model="pickerForm" :rules="pickerFormRules">
            <uni-forms-item label="城市：" name="zone_id">
              <MyZoneSelector v-model="pickerForm.zone_id" />
            </uni-forms-item>
            <uni-forms-item label="详细地址：" name="recv_addr">
              <MyInput v-model="pickerForm.recv_addr" placeholder="请输入详细地址" />
            </uni-forms-item>
            <uni-forms-item label="收件人：" name="receiver">
              <MyInput v-model="pickerForm.receiver" placeholder="请输入收件人姓名" />
            </uni-forms-item>
            <uni-forms-item label="联系电话：" name="recv_phone">
              <MyInput v-model="pickerForm.recv_phone" placeholder="请输入收件人电话号码" />
            </uni-forms-item>
          </uni-forms>
          <view class="delivery-footer">
            <MyButton type="primary" @tap="onSubmitGetReward">提交</MyButton>
          </view>
        </view>
      </template>
    </view>

  </MyInfoPopup2>

</template>

<script setup>
import { onLoad, onShow } from '@dcloudio/uni-app';
import { computed, ref, watch } from 'vue';
import FlipCard from '@/components/FlipCard/FlipCard.vue';
import { deepToRaw, getParamsFromUrl, hideLoading, navTo, showLoading, showToast } from '@/utils';
import { clockInActivity, getTaskReward, shjActivityGetTaskInfo } from '@/api/activity';
import { UserTaskStatusDone, UserTaskStatusProcessing, UserTaskStatusWaitReward } from '@/utils/constmap_activity';
import FloatButton from "@/pages/activity/components/FloatButton.vue";
import MyInfoPopup2 from "@/components/MyInfoPopup2/MyInfoPopup2.vue";
import MyButton from "@/components/MyButton/MyButton.vue";
import UniForms from "@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue";
import UniFormsItem from "@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue";
import MyZoneSelector from "@/components/MyZoneSelector/MyZoneSelector.vue";
import MyInput from "@/components/MyInput/MyInput.vue";
import { Disable, Enable } from "@/utils/constmap";
import { useGlobalStore } from "@/store/global";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
// 卡片数据
const cards = ref([]);

// 当前选中的卡片索引
const currentIndex = ref(0);

// 活动状态：'in-progress'(进行中), 'can-claim'(可领取), 'claimed'(已领取)
const activityStatus = ref('in-progress');

const params = ref({});

// 计算已翻开的卡片数量
const flippedCount = computed(() => {
  // 使用cardFlipStates数组来计算已翻开的卡片数量
  return cardFlipStates.value.filter(isFlipped => isFlipped).length;
});

// 创建布尔值数组，用于控制卡片翻转状态
const cardFlipStates = ref([]);

// 打卡成功动画相关状态
const showSuccessAnimation = ref(false); // 控制成功动画遮罩层的显示
const successCardIndex = ref(0); // 存储当前成功卡片的索引
const animationCardFlipped = ref(false); // 控制动画卡片的翻转状态
const floatButtonShow = ref(false) //配送自提浮动按钮
const showDeliveryPickup = ref(false)
const userSelectDeliveryInfo = ref({})
const rewardPickupAddr = ref('')
const clockIning = ref(false) //是否正在打卡中
const globalStore = useGlobalStore()
const pickerForm = ref({
  self_recv: Disable,
  receiver: '',
  recv_phone: '',
  recv_addr: '',
  order_id: '',
  zone_id: '',
})
const pickerFormRef = ref(null)
const pickerFormRules = ref({
  zone_id: {
    rules: [
      {
        required: true,
        errorMessage: '请选择城市',
      }
    ]
  },
  receiver: {
    rules: [
      {
        required: true,
        errorMessage: '请输入收件人姓名',
      }
    ]
  },
  recv_phone: {
    rules: [
      {
        required: true,
        errorMessage: '请输入收件人电话号码',
      }
    ]
  },
  recv_addr: {
    rules: [
      {
        required: true,
        errorMessage: '请输入详细地址',
      }
    ]
  }
})

// 监听cards变化，初始化cardFlipStates
watch(cards, (newCards) => {
  // 根据card.user_task_state初始化cardFlipStates
  const newStates = newCards.map(card => {
    const isFlipped = card.user_task_state === UserTaskStatusDone;
    return isFlipped;
  });
  cardFlipStates.value = newStates;
}, { immediate: true });

// 监听cardFlipStates变化，更新card.user_task_state和card.isFlipped
watch(cardFlipStates, (newStates) => {
  cards.value.forEach((card, index) => {
    if (index < newStates.length) {
      // 根据布尔值设置对应的状态值：3(已完成)或1(进行中)
      const oldState = card.user_task_state;
      const newState = newStates[index] ? UserTaskStatusDone : UserTaskStatusProcessing;
      card.user_task_state = newState;
      // 同时更新isFlipped属性，用于UI显示
      card.isFlipped = newStates[index];
    }
  });
}, { deep: true });

// 监听已翻开的卡片数量，当全部翻开时更新状态
watch(flippedCount, (newCount) => {
  if (newCount === cards.value.length && activityStatus.value === 'in-progress') {
    activityStatus.value = 'can-claim';
  }
});

function onDelivery(item) {
  userSelectDeliveryInfo.value = item
  floatButtonShow.value = !floatButtonShow.value
  showDeliveryPickup.value = !showDeliveryPickup.value

  pickerForm.value.self_recv = item.key === 'self' ? Enable : Disable
}

function onSubmitGetReward() {
  console.log(pickerForm.value)
  if (pickerForm.value.self_recv === Disable) {
    pickerFormRef.value.validate().then(res => {
      const data = deepToRaw(pickerForm.value)
      data.zone_id = data.zone_id.split('-')
      data.recv_zone_id = data.zone_id[data.zone_id.length - 1]

      delete data.zone_id

      getTaskReward(data).then(() => {
        showToast('填写完成')
        getDetail()
        showDeliveryPickup.value = !showDeliveryPickup.value
      })
    })
  } else {
    getTaskReward({
      order_id: pickerForm.value.order_id,
      self_recv: Enable,
    }).then(() => {
      showToast('填写完成')
      getDetail()
      showDeliveryPickup.value = !showDeliveryPickup.value
    })
  }


}

//查看详情
function tapSearchScene(id) {
  navTo('pages/scenic/scenic', { id: id });
}

//调起导航
function tapNation(data) {
  uni.openLocation({
    latitude: data.scene.lat,
    longitude: data.scene.lng,
    name: data.name,
  })
}

// 处理卡片点击事件
const handleCardTap = (index) => {

  // 更新当前选中的卡片索引
  currentIndex.value = index;

};

// 处理右下角贴边视图点击事件
const handleBottomRightTap = () => {
  if (activityStatus.value === 'can-claim') {
    // 如果状态是可领取，则变为已领取
    // activityStatus.value = 'claimed';
    floatButtonShow.value = !floatButtonShow.value
  } else if (activityStatus.value === 'in-progress') {
    // 如果状态是进行中，提示用户继续翻卡
    showToast(`请继续翻开${cards.value.length - flippedCount.value}个秘境`);
  }
};

// 处理动画完成后的回调函数
const onAnimationComplete = () => {
  // 等待1秒后自动关闭遮罩层
  setTimeout(() => {
    showSuccessAnimation.value = false;
    animationCardFlipped.value = false; // 重置动画卡片的翻转状态
    // 重新调用详情接口刷新数据
    getDetail();
  }, 1000);
};

// 关闭成功动画遮罩层
const closeSuccessAnimation = () => {
  showSuccessAnimation.value = false;
  animationCardFlipped.value = false; // 重置动画卡片的翻转状态
};

// 监听currentIndex变化，确保对应的卡片被翻转
watch(currentIndex, (newIndex, oldIndex) => {
  // 不再重置所有卡片的翻转状态，允许多张卡片同时翻开
});

function getDetail() {
  if (!params.value.tuan_id) return;
  shjActivityGetTaskInfo(params.value).then(({ data: { task, reward, reward_pickup_addr, order_id } }) => {
    // Object.assign(cards.value, task.children);
    cards.value = task.children;
    rewardPickupAddr.value = reward_pickup_addr
    activityStatus.value = task.user_task_state === UserTaskStatusDone ? 'claimed' :
      task.user_task_state === UserTaskStatusWaitReward ?
        'can-claim' :
        'in-progress'
    // 检查任务状态，如果是进行中状态则调用打卡接口
    if (task.children && task.children.length > 0) {
      for (let i = 0; i < task.children.length; i++) {
        const child = task.children[i];
        if (child.scene?.id == params.value.scene_id) {
          // 设置currentIndex为当前循环的索引
          currentIndex.value = i;
          if (child.user_task_state === UserTaskStatusProcessing) {
            clockIn();
          }
          break;
        }
      }
      pickerForm.value.order_id = order_id
    } else {
      console.log('没有任务子项数据');
    }
  }).catch(data => {
    if (data.code === 900020) {
      //判断data的code为 900020时,订单不存在,需要跳转到该景点的详情里面
      navTo('pages/tuan/detail', { id: params.value.tuan_id }, true)
    }
  })
}

//进入该页面先调用打卡接口
async function clockIn() {
  if (!params.value.tuan_id) {
    return;
  }
  console.log('==============clockin', Math.random())
  if (clockIning.value) {
    return
  }
  clockIning.value = true
  showLoading('获取位置中...')
  try {
    let location = await globalStore.getLocation(true)
    hideLoading()
    await clockInActivity({
      ...params.value,
      loc_lat: location.lat,
      loc_lng: location.lng,
    })
    // 显示打卡成功动画
    if (currentIndex.value >= 0 && currentIndex.value < cards.value.length) {
      // 设置成功卡片索引
      successCardIndex.value = currentIndex.value;

      // 显示遮罩层
      showSuccessAnimation.value = true;

      // 延时触发卡片翻转动画
      setTimeout(() => {
        animationCardFlipped.value = true;
      }, 800); // 等待卡片出现动画完成后再触发翻转
    } else {
      console.log('当前卡片索引无效，不显示动画');
    }
    // 直接重新调用详情接口
    getDetail();
  } catch (err) {
    console.error('打卡失败:', err);
  } finally {
    clockIning.value = false
  }
}

onShow(() => {
  getDetail();
});

// 页面加载时的处理
onLoad((query) => {
  if (query.q) {
    let decodeUrl = decodeURIComponent(query.q);
    let qrcodeParams = getParamsFromUrl(decodeUrl);
    Object.assign(params.value, qrcodeParams);
  } else {
    params.value = query
  }
});
</script>

<style lang="scss" scoped>
@import "./shj_activity.scss";
</style>
