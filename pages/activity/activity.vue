<script setup>

import {onLoad, onShareAppMessage, onShow} from "@dcloudio/uni-app";
import {computed, getCurrentInstance, nextTick, ref, watch} from "vue";
import MyButton from "@/components/MyButton/MyButton.vue";
import {encodeObjToUrlParams, formatTime, navTo, richtext, showToast} from "@/utils";
import {
  CodeLaxin,
  TaskCondTypeAct,
  TaskCondTypePlan,
  TaskCondTypeSavePlan,
  TaskCondTypeShare,
  TaskCondTypeSharePlan,
  UserTaskStatusDone,
  UserTaskStatusProcessing,
  UserTaskStatusWaitReward
} from "@/utils/constmap_activity";
import {activityDetail, activityGetReward, activityJoin, activityPoster, taskLogs} from "@/api/activity";
import {useUserStore} from "@/store/user";
import MyActivityPopup from "@/components/MyActivityPopup/MyActivityPopup.vue";
import {Enable} from "@/utils/constmap";
import {cardGet, cardInfo} from "@/api/wifi";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import {trackEvent} from "@/utils/tracker";
import MyActivityDoTaskPopup from "@/components/MyActivityDoTaskPopup/MyActivityDoTaskPopup.vue";

const userStore = useUserStore()
const code = ref('')
const shareUid = ref('')
const activity = ref({
  title: '测试活动',
  has_join: false,
  task_id: 0,
  task: {
    children: []
  }
})
const integral = computed(() => {
  const {cond_acc_amount = 0} = activity.value.task.utask
  return cond_acc_amount
})
const percent = computed(() => {
  const percent = (integral.value / activity.value.task.cond_amount) * 100
  return percent > 100 ? 100 : percent
})
const rpx2px = uni.rpx2px
const records = ref([])
const recordTotal = ref(0)
const curTab = ref('reward')
const tutorial = ref({})
const showTutorial = computed(() => Object.keys(tutorial.value).length > 0)
const containerStyle = ref({
  paddingTop: rpx2px(644),
})
const qr = uni.createSelectorQuery(getCurrentInstance().proxy)
const tasksStyles = ref({})
const footerExpand = ref(false)
const isDragging = ref(false)
const touchY = ref(0) // 当前触摸点Y坐标
const lastTouchY = ref(0) // 上一次触摸点Y坐标
const currentHeight = ref(432)
const minHeight = 432
const maxHeight = 900
const showDragMask = ref(false)
const footerTop = ref(0) // footer元素的顶部位置
const rewardStatus = computed(() => {
  if (!activity.value.task.utask) {
    return UserTaskStatusProcessing
  }
  return activity.value.task.utask.state
})
const showRewardPop = ref(false)
const joinActReward = ref({})
const rewardPopInfo = ref({
  pic: '',
  airlines: [],
  selected: -1,
  code: '',
  airline: '',
})

watch(showTutorial, (val) => {
  containerStyle.value.overflow = val ? 'hidden' : 'auto'
})

function getDetail() {
  activityDetail({
    uuid: code.value,
    share_uid: shareUid.value,
  }).then(async ({data}) => {
    Object.assign(activity.value, data)

    uni.setNavigationBarTitle({
      title: activity.value.title
    })
    if (activity.value.has_join) {
      await getRecords()
    }
    nextTick(initTaskStyles)
  })
}

function initTaskStyles() {
  Promise.all([
    new Promise(r => qr.select('#footer').boundingClientRect(r).exec()),
  ]).then(([infoFooter]) => {
    Object.assign(tasksStyles.value, {
      marginBottom: (infoFooter.height + 20) + 'px',
    })

    // 记录footer元素的顶部位置，用于后续事件处理
    footerTop.value = infoFooter.top
  })
}

async function doGetReward() {
  const {code, selected, airlines} = rewardPopInfo.value
  if (selected >= airlines.length || selected < 0) {
    showToast('请选择航司')
    return
  }
  await cardGet({
    code,
    airline: airlines[selected].code,
  })
  await showToast('领取成功')
  navTo('pages/mine/wifis/wifis', undefined, true)
}

async function doReward() {
  if (rewardStatus.value != UserTaskStatusWaitReward) {
    return
  }
  const {data: {reward_code, red_pack_pic}} = await activityGetReward(code.value, activity.value.task.id)
  const {data: info} = await cardInfo(reward_code)
  Object.assign(rewardPopInfo.value, {
    pic: red_pack_pic,
    airlines: info.airlines,
    code: reward_code,
  })
  showRewardPop.value = true
}

onShareAppMessage(async (res) => {
  const params = {
    uuid: code.value,
    user_id: userStore.userInfo.user_id || '',
  }
  const {data: {url}} = await activityPoster(params)

  return {
    path: '/pages/activity/activity?' + encodeObjToUrlParams({
      code: code.value,
      share_uid: userStore.userInfo.user_id || '',
    }),
    title: activity.value.title,
    imageUrl: url
  }
})

onShow(() => {
  setTimeout(getDetail, 100)
})

onLoad((query) => {
  code.value = query?.code || CodeLaxin
  shareUid.value = query?.share_uid || ''
})

function getRecords() {
  return taskLogs(activity.value.task_id).then(({data}) => {
    const {list, total} = data
    records.value = list
    recordTotal.value = total
  })
}

function onJoin() {
  trackEvent('click_join_activity', {code: code.value})

  if (!activity.value.has_join) {
    activityJoin({uuid: code.value}).then(async ({data}) => {
      getDetail()

      //#ifdef MP
      if (activity.value.subs_tpl_ids.length > 0) {
        await new Promise(resolve => {
          uni.requestSubscribeMessage({
            tmplIds: activity.value.subs_tpl_ids,
            fail(e) {
              console.log('=========requestSubscribeMessage error', e)
            },
            complete: resolve,
          })
        })
      }
      //#endif
      joinActReward.value = {
        name: data.name,
        reward_amount: data.reward_amount,
      }
    })
  }
}

function onNavBarLoad(data) {
  containerStyle.value.paddingTop += data.height
}

function doTask() {
  const {cond_type} = tutorial.value
  if (cond_type == TaskCondTypeSharePlan || cond_type == TaskCondTypeSavePlan) {
    navTo('pages/index/index', {
      expand_history: Enable,
    })
  } else if (cond_type == TaskCondTypePlan) {
    navTo('pages/chat/chat')
  } else if (cond_type == TaskCondTypeAct) {
    onJoin()
  } else if (cond_type == TaskCondTypeShare) {
    showToast('点击右上角分享')
  }
}

function doFooterExpand() {
  footerExpand.value = !footerExpand.value
  currentHeight.value = footerExpand.value ? maxHeight : minHeight
}

function onFooterTouchStart(event) {
  // 阻止事件冒泡，避免触发其他元素的点击事件
  event.stopPropagation()

  // 先显示遮罩层，再设置拖动状态
  showDragMask.value = true
  isDragging.value = true

  // 记录初始触摸位置
  touchY.value = event.touches[0].clientY
  lastTouchY.value = touchY.value

  // 禁用底层视图的滑动
  containerStyle.value.overflow = 'hidden'

  // 确保在footer展开时也能正常拖动
  if (footerExpand.value) {
    // 如果footer已经展开，则强制设置拖动状态
    isDragging.value = true
  }
}

function onFooterTouchMove(event) {
  if (!isDragging.value) return

  // 阻止默认滚动行为
  event.preventDefault()
  // 阻止事件冒泡
  event.stopPropagation()

  // 获取当前触摸位置
  touchY.value = event.touches[0].clientY

  // 计算与上一次位置的差异，而不是与起始位置的差异
  // 向上拖动为负值，向下拖动为正值
  const deltaY = lastTouchY.value - touchY.value

  // 直接增加或减少当前高度，快速响应当前的移动
  let newHeight = currentHeight.value + deltaY

  // 限制在最小和最大高度之间
  if (newHeight < minHeight) newHeight = minHeight
  if (newHeight > maxHeight) newHeight = maxHeight

  // 更新当前高度
  currentHeight.value = newHeight

  // 更新上一次的触摸位置为当前位置，准备下一次移动
  lastTouchY.value = touchY.value
}

// 处理footer内容区域的触摸事件，阻止事件冒泡和默认行为
function onFooterContentTouch(event) {
  // 检查事件目标是否是.hand元素或其子元素
  let target = event.target;
  while (target) {
    if (target.className && typeof target.className === 'string' && target.className.includes('hand')) {
      // 如果是.hand元素或其子元素，则不阻止事件
      return;
    }
    target = target.parentNode;
  }

  // 如果不是.hand元素或其子元素，则阻止事件冒泡和默认行为，防止底层视图滚动
  event.stopPropagation()
  event.preventDefault()
  return false
}

function onFooterTouchEnd() {
  isDragging.value = false

  // 判断最终位置，如果拖动超过一半则展开，否则收起
  const halfwayPoint = (maxHeight + minHeight) / 2
  footerExpand.value = currentHeight.value > halfwayPoint
  currentHeight.value = footerExpand.value ? maxHeight : minHeight

  // 根据footer展开状态设置底层容器的滚动行为和遮罩层
  if (footerExpand.value) {
    // footer展开时，禁用底层容器的滚动并显示遮罩层
    containerStyle.value.overflow = 'hidden'
    showDragMask.value = true
  } else {
    // footer收起时，恢复底层容器的滚动并隐藏遮罩层
    showDragMask.value = false
    if (!showTutorial.value) {
      containerStyle.value.overflow = 'auto'
    }
  }

  // 添加短暂延迟，确保所有状态都已清理
  setTimeout(() => {
    if (!isDragging.value && !footerExpand.value) {
      // 再次确认状态已清理，且footer未展开
      containerStyle.value.overflow = 'auto'
    }
  }, 50)
}

// 处理容器区域的触摸事件，判断是否应该阻止滚动
// 仅在触摸位置在footer上方但不在footer内部时阻止滚动
function onContainerTouch(event) {
  // 确保在滚动过程中也隐藏滚动条
  // 这里不需要额外的代码，因为我们已经在CSS中处理了

  if (isDragging.value) {
    // 已经处于拖动状态，阻止所有事件
    event.preventDefault()
    event.stopPropagation()
    return false
  }

  // 判断触摸位置是否在footer上方
  const touchY = event.touches[0].clientY
  if (touchY >= footerTop.value) {
    // 触摸在footer上方，阻止底层列表的滚动
    event.preventDefault()
    event.stopPropagation()
    return false
  }
}

function getTutorialBtnText(cond_type) {
  if (cond_type == TaskCondTypeSharePlan) {
    return '去分享'
  } else if (cond_type == TaskCondTypeSavePlan) {
    return '去收藏'
  } else if (cond_type == TaskCondTypePlan) {
    return '去生成'
  } else if (cond_type == TaskCondTypeAct) {
    return '去参加'
  } else if (cond_type == TaskCondTypeShare) {
    return '去分享'
  }
}

watch(() => [userStore.isLogin, activity.value.task_id], (val) => {
  if (!val[0] || !val[1]) {
    return
  }

  // //登录成功后自动领取任务
  // if (!activity.value.has_join) {
  //   activityJoin({uuid: code.value}).then(({data}) => {
  //     activity.value.has_join = true
  //   })
  // }
})

// 在footer展开/收起时更新底层容器的滚动行为和遮罩层状态
watch(() => footerExpand.value, (newVal) => {
  if (newVal) {
    // footer展开时，禁用底层容器的滚动并显示遮罩层
    containerStyle.value.overflow = 'hidden'
    showDragMask.value = true
  } else {
    // footer收起时，恢复底层容器的滚动并隐藏遮罩层
    showDragMask.value = false
    if (!showTutorial.value && !isDragging.value) {
      containerStyle.value.overflow = 'auto'
    }
  }
})

</script>

<template>
  <YjNavBar :custom-style="{
    backgroundColor: 'none',
  }" arrow-color="#fff" custom-class="navbar" @load="onNavBarLoad"
            @not-found="() => navTo('pages/index/index')"/>
  <view :style="containerStyle" class="container" @touchmove.passive="onContainerTouch">
    <!-- 拖动遮罩层，防止底层交互 -->
    <view v-if="showDragMask" class="drag-mask" @touchmove.prevent @touchstart.stop @touchend.stop></view>

    <view class="top">
      <!--      <view class="intro">-->
      <!--        <view>登录 分享 收藏 完成行程规划</view>-->
      <!--        <view>都可以增加积分</view>-->
      <!--      </view>-->
      <!--      <view class="tips">积分满{{ activity.task.cond_amount }}领取机上WIFI兑换码（价值50元）</view>-->
    </view>
    <view id="tasks" :style="tasksStyles" class="tasks" @touchmove.passive="onContainerTouch">
      <view v-for="(item, index) in activity.task.children" :key="index" class="task-item">
        <view class="icon"/>
        <view class="content">
          <view class="left">
            <view class="name">
              {{ item.name }}
              <text>+{{ item.reward_amount }}积分</text>
              <view v-if="item?.utask?.state === UserTaskStatusDone" class="done">已领取</view>
            </view>
            <view class="short-desc">{{ item.short_desc }}</view>
          </view>
          <view v-if="activity.has_join" class="right">
            <my-button v-if="item.cond_type === 5" class="filled" @tap="navTo('pages/index/index')">去生成</my-button>
            <my-button v-else-if="!item.utask || item.utask.state !== UserTaskStatusDone" @tap="tutorial = item">
              看攻略
            </my-button>
          </view>
        </view>
      </view>
    </view>

    <view id="footer" :class="{
      'be-join': !activity.has_join,
      expand: footerExpand,
      'no-transition': isDragging
    }" :style="isDragging ? { height: currentHeight + 'rpx' } : {}" class="footer"
          @touchmove.stop.prevent="onFooterContentTouch">
      <template v-if="activity.has_join">
        <view class="hand"
              @touchstart.stop.prevent="onFooterTouchStart"
              @touchmove.stop.prevent="onFooterTouchMove"
              @touchend.stop="onFooterTouchEnd"
              @touchcancel.stop="onFooterTouchEnd">
          <view class="inner"/>
        </view>

        <view class="content" @touchmove.stop.prevent="onFooterContentTouch">
          <view class="tabs">
            <template
                v-for="item in [{ label: '积分进度', value: 'reward' }, { label: '积分规则', value: 'rule' }]"
                :key="item.value">
              <view :class="{
                active: curTab === item.value
              }" class="tab-item" @tap="curTab = curTab == 'reward' ? 'rule' : 'reward'">{{ item.label }}
              </view>
            </template>
          </view>
          <template v-if="curTab === 'reward'">
            <view class="progress-bar">
              <view class="a">
                <progress :border-radius="rpx2px(10)" :percent="percent" :stroke-width="rpx2px(20)"
                          active-color="#022BAB" background-color="#9BB4FF">
                  <view :style="{ left: percent + '%' }" class="percent img-bg">
                    <text>{{ percent }}</text>
                  </view>
                </progress>
                <view :class="{ shake: rewardStatus == UserTaskStatusWaitReward }" class="b img-bg" @tap="doReward">
                </view>
              </view>
            </view>
            <view v-if="[UserTaskStatusWaitReward, UserTaskStatusDone].includes(rewardStatus)" class="reward-btn">
              <my-button class="rbtn" @tap="doReward">
                {{ rewardStatus == UserTaskStatusDone ? '已领取' : '领取奖励' }}
              </my-button>
            </view>
            <view v-if="rewardStatus == UserTaskStatusProcessing" class="records">
              <view>
                <view class="title">领取记录</view>
                <view class="list">
                  <view v-for="(item, index) in records" :key="index" class="list-item">
                    <view class="name">{{ `${item.name}+${item.amount}` }}</view>
                    <view class="time">{{ formatTime(item.time, 'YYYY-M-D HH:mm') }}</view>
                  </view>
                </view>
              </view>
            </view>
          </template>
          <view v-else class="activity-rule">
            <rich-text :nodes="richtext(activity.rules)"/>
          </view>
        </view>
      </template>
      <view v-else class="btn-join" @tap="onJoin">参与活动</view>
    </view>

    <my-activity-popup v-if="showTutorial" class="tutorial-popup" type="bottom" @close="tutorial = {}">
      <view class="tutorial-content">
        <rich-text :nodes="richtext(tutorial.tutorial)"/>
      </view>
      <template #footer>
        <view class="tutorial-footer">
          <my-button @tap="doTask">
            {{ getTutorialBtnText(tutorial.cond_type) }}
          </my-button>
        </view>

      </template>
    </my-activity-popup>

    <my-activity-popup v-if="showRewardPop" class="reward-popup" type="bottom" @close="showRewardPop = false">
      <view class="reward-content">
        <image :src="rewardPopInfo.pic" class="pic" mode="widthFix"/>
        <view class="wrap">
          <radio-group @change="e => rewardPopInfo.selected = e.detail.value">
            <view class="label">请选择航司：</view>
            <view class="airlines">
              <view v-for="(item, i) in rewardPopInfo.airlines" :key="i" class="airline">
                <image :src="item.logo" class="logo"></image>
                <view class="name">{{ item.name }}</view>
                <radio :checked="rewardPopInfo.selected == i" :value="i"/>
              </view>
            </view>
          </radio-group>
        </view>
        <my-button class="do-btn" @tap="doGetReward">领取</my-button>
      </view>
      <template #footer></template>
    </my-activity-popup>

    <MyActivityDoTaskPopup :joinActReward="joinActReward"></MyActivityDoTaskPopup>
  </view>
</template>

<style lang="scss" scoped>
@import "activity";
</style>