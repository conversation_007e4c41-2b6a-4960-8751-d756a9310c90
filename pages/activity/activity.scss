@use "../../styles/mix";
@import "../../styles/define";

// 隐藏滚动条的混合样式
@mixin hide-scrollbar {
  // 隐藏滚动条 - WebKit浏览器（Chrome、Safari、新版Edge等）
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  // 隐藏滚动条 - Firefox
  scrollbar-width: none;

  // 隐藏滚动条 - IE和Edge
  -ms-overflow-style: none;

  // 防止滚动条在滚动过程中出现
  overflow-scrolling: touch;
  -webkit-overflow-scrolling: touch;
}

// 拖动遮罩层样式
.drag-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw; // 确保覆盖整个视口宽度
  height: 100vh; // 确保覆盖整个视口高度
  background-color: transparent; // 全透明背景
  z-index: 8; // 设置z-index低于footer的z-index(9)，但高于底层视图
  // 确保遮盖滚动条
  right: 0;
  bottom: 0;
}

:deep(.navbar) {
  z-index: 1000;
  background-image: url("https://rp.yjsoft.com.cn/yiban/static/activity/new/top.png?v=2");
  background-position: top center;
  background-size: cover;
}

.container {
  min-height: 100vh;
  overflow-y: auto;
  position: relative;
  background: #022BAB url("https://rp.yjsoft.com.cn/yiban/static/activity/new/top.png?v=2") no-repeat top center;
  background-size: contain;
  padding-top: 644rpx;
  @include hide-scrollbar;
}

.top {
  display: none;
  //background-image: url('https://rp.yjsoft.com.cn/nanmu/static/top-bg.jpg');
  height: 644rpx;
  background-size: contain;
  background-repeat: no-repeat;
  position: relative;

  .intro,
  .tips {
    position: absolute;
    bottom: 123rpx;
    left: 40%;
  }

  .tips {
    font-size: 20rpx;
    bottom: 54rpx;
  }
}

.tasks {
  padding: 28rpx 28rpx 0 28rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  overflow-y: auto;
  @include hide-scrollbar;

  .task-item {
    @include mix.center();
    justify-content: flex-start;
    background: #0E39BE;
    padding: 30rpx 20rpx;
    gap: 8rpx;
    border-radius: 20rpx;

    .icon {
      width: 40rpx;
      height: 40rpx;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url("https://rp.yjsoft.com.cn/yiban/static/activity/new/bonus-s.png");
    }

    .content {
      flex: 1;
      @include mix.center();
      gap: 2rpx;

      .left {
        flex: 1;

        .name,
        .short-desc {
          color: white;
        }

        .name {
          display: flex;
          gap: 8rpx;
          align-items: flex-end;

          .done {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            border: 4rpx solid #FBD35A;
            @include mix.center();
            font-size: 14rpx;
            transform: rotate(-45deg);
          }
        }

        .short-desc {
          font-size: 20rpx;
          font-weight: 400;
        }
      }

      :deep(.my-button) {
        padding: 12rpx 28rpx;
        border-radius: 32rpx;
        background: white;
        border-color: transparent;

        .my-button-content {
          color: #FBD35A;
        }
      }

      .filled :deep(.my-button),
      :deep(.filled.my-button) {
        background: #FBD35A;

        .my-button-content {
          color: white !important;
        }
      }
    }
  }
}

.footer {
  background: #FFFFFF;
  box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.5);
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  @include mix.center(column);
  justify-content: flex-start;
  padding: 60rpx 28rpx 0 28rpx;
  position: fixed;
  width: 100%;
  height: 432rpx;
  left: 0;
  bottom: 0;
  z-index: 9;
  transition: height .5s ease-in-out;

  // 拖动时禁用过渡效果，使拖动立即响应
  &.no-transition {
    transition: none;
    z-index: 100; // 拖动时提高z-index
  }

  &.expand {
    height: 900rpx;
  }

  .hand {
    position: absolute;
    top: 0;
    left: 0;
    padding: 20rpx 0 30rpx 0; // 增加上下内边距，扩大触摸区域
    width: 100%;
    @include mix.center(column);
    cursor: grab; // 在支持的平台上显示抓取光标

    .inner {
      background: #D8D8D8;
      border-radius: 4rpx;
      width: 232rpx;
      height: 8rpx;
    }

    &:active {
      cursor: grabbing; // 在支持的平台上显示抓取中光标
    }
  }

  .content {
    height: 300rpx;
    overflow: visible;
    touch-action: pan-y; // 只允许垂直滚动，不允许其他触摸操作
    @include hide-scrollbar;

    .tabs {
      @include mix.center();
      background: #F6F9DC;
      border-radius: 48rpx;

      .tab-item {
        background: #F6F9DC;
        padding: 28rpx 112rpx;
        color: #022BAB;
        border-radius: 48rpx;

        &:first-child {
          &.active {
            border-top-right-radius: 48rpx;
            border-bottom-right-radius: 48rpx;
          }
        }

        &.active {
          background: #FBD35A;
        }
      }
    }

    .progress-bar {
      margin: 24rpx 0 6rpx 0;
      padding-top: 62rpx;

      .a {
        @include mix.center();

        progress {
          flex: 1;
          position: relative;

          .percent {
            transform: translateX(-28rpx);
            left: 0;
            position: absolute;
            bottom: 20rpx;
            color: #022BAB;
            @include mix.center();
            background-image: url("https://rp.yjsoft.com.cn/yiban/static/activity/new/percent.png");
            width: 56rpx;
            height: 72rpx;

            text {
              font-weight: bold;
              margin-top: -8rpx;
            }
          }
        }

        .b {
          background-image: url("https://rp.yjsoft.com.cn/yiban/static/activity/new/lipin-2-2.png");
          width: 56rpx;
          height: 56rpx;

          &.shake {
            animation: shake-loop 2s infinite;
          }

          @keyframes shake-loop {

            0%,
            20% {
              animation-timing-function: ease-in-out;
              transform: rotate(0deg);
            }

            5% {
              transform: rotate(-15deg);
            }

            10% {
              transform: rotate(15deg);
            }

            15% {
              transform: rotate(-15deg);
            }

            20% {
              transform: rotate(-15deg);
            }

            20.01%,
            100% {
              transform: rotate(0deg);
            }
          }
        }
      }
    }

    .records {
      > view {
        position: relative;
        padding-top: 30rpx;
      }

      .title {
        border-radius: 20rpx;
        color: #999999;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: 10rpx;
        padding: 12rpx 42rpx;
        background: #EDEEF0;
        z-index: 999;
      }

      .list {
        background: #F7F7F9;
        padding: 66rpx 20rpx 20rpx 20rpx;
        border-radius: 16rpx;
        display: flex;
        flex-direction: column;
        color: #9BB4FF;
        overflow-y: auto;
        @include hide-scrollbar;

        .list-item {
          gap: 6rpx;
          @include mix.center();
          justify-content: space-between;
        }
      }
    }
  }

  .activity-rule {
    padding: 28rpx 0;
    overflow-y: auto;
    @include hide-scrollbar;
  }

  &.be-join {
    height: auto;
    padding: 60rpx 28rpx;
    @include mix.center();

    .btn-join {
      background: #FBD35A;
      color: #022BAB;
      flex: 1;
      padding: 28rpx 290rpx;
      border-radius: 48rpx;
    }
  }

  .reward-btn {
    @include mix.center();

    .rbtn {
      :deep(.my-button) {
        padding: 0;
        @include mix.center();
        width: 256rpx;
        height: 64rpx;
        background: #FF4D4F;
        border-radius: 48rpx;
        border-width: 0;
        color: #FFFFFF;
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
}

.tutorial-popup {
  //:deep(.my-activity-popup-content) {
  //  background: white;
  //}

  .tutorial-content {
    background: #EDEEF0;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 20rpx;
    max-height: 50vh;
    overflow-y: auto;
    @include hide-scrollbar;
  }

  .tutorial-footer {
    background: white;
    padding: 54rpx 28rpx 60rpx 28rpx;

    :deep(.my-button) {
      background: #FBD35A;
    }
  }


}

.reward-content {
  @include mix.center(column);
  width: 100%;
  background: white;
  border-radius: 20rpx 20rpx 0rpx 0rpx;

  .pic {
    width: 100%;
  }

  .wrap {
    width: 100%;

    .label {
      margin: 20rpx 0 0 48rpx;
      font-size: 28rpx;
      color: $black-color;
    }

    .airlines {
      @include mix.center();
      justify-content: space-around;

      .airline {
        @include mix.center(column);
        gap: 20rpx;

        .logo {
          height: 68rpx;
          width: 68rpx;
        }

        .name {
          color: $black-color;
          font-size: 28rpx;
        }
      }
    }
  }

  .do-btn {
    margin: 104rpx 0 60rpx 0;

    :deep(.my-button) {
      width: 694rpx;
      height: 96rpx;
      background: #FBD35A;
      border-radius: 48rpx;
      border-width: 0;
      @include mix.center();
      color: #022BAB;
      font-size: 28rpx;
      font-weight: bold;
    }
  }
}
