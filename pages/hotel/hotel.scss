@import '../../styles/_mix.scss';
@import '../../styles/_define.scss';

.container {
  background: #f7f7f9;
}

.hotel-banner {
  height: 420rpx;

  swiper {
    width: 100%;
    height: 100%;

    swiper-item {
      width: 100%;
      height: 100%;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.hotel-cont {
  margin: 0rpx auto;
  background: #ffffff;
  border-radius: $border-radius-middle $border-radius-middle 0rpx 0rpx;
  box-shadow: 0rpx 3rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
  position: relative;
  top: -50rpx;
  padding: $padding-page;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: $padding-small;
}

.title {
  // margin-bottom: 10rpx;
  @include center();
  justify-content: space-between;
  font-size: $h3-v2;
  font-weight: bold;

  .left {
    display: flex;
    align-items: baseline;
    gap: $padding-small;

    .cont-title {
      font-family: PingFang SC, PingFang SC-Bold;
      @include ellipse();
      max-width: 450rpx;
    }

    .star-text {
      color: #faca14;
      margin-right: $padding-mini;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .score {
    color: $plan-color-1;
  }
}

.line-2 {
  @include center();
  justify-content: flex-start;
  gap: 34rpx;

  .iconfont {
    color: black;
    margin-right: $padding-small;
  }

  .good-rate {
    .value {
      color: $plan-color-2;
    }
  }

  .cost {
    .value {
      color: $plan-color-1;
    }
  }
}

.tel-locaton {
  display: flex;
  justify-content: flex-end;
  gap: $padding-v2;
  border-bottom: 1rpx solid $border-color-v2;
  padding-bottom: $padding-page;

  >view,
  .iconfont {
    @include center();
  }

  .iconfont {
    width: 41rpx;
    height: 41rpx;
    border-radius: 50%;
    background: $gray-color-v2;
    color: $font-color-gray;
    font-size: $fontsize-small;
  }

  >view {
    gap: $padding-small;
  }
}

.cont-introduce {
  margin-bottom: 51rpx;
}

.cont-1 {
  margin-bottom: 30rpx;
}

.rooms {
  padding: 0 $padding-page $padding-page $padding-page;
  display: flex;
  flex-direction: column;
  gap: $padding-page;

  .no-more {
    @include center(column);
    color: $font-color-gray;
  }

  .room-item {
    border-radius: $border-radius-middle;
    background: white;
    min-height: 187rpx;

    .sale-price {
      color: $price-color;
    }

    .room {
      display: flex;
      gap: $padding-v2;
      padding: $padding-v2;

      &.open {
        border-bottom: 1rpx solid $border-color-v2;
      }

      .left {
        width: 140rpx;
        height: 140rpx;
        @include center();

        image {
          max-width: 100%;
          max-height: 100%;
          border-radius: $border-radius;
        }
      }

      .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .room-name {
          font-weight: 500;
          @include center();
          justify-content: space-between;

          .iconfont {
            @include center(column);
            color: $font-color-gray;
            font-size: $fontsize-small;
            width: 34rpx;
            height: 34rpx;
            border-radius: 50%;
            border: 2rpx solid $border-color-v2;
          }
        }

        .bed_desc {
          margin-top: $padding-small;
          font-size: $fontsize-small;
          font-weight: normal;
          @include ellipse(2);
        }
      }

      .price-area {
        @include center();
        justify-content: space-between;

        .market-price {
          text-decoration: line-through;
          color: $info-color;
        }

        .sale-price {
          display: flex;
          align-items: flex-end;

          text {
            font-size: $fontsize-small - 4;
          }
        }
      }
    }

    .policy {
      display: none;

      &.show {
        display: block;
      }

      .policy-item {
        display: flex;
        flex-direction: column;
        border-bottom: 1rpx dashed $border-color-v2;
        padding: $padding-v2;
        gap: $padding-small;

        &:last-child {
          border-bottom: none;
        }

        .policy-name {
          display: flex;
          gap: 17rpx;

          .breakfast {
            max-width: 73rpx;
            font-weight: 500;
          }

          .cancel-rule {
            flex: 1;
            border-left: 1rpx solid black;
            padding-left: 17rpx;
            @include ellipse();
          }
        }

        .actions {

          &,
          .left,
          .right {
            display: flex;
          }

          align-items: center;
          justify-content: space-between;

          .left {
            gap: $padding-small;
            flex-direction: column;

            .lock {
              color: $font-color-gray;
            }
          }

          .right {
            gap: $padding-small;
            align-items: flex-end;

            .sale-price {
              font-size: $h3-v2;
            }

            .btn {
              padding: $padding-small $padding-v2;
              text-align: center;
              font-size: $h2-v2;
            }
          }
        }
      }
    }
  }
}

.date-dialog {
  :deep(.popup-content) {
    padding-bottom: $padding-big !important;
  }
}

.peoples-dialog {
  .notice {
    background: #e7f4ff;
    display: flex;
    gap: $padding-v2;
    padding: $padding-v2 $padding-page;

    .iconfont {
      color: $plan-color-3;
    }
  }

  :deep(.popup-content) {
    padding: 0 !important;
  }

  .list-item {
    @include center();
    justify-content: space-between;
    border-bottom: 1rpx solid $border-color-v2;
    padding: $padding-page $padding-v2;

    &:last-child {
      border-bottom: none;
    }
  }
}

.options {
  @include center();
  justify-content: space-between;
  align-items: flex-end;

  .checkin {
    padding-right: $padding-middle;
    border-right: 1rpx solid $border-color-v2;
  }

  .peoples {

    .h,
    .iconfont {
      color: $font-color-gray;
    }

    >view {
      &:last-child {
        @include center();
        gap: $padding-middle;

        .iconfont {
          margin-left: $padding-small;
        }
      }
    }
  }
}

.hotel-detail {
  padding-bottom: 10rpx;
}

rich-text {
  width: 100%;
  overflow: hidden;

  img {
    width: 100%;
    margin: 0;
    padding: 0;
  }
}