<template>
	<view class="container">
		<view class="list">
			<view class="list-item" v-for="(item, index) in list" :key="index">
				<view class="left" @tap="navTo('pages/card/got/got', { rel_id: item.rel_id })">
					<image mode="widthFix" :src="item.logo" />
					<view>
						<view class="name">{{ item.airline_product_name }}</view>
						<view>空地互联上网卡</view>
						<view>有效期至{{ formatTime(item.expire_time, 'YYYY年MM月DD日') }}</view>
					</view>
				</view>
				<view class="right">
					<view>
						<view class="state">{{ item.got_state_text }}</view>
						<text @tap="onCopyItem(item)" class="iconfont icon-fuzhi"></text>
					</view>

					<view class="code">上网码<text>{{ item.code }}</text></view>
				</view>
			</view>
		</view>

		<uni-load-more :status="status"></uni-load-more>
	</view>
</template>

<script setup>
import {
	ref
} from 'vue'

import {
	userWifis
} from '@/api/wifi';
import {
	onLoad,
	onShow
} from '@dcloudio/uni-app'
import {
	formatTime,
	navTo
} from '@/utils';

const list = ref([])
const status = ref('loading')

function getList() {
	userWifis().then(({
		data
	}) => {
		list.value = data.list
		status.value = 'noMore'
	})
}

function onCopyItem(item) {
	uni.setClipboardData({
		data: item.code,
		success: () => {
			uni.showToast({
				title: '复制成功'
			})
		}
	})
}

	onLoad(() => {
		getList()
	})
</script>

<style lang="scss" scoped>
@import 'wifi.scss';
</style>