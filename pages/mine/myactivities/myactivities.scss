@import "../../../styles/_define.scss";
@import "../../../styles/_mix.scss";

// 活动列表项样式
.activity-item {
  background: white;
  border-radius: 20rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.08);

  // 上半部分：图片区域
  .image-section {
    position: relative;
    height: 200rpx;

    .activity-image {
      width: 100%;
      height: 100%;
    }

    // 活动状态标签
    .status-tag {
      position: absolute;
      top: 20rpx;
      left: 20rpx;
      width: 140rpx;
      height: auto;
      padding: 8rpx 16rpx;
      background: linear-gradient(90deg, #A0FFC7 0%, #E0F6A1 48%, #FED531 100%);
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      z-index: 10;

      .status-icon {
        width: 28rpx;
        height: 28rpx;
      }

      .status-text {
        font-size: 20rpx;
        color: #333333;
        line-height: 1;
      }

      // 已结束状态样式
      &.ended {
        background: #EDEEF0;

        .status-text {
          color: #999999;
        }
      }
    }
  }

  // 下半部分：信息区域
  .info-section {
    padding: 20rpx;
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    // 活动标题
    .activity-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      line-height: 1.4;
    }

    // 奖励信息区域
    .reward-info {
      background: #FACA14;
      border-radius: 20rpx;
      padding: 0 6rpx;
      display: flex;
      align-items: center;
      gap: 4rpx;
      align-self: flex-start;

      .reward-icon {
        width: 42rpx;
        height: 42rpx;
      }

      .reward-text {
        font-size: 24rpx;
        margin: 4rpx 24rpx 4rpx 0;
        color: #333333;
      }
    }

    // 底部信息区域
    .bottom-info {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      height: 44rpx;

      .time-info {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .time-icon {
          width: 24rpx;
          height: 24rpx;
        }

        .end-time {
          font-size: 20rpx;
          color: #999999;
          line-height: 1.2;
        }
      }

      .action-button {
        width: 148rpx;
        height: 52rpx;
        background: #1890FF;
        border-radius: 26rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .button-text {
          font-size: 24rpx;
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}