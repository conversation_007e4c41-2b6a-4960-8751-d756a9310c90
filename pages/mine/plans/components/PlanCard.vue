<template>
  <view class="plan-card-wrapper">
    <view :class="{ 'has-pdf-status': data.pdf_state && data.pdf_state > 0 }" class="travel">
      <view class="header">
        <view v-if="data.created_at">{{ formatTime(data.created_at) }}</view>
        <view v-if="showRemove">
          <text class="iconfont icon-shanchu delete" @tap="onRemove"></text>
        </view>
      </view>

      <view @tap="onTap">
        <view class="from-to">
          <text>{{ data.from }}</text>
          <view>
            <image mode="scaleToFill" :src="getCdnUrl('/static/fly-to.png')"></image>
          </view>
          <text>{{ data.to }}</text>
        </view>
        <view class="summary">
          <view>
            <view>{{ data.days }}天</view>
            <view>时间</view>
          </view>
          <view>
            <view>{{ data.days - 1 }}夜</view>
            <view>住宿</view>
          </view>
          <view>
            <view>{{ data.scene_num }}个</view>
            <view>景点</view>
          </view>
          <view>
            <view>{{ data.prompt_options.transport }}</view>
            <view>出行</view>
          </view>
        </view>
      </view>
    </view>

    <!-- PDF 状态展示区域 -->
    <view v-if="data.pdf_state && data.pdf_state > 0" class="pdf-status">
      <view class="pdf-left">
        <image class="pdf-icon" mode="scaleToFill" :src="getCdnUrl('/static/plan/dl_pdf.png')"></image>
        <text class="pdf-label">下载</text>
      </view>

      <view class="pdf-center">
        <text :style="{ color: pdfStatusConfig.color }" class="pdf-status-text">
          {{ pdfStatusConfig.text }}
        </text>
      </view>

      <view class="pdf-right">
        <!-- 已完成状态：显示查看和下载按钮 -->
        <template v-if="data.pdf_state === 2">
          <view class="pdf-btn" @tap="onViewPdf(data)">
            <text class="iconfont icon-yulan1"></text>
          </view>
          <view class="pdf-btn" @tap="onDownloadPdf(data)">
            <text class="iconfont icon-24px"></text>
          </view>
        </template>

        <!-- 生成失败状态：显示查看原因按钮 -->
        <template v-if="data.pdf_state === 3">
          <MyButton size="small" type="primary" @tap="onViewFailReason">查看原因</MyButton>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { planRemove } from '@/api/user';
import { formatTime, navTo, showConfirm, showToast, getCdnUrl } from '@/utils';
import MyButton from '@/components/MyButton/MyButton.vue';

const props = defineProps({
  data: {
    type: Object,
    default: () => {
    }
  },
  showRemove: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['remove'])

// PDF 状态配置
const pdfStatusConfig = computed(() => {
  const statusMap = {
    1: { text: '生成中', color: '#1890FF' },
    2: { text: '已完成', color: '#52C41A' },
    3: { text: '生成失败', color: '#FF4D4F' }
  }
  return statusMap[props.data.pdf_state] || { text: '', color: '#000000' }
})

function onRemove() {
  showConfirm('删除行程', '您确定要删除该行程吗？').then(() => {
    planRemove(props.data.id).then(() => {
      showToast('删除成功').then(() => {
        emit('remove')
      })
    })
  })
}

function onTap() {
  navTo('pages/details/details', { plan_id: props.data.id })
}

// PDF 相关交互方法
function onViewPdf(item) {
  uni.previewImage({
    urls: [item.pdf_cover]
  })
}

function onDownloadPdf(item) {
  uni.downloadFile({
    url: item.pdf_url,
    success: ({ tempFilePath }) => {
      // #ifdef MP-WEIXIN
      const fileSystem = uni.getFileSystemManager();
      const fileName = `${item.subject}.pdf`
      const filePath = `${uni.env.USER_DATA_PATH}/${fileName}`

      fileSystem.saveFile({
        tempFilePath,
        filePath,
        success({ savedFilePath }) {
          showToast(`已保存到${fileName}`).then(() => {
            uni.openDocument({
              filePath: savedFilePath,
              fileType: 'pdf',
              showMenu: true,
            })
          })
        },
        fail(err) {
          showToast(`保存失败：${err.errMsg}`)
        }
      })
      // #endif

      // #ifdef H5
      showToast(`下载成功`)
      // #endif
    }
  })
}

function onViewFailReason() {
  // TODO: 实现查看失败原因逻辑
  console.log('查看失败原因', props.data.id)
}

</script>

<style lang="scss" scoped>
@import '../../../../styles/_define.scss';
@import '../../../../styles/_mix.scss';

.plan-card-wrapper {
  // 包装器样式
}

.travel {
  background: white;
  border-radius: $border-radius-middle;
  border: 1rpx solid $border-color-v2;
  padding: $padding-page;

  // 当有 PDF 状态区域时，只保留上圆角
  &.has-pdf-status {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: none;
  }

  .header {
    @include center();
    justify-content: space-between;
    margin-bottom: $padding-v2;
    font-size: $fontsize-small;
    color: $font-color-gray;
  }

  .from-to {
    @include center();
    gap: 100rpx;
    @include h3();

    image {
      width: 150rpx;
      height: 29rpx;
    }
  }

  .summary {
    display: flex;
    justify-content: space-between;
    padding: calc($padding-v2 * 2);

    >view {
      display: flex;
      flex-direction: column;
      gap: $padding-page;

      view {
        font-size: $fontsize-small;
        text-align: center;
        color: $font-color-gray;

        &:first-child {
          font-size: $fontsize;
          font-weight: 500;
          color: black;
          letter-spacing: 5rpx;
        }
      }
    }
  }
}

// PDF 状态区域样式
.pdf-status {
  background: #F7F7F9;
  height: 96rpx;
  border: 1rpx solid $border-color-v2;
  border-top: none;
  border-bottom-left-radius: $border-radius-middle;
  border-bottom-right-radius: $border-radius-middle;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .pdf-left {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .pdf-icon {
      width: 80rpx;
      height: 80rpx;
    }

    .pdf-label {
      font-size: 28rpx;
      color: black;
      font-weight: 500;
    }
  }

  .pdf-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .pdf-status-text {
      font-size: 28rpx;
      font-weight: 500;
    }
  }

  .pdf-right {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .pdf-btn {
      width: 55rpx;
      height: 55rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1890FF;

      text {
        font-size: 30rpx;
        color: white;
      }

      &:first-child {
        background: #52C41A;
      }
    }
  }
}
</style>
