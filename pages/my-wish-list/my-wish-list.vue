<template>
  <YjNavBar :custom-style="{
    background: 'transport'
  }" title="我的心愿单" @load="({ height, paddingBottom }) => pageStyle.paddingTop = `${height + paddingBottom}px`"
            @not-found="navTo('pages/index/index')"/>
  <view :style="pageStyle" class="my-wish-list">
    <!-- 固定加号按钮 -->
    <view class="fixed-add-btn" @click="navTo('pages/publish-wish/publish-wish')">
      <text class="iconfont icon-jia2"/>
    </view>
    <!-- 顶部搜索和过滤 Tab -->
    <view class="filter-tabs">
      <view v-for="tab in tabs" :key="tab.value" :class="['tab-item', { active: activeTab === tab.value }]"
            @click="activeTab = tab.value">
        {{ tab.label }}
      </view>
    </view>

    <!-- 心愿列表 -->
    <view class="wish-list">
      <template v-if="wishes.length > 0">
        <view v-for="(wish, index) in wishes"
              :key="index"
              class="wish-item">
          <view @tap="navTo('pages/wish-detail/wish-detail', {id: wish.wish_id})">
            <view class="wish-header">
              <view class="left">
                <text class="wish-title">{{ wish.title }}</text>
                <text class="wish-description">{{ wish.wish_desc }}</text>
              </view>

              <view class="wish-status">
                <text :class="{
                'icon-gongshigongkai': wish.open_scope === WishOpenScope.Public,
                'icon-simi': wish.open_scope !== WishOpenScope.Public
              }" class="iconfont"></text>
                {{ wish.open_scope === WishOpenScope.Public ? '公开' : '私密' }}
              </view>
            </view>

            <view class="wish-tags">
              <text v-for="(tag, tagIndex) in wish.tags" :key="tagIndex" class="tag">
                #{{ tag }}
              </text>
            </view>
          </view>

          <view class="wish-info">
            <view class="wish-details">
              <text class="detail">
                <text class="iconfont icon-yuefen"/>
                {{ wish.date_str }}
              </text>
              <text class="detail">
                <text class="iconfont icon-ren"/>
                {{ wish.total_people }}人
              </text>
              <text class="detail">
                <text class="iconfont icon-ditu"/>
                {{ wish.to }}
              </text>
            </view>
            <view class="wish-actions">
              <text>
                <text class="iconfont icon-wanchengdu"/>
                {{ `${wish.current_people}/${wish.total_people}` }}
              </text>
              <view @tap="closeWish(wish)" v-if="wish.can_close">
                <text class="iconfont icon-bukejian"/>
                关闭
              </view>
            </view>
          </view>
          <view v-if="wish.plan_id" @tap="navTo('pages/details/details', {plan_id: wish.plan_id})" class="view-plan">
            查看行程详情 &gt;
          </view>


        </view>
      </template>
      <template v-else>
        <view class="empty-placeholder">
          <text class="iconfont icon-wushuju"/>
          <text class="empty-text">暂无心愿单</text>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup>
import {computed, ref, watch} from 'vue';
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import {navTo, showConfirm} from "@/utils";
import {onLoad, onReachBottom} from "@dcloudio/uni-app";
import {userWishList, wishClose} from "@/api/user";
import {WishOpenScope} from "@/utils/constmap";

const pageStyle = ref({})

const tabs = [
  {label: '全部', value: 'all'},
  {label: '想去', value: 'want'},
  {label: '已去', value: 'reach'},
];

const activeTab = ref('all');
const pageSize = 20
let page = 1
const total = ref(0)
const hasMore = computed(() => total.value > pageSize * page)

const wishes = ref([]);

watch(activeTab, (newVal) => {
  page = 1
  wishes.value = []
  getList()
})

const getList = () => {
  const data = {
    page,
    page_size: pageSize,
    tab: activeTab.value
  }
  userWishList(data).then(({data}) => {
    wishes.value.push(...data.list)
    total.value = data.total
  })
}

function closeWish(item) {
  showConfirm('确定关闭心愿单吗？', '').then(() => {
    const data = {wish_id: item.wish_id}

    wishClose(data).then(() => {
      item.can_close = false
    })
  })
}

onLoad(() => {
  getList()
})

// 监听页面滚动到底部事件
onReachBottom(() => {
  if (hasMore.value) {
    page++
    getList()
  }
})

</script>

<style lang="scss" scoped>
@import './my-wish-list.scss';
</style>
