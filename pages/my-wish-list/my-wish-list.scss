@use "../../styles/mix";
@use "../../styles/define";

.my-wish-list {
  position: relative;
  min-height: 100vh;
  padding: define.$padding-page;
  @include mix.linear-gradient();
}

.filter-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 20rpx;
  gap: 90rpx;

  .tab-item {
    color: #999999;
    border-bottom: 4rpx solid transparent;

    &.active {
      color: #333333;
      font-weight: bold;
      border-color: define.$plan-color-3;
    }
  }
}

.wish-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;

  .wish-item {
    border: 2rpx solid #EDEEF0;
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    font-size: 20rpx;

    .wish-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 18rpx;

      .left {
        display: flex;
        flex-direction: column;
        flex: 1;

        .wish-title {
          font-weight: bold;
          font-size: 28rpx;
          @include mix.ellipse();
        }
      }

      .wish-title, .wish-description {
        max-width: 520rpx;
      }

      .wish-description, .wish-status {
        color: #999999;
        font-size: 20rpx;
      }

      .wish-description {
        @include mix.ellipse(2);
      }

      .wish-status {
        @include mix.center(column);
      }
    }


    .wish-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10rpx;

      .tag {
        border-radius: 30rpx;
        padding: 8rpx 20rpx;
        @include mix.tag-bg-colors();
      }
    }

    .wish-info {
      margin-top: 20rpx;
      @include mix.center();
      justify-content: space-between;

      .wish-details {
        flex: 1;
        display: flex;

        font-size: 20rpx;

        .detail {
          color: #999;
          @include mix.center();
        }
      }

      .wish-actions {
        @include mix.center();
        gap: 8rpx;

        > view {
          @include mix.center();
        }
      }
    }

    .view-plan {
      color: define.$plan-color-3;
      margin-top: 10rpx;
    }
  }
}

.fixed-add-btn {
  position: fixed;
  right: 32rpx;
  bottom: 32rpx;
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  background-color: define.$plan-color-3;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
  font-size: 48rpx;
  color: #fff;
  font-weight: bold;
}

.empty-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;

  .iconfont {
    font-size: 48px;
    margin-bottom: 16px;
  }

  .empty-text {
    font-size: 16px;
  }
}
