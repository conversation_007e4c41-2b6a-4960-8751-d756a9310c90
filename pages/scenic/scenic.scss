@import '../../styles/_mix.scss';
@import '../../styles/_define.scss';

.container {
  background: #f7f7f9;
  min-height: 100vh;
}

:deep(.tabs) {
  padding: 0 $padding-page;
}

.hotel-banner {
  height: 420rpx;

  swiper {
    width: 100%;
    height: 100%;

    swiper-item {
      width: 100%;
      height: 100%;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.scenic-cont {
  margin: 0rpx auto;
  background: #ffffff;
  border-radius: $border-radius-middle $border-radius-middle 0rpx 0rpx;
  box-shadow: 0rpx 3rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
  position: relative;
  top: -50rpx;
  padding: $padding-page $padding-page 0 $padding-page;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: $padding-small;

  .title {
    // margin-bottom: 10rpx;

    .cont-title {
      font-size: $h3-v2;
      font-weight: bold;
    }

    .level {
      margin-left: $padding-small;
    }
  }

  .open-time {
    gap: $padding-small;
    @include center();
    justify-content: flex-start;
  }

  .tel-locaton {
    display: flex;
    justify-content: flex-end;
    gap: $padding-v2;
    padding-bottom: $padding-page;

    >view,
    .iconfont {
      @include center();
    }

    .iconfont {
      width: 41rpx;
      height: 41rpx;
      border-radius: 50%;
      background: $gray-color-v2;
      color: $font-color-gray;
      font-size: $fontsize-small;
    }

    >view {
      gap: $padding-small;
    }
  }
}

.rich {
  width: 100%;
  overflow: hidden;
}

.rich image {
  width: 100%;
}

.rich img {
  width: 100% !important;
}

.cont-ticket {
  margin: $padding-page;
  background: white;
  border-radius: $border-radius-v2;

  .item {
    border-bottom: 1rpx dashed $border-color-v2;
    padding: $padding-page $padding-page $padding-page 0;

    &.no-more {
      @include center(column);
      color: $font-color-gray;

      border-bottom: none;
    }

    .title {
      @include ellipse();
      font-weight: 500;
      @include center();
      justify-content: flex-start;

      &:before {
        margin-right: $padding-page;
        width: 7rpx;
        content: ' ';
        height: 35rpx;
        background: #c4c4c4;
        display: inline-block;
      }
    }

    .extra {
      padding-left: $padding-page;
      margin-top: $padding-small;
      @include center();
      justify-content: space-between;

      .cancel {
        color: $font-color-gray;
      }

      .right {
        @include center();
        gap: $padding;

        >view {
          &:first-child {
            @include center(column);
          }
        }

        .price {
          color: $price-color;
          font-size: $h2;
        }
      }
    }
  }
}

.pics,
.desc {
  image {
    width: 100%;
  }

  :deep(.card) {
    margin: 0 $padding-page;
    margin-bottom: $padding-page;
  }
}

.pics {
  :deep(.content) {
    padding: 0 !important;
  }
}