<template>
	<view class="container">
		<!-- 轮播图 -->
		<view class="hotel-banner">
			<swiper autoplay="true">
				<swiper-item v-for="(item, index) in [detail.pic]" :key="index">
					<image :src="item"></image>
				</swiper-item>
			</swiper>
		</view>
		<view class="scenic-cont">
			<view class="title">
				<text class="cont-title">
					{{ detail.name }}
				</text>

				<view class="level tag">{{ detail.level_text }}</view>
			</view>

			<view class="open-time">
				<view class="tag" :class="{
					success: detail.state == 1
				}">营业中</view>
				<text>{{ detail.open_time }}</text>
			</view>

			<view>{{ detail.address }}</view>

			<view class="tel-locaton">
				<view @tap="onCall"><text class="iconfont icon-dianhua1 tel"></text>电话</view>
				<view @tap="openLocation">
					<text class="iconfont icon-ditu location"></text>地图
				</view>
			</view>
		</view>
		<YjTabs active-color="#1890FF" :data="tabData" v-model="activeTab">
		</YjTabs>

		<view class="cont-ticket" id="tickets">
			<view class="item no-more" v-if="tickets.length == 0">
				<view>
					<text class="iconfont icon-huojiakongkong"></text>
					暂无可预定门票
				</view>
			</view>

			<view class="item" v-for="(item, index) in tickets" :key="index">
				<view class="title">{{ item.title }}</view>
				<view class="extra">
					<view class="cancel">
						<template v-if="item.is_cancel && item.cancel_day >= 0">
							{{ item.cancel_day == 0 ? '随时可退' : `提前${item.cancel_day}天可退` }}
						</template>

					</view>
					<view class="right">
						<text class="price">{{ formatMoney(item.sale_price) }}</text>
						<view @tap="onOrder(item)" class="btn danger">选购</view>
					</view>

				</view>
			</view>
		</view>

		<view id="details">
			<Card class="desc" title="景点介绍" v-if="detail.desc.length > 0">
				<rich-text :nodes="richtext(detail.desc)"></rich-text>
			</Card>

			<Card class="pics" title="景点图片" v-if="detail.pics.length > 0">
				<view>
					<image mode="aspectFill" :src="url" v-for="(url, index) in detail.pics" :key="index"></image>
				</view>
			</Card>
		</view>


	</view>
</template>

<script setup>
import {
	computed,
	ref,
	watch
} from 'vue';
import {
	onLoad
} from '@dcloudio/uni-app'
import {
	scenicDetail
} from '../../api/scene';
import qs from 'qs'

import {
	formatMoney,
	richtext
} from '../../utils/index.js'
import {
	sceneTickets
} from '../../api/scene';
import YjTabs from '@/components/YjTabs/YjTabs.vue';

const detail = ref({
	pics: [],
	pic: '',
	state: 1,
	desc: '',
})
const tickets = ref([])
const tabData = [{
	label: '门票预定',
	value: 'booking'
},
{
	label: '景点详情',
	value: 'desc'
},
{
	label: '图片/视频',
	value: 'fac'
},
{
	label: '必看指南',
	value: 'notice'
},
]
const activeTab = ref('booking')
const id = ref('')

watch(activeTab, val => {
	const query = uni.createSelectorQuery()

	if (val == 'booking') {
		query.select('#tickets').boundingClientRect()
	} else {
		query.select('#details').boundingClientRect()
	}

	query.exec(rect => {
		if (rect[0]) {
			uni.pageScrollTo({
				scrollTop: rect[0].top,
				duration: 300,
			})
		}
	})

})

function onCall() {
	if (detail.value.tel.length == 0) {
		return
	}

	const tmp = detail.value.tel.split(';')
	uni.makePhoneCall({
		phoneNumber: tmp[0]
	})
}

function openLocation() {
	uni.openLocation({
		latitude: detail.value.lat,
		longitude: detail.value.lng,
		name: detail.value.name,
	})
}

function onOrder(item) {
	const query = {
		ticket_id: item.ticket_id,
		ota: item.ota,
		product_type: item.product_type,
		scenic_id: id.value
	}

	uni.navigateTo({
		url: `/pages/orderpreview/orderpreview?` + qs.stringify(query)
	})
}

onLoad((query) => {
	id.value = query.id

	scenicDetail(id.value).then(res => {
		Object.assign(detail.value, res.data)

		uni.setNavigationBarTitle({
			title: detail.value.name,
		})

		sceneTickets(id.value).then(res => {
			tickets.value = res.data.list
		})
	})
})
</script>

<style lang="scss" scoped>
@import 'scenic.scss'
</style>