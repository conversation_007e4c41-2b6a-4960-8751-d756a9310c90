<script setup>

import {onLoad, onPageScroll} from "@dcloudio/uni-app";
import {computed, ref} from "vue";
import {planCovers, planDetail} from "@/api/plan";
import Info from "@/pages/details/components/Info.vue";
import YjNavBar from "@/components/YjNavBar/YjNavBar.vue";
import TimelinePics from "@/pages/details/components/TimelinePics.vue";
import {decodeQuery, navTo} from "@/utils";

const plan_id = ref('')
const query = ref({})
const detail = ref({
  sections: []
})
const bodyStyle = ref({})
const covers = ref([])

const planQueryParams = computed(() => {
  const p = {}

  if (detail.value.plan_id) {
    p.plan_id = detail.value.plan_id
  } else if (query.value.plan_id) {
    p.plan_id = query.value.plan_id
  } else if (query.value.ai_reqid) {
    p.ai_reqid = query.value.ai_reqid
  }

  return p
})
const pageScroll = ref(0)
const navBg = computed(() => {
  const max = 30
  let st = pageScroll.value
  if (st >= max) {
    st = max
  }
  return `rgba(255,255,255, ${st / max})`
})
const navbarStyle = computed(() => {
  return {
    backgroundColor: navBg.value
  }
})

function onNavBarLoad(res) {
  bodyStyle.value = {
    paddingTop: `${res.height + res.paddingBottom}px`
  }
}

function getDetail() {
  planDetail(planQueryParams.value).then(({data}) => {
    Object.assign(detail.value, data)

    uni.setNavigationBarTitle({
      title: detail.value.subject
    })
  })
}

onLoad(q => {
  Object.assign(query.value, decodeQuery(q))

  getDetail()

  planCovers(planQueryParams.value).then(({data}) => {
    Object.assign(covers.value, data.sections)
  })
})

onPageScroll((e) => {
  pageScroll.value = e.scrollTop
})

</script>

<template>
  <YjNavBar :custom-style="navbarStyle" @load="onNavBarLoad"
            @not-found="navTo('pages/index/index')"/>
  <view :style="bodyStyle" class="container">
    <Info :detail="detail" @reload="getDetail"/>
    <view class="section-list">
      <view v-for="(section, index) in covers" :key="index" class="section-item">
        <view class="subject">{{ `${section.section_name}-${section.subject}` }}</view>
        <template v-for="(time, tIndex) in section.timeline" :key="tIndex">
          <view v-if="time.pics.length > 0">
            <view class="title">
              <view>
                <text/>
              </view>
              {{ time.title }}
            </view>
            <TimelinePics :pics="time.pics"/>
          </view>
        </template>

      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import "pics";
</style>