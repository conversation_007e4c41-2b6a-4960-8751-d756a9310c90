@use "../../styles/define";
@use "../../styles/mix";

.expense-page {
  padding: define.$padding-page;
  //background: radial-gradient(0% 57% at 79% 91%, rgba(135, 239, 255, 0.2) 0%, rgba(255, 255, 255, 0) 100%),
  //linear-gradient(101deg, rgba(255, 255, 220, 0.2) 0%, rgba(255, 255, 255, 0) 100%),
  //linear-gradient(180deg, #FFFFDC 0%, #87EFFF 100%);
  @include mix.linear-gradient();
  min-height: 70vh;
}

// 费用总计卡片
.expense-total-card {
  margin-bottom: 40rpx;

  .title {
    font-size: 34rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .expense-header-row {
    @include mix.center();
    justify-content: space-between;

    .person-selector {
      display: flex;
      flex: 1;
      justify-content: flex-end;

      .easy-input {
        width: 130rpx;

        :deep(.input-number) {
          .input {
            min-width: auto;
          }
        }
      }
    }
  }
}

// 费用占比图表卡片
.expense-chart-card {
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);

  .chart-container {
    width: 100%;
    height: 440rpx;
    position: relative;

    // 图表加载动画
    .chart-loading {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      z-index: 10;

      .loading-icon {
        width: 60rpx;
        height: 60rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #1890FF;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }

  .chart-header {
    @include mix.center();
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .per-person, .trip-days {
      display: flex;
      flex-direction: column;

      text {
        color: #999999;
        font-size: 20rpx;
      }
    }

    .per-person {
      color: #1890FF;
      font-size: 34rpx;
      font-weight: bold;
    }

    .trip-days {
      font-weight: bold;
    }
  }
}

// 费用类目卡片
.expense-categories {
  margin: 40rpx 0;

  .expense-card {
    padding-top: 20rpx;
    padding-bottom: 20rpx;
    border-bottom: 2rpx solid #EDEEF0;

    &:last-child {
      border-bottom: none;
    }

    &:first-child {
      padding-top: 0;
    }

    .expense-card-summary {
      @include mix.center();
      justify-content: space-between;

      .title {
        font-weight: bold;
        gap: 8rpx;

        &, .iconfont {
          @include mix.center();
        }

        .iconfont {
          background: #1890FF;
          color: white;
          border-radius: 50%;
          font-size: 20rpx;
          width: 40rpx;
          height: 40rpx;
        }
      }

      .expense-card-amount {
        color: #1890FF;
      }
    }

    .expense-small-item {
      color: #999999;
      font-size: 20rpx;
      margin-left: 40rpx;
    }

    .expense-card-list {
      margin-top: 20rpx;
      @include mix.center(column);
      gap: 20rpx;
      align-items: stretch;

      .item {
        display: flex;
        justify-content: space-between;
        margin-left: 48rpx;
      }
    }
  }
}

.transport, .other {
  .expense-card-list {
    .item {
      > view {
        &:first-child {
          display: flex;
          flex-direction: column;

          text {
            font-size: 20rpx;
            color: #999999;
          }
        }
      }
    }
  }
}

.transport {
  .item {
    .iconfont {
      margin-right: 14rpx;


      &:last-child {
        color: #FACA14 !important;
      }
    }

    &:first-child {
      .iconfont {
        color: #1890FF !important;
      }
    }
  }

}

.other {
  .expense-card-list {
    .sub {
      display: flex;
      flex-direction: column;
    }
  }
}

// 免责声明样式
.disclaimer {
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 30rpx 34rpx;

  view {
    color: #FFA01D;
    font-weight: bold;
    display: flex;
    gap: 4rpx;
    margin-bottom: 10rpx;
  }
}
