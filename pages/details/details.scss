@import "../../styles/_define.scss";
@import "../../styles/_mix.scss";


.container {
  background: $page-bg-color;
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

$transition-fast: 0.2s ease;
$map-initial-height: 422rpx;


// 拖拽手柄样式
.drag-handle {
  width: 100%;
  height: 60rpx; // 增大高度，提供更大的交互面积
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
  border-radius: 20rpx 20rpx 0 0;
  touch-action: none;
  -webkit-user-select: none;
  user-select: none;
  z-index: 10;
  position: relative; // 添加定位属性
  background-color: rgba(255, 255, 255, 0.95); // 添加轻微背景色

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
  }

  .drag-indicator {
    width: 80rpx; // 增大指示器宽度
    height: 8rpx; // 增大指示器高度
    background-color: #cccccc; // 更深的颜色使其更明显
    border-radius: 4rpx;
    margin-top: 10rpx;
    margin-bottom: 10rpx;
    position: relative;
    z-index: 2;
  }

  &:active {
    cursor: grabbing;

    .drag-indicator {
      background-color: #999999; // 拖动时颜色变深
      width: 100rpx; // 拖动时增大宽度，提供视觉反馈
      transition: all $transition-fast;
    }
  }
}

.section-shortcut {
  //margin-bottom: 64rpx;
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;

  scroll-view {
    width: 100%;
    white-space: nowrap;
  }

  .item {
    margin-right: 50rpx;
    display: inline-block;
    color: #694209;
    font-size: 20rpx;
    font-weight: bold;
    border-radius: 20rpx;
    padding: 6rpx;
    position: relative;

    text {
      font-size: 40rpx;
    }

    &:last-child {
      margin-right: 0;
    }

    &.active {
      background: #E7F4FF;

      &:after {
        content: ' ';
        background: rgba(250, 202, 20, 0.7);
        border-radius: 14rpx;
        height: 28rpx;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 100%;
        z-index: 1;
      }
    }
  }
}


.map-container {
  height: 100vh;
  position: relative;

  map {
    width: 100%;
    height: 100%;
  }

  :deep(.tupian) {
    z-index: 83;
    position: absolute;
    left: $padding-page;
    padding: 20rpx 16rpx;
    bottom: calc(100vh - $map-initial-height + 20rpx);
    transition: bottom 0.3s ease;
  }
}

.pics-container {
  position: relative;
  height: $map-initial-height;

  swiper {
    height: 100%;

    image {
      width: 100%;
    }
  }

  .pics-footer {
    padding: 0 $padding-page;
    position: absolute;
    left: 0;
    bottom: 20rpx;
    right: 0;
    @include center();
    justify-content: space-between;

    > view {
      &:last-child {
        @include center();
        justify-content: flex-start;
        gap: 8rpx;

        .s {
          gap: 4rpx;
          display: flex;

          view {
            background: white;
            opacity: 0.5;
            width: 12rpx;
            height: 12rpx;

            &.active {
              opacity: 1;
            }
          }
        }

        .btn-all {
          border-radius: 20rpx;
          font-size: 20rpx;
          opacity: 0.8;
          padding: 6rpx 16rpx;
          background: white;
        }
      }
    }
  }
}

.main-data {
  position: absolute;
  z-index: 10;
  left: 0;
  right: 0;
  width: 100%;
  bottom: 0;
  height: calc(100vh - $map-initial-height);
  //transform: translateY(0px);
  background: white;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.08);
  display: flex;
  padding: 0 $padding-page 290rpx;
  flex-direction: column;

  .main-scroll-view {
    flex: 1;
    overflow-y: auto;
    //overflow: auto;
    -webkit-overflow-scrolling: touch; /* 优化滚动体验 */
  }
}

.section-list {
  margin-top: 68rpx;

  .section-item {
    margin-bottom: 60rpx;

    &:nth-child(odd) {
      .section-item-h {
        view {
          &:first-child {
            &:after,
            text {
              background: #1890ff;
            }
          }
        }
      }

      .timeline-item {
        .left {
          &:after,
          &:before,
          text {
            background: #1890ff;
          }
        }
      }
    }

    &:nth-child(even) {
      .section-item-h {
        view {
          &:first-child {
            &:after,
            text {
              background: #52c41a;
            }
          }
        }
      }

      .timeline-item {
        .left {
          &:after,
          &:before,
          text {
            background: #52c41a;
          }

          &:not(.line-text) {
            .right {
              background: #f4fff0 !important;
              border-color: #f4fff0 !important;

              .timeline-icon:not(.disable) {
                background: #52c41a !important;
              }
            }
          }
        }
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .section-item-h {
      display: flex;
      height: 100rpx;
      gap: 14rpx;

      view {
        font-weight: bold;

        &:first-child {
          @include center(column);

          &:after {
            content: "";
            width: 4rpx;
            flex: 1;
          }

          text {
            width: 32rpx;
            height: 32rpx;
            border-radius: 50%;
          }
        }
      }
    }

    .timeline {
      display: flex;
      flex-direction: column;

      .timeline-item {
        @include center();
        justify-content: flex-start;
        align-items: stretch;
        gap: 14rpx;

        &.line-text {
          .right {
            background: #f7f7f9;
          }
        }

        &:last-child {
          .right {
            margin-bottom: 0;
          }
        }

        .right {
          position: relative;
          margin-bottom: 20rpx;
          flex: 1;
          background: #f0fdff;
          border-radius: 16rpx;
          border: 2rpx solid #e7f4ff;
          padding: 30rpx;
          display: flex;
          gap: 16rpx;

          .timeline-icon {
            position: absolute;
            right: 0;
            top: 0;
            width: 40rpx;
            height: 40rpx;
            border-radius: 0rpx 16rpx 0rpx 16rpx;
            background: #1890ff;
            color: white;
            @include center();

            &.disable {
              background: #b7b9bd;
            }
          }

          .pic {
            width: 136rpx;
            max-height: 136rpx;
            border-radius: $border-radius-v2;
          }

          .transport {
            @include center();
            justify-content: flex-start;
            gap: $padding-page;

            image {
              width: 126rpx;
              margin-left: $padding-page;
            }

            .transport-from,
            .transport-to {
              display: flex;
              flex-direction: column;
              gap: 10rpx;
            }

            .station {
              color: $font-color-gray;
              font-size: $fontsize-mini;
            }
          }

          .poi-detail-info {
            flex: 1;

            .time-title {
              @include ellipse();
              max-width: 300rpx;
              margin-bottom: 20rpx;
              font-weight: bold;
            }

            .time-desc {
              max-width: 430rpx;
              @include ellipse(2);
            }

            .tags {
              margin-top: 20rpx;
              display: flex;
              gap: 16rpx;

              text {
                padding: 4rpx 12rpx;
                background: #c0faff;
                border-radius: 4rpx;
                font-size: 20rpx;
                color: #1890ff;

                &:last-child {
                  color: #690969;
                  background: #f5dfff;
                }

                &:nth-child(2) {
                  background: #fff2c1;
                  color: #694209;
                }
              }
            }
          }
        }

        .left {
          @include center(column);
          width: 32rpx;

          &:before,
          &:after {
            content: "";
            width: 4rpx;
            flex: 1;
          }

          text {
            width: 20rpx;
            height: 20rpx;
            border-radius: 50%;
          }
        }

        &:last-child {
          .left {
            > view {
              &:last-child {
                &::after {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

.notice {
  background: #fffcf0;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
  margin-top: 20rpx;

  > view {
    &:first-child {
      color: #ffa01d;
      font-weight: bold;
    }
  }

  .content {
    padding: 20rpx 30rpx 10rpx 30rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 86;
  background: white;
  padding: 20rpx $padding-page 60rpx $padding-page;

  > view {
    &:first-child {
      @include center();
      justify-content: space-around;

      .item {
        gap: 8rpx;
        @include center(column);

        .iconfont {
          font-size: 40rpx;
          color: #1890ff;
        }
      }

      .fav {
        .active {
          color: red;
        }
      }
    }

    &:last-child {
      margin-top: 20rpx;
    }
  }
}

// 弹窗打开时禁用页面滚动
.container.popup-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

// PDF 导出弹窗样式
.pdf-export-popup {
  background: white;
  border-radius: 28rpx;
  width: 520rpx;
  max-width: 85vw;
  overflow: hidden;
  position: relative;
  touch-action: none;
  box-shadow: 0rpx 20rpx 60rpx rgba(0, 0, 0, 0.3),
  0rpx 0rpx 0rpx 1rpx rgba(255, 255, 255, 0.2);

  // 动态背景边框效果
  &:before {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
    border-radius: 36rpx;
    z-index: -1;
  }

  .pdf-export-header {
    padding: 30rpx 24rpx 28rpx;
    text-align: center;
    background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 50%, #2563EB 100%);
    color: white;
    position: relative;
    overflow: hidden;
    z-index: 2;

    // 动态几何装饰 - 旋转圆环
    &:before {
      content: '';
      position: absolute;
      top: -100rpx;
      right: -100rpx;
      width: 300rpx;
      height: 300rpx;
      background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      border-radius: 50%;
      animation: rotate 10s linear infinite;
    }

    // 浮动三角形装饰
    &:after {
      content: '';
      position: absolute;
      bottom: -50rpx;
      left: -50rpx;
      width: 200rpx;
      height: 200rpx;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
      clip-path: polygon(0 0, 100% 0, 50% 100%);
      animation: float 6s ease-in-out infinite;
    }

    .header-decoration {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1;

      .deco-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;

        &.deco-1 {
          width: 120rpx;
          height: 120rpx;
          top: 20rpx;
          left: 40rpx;
          animation-delay: 0s;
        }

        &.deco-2 {
          width: 80rpx;
          height: 80rpx;
          top: 40rpx;
          right: 60rpx;
          animation-delay: 2s;
        }
      }

      .deco-triangle {
        position: absolute;
        width: 0;
        height: 0;
        border-left: 30rpx solid transparent;
        border-right: 30rpx solid transparent;
        border-bottom: 50rpx solid rgba(255, 255, 255, 0.08);
        bottom: 30rpx;
        left: 80rpx;
        animation: rotate 8s linear infinite;
      }
    }

    .pdf-title-main {
      font-size: 32rpx;
      font-weight: 800;
      color: white;
      position: relative;
      z-index: 3;
      text-shadow: 0rpx 4rpx 20rpx rgba(0, 0, 0, 0.4);
      letter-spacing: 1rpx;
      background: linear-gradient(45deg, #ffffff 0%, #f0f8ff 50%, #ffffff 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: titleGlow 3s ease-in-out infinite alternate;
    }

    .header-sparkles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 2;

      .sparkle {
        position: absolute;
        font-size: 20rpx;
        animation: sparkle 4s ease-in-out infinite;

        &.sparkle-1 {
          top: 36rpx;
          right: 72rpx;
          animation-delay: 0s;
        }

        &.sparkle-2 {
          bottom: 48rpx;
          right: 24rpx;
          animation-delay: 1.5s;
        }

        &.sparkle-3 {
          top: 72rpx;
          left: 36rpx;
          animation-delay: 3s;
        }

        &.sparkle-4 {
          top: 20rpx;
          left: 120rpx;
          animation-delay: 0.8s;
          font-size: 18rpx;
        }

        &.sparkle-5 {
          bottom: 30rpx;
          left: 80rpx;
          animation-delay: 2.2s;
          font-size: 16rpx;
        }

        &.sparkle-6 {
          top: 50rpx;
          right: 40rpx;
          animation-delay: 1.2s;
          font-size: 18rpx;
        }

        &.sparkle-7 {
          bottom: 60rpx;
          left: 50rpx;
          animation-delay: 2.8s;
          font-size: 22rpx;
        }
      }
    }
  }

  .pdf-export-content {
    padding: 28rpx 24rpx;
    background: white;
    position: relative;
    z-index: 2;

    .pdf-advantages {
      margin-bottom: 28rpx;

      .advantage-item {
        display: flex;
        align-items: center;
        margin-bottom: 14rpx;
        padding: 20rpx 16rpx;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border-radius: 16rpx;
        position: relative;
        overflow: hidden;
        box-shadow: 0rpx 4rpx 16rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 2rpx solid transparent;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateY(-4rpx);
          box-shadow: 0rpx 12rpx 32rpx rgba(96, 165, 250, 0.15);
          border-color: rgba(96, 165, 250, 0.2);
        }

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(96, 165, 250, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 1;
        }

        &:hover::before {
          opacity: 1;
        }

        .advantage-icon-wrapper {
          position: relative;
          margin-right: 16rpx;
          width: 60rpx;
          height: 60rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 3;

          .iconfont {
            font-size: 36rpx;
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            z-index: 2;
            animation: iconFloat 3s ease-in-out infinite;
          }

          .icon-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60rpx;
            height: 60rpx;
            background: radial-gradient(circle, rgba(96, 165, 250, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            animation: iconGlow 2s ease-in-out infinite alternate;
            z-index: 1;
          }
        }

        .advantage-content {
          flex: 1;
          position: relative;
          z-index: 3;

          .advantage-title {
            font-size: 26rpx;
            font-weight: 700;
            background: linear-gradient(135deg, #2563EB, #1D4ED8);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 4rpx;
            animation: titleSlide 0.6s ease-out;
          }

          .advantage-desc {
            font-size: 22rpx;
            color: #7f8c8d;
            line-height: 1.3;
            animation: descSlide 0.8s ease-out;
          }
        }

        .advantage-decoration {
          position: absolute;
          right: 20rpx;
          top: 50%;
          transform: translateY(-50%);
          z-index: 2;

          .deco-dot {
            width: 8rpx;
            height: 8rpx;
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            border-radius: 50%;
            margin-bottom: 6rpx;
            animation: dotPulse 2s ease-in-out infinite;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .deco-line {
            width: 2rpx;
            height: 30rpx;
            background: linear-gradient(to bottom, #60A5FA, transparent);
            margin: 6rpx auto;
            animation: lineGrow 1.5s ease-in-out infinite alternate;
          }
        }
      }
    }

    .pdf-export-tips {
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      border-radius: 14rpx;
      padding: 20rpx;
      position: relative;
      box-shadow: 0rpx 4rpx 16rpx rgba(0, 0, 0, 0.08);
      border: 2rpx solid rgba(96, 165, 250, 0.1);
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, #60A5FA 0%, #3B82F6 50%, #2563EB 100%);
        z-index: 1;
      }

      .tips-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24rpx;
        padding-bottom: 16rpx;
        border-bottom: 1rpx solid rgba(96, 165, 250, 0.1);

        .tips-title {
          font-size: 24rpx;
          font-weight: 700;
          background: linear-gradient(135deg, #60A5FA, #3B82F6);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .tips-decoration {
          display: flex;
          gap: 6rpx;

          .tips-dot {
            width: 6rpx;
            height: 6rpx;
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            border-radius: 50%;
            animation: dotWave 1.5s ease-in-out infinite;

            &:nth-child(2) {
              animation-delay: 0.2s;
            }

            &:nth-child(3) {
              animation-delay: 0.4s;
            }
          }
        }
      }

      .tip-item {
        margin-bottom: 12rpx;
        font-size: 24rpx;
        color: #5a6c7d;
        display: flex;
        align-items: center;
        padding: 10rpx 14rpx;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 10rpx;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        &:hover {
          transform: translateX(8rpx);
          background: rgba(96, 165, 250, 0.05);
        }

        .tip-icon-wrapper {
          width: 32rpx;
          height: 32rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 10rpx;
          background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(59, 130, 246, 0.1));
          border-radius: 50%;
          position: relative;

          .iconfont {
            font-size: 18rpx;
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }

          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(96, 165, 250, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            animation: iconRipple 2s ease-in-out infinite;
          }
        }

        .tip-content {
          flex: 1;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }

        &.cost-item {
          .cost-amount {
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 32rpx;
            font-weight: 800;
            margin: 0 4rpx;
            animation: costGlow 2s ease-in-out infinite alternate;

            &.free {
              background: linear-gradient(135deg, #38BDF8, #0EA5E9);
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }

        &.info-item {
          font-size: 20rpx;
          color: #95a5a6;
          line-height: 1.3;
          margin-top: 4rpx;

          .tip-content {
            text-align: left;
          }
        }
      }
    }
  }

  .pdf-export-footer {
    padding: 0 24rpx 28rpx;
    background: white;
    display: flex;
    justify-content: space-between;
    gap: 12rpx;
    z-index: 2;
    position: relative;

    .pdf-btn {
      width: calc(50% - 6rpx);
      padding: 18rpx 28rpx;
      border-radius: 24rpx;
      font-size: 26rpx;
      font-weight: 700;
      text-align: center;
      line-height: 100%;
      border: none;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2rpx);
      }

      &.pdf-btn-cancel {
        background: linear-gradient(135deg, #DBEAFE, #BFDBFE);
        color: #2563EB;
        box-shadow: 0rpx 8rpx 25rpx rgba(37, 99, 235, 0.3);
      }

      &.pdf-btn-primary {
        background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 50%, #2563EB 100%);
        color: white;
        box-shadow: 0rpx 12rpx 35rpx rgba(96, 165, 250, 0.5);
        position: relative;
        overflow: hidden;
        animation: buttonPulse 2s ease-in-out infinite;

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          transition: left 0.6s ease;
          animation: shimmerMove 3s ease-in-out infinite;
        }

        &:after {
          content: '⬇';
          position: absolute;
          right: 20rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 24rpx;
          animation: downloadBounce 1.5s ease-in-out infinite;
        }

        &:hover {
          transform: translateY(-4rpx);
          box-shadow: 0rpx 16rpx 45rpx rgba(96, 165, 250, 0.7);
          animation: buttonHover 0.3s ease forwards;
        }

        &:hover:before {
          left: 100%;
        }

        &:active {
          transform: translateY(-2rpx);
          box-shadow: 0rpx 8rpx 25rpx rgba(96, 165, 250, 0.6);
        }
      }
    }
  }


  .laxin-activity {
    width: 731rpx;
    height: 695rpx;

    //width: 500rpx;
    //height: 475rpx;

    background-size: contain;
    background-repeat: no-repeat;
    position: relative;

    .data {
      position: absolute;
      bottom: 100rpx;
      left: 250rpx;

      image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
      }

      > view {
        &:first-child {
          margin-bottom: 22rpx;
          @include center();
          justify-content: flex-start;
          gap: 10rpx;

          .nickname {
            color: #1890ff;
            @include ellipse();
            max-width: 200rpx;
          }
        }
      }
    }
  }

  // 动画定义
  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0rpx);
    }
    50% {
      transform: translateY(-20rpx);
    }
  }

  @keyframes pulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }

  @keyframes glow {
    from {
      text-shadow: 0rpx 0rpx 10rpx rgba(255, 107, 107, 0.5);
    }
    to {
      text-shadow: 0rpx 0rpx 20rpx rgba(255, 107, 107, 0.8);
    }
  }

  @keyframes titleGlow {
    from {
      text-shadow: 0rpx 4rpx 20rpx rgba(0, 0, 0, 0.4);
      transform: scale(1);
    }
    to {
      text-shadow: 0rpx 6rpx 30rpx rgba(255, 255, 255, 0.3);
      transform: scale(1.02);
    }
  }

  @keyframes sparkle {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1) rotate(0deg);
    }
    50% {
      opacity: 1;
      transform: scale(1.2) rotate(180deg);
    }
  }

  @keyframes buttonPulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0rpx 12rpx 35rpx rgba(96, 165, 250, 0.5);
    }
    50% {
      transform: scale(1.02);
      box-shadow: 0rpx 16rpx 45rpx rgba(96, 165, 250, 0.7);
    }
  }

  @keyframes shimmerMove {
    0% {
      left: -100%;
    }
    50% {
      left: -50%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes downloadBounce {
    0%, 100% {
      transform: translateY(-50%) translateX(0);
    }
    50% {
      transform: translateY(-50%) translateX(0) translateY(-4rpx);
    }
  }

  @keyframes buttonHover {
    0% {
      transform: translateY(-2rpx);
    }
    100% {
      transform: translateY(-4rpx);
    }
  }

  @keyframes iconFloat {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-4rpx);
    }
  }

  @keyframes iconGlow {
    0% {
      opacity: 0.3;
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @keyframes titleSlide {
    0% {
      opacity: 0;
      transform: translateX(-20rpx);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes descSlide {
    0% {
      opacity: 0;
      transform: translateX(-20rpx);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes dotPulse {
    0%, 100% {
      opacity: 0.4;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  @keyframes lineGrow {
    0% {
      height: 20rpx;
      opacity: 0.4;
    }
    100% {
      height: 30rpx;
      opacity: 0.8;
    }
  }

  @keyframes dotWave {
    0%, 100% {
      transform: translateY(0);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-4rpx);
      opacity: 1;
    }
  }

  @keyframes iconRipple {
    0% {
      opacity: 0.3;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.1;
      transform: translate(-50%, -50%) scale(1.3);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(1.5);
    }
  }

  @keyframes costGlow {
    0% {
      text-shadow: 0rpx 0rpx 10rpx rgba(96, 165, 250, 0.5);
      transform: scale(1);
    }
    100% {
      text-shadow: 0rpx 0rpx 20rpx rgba(96, 165, 250, 0.8);
      transform: scale(1.05);
    }
  }
}
