<script setup>

import {computed} from "vue";

const props = defineProps({
  pics: Array,
  default: () => []
})

const list = computed(() => props.pics.slice(0, 3))

function onPreview(index) {
  uni.previewImage({
    urls: props.pics,
    current: index
  })
}

</script>

<template>
  <!-- 当图片数量大于3时，显示左边一张图，右边两张上下结构 -->
  <view class="data">
    <view v-if="list.length === 3" class="pics-complex">
      <view class="left-pics">
        <image :src="list[0]" class="left-pic" mode="aspectFill" @tap="onPreview(0)"></image>
      </view>

      <view class="right-pics">
        <image :src="list[1]" class="top-pic" mode="aspectFill" @tap="onPreview(1)"></image>
        <image :src="list[2]" class="bottom-pic" mode="aspectFill" @tap="onPreview(2)"></image>
      </view>
    </view>
    <view v-else class="pics-simple">
      <image :src="list[0]" class="pic" mode="widthFix" @tap="onPreview(0)"></image>
    </view>
  </view>

</template>

<style lang="scss" scoped>
$gap: 5rpx;
$border-radius: 10rpx;
image {
  width: 100%;
  height: 100%;
}

.pics-simple {
  //height: 390rpx;

  image {
    border-radius: $border-radius;
  }
}

.pics-complex {
  display: flex;
  align-items: stretch;
  gap: $gap;

  .left-pics {
    height: 694rpx;

    image {
      border-top-left-radius: $border-radius;
      border-bottom-left-radius: $border-radius;
    }
  }

  > .left-pics, .right-pics {
    flex: 1;
    display: flex;
  }


  .right-pics {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: $gap;

    > image {
      flex: 1;
      height: 50%;
      border-top-right-radius: $border-radius;
      border-bottom-right-radius: $border-radius;
    }
  }
}
</style>