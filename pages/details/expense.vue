嗯
<template>
  <YjNavBar :custom-style="{ ...navbarStyle, transition: 'background-color 0.3s, box-shadow 0.3s' }"
            :title="navbarTitle"
            @load="({ height, paddingBottom }) => pageStyle.paddingTop = `${height + paddingBottom}px`"
            @not-found="navTo('pages/index/index')"/>
  <view :style="pageStyle" class="expense-page">
    <!-- 费用总计卡片 -->
    <view class="expense-total-card">
      <view class="title">{{ planCost.plan_info && planCost.plan_info.subject }}</view>
      <view class="expense-header-row">
        <view class="date-range">
          <text>出发日期{{
              planCost.plan_info && planCost.plan_info.start_date ? formatTime(planCost.plan_info.start_date
              ) : ''
            }}
          </text>
        </view>
        <view class="person-selector">
          <view class="person-label">出行人数</view>
          <view class="easy-input">
            <MyInputNumber v-model="personCount" :max="10" :min="1"/>
          </view>

        </view>
      </view>
    </view>

    <!-- 费用占比图表 -->
    <view class="expense-chart-card">
      <view class="chart-header">
        <view class="per-person">
          {{ formatMoney(totalAmount) }}
          <text>预估总费用</text>
        </view>
        <view class="trip-days">{{ planCost.plan_info && planCost.plan_info.day_num ? planCost.plan_info.day_num : 0 }}天
          <text>总天数</text>
        </view>
      </view>
      <view class="chart-container">
        <view v-if="isChartLoading" class="chart-loading">
          <view class="loading-icon"></view>
        </view>
        <qiun-data-charts v-if="!isChartLoading" :chart-data="chartData" :opts="opts" type="pie"
                          @complete="onChartComplete"
                          @error="onChartError"/>
      </view>
    </view>

    <!-- 费用类目明细 -->
    <view class="expense-categories">
      <!-- 住宿费用 -->
      <view class="expense-card">
        <view class="expense-card-summary">
          <view class="title">
            <text class="iconfont icon-chuang"/>
            住宿
          </view>
          <view class="expense-card-amount">{{ formatMoney(hotelTotal) }}</view>
        </view>
        <view class="expense-small-item">标准间x{{ nightCount }}晚（约{{ formatMoney(perNightPrice) }}/晚）</view>
      </view>

      <!-- 交通费用 -->
      <view class="expense-card transport">
        <view class="expense-card-summary">
          <view class="title">
            <text class="iconfont icon-zuchefuwu"/>
            交通费用
          </view>
          <view class="expense-card-amount">{{ formatMoney(transportationTotal) }}</view>
        </view>
        <view class="expense-card-list">
          <view class="item">
            <view>
              <view>
                自驾油费
              </view>
              <text>汽油7.9L/100KM（约 {{ Math.round(planCost.distance / 1000) }}公里）</text>
            </view>
            <view>{{ formatMoney(planCost.fuel) }}</view>
          </view>
          <view class="item">
            <view>
              <view>
                高速费用
              </view>
            </view>
            <view>{{ formatMoney(planCost.highway) }}</view>
          </view>
        </view>
      </view>

      <!-- 餐饮费用 -->
      <view class="expense-card">
        <view class="expense-card-summary">
          <view class="title">
            <text class="iconfont icon-daocha"/>
            餐饮费用
          </view>
          <view class="expense-card-amount">{{ formatMoney(foodTotal) }}</view>
        </view>
        <view class="expense-small-item">{{
            planCost.plan_info && planCost.plan_info.day_num ?
                planCost.plan_info.day_num : 0
          }}天x3餐x{{ personCount }}人
        </view>
      </view>

      <!-- 景点门票 -->
      <view class="expense-card">
        <view class="expense-card-summary">
          <view class="title">
            <text class="iconfont icon-ancient-gate-fill"/>
            景点门票
          </view>
          <view class="expense-card-amount">{{ formatMoney(ticketTotal) }}</view>
        </view>
        <view class="expense-card-list">
          <view v-for="(scene, index) in planCost.scenes" :key="index" class="item">
            <view>{{ scene.name }}</view>
            <view v-if="scene.is_free">免费</view>
            <view v-else-if="scene.fee === 0">-</view>
            <view v-else>{{ formatMoney(scene.fee) }}/人</view>
          </view>
          <view v-if="planCost.scenes && planCost.scenes.length === 0" class="item">
            <view>暂无景点门票信息</view>
            <view>{{ formatMoney(0) }}</view>
          </view>
        </view>
      </view>

      <!-- 其他费用 -->
      <view class="expense-card other">
        <view class="expense-card-summary">
          <view class="title">
            <text class="iconfont icon-gengduo1"/>
            其他费用
          </view>
          <view class="expense-card-amount">{{ formatMoney(specialitiesTotal) }}</view>
        </view>
        <view class="expense-card-list">
          <view v-if="planCost.specialities && planCost.specialities.length > 0" class="item">
            <view>
              伴手礼
              <view class="sub">
                <text v-for="(item, index) in planCost.specialities" :key="index">
                  —{{ item.name }}（{{ formatMoney(item.fee) }}）
                </text>
              </view>
            </view>
            <view>{{ formatMoney(specialitiesTotal) }}</view>
          </view>
          <view v-else class="item">
            <view>暂无特产费用信息</view>
            <view>{{ formatMoney(0) }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- AI数据免责声明 -->
    <view class="disclaimer">
      <view>
        <text class="iconfont icon-wenxintishi"/>
        此费用为AI模拟费用，只做参考。
      </view>
      <text>免责声明：本页面展示的费用数据由AI生成，仅供参考，不代表实际消费情况。</text>
    </view>
  </view>
</template>

<script setup>
import {ref, computed} from 'vue'
import YjNavBar from '@/components/YjNavBar/YjNavBar.vue'
import {formatMoney, navTo, formatTime, showToast, navBack} from '../../utils/index'
import QiunDataCharts from "@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue";
import {onPageScroll, onLoad} from "@dcloudio/uni-app";
import {planCostDetail} from '@/api/plan'
import MyInputNumber from "@/components/MyInputNumber/MyInputNumber.vue";

// 存储滚动阈值
const scrollThreshold = ref(0);
const navbarTitle = ref('')

// 页面滚动处理
const handlePageScroll = (e) => {
  // 根据滚动位置设置导航栏样式
  if (e.scrollTop > scrollThreshold.value) {
    navbarStyle.value = {
      backgroundColor: '#ffffff',
      boxShadow: '0 2rpx 10rpx rgba(0, 0, 0, 0.1)'
    };
    navbarTitle.value = planCost.value.plan_info.subject
  } else {
    navbarStyle.value = {
      backgroundColor: 'transparent',
      boxShadow: 'none'
    };
    navbarTitle.value = ''
  }
};

// 注册页面滚动事件
onPageScroll(handlePageScroll);

// 图表加载状态
const isChartLoading = ref(true)

const navbarStyle = ref({})
const pageStyle = ref({})

// 人数选择
const personCount = ref(1)

// 费用数据
const planCost = ref({
  total: 0,
  ticket: 0,
  hotel: 0,
  highway: 0,
  fuel: 0,
  food: 0,
  distance: 0,
  specialities: [],
  scenes: [],
  plan_info: {
    subject: '',
    start_date: 0,
    day_num: 0
  }
})

// 计算住宿晚数
const nightCount = computed(() => {
  return (planCost.value.plan_info && planCost.value.plan_info.day_num ? planCost.value.plan_info.day_num - 1 : 0)
})

// 计算每晚价格
const perNightPrice = computed(() => {
  return nightCount.value > 0 ? Math.round(planCost.value.hotel / nightCount.value) : 0
})

// 计算房间数
const roomCount = computed(() => {
  return Math.ceil(personCount.value / 2) // 每2人一间房，向上取整
})

// 计算各类别总金额
const hotelTotal = computed(() => {
  // 假设planCost.value.hotel是单间房价
  return planCost.value.hotel * roomCount.value
})

const transportationTotal = computed(() => {
  return planCost.value.highway + planCost.value.fuel
})

const foodTotal = computed(() => {
  // 餐饮费用按人数计算
  return planCost.value.food * personCount.value
})

const ticketTotal = computed(() => {
  // 门票费用按人数计算
  return planCost.value.ticket * personCount.value
})

const specialitiesTotal = computed(() => {
  // 特产费用不按人数计算
  return planCost.value.specialities.reduce((sum, item) => sum + item.fee, 0)
})

// 计算总金额
const totalAmount = computed(() => {
  // 根据各项费用动态计算总金额，考虑房间数变化
  return hotelTotal.value + transportationTotal.value + foodTotal.value + ticketTotal.value + specialitiesTotal.value
})

// 图表数据
const chartData = computed(() => {
  const res = {
    series: [
      {
        data: [
          {
            name: '住宿',
            value: parseFloat(hotelTotal.value),
          },
          {
            name: '交通',
            value: parseFloat(transportationTotal.value)
          },
          {
            name: '餐饮',
            value: parseFloat(foodTotal.value)
          },
          {
            name: '景点',
            value: parseFloat(ticketTotal.value)
          },
          {
            name: '其他费用',
            value: parseFloat(specialitiesTotal.value)
          }
        ]
      }
    ]
  }
  res.series.forEach(item => {
    item.data.forEach(value => {
      value.labelText = `${value.name}: ${formatMoney(value.value)}`
    })
  })

  return res
})

// 图表配置
const opts = computed(() => {
  return {
    color: ["#1890FF", "#91CB74", "#FAC858", "#EE6666", "#73C0DE"],
    padding: [5, 5, 5, 5],
    enableScroll: false,
    legend: {
      show: false
    },
    extra: {
      pie: {
        activeOpacity: 0.5,
        activeRadius: 10,
        offsetAngle: 0,
        labelWidth: 15,
        border: true,
        borderWidth: 3,
        borderColor: "#FFFFFF",
        linearType: "custom",
        labelLine: true,
        labelLineLength: 15,
        labelLineStyle: "solid",
        labelLineColor: "#666666",
      }
    }
  }
})

// 图表加载完成回调
const onChartComplete = () => {
  isChartLoading.value = false
}

// 图表加载错误回调
const onChartError = () => {
  isChartLoading.value = false
}

// 页面加载时获取费用明细
onLoad((query) => {
  const params = {}
  if (query.ai_reqid) {
    params.ai_reqid = decodeURIComponent(query.ai_reqid)
  } else if (query.plan_id) {
    params.plan_id = query.plan_id
  }

  if (params.ai_reqid || params.plan_id) {
    planCostDetail(params)
        .then(({data}) => {
          if (!data.cost_ok) {
            uni.showModal({
              title: '提示',
              content: '预估费用正在计算中',
              showCancel: false,
              success: () => {
                navBack()
              }
            })
            return;
          }
          // 更新费用明细数据
          planCost.value = data.plan_cost || {
            total: 0,
            ticket: 0,
            hotel: 0,
            highway: 0,
            fuel: 0,
            food: 0,
            distance: 0,
            specialities: [],
            scenes: [],
            plan_info: {
              subject: '',
              start_date: 0,
              day_num: 0
            }
          }

          // 确保 plan_info 存在
          if (!planCost.value.plan_info) {
            planCost.value.plan_info = data.plan_info || {
              subject: '',
              start_date: 0,
              day_num: 0
            }
          }
        })
        .catch(error => {
          // 静默处理错误
        })
        .finally(() => {
          // 数据加载成功后更新加载状态
          isChartLoading.value = false
        });
  }

  // 设置导航栏初始样式
  navbarStyle.value = {
    backgroundColor: 'transparent',
    boxShadow: 'none'
  }

  // 获取系统信息并计算滚动阈值
  const systemInfo = uni.getSystemInfoSync();
  const pageHeight = systemInfo.windowHeight;
  scrollThreshold.value = pageHeight * 0.1; // 页面高度的5%

  // 图表加载状态会在图表完成加载时通过onChartComplete回调更新
})

</script>

<style lang="scss" scoped>
@import "expense";
</style>
