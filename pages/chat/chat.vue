<template>
  <view :style="contentStyle" class="container">
    <YjNavBar :custom-style="{
      backgroundColor: 'transparent',
    }" arrow-color="#666" @load="onNavBarLoad" @not-found="() => navTo('pages/index/index')">
      <template #title>
        <view class="title_icon" @tap="addNewChat">
          <image :src="getCdnUrl('/static/chat/xinduihua.png')"/>
        </view>
      </template>
    </YjNavBar>
    <view :style="{
      height: `calc(100vh - ${contentStyle.paddingTop})`
    }">
      <MyChat ref="chat" v-model="pageQuery"
              @hotel-click="({ hotel }) => navTo('pages/hotel/hotel', { id: hotel.id })"/>
    </view>

  </view>
  <MyCustomerService/>
</template>

<script setup>
import YjNavBar from '@/components/YjNavBar/YjNavBar.vue'
import {navTo, getCdnUrl} from '@/utils'
import {onLoad, onShow} from '@dcloudio/uni-app'
import {ref} from 'vue'
import MyCustomerService from "@/components/MyCustomerService/MyCustomerService.vue";
import MyChat from "@/components/MyChat/MyChat.vue";

const contentStyle = ref({
  paddingTop: '0px'
})

function onNavBarLoad({height, paddingBottom}) {
  contentStyle.value.paddingTop = `${height + paddingBottom}px`
}

const chat = ref(null)
const pageQuery = ref({})

onShow(() => {
  console.log('chat.value:', chat.value)
  chat.value?.unlock()
})

onLoad(query => {
  pageQuery.value = query
})

function addNewChat() {
  pageQuery.value = {}
  chat.value?.addNewChat()
}
</script>

<style lang="scss" scoped>
@import 'chat.scss';
</style>
