@import '../../styles/mix';
@import '../../styles/define';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // 覆盖index.scss中的背景色
  background: linear-gradient(to left top, rgba(135, 239, 255, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom right, rgba(255, 255, 220, 0.2), rgba(255, 255, 255, 0)),
  linear-gradient(to bottom, rgba(255, 255, 220, 0.1), rgba(135, 239, 255, 0.1)) !important;
  // 确保背景覆盖整个容器
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.title_icon {
  margin-left: 20rpx;
  width: 48rpx;
  height: 48rpx;
}
