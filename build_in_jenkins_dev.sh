#!/bin/bash

# 项目构建脚本 - 用于 Jenkins 自动化构建 roadtrip-api 项目

# 定义变量
PROJECT_NAME="roadtrip-api"
BUILD_DIR="$(pwd)"
BUILD_TARGET_DIR="${BUILD_DIR}/target"  # 构建输出目录
DEPLOY_DIR="/data/roadtrip/api"         # 部署目录
BINARY_NAME="${PROJECT_NAME}"
SUPERVISOR_PROCESS_NAME="${PROJECT_NAME}"

# 设置环境变量（集中放置）
export GOPATH=$JENKINS_HOME/go
export GOMODCACHE=$GOPATH/pkg/mod
export GOCACHE=$GOPATH/cache
export GOPROXY=https://goproxy.cn,direct

# 输出基本信息
echo "===== 开始构建 ${PROJECT_NAME} ====="
echo "当前目录: ${BUILD_DIR}"
echo "构建时间: $(date)"

# 创建必要目录
mkdir -p $GOMODCACHE
mkdir -p "${BUILD_TARGET_DIR}"

# 清理旧的构建文件
echo "清理旧的构建文件..."
rm -f "${BUILD_TARGET_DIR}/${BINARY_NAME}"

# 检查 Golang 环境
echo "检查 Golang 环境..."
if ! command -v go &> /dev/null; then
    echo "错误: 未找到 Golang 环境"
    exit 1
fi

go version

# 下载依赖
echo "下载项目依赖..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "错误: 下载依赖失败"
    exit 1
fi

# 检查 UPX 是否安装
echo "检查 UPX 是否安装..."
if ! command -v upx &> /dev/null; then
    echo "警告: 未找到 UPX 压缩工具，将跳过压缩步骤"
    UPX_ENABLED=false
else
    echo "UPX 版本: $(upx --version | head -n 1)"
    UPX_ENABLED=true
fi

# 构建项目 - 去除调试信息
echo "开始构建项目（去除调试信息）..."
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
  -a \
  -ldflags="-w -s" \
  -o "${BUILD_TARGET_DIR}/${BINARY_NAME}"

if [ $? -ne 0 ]; then
    echo "错误: 项目构建失败"
    exit 1
fi

# 检查二进制文件是否存在
if [ ! -f "${BUILD_TARGET_DIR}/${BINARY_NAME}" ]; then
    echo "错误: 构建后的二进制文件不存在"
    exit 1
fi

# 输出构建后大小
echo "构建后二进制文件大小: $(du -h "${BUILD_TARGET_DIR}/${BINARY_NAME}" | awk '{print $1}')"

# 使用 UPX 压缩
if [ "$UPX_ENABLED" = true ]; then
    echo "使用 UPX 压缩二进制文件..."
    upx --best --lzma "${BUILD_TARGET_DIR}/${BINARY_NAME}"

    if [ $? -ne 0 ]; then
        echo "警告: UPX 压缩失败，但继续执行部署流程"
    else
        echo "压缩后二进制文件大小: $(du -h "${BUILD_TARGET_DIR}/${BINARY_NAME}" | awk '{print $1}')"
    fi
fi

# 赋予执行权限
chmod +x "${BUILD_TARGET_DIR}/${BINARY_NAME}"

# 输出构建信息
echo "===== 构建成功 ====="
echo "二进制文件位置: ${BUILD_TARGET_DIR}/${BINARY_NAME}"
echo "最终文件大小: $(du -h "${BUILD_TARGET_DIR}/${BINARY_NAME}" | awk '{print $1}')"
echo "MD5 校验和: $(md5sum "${BUILD_TARGET_DIR}/${BINARY_NAME}" | awk '{print $1}')"

# 确保部署目录存在
echo "准备部署目录..."
mkdir -p "${DEPLOY_DIR}"

# 将构建好的二进制文件移动到部署目录
echo "移动二进制文件到部署目录..."
mv "${BUILD_TARGET_DIR}/${BINARY_NAME}" "${DEPLOY_DIR}/"

if [ ! -f "${DEPLOY_DIR}/${BINARY_NAME}" ]; then
    echo "错误: 二进制文件移动失败"
    exit 1
fi

echo "部署完成，新二进制文件已部署到: ${DEPLOY_DIR}/${BINARY_NAME}"

# 检查 supervisorctl 是否可用
echo "检查 supervisorctl 状态..."
if ! command -v supervisorctl &> /dev/null; then
    echo "警告: 未找到 supervisorctl 命令，无法重启服务"
    exit 0
fi

# 检查服务是否存在（使用sudo）
echo "检查 supervisor 服务是否存在..."
if ! sudo supervisorctl status "${SUPERVISOR_PROCESS_NAME}" &> /dev/null; then
    echo "警告: supervisor 中未找到名为 ${SUPERVISOR_PROCESS_NAME} 的进程"
    exit 0
fi

# 重启 supervisor 服务（使用sudo）
echo "重启 ${SUPERVISOR_PROCESS_NAME} 服务..."
sudo supervisorctl restart "${SUPERVISOR_PROCESS_NAME}"
if [ $? -ne 0 ]; then
    echo "错误: 重启服务失败"
    exit 1
fi

echo "===== 构建、部署和重启服务完成 ====="
exit 0
